import 'package:detoxme/data/local/models/failed_update_hive_model.dart';
import 'package:detoxme/data/local/models/queued_update_hive_model.dart';

/// Abstract repository for managing the generic queue of pending updates.
/// This contract is defined in the domain layer, and implemented in the data layer.
abstract class LocalSyncQueueRepository {
  /// Adds a generic update to the sync queue.
  Future<void> addUpdateToQueue(QueuedUpdateHiveModel update);

  /// Retrieves all pending updates from the queue.
  /// Returns a list of the queued generic models.
  Future<List<QueuedUpdateHiveModel>> getQueuedUpdates();

  /// Removes a specific update from the queue using its Hive key.
  Future<void> removeUpdateFromQueue(dynamic key);

  /// Adds a permanently failed update to the dead-letter queue.
  Future<void> addFailedUpdate(FailedUpdateHiveModel failedUpdate);
}
