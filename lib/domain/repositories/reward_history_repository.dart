import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository interface for handling reward history
abstract class RewardHistoryRepository {
  /// Get all rewards for the current user
  Future<Either<Failure, List<Reward>>> getRewardHistory();

  /// Get reward history summary by type (counts and total points per type)
  Future<Either<Failure, Map<String, RewardSummary>>> getRewardSummary();

  /// Add a custom reward (for testing or manual additions)
  Future<Either<Failure, Reward>> addCustomReward({
    required String title,
    required String description,
    required int pointsEarned,
    required String rewardType,
    Map<String, dynamic>? metadata,
  });
}

/// Summary of rewards by type
class RewardSummary {
  /// Type of reward
  final String rewardType;

  /// Count of rewards of this type
  final int count;

  /// Total points earned from this type
  final int totalPoints;

  /// Constructor for RewardSummary
  const RewardSummary({
    required this.rewardType,
    required this.count,
    required this.totalPoints,
  });
}
