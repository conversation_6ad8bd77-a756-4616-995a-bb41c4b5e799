import 'package:dartz/dartz.dart';
import '../entities/detox_points.dart';
import '../entities/reward.dart';
import '../entities/points_history.dart';
import '../failure/failure.dart';

/// Repository interface for managing detox points and rewards
abstract class DetoxPointsRepository {
  /// Get the current user's detox points
  Future<Either<Failure, DetoxPoints>> getUserPoints(String userId);

  /// Get the leaderboard of top users by detox points
  Future<Either<Failure, List<DetoxPoints>>> getLeaderboard({int limit = 10});

  /// Add points to a user's account
  Future<Either<Failure, DetoxPoints>> addPoints(
    String userId,
    int points,
    String reason,
  );

  /// Get available rewards that can be redeemed with points
  Future<Either<Failure, List<Reward>>> getAvailableRewards();

  /// Get rewards filtered by category
  Future<Either<Failure, List<Reward>>> getRewardsByCategory(
    RewardCategory category,
  );

  /// Get rewards filtered by location (distance in kilometers)
  Future<Either<Failure, List<Reward>>> getRewardsByDistance(
    double latitude,
    double longitude,
    double radiusKm,
  );

  /// Redeem a reward with user points
  Future<Either<Failure, bool>> redeemReward(String userId, String rewardId);

  /// Get a user's redemption history
  Future<Either<Failure, List<Reward>>> getUserRedemptionHistory(String userId);

  /// Get points history for different time periods
  Future<Either<Failure, PointsHistory>> getPointsHistory(String userId);
}
