import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository interface for the Food Mood feature
abstract class FoodMoodRepository {
  /// Get all food recommendations
  Future<Either<Failure, List<FoodRecommendation>>> getAllFoodRecommendations({
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
  });

  /// Get food recommendations for a specific mood
  Future<Either<Failure, List<FoodRecommendation>>>
  getFoodRecommendationsForMood(
    MoodType mood, {
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
    String? countryCode,
  });

  /// Get a food recommendation by ID
  Future<Either<Failure, FoodRecommendation>> getFoodRecommendationById(
    String id,
  );

  /// Submit a vote for a food recommendation
  /// Returns the resulting vote status (null if vote was removed)
  Future<Either<Failure, VoteType?>> submitVote(
    String recommendationId,
    VoteType vote,
  );

  /// Get food recommendations with optional filters
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  });
}
