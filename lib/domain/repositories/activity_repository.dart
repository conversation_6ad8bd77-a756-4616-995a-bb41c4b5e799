import 'package:dartz/dartz.dart';

import '../entities/activity.dart';
import '../entities/activity_comment.dart';
import '../entities/mood.dart';
import '../failure/failure.dart';

/// Repository interface for managing activities
abstract class ActivityRepository {
  /// Get all available activities
  Future<Either<Failure, List<Activity>>> getActivities();

  /// Get activities recommended for a specific mood
  Future<Either<Failure, List<Activity>>> getActivitiesForMood(MoodType mood);

  /// Log a user's mood
  Future<Either<Failure, void>> logMood(String userId, Mood mood);

  /// Get user's mood history
  Future<Either<Failure, List<Mood>>> getMoodHistory(String userId);

  /// Create a new user activity
  Future<Either<Failure, Activity>> createActivity({
    required String userId,
    required String title,
    required String description,
    required ActivityCategory category,
    required int duration,
    required List<MoodType> recommendedFor,
    List<String> healthBenefits = const [],
    String? imageUrl,
    String? creatorName,
    bool showCreatorName = true,
    bool isPrivate = false,
    String countryCode = 'en',
  });

  /// Get activities created by a specific user
  Future<Either<Failure, List<Activity>>> getUserActivities(String userId);

  /// Update an existing activity
  Future<Either<Failure, Activity>> updateActivity(Activity activity);

  /// Delete an activity
  Future<Either<Failure, void>> deleteActivity(
    String activityId,
    String userId,
  );

  /// Upvote an activity
  Future<Either<Failure, Activity>> upvoteActivity(
    String activityId,
    String userId,
  );

  /// Downvote an activity
  Future<Either<Failure, Activity>> downvoteActivity(
    String activityId,
    String userId,
  );

  /// Remove a vote from an activity
  Future<Either<Failure, Activity>> removeVote(
    String activityId,
    String userId,
  );
  
  /// Get an activity by its ID
  Future<Either<Failure, Activity>> getActivityById(String activityId);

  /// Get sorted and filtered activities
  Future<Either<Failure, List<Activity>>> getFilteredActivities({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    bool includePrivate = false,
    String? userId,
    bool hideDownvoted = true,
    List<String>? countryCodes,
  });

  /// Save activity as favorite
  Future<Either<Failure, void>> setFavorite(
    String activityId,
    String userId,
    bool isFavorite,
  );

  /// Get user's favorite activities
  Future<Either<Failure, List<Activity>>> getFavoriteActivities(String userId);

  /// Save food as favorite
  Future<Either<Failure, void>> setFoodFavorite(
    String foodId,
    String userId,
    bool isFavorite,
  );

  /// Get user's favorite foods
  Future<Either<Failure, List<dynamic>>> getFavoriteFoods(String userId);

  /// Add a comment to an activity
  Future<Either<Failure, ActivityComment>> addComment({
    required String activityId,
    required String userId,
    required String userName,
    required String text,
  });

  /// Get comments for an activity
  Future<Either<Failure, List<ActivityComment>>> getComments(String activityId);

  /// Get comments for an activity as a stream
  Stream<List<ActivityComment>> getCommentsStream(String activityId);

  /// Delete a comment
  Future<Either<Failure, void>> deleteComment({
    required String commentId,
    required String userId,
  });

  /// Edit a comment
  Future<Either<Failure, ActivityComment>> editComment({
    required String commentId,
    required String userId,
    required String newText,
  });

  /// Like or unlike a comment
  Future<Either<Failure, ActivityComment>> toggleCommentLike({
    required String commentId,
    required String userId,
  });

  /// Report a comment
  Future<Either<Failure, void>> reportComment({
    required String commentId,
    required String userId,
    required String reason,
  });

  /// Updates the activity status after completion or abortion
  ///
  /// [activityId] ID of the activity to update
  /// [userId] ID of the user who performed the activity
  /// [completed] Whether the activity was completed successfully
  /// [timeSpent] Duration spent on the activity
  Future<Either<Failure, void>> updateActivityStatus({
    required String activityId,
    required String userId,
    required bool completed,
    required Duration timeSpent,
  });
}

/// Options for sorting activities
enum ActivitySortOption {
  /// Sort by newest first
  newest,

  /// Sort by oldest first
  oldest,

  /// Sort by most upvotes
  mostUpvoted,

  /// Sort by highest upvote ratio
  highestRated,

  /// Sort by most popular (most total votes)
  mostPopular,
}
