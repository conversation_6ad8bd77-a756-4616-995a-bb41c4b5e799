import 'package:detoxme/domain/entities/device_token.dart';

/// Repository interface for managing device FCM tokens
abstract class DeviceTokenRepository {
  /// Save or update an FCM token for a user
  Future<DeviceToken> saveToken(DeviceToken token);

  /// Get all tokens for a specific user
  Future<List<DeviceToken>> getTokensForUser(String userId);

  /// Delete a specific token
  Future<void> deleteToken(String tokenId);

  /// Delete all tokens for a user (e.g., on logout)
  Future<void> deleteAllUserTokens(String userId);
}
