import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/redemption.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository interface for coupon-related operations
abstract class CouponRepository {
  /// Retrieves all available coupons
  Future<Either<Failure, List<Coupon>>> getAllCoupons();

  /// Retrieves coupons recommended for a specific mood
  Future<Either<Failure, List<Coupon>>> getCouponsForMood(MoodType mood);

  /// Retrieves a coupon by its ID
  Future<Either<Failure, Coupon>> getCouponById(String couponId);

  /// Redeems a coupon for the current user
  Future<Either<Failure, Redemption>> redeemCoupon(String couponId);

  /// Generates a QR code for a redemption
  Future<Either<Failure, String>> generateQRCode(String redemptionId);

  /// Provides feedback for a redeemed coupon
  Future<Either<Failure, void>> provideFeedback(
    String redemptionId,
    int rating, {
    String? comment,
  });

  /// Gets all redemptions for the current user
  Future<Either<Failure, List<Redemption>>> getUserRedemptions();

  /// Gets all partners
  Future<Either<Failure, List<Partner>>> getAllPartners();

  /// Gets partners near a location
  Future<Either<Failure, List<Partner>>> getNearbyPartners(
    double latitude,
    double longitude,
    double radiusKm,
  );
}
