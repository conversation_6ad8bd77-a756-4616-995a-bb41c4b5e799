import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/mood_quote.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository interface for mood quotes
abstract class MoodQuoteRepository {
  /// Gets quotes based on mood type and language
  Future<Either<Failure, List<MoodQuote>>> getQuotesByMood({
    required String moodType,
    required String language,
    int? limit,
  });

  /// Gets a random quote for the specified mood and language
  Future<Either<Failure, MoodQuote>> getRandomQuoteByMood({
    required String moodType,
    required String language,
  });

  /// Gets all available mood types
  Future<Either<Failure, List<String>>> getAvailableMoodTypes();

  /// Creates a new mood quote (admin only)
  Future<Either<Failure, MoodQuote>> createMoodQuote(MoodQuote quote);

  /// Updates an existing mood quote (admin only)
  Future<Either<Failure, MoodQuote>> updateMoodQuote(MoodQuote quote);

  /// Deletes a mood quote (admin only)
  Future<Either<Failure, void>> deleteMoodQuote(String id);
}