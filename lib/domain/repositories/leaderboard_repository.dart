import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/leaderboard_entry.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Abstract repository for fetching leaderboard data.
abstract class LeaderboardRepository {
  /// Fetches the top leaderboard entries.
  ///
  /// Returns a [Failure] on error or a list of [LeaderboardEntry] on success.
  Future<Either<Failure, List<LeaderboardEntry>>> getLeaderboard();
}
