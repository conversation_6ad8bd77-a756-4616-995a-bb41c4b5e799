import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/user_profile.dart'; // Use the model
import 'package:detoxme/domain/failure/failure.dart';

/// Status of a profile save operation
enum SyncStatus {
  /// Data saved to local storage only, will sync later
  syncedLocally,

  /// Data saved to local storage and remote server
  syncedRemotely,
}

abstract class UserRepository {
  // Renamed interface
  Future<Either<Failure, UserProfile>> getProfile(); // Use the model
  // Keep SyncStatus return type for now, can simplify later if needed
  Future<Either<Failure, SyncStatus>> saveProfile(
    UserProfile profile,
  ); // Use the model
  
  /// Gets profile by ID (for getting other users' profiles)
  Future<Either<Failure, UserProfile?>> getProfileById(String userId);

  /// Checks if a profile exists
  Future<Either<Failure, bool>> profileExists(String userId);
}
