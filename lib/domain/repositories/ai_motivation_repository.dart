import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository for accessing AI motivational content
abstract class AiMotivationRepository {
  /// Get the best matching motivational content for a user
  /// 
  /// Prioritizes matches in this order:
  /// 1. language
  /// 2. life_situation
  /// 3. goal
  /// 4. gender
  Future<Either<Failure, AiMotivation>> getBestMotivationMatch({
    required String language,
    required String lifeSituation,
    required List<String> goals,
    required String gender,
    required int birthYear,
  });

  /// Get all available motivations
  Future<Either<Failure, List<AiMotivation>>> getAllMotivations();
}
