import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/user_notification.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Repository interface for managing notifications
abstract class NotificationRepository {
  /// Get notifications for a user as a stream
  Stream<List<UserNotification>> getUserNotificationsStream(String userId);

  /// Get notifications for a user (one-time fetch)
  Future<Either<Failure, List<UserNotification>>> getUserNotifications(
    String userId,
  );

  /// Mark a notification as read
  Future<Either<Failure, void>> markNotificationAsRead(
    String notificationId,
    String userId,
  );

  /// Mark all notifications as read for a user
  Future<Either<Failure, void>> markAllNotificationsAsRead(String userId);

  /// Delete a notification
  Future<Either<Failure, void>> deleteNotification(
    String notificationId,
    String userId,
  );

  /// Create a new notification
  Future<Either<Failure, UserNotification>> createNotification({
    required String userId,
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? actionData,
  });

  /// Get count of unread notifications
  Future<Either<Failure, int>> getUnreadNotificationCount(String userId);

  /// Stream of unread notification count
  Stream<int> getUnreadNotificationCountStream(String userId);

  /// Create a comment like notification
  Future<Either<Failure, UserNotification>> createCommentLikeNotification({
    required String commentAuthorId,
    required String likerUserName,
    required String commentText,
    required String activityId,
    required String commentId,
  });
}
