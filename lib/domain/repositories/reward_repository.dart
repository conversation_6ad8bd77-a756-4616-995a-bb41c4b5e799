import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Enum indicating the result of a save operation involving sync.
enum SyncStatus {
  syncedLocally, // Saved to local storage only (e.g., offline or remote error)
  syncedRemotely, // Saved locally and successfully synced to remote server
}

/// Abstract repository for managing player rewards and stats.
abstract class RewardRepository {
  /// Retrieves the current player statistics.
  Future<Either<Failure, PlayerStats>> getPlayerStats();

  /// Saves the player statistics.
  /// Returns [SyncStatus] on success indicating if remote sync also occurred.
  Future<Either<Failure, SyncStatus>> savePlayerStats(PlayerStats stats);
  
  /// Retrieves the list of rewards that have been redeemed by the player.
  Future<List<Reward>> getRedeemedRewards();
  
  // Maybe add methods later for getting specific badge/card definitions
}
