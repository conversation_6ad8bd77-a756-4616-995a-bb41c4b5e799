import 'package:equatable/equatable.dart';

/// Model representing an AI-based personalized motivation
class AiMotivation extends Equatable {
  /// Life situation of the target user
  final String lifeSituation;

  /// Gender of the target user
  final String gender;

  /// Age group of the target user
  final String ageGroup;

  /// Goal that this motivation addresses
  final String goal;

  /// Language of the motivation text
  final String language;

  /// The motivational text to show
  final String motivationalText;

  /// The ID of the activity related to this motivation
  final String activityId;

  /// Create a new AI Motivation
  const AiMotivation({
    required this.lifeSituation,
    required this.gender,
    required this.ageGroup,
    required this.goal,
    required this.language,
    required this.motivationalText,
    required this.activityId,
  });

  /// Create an empty AI Motivation
  factory AiMotivation.empty() => const AiMotivation(
        lifeSituation: '',
        gender: '',
        ageGroup: '',
        goal: '',
        language: 'en',
        motivationalText: '',
        activityId: '',
      );

  /// Convert to a map for database storage
  Map<String, dynamic> toMap() {
    return {
      'life_situation': lifeSituation,
      'gender': gender,
      'age_group': ageGroup,
      'goal': goal,
      'language': language,
      'motivational_text': motivationalText,
      'activity_id': activityId,
    };
  }

  /// Create an AI Motivation from a database map
  factory AiMotivation.fromMap(Map<String, dynamic> map) {
    return AiMotivation(
      lifeSituation: map['life_situation'] as String,
      gender: map['gender'] as String,
      ageGroup: map['age_group'] as String,
      goal: map['goal'] as String,
      language: map['language'] as String,
      motivationalText: map['motivational_text'] as String,
      activityId: map['activity_id'] as String,
    );
  }

  /// Create a copy with certain fields replaced
  AiMotivation copyWith({
    String? lifeSituation,
    String? gender,
    String? ageGroup,
    String? goal,
    String? language,
    String? motivationalText,
    String? activityId,
  }) {
    return AiMotivation(
      lifeSituation: lifeSituation ?? this.lifeSituation,
      gender: gender ?? this.gender,
      ageGroup: ageGroup ?? this.ageGroup,
      goal: goal ?? this.goal,
      language: language ?? this.language,
      motivationalText: motivationalText ?? this.motivationalText,
      activityId: activityId ?? this.activityId,
    );
  }

  @override
  List<Object> get props => [
        lifeSituation,
        gender,
        ageGroup,
        goal,
        language,
        motivationalText,
        activityId,
      ];
}
