import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents different weather conditions
enum WeatherCondition {
  sunny,
  partlyCloudy,
  cloudy,
  rainy,
  stormy,
  snowy,
  foggy,
  windy,
  unknown,
}

/// Weather data model to store weather information
class WeatherData extends Equatable {
  /// Temperature in Celsius
  final double temperature;

  /// Weather condition (Clear, Clouds, Rain, etc.)
  final WeatherCondition condition;

  /// Detailed weather description
  final String description;

  /// Icon code from OpenWeatherMap
  final String iconCode;

  /// Location name
  final String location;

  /// Timestamp of weather data
  final DateTime timestamp;

  /// Humidity percentage
  final int humidity;

  /// Wind speed in km/h
  final double windSpeed;

  /// Visibility in kilometers
  final double visibility;

  /// Constructor
  const WeatherData({
    required this.temperature,
    required this.condition,
    required this.description,
    required this.iconCode,
    required this.location,
    required this.timestamp,
    required this.humidity,
    required this.windSpeed,
    required this.visibility,
  });

  /// Create from JSON
  factory WeatherData.fromJson(Map<String, dynamic> json) {
    final weatherJson = json['weather'][0];

    return WeatherData(
      temperature: (json['main']['temp'] as num).toDouble(),
      condition: WeatherCondition.values[json['weather'][0]['main']],
      description: weatherJson['description'],
      iconCode: weatherJson['icon'],
      location: json['name'],
      timestamp: DateTime.now(),
      humidity: json['main']['humidity'] as int,
      windSpeed: json['wind']['speed'] as double,
      visibility: json['visibility'] as double,
    );
  }

  /// Get icon URL
  String get iconUrl => 'https://openweathermap.org/img/wn/$<EMAIL>';

  /// Get weather title (friendly display)
  String get weatherTitle {
    switch (condition) {
      case WeatherCondition.sunny:
        return iconCode.endsWith('n') ? 'Clear Night' : 'Sunny';
      case WeatherCondition.rainy:
        return 'Rainy';
      case WeatherCondition.stormy:
        return 'Stormy';
      default:
        return condition.toString().split('.').last;
    }
  }

  /// Get emoji for weather condition
  String get emoji {
    switch (condition) {
      case WeatherCondition.sunny:
        return iconCode.endsWith('n') ? '🌙' : '☀️';
      case WeatherCondition.partlyCloudy:
        return iconCode == '02d' || iconCode == '02n' ? '⛅' : '☁️';
      case WeatherCondition.rainy:
        return '🌧️';
      case WeatherCondition.stormy:
        return '⚡';
      case WeatherCondition.snowy:
        return '❄️';
      case WeatherCondition.foggy:
        return '🌫️';
      default:
        return '🌡️';
    }
  }

  /// Get color for weather condition
  Color get weatherColor {
    switch (condition) {
      case WeatherCondition.sunny:
        return iconCode.endsWith('n')
            ? const Color(0xFF1A237E) // Deep blue for night
            : const Color(0xFFFFA000); // Orange for sunny
      case WeatherCondition.partlyCloudy:
        return const Color(0xFF78909C); // Blueish gray
      case WeatherCondition.rainy:
        return const Color(0xFF42A5F5); // Light blue
      case WeatherCondition.stormy:
        return const Color(0xFF5E35B1); // Deep purple
      case WeatherCondition.snowy:
        return const Color(0xFFE0E0E0); // Light gray
      case WeatherCondition.foggy:
        return const Color(0xFFBDBDBD); // Gray
      default:
        return const Color(0xFF90CAF9); // Default light blue
    }
  }

  /// Generate a weather greeting with localized time greetings
  String getWeatherGreeting({
    required String goodMorning,
    required String goodAfternoon,
    required String goodEvening,
    required String goodNight,
  }) {
    final hour = timestamp.hour;
    String timeGreeting;

    // Time-based greeting
    if (hour >= 5 && hour < 12) {
      timeGreeting = goodMorning;
    } else if (hour >= 12 && hour < 17) {
      timeGreeting = goodAfternoon;
    } else if (hour >= 17 && hour < 21) {
      timeGreeting = goodEvening;
    } else {
      timeGreeting = goodNight;
    }

    // Weather-based addition
    if (condition == WeatherCondition.sunny && !iconCode.endsWith('n')) {
      return '$timeGreeting! It\'s a beautiful sunny day.';
    } else if (condition == WeatherCondition.sunny && iconCode.endsWith('n')) {
      return '$timeGreeting! Enjoy the clear night sky.';
    } else if (condition == WeatherCondition.rainy ||
        condition == WeatherCondition.rainy) {
      return '$timeGreeting! Stay dry in this rainy weather.';
    } else if (condition == WeatherCondition.snowy) {
      return '$timeGreeting! Bundle up, it\'s snowing outside.';
    } else if (condition == WeatherCondition.stormy) {
      return '$timeGreeting! Be safe during the thunderstorm.';
    } else {
      return '$timeGreeting! Have a wonderful day.';
    }
  }

  @override
  List<Object?> get props => [
    temperature,
    condition,
    description,
    iconCode,
    location,
    timestamp,
    humidity,
    windSpeed,
    visibility,
  ];

  /// Creates a copy of this WeatherData with the given fields replaced
  WeatherData copyWith({
    double? temperature,
    WeatherCondition? condition,
    String? description,
    String? iconCode,
    String? location,
    DateTime? timestamp,
    int? humidity,
    double? windSpeed,
    double? visibility,
  }) {
    return WeatherData(
      temperature: temperature ?? this.temperature,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      iconCode: iconCode ?? this.iconCode,
      location: location ?? this.location,
      timestamp: timestamp ?? this.timestamp,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      visibility: visibility ?? this.visibility,
    );
  }
}
