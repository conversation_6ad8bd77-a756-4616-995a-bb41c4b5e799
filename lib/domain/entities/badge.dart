import 'package:equatable/equatable.dart';

class Badge extends Equatable {
  final String id; // Unique ID for the badge
  final String name;
  final String description;
  final String imageAssetPath; // Path to the badge image

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.imageAssetPath,
  });

  @override
  List<Object?> get props => [id, name, description, imageAssetPath];
}
