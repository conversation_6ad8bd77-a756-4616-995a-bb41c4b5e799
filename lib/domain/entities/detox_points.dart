import 'package:equatable/equatable.dart';

/// Entity representing a user's detox points balance and history
class DetoxPoints extends Equatable {
  /// Unique identifier for the points record
  final String id;

  /// User ID to whom these points belong
  final String userId;

  /// User's display name
  final String userName;

  /// URL to user's avatar image
  final String? avatarUrl;

  /// User's current level
  final int level;

  /// Points needed to reach next level
  final int pointsToNextLevel;

  /// Total number of points earned by the user
  final int totalPoints;

  /// Points earned today
  final int dailyPoints;

  /// User's daily goal target
  final int dailyGoalTarget;

  /// Date for the daily points/goal
  final DateTime dailyDate;

  /// Number of points currently available for redemption
  final int availablePoints;

  /// Number of points used for rewards
  final int usedPoints;

  /// Date when the points were last updated
  final DateTime lastUpdated;

  /// Points history (optional)
  final List<PointsTransaction>? history;

  const DetoxPoints({
    required this.id,
    required this.userId,
    required this.userName,
    this.avatarUrl,
    required this.level,
    required this.pointsToNextLevel,
    required this.totalPoints,
    required this.dailyPoints,
    required this.dailyGoalTarget,
    required this.dailyDate,
    required this.availablePoints,
    required this.usedPoints,
    required this.lastUpdated,
    this.history,
  });

  /// Create a copy of this DetoxPoints with modified values
  DetoxPoints copyWith({
    String? id,
    String? userId,
    String? userName,
    String? avatarUrl,
    int? level,
    int? pointsToNextLevel,
    int? totalPoints,
    int? dailyPoints,
    int? dailyGoalTarget,
    DateTime? dailyDate,
    int? availablePoints,
    int? usedPoints,
    DateTime? lastUpdated,
    List<PointsTransaction>? history,
  }) {
    return DetoxPoints(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      level: level ?? this.level,
      pointsToNextLevel: pointsToNextLevel ?? this.pointsToNextLevel,
      totalPoints: totalPoints ?? this.totalPoints,
      dailyPoints: dailyPoints ?? this.dailyPoints,
      dailyGoalTarget: dailyGoalTarget ?? this.dailyGoalTarget,
      dailyDate: dailyDate ?? this.dailyDate,
      availablePoints: availablePoints ?? this.availablePoints,
      usedPoints: usedPoints ?? this.usedPoints,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      history: history ?? this.history,
    );
  }

  /// Factory constructor to create DetoxPoints from a JSON map
  factory DetoxPoints.fromJson(Map<String, dynamic> json) {
    return DetoxPoints(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      userName: json['user_name'] as String? ?? 'User',
      avatarUrl: json['avatar_url'] as String?,
      level: json['level'] as int? ?? 1,
      pointsToNextLevel: json['points_to_next_level'] as int? ?? 100,
      totalPoints: json['total_points'] as int,
      dailyPoints: json['daily_points'] as int? ?? 0,
      dailyGoalTarget:
          json['daily_goal_target'] as int? ?? 50, // Default daily goal is 50
      dailyDate: DateTime.parse(json['daily_date'] as String),
      availablePoints: json['available_points'] as int,
      usedPoints: json['used_points'] as int,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      history:
          json['history'] != null
              ? (json['history'] as List)
                  .map((e) => PointsTransaction.fromJson(e))
                  .toList()
              : null,
    );
  }

  /// Convert this DetoxPoints to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
      'level': level,
      'points_to_next_level': pointsToNextLevel,
      'total_points': totalPoints,
      'daily_points': dailyPoints,
      'daily_goal_target': dailyGoalTarget,
      'daily_date': dailyDate.toIso8601String(),
      'available_points': availablePoints,
      'used_points': usedPoints,
      'last_updated': lastUpdated.toIso8601String(),
      if (history != null) 'history': history!.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    userName,
    avatarUrl,
    level,
    pointsToNextLevel,
    totalPoints,
    dailyPoints,
    dailyGoalTarget,
    dailyDate,
    availablePoints,
    usedPoints,
    lastUpdated,
    history,
  ];
}

/// Class representing a transaction in the points history
class PointsTransaction extends Equatable {
  /// Unique identifier for the transaction
  final String id;

  /// Points amount (positive for earned, negative for spent)
  final int amount;

  /// Description of the transaction
  final String description;

  /// Type of transaction
  final PointsTransactionType type;

  /// Date when the transaction occurred
  final DateTime date;

  /// Reference ID (task ID, reward ID, etc.)
  final String? referenceId;

  const PointsTransaction({
    required this.id,
    required this.amount,
    required this.description,
    required this.type,
    required this.date,
    this.referenceId,
  });

  /// Create a copy of this PointsTransaction with modified values
  PointsTransaction copyWith({
    String? id,
    int? amount,
    String? description,
    PointsTransactionType? type,
    DateTime? date,
    String? referenceId,
  }) {
    return PointsTransaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      type: type ?? this.type,
      date: date ?? this.date,
      referenceId: referenceId ?? this.referenceId,
    );
  }

  /// Factory constructor to create PointsTransaction from a JSON map
  factory PointsTransaction.fromJson(Map<String, dynamic> json) {
    return PointsTransaction(
      id: json['id'] as String,
      amount: json['amount'] as int,
      description: json['description'] as String,
      type: PointsTransactionType.values.firstWhere(
        (e) => e.toString() == 'PointsTransactionType.${json['type']}',
      ),
      date: DateTime.parse(json['date'] as String),
      referenceId: json['reference_id'] as String?,
    );
  }

  /// Convert this PointsTransaction to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'description': description,
      'type': type.toString().split('.').last,
      'date': date.toIso8601String(),
      if (referenceId != null) 'reference_id': referenceId,
    };
  }

  @override
  List<Object?> get props => [id, amount, description, type, date, referenceId];
}

/// Enum for transaction types
enum PointsTransactionType {
  /// Points earned from completing tasks
  taskCompletion,

  /// Points earned from achievements
  achievement,

  /// Points spent on rewards
  rewardRedemption,

  /// Points awarded by admin
  adminAward,

  /// Points awarded for referrals
  referral,

  /// Points from other sources
  other,
}
