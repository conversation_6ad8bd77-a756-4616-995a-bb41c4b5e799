import 'package:equatable/equatable.dart';

class CollectibleCard extends Equatable {
  final String id; // Unique ID for the card
  final String name;
  final String description; // Optional flavour text
  final String imageAssetPath; // Path to the card image
  final String rarity; // e.g., "Common", "Rare", "Epic"

  const CollectibleCard({
    required this.id,
    required this.name,
    required this.description,
    required this.imageAssetPath,
    required this.rarity,
  });

  @override
  List<Object?> get props => [id, name, description, imageAssetPath, rarity];
}
