import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'food_mood.g.dart';

/// Enum for sorting food recommendations
@HiveType(typeId: 13)
enum FoodSortOption {
  /// Default sorting (by name or ID)
  @HiveField(0)
  default_,

  /// Sort by most upvoted
  @HiveField(1)
  highestVotes,

  /// Sort by newest
  @HiveField(2)
  newest,

  /// Sort by oldest first
  @HiveField(3)
  oldest,
}

/// Type of vote a user can cast on a food recommendation
@HiveType(typeId: 12)
enum VoteType {
  /// Upvote (positive)
  @HiveField(0)
  upvote,

  /// Downvote (negative)
  @HiveField(1)
  downvote,
}

/// A food recommendation entity
@HiveType(typeId: 10)
class FoodRecommendation extends Equatable {
  /// Unique identifier
  @HiveField(0)
  final String id;

  /// Name of the food
  @HiveField(1)
  final String name;

  /// Description of the food
  @HiveField(2)
  final String description;

  /// URL to the food's image
  @HiveField(3)
  final String imageUrl;

  /// When the recommendation was created
  @HiveField(4)
  final DateTime createdAt;

  /// When the recommendation was last updated
  @HiveField(5)
  final DateTime updatedAt;

  /// Number of upvotes
  @HiveField(6)
  final int upvotes;

  /// Number of downvotes
  @HiveField(7)
  final int downvotes;

  /// The current user's vote on this recommendation
  @HiveField(8)
  final VoteType? userVote;

  /// User ID who created the recommendation
  @HiveField(9)
  final String userId;

  /// Username who created the recommendation
  @HiveField(10)
  final String username;

  /// User avatar URL who created the recommendation
  @HiveField(11)
  final String? userAvatarUrl;

  /// List of moods this food is recommended for
  @HiveField(12)
  final List<MoodType>? moodTypes;

  /// Country code for which this recommendation is relevant
  @HiveField(13)
  final String countryCode;

  /// Creates a new [FoodRecommendation]
  const FoodRecommendation({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.upvotes,
    required this.downvotes,
    this.userVote,
    required this.userId,
    required this.username,
    this.userAvatarUrl,
    this.moodTypes,
    required this.countryCode,
  });

  /// Get the total number of votes (upvotes - downvotes)
  int get totalVotes => upvotes - downvotes;

  /// Creates a copy of this recommendation with the given fields replaced with new values
  FoodRecommendation copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? upvotes,
    int? downvotes,
    VoteType? userVote,
    String? userId,
    String? username,
    String? userAvatarUrl,
    List<MoodType>? moodTypes,
    String? countryCode,
  }) {
    return FoodRecommendation(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      upvotes: upvotes ?? this.upvotes,
      downvotes: downvotes ?? this.downvotes,
      userVote: userVote ?? this.userVote,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userAvatarUrl: userAvatarUrl ?? this.userAvatarUrl,
      moodTypes: moodTypes ?? this.moodTypes,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    imageUrl,
    createdAt,
    updatedAt,
    upvotes,
    downvotes,
    userVote,
    userId,
    username,
    userAvatarUrl,
    moodTypes,
    countryCode,
  ];
}
