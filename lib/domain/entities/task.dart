import 'package:equatable/equatable.dart';

class Task extends Equatable {
  final String id; // Unique identifier for the task
  final String title;
  final String description;
  final Duration duration; // How long the task/break should last

  const Task({
    required this.id,
    required this.title,
    required this.description,
    required this.duration,
  });

  @override
  List<Object?> get props => [id, title, description, duration];
}
