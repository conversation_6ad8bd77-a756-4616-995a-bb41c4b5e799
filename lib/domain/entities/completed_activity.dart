import 'package:detoxme/domain/entities/activity.dart';

/// Represents a completed activity by a user
class CompletedActivity {
  /// Unique identifier for the completed activity
  final String id;
  
  /// The user who completed the activity
  final String userId;
  
  /// The activity that was completed
  final Activity activity;
  
  /// When the activity was completed
  final DateTime completedAt;
  
  /// Optional notes or reflection from the user
  final String? notes;
  
  /// Optional mood after completing the activity
  final String? moodAfter;
  
  /// Optional rating of the activity (1-5)
  final int? rating;

  /// Creates a new CompletedActivity
  const CompletedActivity({
    required this.id,
    required this.userId,
    required this.activity,
    required this.completedAt,
    this.notes,
    this.moodAfter,
    this.rating,
  });
  
  /// Creates a copy of this CompletedActivity with the given fields replaced with the new values
  CompletedActivity copyWith({
    String? id,
    String? userId,
    Activity? activity,
    DateTime? completedAt,
    String? notes,
    String? moodAfter,
    int? rating,
  }) {
    return CompletedActivity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      activity: activity ?? this.activity,
      completedAt: completedAt ?? this.completedAt,
      notes: notes ?? this.notes,
      moodAfter: moodAfter ?? this.moodAfter,
      rating: rating ?? this.rating,
    );
  }
}
