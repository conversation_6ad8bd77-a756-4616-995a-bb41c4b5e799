import 'package:equatable/equatable.dart';

/// Represents a coupon redemption by a user
class Redemption extends Equatable {
  /// Unique identifier for the redemption
  final String id;
  
  /// The ID of the coupon that was redeemed
  final String couponId;
  
  /// The ID of the user who redeemed the coupon
  final String userId;
  
  /// Timestamp when the coupon was redeemed
  final DateTime redeemedAt;
  
  /// Unique QR code for this redemption
  final String qrCode;
  
  /// Whether the user provided feedback after redemption
  final bool hasFeedback;
  
  /// User's feedback rating (1-5), null if no feedback
  final int? feedbackRating;
  
  /// User's feedback comment, null if no comment
  final String? feedbackComment;

  /// Constructor for Redemption
  const Redemption({
    required this.id,
    required this.couponId,
    required this.userId,
    required this.redeemedAt,
    required this.qrCode,
    this.hasFeedback = false,
    this.feedbackRating,
    this.feedbackComment,
  });

  @override
  List<Object?> get props => [
        id,
        couponId,
        userId,
        redeemedAt,
        qrCode,
        hasFeedback,
        feedbackRating,
        feedbackComment,
      ];

  /// Creates a copy of this Redemption with the given fields replaced with the new values
  Redemption copyWith({
    String? id,
    String? couponId,
    String? userId,
    DateTime? redeemedAt,
    String? qrCode,
    bool? hasFeedback,
    int? feedbackRating,
    String? feedbackComment,
  }) {
    return Redemption(
      id: id ?? this.id,
      couponId: couponId ?? this.couponId,
      userId: userId ?? this.userId,
      redeemedAt: redeemedAt ?? this.redeemedAt,
      qrCode: qrCode ?? this.qrCode,
      hasFeedback: hasFeedback ?? this.hasFeedback,
      feedbackRating: feedbackRating ?? this.feedbackRating,
      feedbackComment: feedbackComment ?? this.feedbackComment,
    );
  }
}
