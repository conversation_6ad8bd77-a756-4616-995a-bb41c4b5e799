import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Represents a coupon offered by a partner
class Coupon extends Equatable {
  /// Unique identifier for the coupon
  final String id;

  /// Partner that offers this coupon
  final Partner partner;

  /// Description of the coupon
  final String description;

  /// Discount value (percentage or fixed amount)
  final String discount;

  /// Points required to unlock this coupon
  final int pointsRequired;

  /// Expiration date of the coupon
  final DateTime expirationDate;

  /// List of mood types this coupon is recommended for
  final List<MoodType> recommendedForMoods;

  /// Number of coupons available
  final int? quantity;

  /// Number of redemptions so far
  final int redemptionsCount;

  /// Points earned when coupon is redeemed
  final int pointsEarned;

  /// Whether the coupon is active
  final bool isActive;

  /// Constructor for Coupon
  const Coupon({
    required this.id,
    required this.partner,
    required this.description,
    required this.discount,
    required this.pointsRequired,
    required this.expirationDate,
    required this.recommendedForMoods,
    this.quantity,
    this.redemptionsCount = 0,
    this.pointsEarned = 50,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        id,
        partner,
        description,
        discount,
        pointsRequired,
        expirationDate,
        recommendedForMoods,
        quantity,
        redemptionsCount,
        pointsEarned,
        isActive,
      ];

  /// Creates a copy of this Coupon with the given fields replaced with the new values
  Coupon copyWith({
    String? id,
    Partner? partner,
    String? description,
    String? discount,
    int? pointsRequired,
    DateTime? expirationDate,
    List<MoodType>? recommendedForMoods,
    int? quantity,
    int? redemptionsCount,
    int? pointsEarned,
    bool? isActive,
  }) {
    return Coupon(
      id: id ?? this.id,
      partner: partner ?? this.partner,
      description: description ?? this.description,
      discount: discount ?? this.discount,
      pointsRequired: pointsRequired ?? this.pointsRequired,
      expirationDate: expirationDate ?? this.expirationDate,
      recommendedForMoods: recommendedForMoods ?? this.recommendedForMoods,
      quantity: quantity ?? this.quantity,
      redemptionsCount: redemptionsCount ?? this.redemptionsCount,
      pointsEarned: pointsEarned ?? this.pointsEarned,
      isActive: isActive ?? this.isActive,
    );
  }
}
