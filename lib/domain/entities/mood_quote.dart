import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Model representing a mood-based motivational quote
class MoodQuote extends Equatable {
  /// Unique identifier for the quote
  final String id;

  /// The mood type this quote is associated with
  final MoodType moodType;

  /// The quote text
  final String quoteText;

  /// Language of the quote (en, de, ru, tr, ar)
  final String language;

  /// Author of the quote (optional)
  final String? author;

  /// Category of the quote (motivational, inspirational, calming, etc.)
  final String category;

  /// Whether this quote is active/enabled
  final bool isActive;

  /// Creation timestamp
  final DateTime createdAt;

  /// Create a new Mood Quote
  const MoodQuote({
    required this.id,
    required this.moodType,
    required this.quoteText,
    required this.language,
    this.author,
    required this.category,
    this.isActive = true,
    required this.createdAt,
  });

  /// Create an empty Mood Quote
  factory MoodQuote.empty() => MoodQuote(
        id: '',
        moodType: MoodType.neutral,
        quoteText: '',
        language: 'en',
        category: 'motivational',
        createdAt: DateTime.now(),
      );

  /// Convert to a map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'mood_type': moodType.name,
      'quote_text': quoteText,
      'language': language,
      'author': author,
      'category': category,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create a Mood Quote from a database map
  factory MoodQuote.fromMap(Map<String, dynamic> map) {
    return MoodQuote(
      id: map['id'] as String,
      moodType: MoodType.values.firstWhere(
        (mood) => mood.name == map['mood_type'],
        orElse: () => MoodType.neutral,
      ),
      quoteText: map['quote_text'] as String,
      language: map['language'] as String,
      author: map['author'] as String?,
      category: map['category'] as String,
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// Create a copy with certain fields replaced
  MoodQuote copyWith({
    String? id,
    MoodType? moodType,
    String? quoteText,
    String? language,
    String? author,
    String? category,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return MoodQuote(
      id: id ?? this.id,
      moodType: moodType ?? this.moodType,
      quoteText: quoteText ?? this.quoteText,
      language: language ?? this.language,
      author: author ?? this.author,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        moodType,
        quoteText,
        language,
        author,
        category,
        isActive,
        createdAt,
      ];
}