/// A comment on an activity
class ActivityComment {
  /// Unique identifier for the comment
  final String id;

  /// The ID of the activity this comment belongs to
  final String activityId;

  /// The user ID of the comment author
  final String userId;

  /// The display name of the comment author
  final String userName;

  /// The comment text
  final String text;

  /// When the comment was created
  final DateTime createdAt;

  /// When the comment was last updated
  final DateTime? updatedAt;

  /// Number of likes this comment has received
  final int likes;

  /// Whether the current user has liked this comment
  final bool isLikedByUser;

  /// Whether this comment has been edited
  final bool isEdited;

  /// Avatar URL of the comment author
  final String? userAvatarUrl;

  /// Creates a new activity comment
  const ActivityComment({
    required this.id,
    required this.activityId,
    required this.userId,
    required this.userName,
    required this.text,
    required this.createdAt,
    this.updatedAt,
    this.likes = 0,
    this.isLikedByUser = false,
    this.isEdited = false,
    this.userAvatarUrl,
  });

  /// Creates a copy of this [ActivityComment] with the given fields replaced
  ActivityComment copyWith({
    String? id,
    String? activityId,
    String? userId,
    String? userName,
    String? text,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likes,
    bool? isLikedByUser,
    bool? isEdited,
    String? userAvatarUrl,
  }) {
    return ActivityComment(
      id: id ?? this.id,
      activityId: activityId ?? this.activityId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      text: text ?? this.text,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      isLikedByUser: isLikedByUser ?? this.isLikedByUser,
      isEdited: isEdited ?? this.isEdited,
      userAvatarUrl: userAvatarUrl ?? this.userAvatarUrl,
    );
  }

  /// Convert to map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'activity_id': activityId,
      'user_id': userId,
      'user_name': userName,
      'text': text,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'likes': likes,
      'is_liked_by_user': isLikedByUser,
      'is_edited': isEdited,
      'user_avatar_url': userAvatarUrl,
    };
  }

  /// Create from map for JSON deserialization
  factory ActivityComment.fromMap(Map<String, dynamic> map) {
    return ActivityComment(
      id: map['id'] ?? '',
      activityId: map['activity_id'] ?? '',
      userId: map['user_id'] ?? '',
      userName: map['user_name'] ?? '',
      text: map['text'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      likes: map['likes'] ?? 0,
      isLikedByUser: map['is_liked_by_user'] ?? false,
      isEdited: map['is_edited'] ?? false,
      userAvatarUrl: map['user_avatar_url'],
    );
  }
}
