import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/localization/app_localizations.dart';

/// Enum representing different mood types
enum MoodType {
  // Good moods (4)
  /// Excited mood - very positive, energetic
  excited,

  /// Happy mood - positive, content
  happy,
  
  /// Calm mood - peaceful, relaxed
  calm,
  
  /// Inspired mood - creative, motivated
  inspired,
  
  // Neutral mood (1)
  /// Neutral mood - neither good nor bad, just ok
  neutral,

  // Bad moods (4)
  /// Sad mood - unhappy, down
  sad,

  /// Anxious mood - worried, nervous
  anxious,
  
  /// Tired mood - exhausted, fatigued
  tired,
  
  /// Angry mood - upset, frustrated
  angry,
}

/// Extension on MoodType to get emoji and description
extension MoodTypeExtension on MoodType {
  /// Get the emoji for this mood
  String get emoji {
    switch (this) {
      case MoodType.excited:
        return '🤩';
      case MoodType.happy:
        return '😊';
      case MoodType.calm:
        return '😌';
      case MoodType.inspired:
        return '✨';
      case MoodType.neutral:
        return '🙂';
      case MoodType.sad:
        return '😢';
      case MoodType.anxious:
        return '😰';
      case MoodType.tired:
        return '😴';
      case MoodType.angry:
        return '😠';
    }
  }

  /// Get the description for this mood (localized)
  String localizedDescription(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case MoodType.excited:
        return l10n.moodExcited;
      case MoodType.happy:
        return l10n.moodHappy;
      case MoodType.calm:
        return l10n.moodCalm;
      case MoodType.inspired:
        // Temporary implementation until translations are added
        return 'Inspired';
      case MoodType.neutral:
        // Temporary implementation until translations are added
        return 'Neutral';
      case MoodType.sad:
        return l10n.moodSad;
      case MoodType.anxious:
        return l10n.moodAnxious;
      case MoodType.tired:
        return l10n.moodTired;
      case MoodType.angry:
        return l10n.moodAngry;
    }
  }

  /// Get the color for this mood
  Color get color {
    switch (this) {
      case MoodType.excited:
        return const Color(
          0xFF8E24AA,
        ); // Vibrant purple - represents high energy and excitement
      case MoodType.happy:
        return const Color(0xFFFF9800); // Warm orange - visible and cheerful
      case MoodType.calm:
        return Colors.green;
      case MoodType.inspired:
        return Colors.lightBlue;
      case MoodType.neutral:
        return const Color.fromARGB(255, 128, 135, 221);
      case MoodType.sad:
        return Colors.blue;
      case MoodType.anxious:
        return Colors.orange;
      case MoodType.tired:
        return Colors.indigo;
      case MoodType.angry:
        return Colors.red;
    }
  }
}

/// Mood entity representing a user's mood
class Mood extends Equatable {
  /// The type of the mood
  final MoodType type;

  /// The timestamp when the mood was recorded
  final DateTime timestamp;

  /// Optional note about the mood
  final String? note;

  /// Creates a new Mood instance
  const Mood({required this.type, required this.timestamp, this.note});

  @override
  List<Object?> get props => [type, timestamp, note];

  /// Creates a new Mood with some properties changed
  Mood copyWith({MoodType? type, DateTime? timestamp, String? note}) {
    return Mood(
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      note: note ?? this.note,
    );
  }
}
