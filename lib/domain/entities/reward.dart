import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Categories for rewards
enum RewardCategory {
  /// Food and restaurant related rewards
  food,

  /// Sports and fitness related rewards
  sports,

  /// Shopping related rewards
  shopping,

  /// Entertainment related rewards (movies, events, etc.)
  entertainment,

  /// Wellness and health related rewards
  wellness,

  /// Special achievements or badges
  achievement,

  /// Education related rewards
  education,

  /// Digital content rewards
  digital,

  /// Physical item rewards
  physical,

  /// Experience rewards (events, activities)
  experience,

  /// Donation to causes
  donation,

  /// Other types of rewards
  other,
}

/// Extension to provide display properties for reward categories
extension RewardCategoryExtension on RewardCategory {
  /// Get the display name for the category
  String get displayName {
    switch (this) {
      case RewardCategory.food:
        return 'Food & Drinks';
      case RewardCategory.sports:
        return 'Sports & Fitness';
      case RewardCategory.shopping:
        return 'Shopping';
      case RewardCategory.entertainment:
        return 'Entertainment';
      case RewardCategory.wellness:
        return 'Wellness';
      case RewardCategory.achievement:
        return 'Achievements';
      case RewardCategory.education:
        return 'Education';
      case RewardCategory.digital:
        return 'Digital';
      case RewardCategory.physical:
        return 'Physical';
      case RewardCategory.experience:
        return 'Experience';
      case RewardCategory.donation:
        return 'Donation';
      case RewardCategory.other:
        return 'Other';
    }
  }

  /// Get an icon for the category
  IconData get icon {
    switch (this) {
      case RewardCategory.food:
        return Icons.restaurant;
      case RewardCategory.sports:
        return Icons.fitness_center;
      case RewardCategory.shopping:
        return Icons.shopping_bag;
      case RewardCategory.entertainment:
        return Icons.movie;
      case RewardCategory.wellness:
        return Icons.spa;
      case RewardCategory.achievement:
        return Icons.emoji_events;
      case RewardCategory.education:
        return Icons.school;
      case RewardCategory.digital:
        return Icons.download;
      case RewardCategory.physical:
        return Icons.local_shipping;
      case RewardCategory.experience:
        return Icons.event;
      case RewardCategory.donation:
        return Icons.favorite;
      case RewardCategory.other:
        return Icons.star;
    }
  }

  /// Get a color for the category
  Color get color {
    switch (this) {
      case RewardCategory.food:
        return Colors.orange;
      case RewardCategory.sports:
        return Colors.blue;
      case RewardCategory.shopping:
        return Colors.pink;
      case RewardCategory.entertainment:
        return Colors.purple;
      case RewardCategory.wellness:
        return Colors.teal;
      case RewardCategory.achievement:
        return Colors.amber;
      case RewardCategory.education:
        return Colors.indigo;
      case RewardCategory.digital:
        return Colors.green;
      case RewardCategory.physical:
        return Colors.brown;
      case RewardCategory.experience:
        return Colors.pink;
      case RewardCategory.donation:
        return Colors.red;
      case RewardCategory.other:
        return Colors.grey;
    }
  }
}

/// Constants for reward types
class RewardType {
  /// A reward from redeeming a coupon
  static const String couponRedemption = 'coupon_redemption';
  
  /// A reward from completing an activity
  static const String activityCompletion = 'activity_completion';
  
  /// A bonus reward (system-generated)
  static const String bonus = 'bonus';
  
  /// An achievement reward
  static const String achievement = 'achievement';
  
  /// Private constructor to prevent instantiation
  const RewardType._();
}

/// Represents a reward in the user's reward history
class Reward extends Equatable {
  /// Unique identifier for the reward
  final String id;

  /// User ID who received the reward
  final String userId;

  /// Title of the reward (e.g., "Coupon Redeemed", "Activity Completed")
  final String title;

  /// Description of the reward (typically the activity or coupon description)
  final String? description;

  /// Points earned for this reward
  final int pointsEarned;

  /// Type of reward (e.g., "coupon_redemption", "activity_completion", "bonus")
  final String rewardType;

  /// Source ID (e.g., coupon_id, activity_id), if applicable
  final String? sourceId;

  /// Type of the source (e.g., "coupon", "activity")
  final String? sourceType;

  /// When the reward was created
  final DateTime createdAt;

  /// Additional metadata as Map
  final Map<String, dynamic>? metadata;

  /// Constructor for Reward
  const Reward({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    required this.pointsEarned,
    required this.rewardType,
    this.sourceId,
    this.sourceType,
    required this.createdAt,
    this.metadata,
  });

  @override
  List<Object?> get props => [
    id,
    userId,
    title,
    description,
    pointsEarned,
    rewardType,
    sourceId,
    sourceType,
    createdAt,
    metadata,
  ];

  /// Partner ID from metadata (for coupon redemptions)
  String? get partnerId {
    if (sourceType == 'coupon' && metadata != null) {
      return metadata!['partner_id'] as String?;
    }
    return null;
  }

  /// Difficulty level from metadata (for activity completions)
  String? get difficultyLevel {
    if (sourceType == 'activity' && metadata != null) {
      return metadata!['difficulty_level'] as String?;
    }
    return null;
  }

  /// Creates a copy of this Reward with the given fields replaced with new values
  Reward copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    int? pointsEarned,
    String? rewardType,
    String? sourceId,
    String? sourceType,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return Reward(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      pointsEarned: pointsEarned ?? this.pointsEarned,
      rewardType: rewardType ?? this.rewardType,
      sourceId: sourceId ?? this.sourceId,
      sourceType: sourceType ?? this.sourceType,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Location information for rewards
class RewardLocation extends Equatable {
  /// Name of the location
  final String name;

  /// Address of the location
  final String? address;

  /// City of the location
  final String? city;

  /// Postal code of the location
  final String? postalCode;

  /// Country of the location
  final String? country;

  /// Geographic coordinates of the location
  final GeoPoint? coordinates;

  const RewardLocation({
    required this.name,
    this.address,
    this.city,
    this.postalCode,
    this.country,
    this.coordinates,
  });

  /// Get a formatted address string
  String get formattedAddress {
    final parts = <String>[];
    if (address != null && address!.isNotEmpty) parts.add(address!);
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (postalCode != null && postalCode!.isNotEmpty) parts.add(postalCode!);
    if (country != null && country!.isNotEmpty) parts.add(country!);

    return parts.isEmpty ? name : parts.join(', ');
  }

  /// Create a copy of this RewardLocation with modified values
  RewardLocation copyWith({
    String? name,
    String? address,
    String? city,
    String? postalCode,
    String? country,
    GeoPoint? coordinates,
  }) {
    return RewardLocation(
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      coordinates: coordinates ?? this.coordinates,
    );
  }

  /// Factory constructor to create RewardLocation from a JSON map
  factory RewardLocation.fromJson(Map<String, dynamic> json) {
    return RewardLocation(
      name: json['name'] as String,
      address: json['address'] as String?,
      city: json['city'] as String?,
      postalCode: json['postal_code'] as String?,
      country: json['country'] as String?,
      coordinates:
          json['coordinates'] != null
              ? GeoPoint.fromJson(json['coordinates'] as Map<String, dynamic>)
              : null,
    );
  }

  /// Convert this RewardLocation to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (address != null) 'address': address,
      if (city != null) 'city': city,
      if (postalCode != null) 'postal_code': postalCode,
      if (country != null) 'country': country,
      if (coordinates != null) 'coordinates': coordinates!.toJson(),
    };
  }

  @override
  List<Object?> get props => [
    name,
    address,
    city,
    postalCode,
    country,
    coordinates,
  ];
}

/// Geographic coordinates
class GeoPoint extends Equatable {
  /// Latitude coordinate
  final double latitude;

  /// Longitude coordinate
  final double longitude;

  const GeoPoint({required this.latitude, required this.longitude});

  /// Create a copy of this GeoPoint with modified values
  GeoPoint copyWith({double? latitude, double? longitude}) {
    return GeoPoint(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  /// Factory constructor to create GeoPoint from a JSON map
  factory GeoPoint.fromJson(Map<String, dynamic> json) {
    return GeoPoint(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );
  }

  /// Convert this GeoPoint to a JSON map
  Map<String, dynamic> toJson() {
    return {'latitude': latitude, 'longitude': longitude};
  }

  @override
  List<Object> get props => [latitude, longitude];
}
