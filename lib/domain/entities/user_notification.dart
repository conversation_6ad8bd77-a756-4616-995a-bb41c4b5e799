/// A notification for the user
class UserNotification {
  /// Unique identifier
  final String id;

  /// Notification title
  final String title;

  /// Notification message
  final String message;

  /// When the notification was received
  final DateTime timestamp;

  /// Whether the notification has been read
  final bool isRead;

  /// Type of notification
  final NotificationType type;

  /// Optional action data related to the notification
  final Map<String, dynamic>? actionData;

  /// The user ID this notification belongs to
  final String userId;

  /// Creates a [UserNotification]
  const UserNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.type,
    required this.userId,
    this.isRead = false,
    this.actionData,
  });

  /// Creates a copy of this [UserNotification] with the given fields replaced
  UserNotification copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    NotificationType? type,
    Map<String, dynamic>? actionData,
    String? userId,
  }) {
    return UserNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
      actionData: actionData ?? this.actionData,
      userId: userId ?? this.userId,
    );
  }

  /// Convert to map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'is_read': isRead,
      'type': type.name,
      'action_data': actionData,
      'user_id': userId,
    };
  }

  /// Create from map for JSON deserialization
  factory UserNotification.fromMap(Map<String, dynamic> map) {
    return UserNotification(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      isRead: map['is_read'] ?? false,
      type: NotificationType.fromString(map['type'] ?? 'general'),
      actionData:
          map['action_data'] != null
              ? Map<String, dynamic>.from(map['action_data'])
              : null,
      userId: map['user_id'] ?? '',
    );
  }
}

/// Types of notifications
enum NotificationType {
  /// New activity available
  activity,

  /// New coupon available
  coupon,

  /// Achievement unlocked
  achievement,

  /// Comment like received
  commentLike,

  /// Reminder notifications
  reminder,

  /// Motivational messages
  motivation,

  /// General notifications
  general;

  /// Get the notification type from a string
  static NotificationType fromString(String value) {
    return values.firstWhere(
      (type) => type.name == value,
      orElse: () => NotificationType.general,
    );
  }
}
