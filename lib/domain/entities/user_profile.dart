import 'package:equatable/equatable.dart';

/// Model representing a user's profile information
class UserProfile extends Equatable {
  /// Unique identifier matching Supabase auth user ID
  final String id;

  /// User's chosen username
  final String? username;

  /// User's birth year
  final int? birthYear;

  /// User's selected gender
  final String? gender;

  /// User's life situation
  final String? lifeSituation;

  /// User's selected goals (up to 3)
  final List<String> goals;

  /// User's selected avatar URL
  final String? avatarUrl;

  /// User's bio or description
  final String? bio;

  /// User's email
  final String email;
  
  /// User's preferred language code (e.g., 'en', 'de')
  final String? language;

  /// Creates a user profile
  const UserProfile({
    required this.id,
    required this.username,
    required this.email,
    this.birthYear,
    this.gender,
    this.lifeSituation,
    this.goals = const [],
    this.avatarUrl,
    this.bio,
    this.language,
  });

  /// Creates a copy of this profile with specified fields replaced
  UserProfile copyWith({
    String? id,
    String? username,
    String? email,
    int? birthYear,
    String? gender,
    String? lifeSituation,
    List<String>? goals,
    String? avatarUrl,
    String? bio,
    String? language,
  }) {
    return UserProfile(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      birthYear: birthYear ?? this.birthYear,
      gender: gender ?? this.gender,
      lifeSituation: lifeSituation ?? this.lifeSituation,
      goals: goals ?? this.goals,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      bio: bio ?? this.bio,
      language: language ?? this.language,
    );
  }

  /// Converts profile to a map for database storage
  Map<String, dynamic> toMap() {
    // Start with required fields
    final Map<String, dynamic> map = {
      'id': id,
      'username': username,
    };

    // Add optional fields only if they have values
    if (birthYear != null) {
      map['birth_year'] = birthYear;
    }

    if (gender != null) {
      map['gender'] = gender;
    }

    if (lifeSituation != null) {
      map['life_situation'] = lifeSituation;
    }

    if (goals.isNotEmpty) {
      map['goals'] = goals;
    }

    if (avatarUrl != null) {
      map['avatar_url'] = avatarUrl;
    }

    if (bio != null) {
      map['bio'] = bio;
    }

    if (email.isNotEmpty) {
      map['email'] = email;
    }

    return map;
  }

  /// Creates a profile from a database map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    // Handle missing fields gracefully
    String id = map['id'] as String;
    String? username = map['username'] as String?;

    // Handle email field which might be missing in some database schemas
    String email = '';
    if (map.containsKey('email') && map['email'] != null) {
      email = map['email'] as String;
    }

    int? birthYear;
    if (map.containsKey('birth_year') && map['birth_year'] != null) {
      birthYear = map['birth_year'] as int?;
    }

    String? gender;
    if (map.containsKey('gender') && map['gender'] != null) {
      gender = map['gender'] as String?;
    }

    String? lifeSituation;
    if (map.containsKey('life_situation') && map['life_situation'] != null) {
      lifeSituation = map['life_situation'] as String?;
    }

    List<String> goals = [];
    if (map.containsKey('goals') && map['goals'] != null) {
      goals = (map['goals'] as List?)?.map((e) => e as String).toList() ?? [];
    }

    String? avatarUrl;
    if (map.containsKey('avatar_url') && map['avatar_url'] != null) {
      avatarUrl = map['avatar_url'] as String?;
    }

    String? bio;
    if (map.containsKey('bio') && map['bio'] != null) {
      bio = map['bio'] as String?;
    }

    String? language;
    if (map.containsKey('language') && map['language'] != null) {
      language = map['language'] as String?;
    }

    return UserProfile(
      id: id,
      username: username,
      email: email,
      birthYear: birthYear,
      gender: gender,
      lifeSituation: lifeSituation,
      goals: goals,
      avatarUrl: avatarUrl,
      bio: bio,
      language: language,
    );
  }

  /// Creates an empty profile with just an ID
  factory UserProfile.empty(String userId) {
    return UserProfile(
      id: userId,
      username: '', email: '',
      goals: const [],
    );
  }

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        birthYear,
        gender,
        lifeSituation,
        goals,
        avatarUrl,
        bio,
        language,
      ];
}
