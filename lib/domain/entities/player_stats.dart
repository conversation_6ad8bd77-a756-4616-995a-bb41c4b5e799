import 'package:equatable/equatable.dart';

class PlayerStats extends Equatable {
  final String userId;
  final String username;
  final int currentLevel;
  final int currentXp;
  final List<String> earnedBadgeIds;
  final List<String> earnedCardIds;
  // Maybe add total tasks completed, etc., later

  const PlayerStats({
    required this.userId,
    required this.username,
    this.currentLevel = 1, // Start at level 1
    this.currentXp = 0, // Start with 0 XP
    this.earnedBadgeIds = const [],
    this.earnedCardIds = const [],
  });

  // Initial empty state - requires default userId/username or modification
  // For now, let's assume initial stats are loaded from profile/auth later
  // Or provide placeholder defaults if necessary
  static const initial = PlayerStats(
    userId: 'default_user_id', // Placeholder - Needs actual ID
    username: 'Player', // Placeholder
  );

  // CopyWith method for easier state updates
  PlayerStats copyWith({
    String? userId,
    String? username,
    int? currentLevel,
    int? currentXp,
    List<String>? earnedBadgeIds,
    List<String>? earnedCardIds,
  }) {
    return PlayerStats(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      currentLevel: currentLevel ?? this.currentLevel,
      currentXp: currentXp ?? this.currentXp,
      earnedBadgeIds: earnedBadgeIds ?? this.earnedBadgeIds,
      earnedCardIds: earnedCardIds ?? this.earnedCardIds,
    );
  }

  @override
  List<Object?> get props => [
    userId,
    username,
    currentLevel,
    currentXp,
    earnedBadgeIds,
    earnedCardIds,
  ];
}
