import 'package:equatable/equatable.dart';

/// Represents a local business partner that can provide coupons
class Partner extends Equatable {
  /// Unique identifier for the partner
  final String id;

  /// Name of the partner business
  final String name;

  /// Category of the partner (restaurant, wellness, retail)
  final PartnerCategory category;

  /// Latitude of the partner location
  final double latitude;

  /// Longitude of the partner location
  final double longitude;

  /// Commission rate for the partner (as a percentage)
  final double commissionRate;

  /// Logo URL for the partner
  final String? logoUrl;

  /// Address of the partner
  final String address;

  /// Constructor for Partner
  const Partner({
    required this.id,
    required this.name,
    required this.category,
    required this.latitude,
    required this.longitude,
    required this.commissionRate,
    this.logoUrl,
    required this.address,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        category,
        latitude,
        longitude,
        commissionRate,
        logoUrl,
        address,
      ];

  /// Creates a copy of this Partner with the given fields replaced with the new values
  Partner copyWith({
    String? id,
    String? name,
    PartnerCategory? category,
    double? latitude,
    double? longitude,
    double? commissionRate,
    String? logoUrl,
    String? address,
  }) {
    return Partner(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      commissionRate: commissionRate ?? this.commissionRate,
      logoUrl: logoUrl ?? this.logoUrl,
      address: address ?? this.address,
    );
  }
}

/// Categories for partners
enum PartnerCategory {
  /// Restaurant category
  restaurant,

  /// Wellness category
  wellness,

  /// Retail category
  retail,
}
