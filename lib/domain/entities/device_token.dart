import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Entity representing a device's FCM token
class DeviceToken extends Equatable {
  final String id;
  final String userId;
  final String fcmToken;
  final DateTime lastUpdated;
  final String? deviceInfo; // Optional device name/info

  const DeviceToken({
    required this.id,
    required this.userId,
    required this.fcmToken,
    required this.lastUpdated,
    this.deviceInfo,
  });

  /// Create a new DeviceToken with a generated UUID
  factory DeviceToken.create({
    required String userId,
    required String fcmToken,
    String? deviceInfo,
  }) {
    return DeviceToken(
      id: const Uuid().v4(),
      userId: userId,
      fcmToken: fcmToken,
      lastUpdated: DateTime.now().toUtc(),
      deviceInfo: deviceInfo,
    );
  }

  /// Create a copy of this DeviceToken with the specified fields replaced
  DeviceToken copyWith({
    String? id,
    String? userId,
    String? fcmToken,
    DateTime? lastUpdated,
    String? deviceInfo,
  }) {
    return DeviceToken(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fcmToken: fcmToken ?? this.fcmToken,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      deviceInfo: deviceInfo ?? this.deviceInfo,
    );
  }

  @override
  List<Object?> get props => [id, userId, fcmToken, lastUpdated, deviceInfo];
}
