import 'package:equatable/equatable.dart';

/// Represents daily points data for visualization
class DailyPointsData extends Equatable {
  /// Date for this data point
  final DateTime date;

  /// Points earned on this day
  final int pointsEarned;

  /// Target points for this day
  final int targetPoints;

  /// Constructor for DailyPointsData
  const DailyPointsData({
    required this.date,
    required this.pointsEarned,
    required this.targetPoints,
  });

  @override
  List<Object?> get props => [date, pointsEarned, targetPoints];
}

/// Enum representing different time periods for points display
enum PointsTimePeriod {
  /// Last 24 hours
  day,

  /// Last 7 days
  week,

  /// Last month
  month,
}

/// Points history data for different time periods
class PointsHistory extends Equatable {
  /// Points earned in the last 24 hours
  final int last24Hours;

  /// Points earned in the last 7 days
  final int lastWeek;

  /// Points earned in the last month
  final int lastMonth;

  /// Maximum possible points for 24 hours
  final int maxDaily;

  /// Maximum possible points for a week
  final int maxWeekly;

  /// Maximum possible points for a month
  final int maxMonthly;

  /// Daily breakdown of points data
  final List<DailyPointsData> dailyData;

  /// Constructor for PointsHistory
  const PointsHistory({
    required this.last24Hours,
    required this.lastWeek,
    required this.lastMonth,
    this.maxDaily = 2000,
    this.maxWeekly = 14000, // 2000 * 7
    this.maxMonthly = 60000, // ~2000 * 30
    required this.dailyData,
  });

  /// Get points for a specific time period
  int getPointsForPeriod(PointsTimePeriod period) {
    switch (period) {
      case PointsTimePeriod.day:
        return last24Hours;
      case PointsTimePeriod.week:
        return lastWeek;
      case PointsTimePeriod.month:
        return lastMonth;
    }
  }

  /// Get maximum points for a specific time period
  int getMaxForPeriod(PointsTimePeriod period) {
    switch (period) {
      case PointsTimePeriod.day:
        return maxDaily;
      case PointsTimePeriod.week:
        return maxWeekly;
      case PointsTimePeriod.month:
        return maxMonthly;
    }
  }

  @override
  List<Object?> get props => [
    last24Hours,
    lastWeek,
    lastMonth,
    maxDaily,
    maxWeekly,
    maxMonthly,
    dailyData,
  ];
}
