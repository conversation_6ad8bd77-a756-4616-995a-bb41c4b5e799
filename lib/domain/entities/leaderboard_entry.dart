import 'package:equatable/equatable.dart';

/// Represents a single entry in the leaderboard.
class LeaderboardEntry extends Equatable {
  final int rank;
  final String username; // Consider anonymization later if needed
  final int totalXp;

  const LeaderboardEntry({
    required this.rank,
    required this.username,
    required this.totalXp,
  });

  @override
  List<Object?> get props => [rank, username, totalXp];
}
