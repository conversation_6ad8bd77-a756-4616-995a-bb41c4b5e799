import 'package:equatable/equatable.dart';

class Level extends Equatable {
  final int levelNumber;
  final int xpRequired; // XP needed to *reach* this level from the previous one
  final String name; // e.g., "<PERSON><PERSON><PERSON>", "<PERSON>", "Master"

  const Level({
    required this.levelNumber,
    required this.xpRequired,
    required this.name,
  });

  @override
  List<Object?> get props => [levelNumber, xpRequired, name];
}
