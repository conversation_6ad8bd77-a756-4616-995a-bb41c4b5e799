// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'food_mood.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FoodRecommendationAdapter extends TypeAdapter<FoodRecommendation> {
  @override
  final int typeId = 10;

  @override
  FoodRecommendation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FoodRecommendation(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      imageUrl: fields[3] as String,
      createdAt: fields[4] as DateTime,
      updatedAt: fields[5] as DateTime,
      upvotes: fields[6] as int,
      downvotes: fields[7] as int,
      userVote: fields[8] as VoteType?,
      userId: fields[9] as String,
      username: fields[10] as String,
      userAvatarUrl: fields[11] as String?,
      moodTypes: (fields[12] as List?)?.cast<MoodType>(),
      countryCode: fields[13] as String,
    );
  }

  @override
  void write(BinaryWriter writer, FoodRecommendation obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.imageUrl)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.upvotes)
      ..writeByte(7)
      ..write(obj.downvotes)
      ..writeByte(8)
      ..write(obj.userVote)
      ..writeByte(9)
      ..write(obj.userId)
      ..writeByte(10)
      ..write(obj.username)
      ..writeByte(11)
      ..write(obj.userAvatarUrl)
      ..writeByte(12)
      ..write(obj.moodTypes)
      ..writeByte(13)
      ..write(obj.countryCode);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FoodRecommendationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FoodSortOptionAdapter extends TypeAdapter<FoodSortOption> {
  @override
  final int typeId = 13;

  @override
  FoodSortOption read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return FoodSortOption.default_;
      case 1:
        return FoodSortOption.highestVotes;
      case 2:
        return FoodSortOption.newest;
      case 3:
        return FoodSortOption.oldest;
      default:
        return FoodSortOption.default_;
    }
  }

  @override
  void write(BinaryWriter writer, FoodSortOption obj) {
    switch (obj) {
      case FoodSortOption.default_:
        writer.writeByte(0);
        break;
      case FoodSortOption.highestVotes:
        writer.writeByte(1);
        break;
      case FoodSortOption.newest:
        writer.writeByte(2);
        break;
      case FoodSortOption.oldest:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FoodSortOptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VoteTypeAdapter extends TypeAdapter<VoteType> {
  @override
  final int typeId = 12;

  @override
  VoteType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return VoteType.upvote;
      case 1:
        return VoteType.downvote;
      default:
        return VoteType.upvote;
    }
  }

  @override
  void write(BinaryWriter writer, VoteType obj) {
    switch (obj) {
      case VoteType.upvote:
        writer.writeByte(0);
        break;
      case VoteType.downvote:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VoteTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
