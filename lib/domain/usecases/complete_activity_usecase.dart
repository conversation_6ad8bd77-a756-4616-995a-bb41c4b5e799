import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/goal_status.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/activity_repository.dart';
import 'package:detoxme/domain/repositories/detox_points_repository.dart';
import 'package:flutter/foundation.dart';

/// Use case for completing an activity and calculating points
class CompleteActivityUseCase {
  /// Repository for user points
  final DetoxPointsRepository detoxPointsRepository;

  /// Repository for activities
  final ActivityRepository activityRepository;

  /// Creates a new complete activity use case
  CompleteActivityUseCase({
    required this.detoxPointsRepository,
    required this.activityRepository,
  });

  /// Completes an activity and updates user points
  ///
  /// [userId] The ID of the user completing the activity
  /// [activity] The activity being completed
  /// [timeSpent] The duration spent on the activity
  /// [wasAborted] Whether the activity was aborted before completion
  ///
  /// Returns a [GoalStatus] indicating if any goals were achieved
  Future<Either<Failure, GoalStatus>> call({
    required String userId,
    required Activity activity,
    required Duration timeSpent,
    required bool wasAborted,
  }) async {
    // Calculate points based on time spent
    final int pointsToAdd = _calculatePoints(activity, timeSpent, wasAborted);

    if (kDebugMode) {
      print('CompleteActivityUseCase: Activity "${activity.title}" completed');
      print(
        'CompleteActivityUseCase: Time spent: ${timeSpent.inMinutes} minutes, ${timeSpent.inSeconds % 60} seconds',
      );
      print('CompleteActivityUseCase: Points to add: $pointsToAdd');
      print('CompleteActivityUseCase: Was aborted: $wasAborted');
    }

    if (pointsToAdd <= 0) {
      if (kDebugMode) {
        print(
          'CompleteActivityUseCase: No points to add, returning GoalStatus.none',
        );
      }
      return Right(GoalStatus.none);
    }

    // Update user points first to ensure they get their points
    // even if activity status update fails
    final result = await detoxPointsRepository.addPoints(
      userId,
      pointsToAdd,
      'Activity completion: ${activity.title}',
    );

    if (result.isLeft()) {
      if (kDebugMode) {
        print(
          'CompleteActivityUseCase: Failed to add points, but will still try to update activity status',
        );
      }
    }

    // Update activity status
    final activityResult = await activityRepository.updateActivityStatus(
      activityId: activity.id,
      userId: userId,
      completed: !wasAborted,
      timeSpent: timeSpent,
    );

    if (activityResult.isLeft()) {
      if (kDebugMode) {
        print(
          'CompleteActivityUseCase: Failed to update activity status: ${(activityResult as Left).value}',
        );
        print('CompleteActivityUseCase: Points may still have been added');
      }

      // If points were added successfully but activity status update failed,
      // still return success with no goal status
      if (result.isRight()) {
        if (kDebugMode) {
          print(
            'CompleteActivityUseCase: Points were added successfully, returning success with no goal status',
          );
        }
        return Right(GoalStatus.none);
      }

      return Left((activityResult as Left).value);
    }

    // If we get here, both operations succeeded or points failed but activity succeeded

    return result.fold(
      (failure) {
        if (kDebugMode) {
          print(
            'CompleteActivityUseCase: Failed to add points: ${failure.message}',
          );
        }
        return Left(failure);
      },
      (detoxPoints) {
        // For now, assume that adding points means progress toward goals
        // This logic should be enhanced with actual goal determination
        // if we add the ability to track daily targets in DetoxPoints

        // If total points crossed 1000 milestone, consider it a milestone reached
        bool isMilestoneReached =
            detoxPoints.totalPoints >= 1000 &&
            (detoxPoints.totalPoints - pointsToAdd) < 1000;

        if (kDebugMode) {
          print('CompleteActivityUseCase: Points added successfully');
          print(
            'CompleteActivityUseCase: New total points: ${detoxPoints.totalPoints}',
          );
          print(
            'CompleteActivityUseCase: Milestone reached: $isMilestoneReached',
          );
        }

        // Simplified goal status logic
        if (isMilestoneReached) {
          return Right(GoalStatus.milestoneReached);
        } else {
          return Right(GoalStatus.none);
        }
      },
    );
  }

  /// Calculates points based on activity duration and completion status
  ///
  /// Returns points earned (1 point per minute)
  int _calculatePoints(Activity activity, Duration timeSpent, bool wasAborted) {
    // Special handling for debug activities
    if (activity.title.contains('Debug Activity')) {
      // For debug activities, always give 5 points regardless of time spent
      return 5;
    }

    // If aborted and duration is less than 15 minutes, no points
    if (wasAborted && timeSpent.inMinutes <= 15) {
      return 0;
    }

    // 1 point per minute
    return timeSpent.inMinutes;
  }
}
