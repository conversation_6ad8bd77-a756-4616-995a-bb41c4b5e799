import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/ai_motivation_repository.dart';

/// Use case for getting the best matching motivational content for a user
class GetBestMotivationMatchUseCase {
  /// Repository for AI motivations
  final AiMotivationRepository _repository;

  /// Creates a new instance of the use case
  GetBestMotivationMatchUseCase({required AiMotivationRepository repository})
      : _repository = repository;

  /// Call the use case to get the best matching motivational content
  ///
  /// Returns Either a [Failure] or an [AiMotivation]
  Future<Either<Failure, AiMotivation>> call({
    required String language,
    required String lifeSituation,
    required List<String> goals,
    required String gender,
    required int birthYear,
  }) async {
    return _repository.getBestMotivationMatch(
      language: language,
      lifeSituation: lifeSituation,
      goals: goals,
      gender: gender,
      birthYear: birthYear,
    );
  }
}
