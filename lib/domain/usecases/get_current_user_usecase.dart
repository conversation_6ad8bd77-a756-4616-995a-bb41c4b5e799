import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';

/// Use case to get the current authenticated user
class GetCurrentUserUseCase {
  /// User repository
  final UserRepository _userRepository;

  /// Creates a new get current user use case
  GetCurrentUserUseCase({required UserRepository userRepository})
    : _userRepository = userRepository;

  /// Gets the current user profile
  ///
  /// Returns the current user or a failure
  Future<Either<Failure, UserProfile>> call() async {
    return await _userRepository.getProfile();
  }
}
