import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;

  const Failure(this.message);

  @override
  List<Object> get props => [message];
}

// Specific failure for local storage issues
class CacheFailure extends Failure {
  const CacheFailure([super.message = 'Could not access local cache.']);
}

// Specific failure for remote server/API issues
class ServerFailure extends Failure {
  const ServerFailure([super.message = 'A server error occurred.']);
}

// Specific failure for authentication issues
class AuthFailure extends Failure {
  const AuthFailure([super.message = 'Authentication failed.']);
}

// PIN-related authentication failures
class StorageFailure extends AuthFailure {
  const StorageFailure([super.message = 'Could not access local storage.']);
}

class PinNotSetFailure extends AuthFailure {
  const PinNotSetFailure([super.message = 'PIN is not set.']);
}

class IncorrectPinFailure extends AuthFailure {
  const IncorrectPinFailure([super.message = 'Incorrect PIN entered.']);
}

// Specific failure for resource not found
class NotFoundFailure extends Failure {
  const NotFoundFailure([super.message = 'Resource not found.']);
}

// Specific failure for permission issues
class PermissionFailure extends Failure {
  const PermissionFailure([super.message = 'Permission denied.']);
}

// Specific failure for network connectivity issues
class NetworkFailure extends Failure {
  const NetworkFailure([super.message = 'No internet connection. Please check your connection.']);
}

// Specific failure for insufficient points
class InsufficientPointsFailure extends Failure {
  const InsufficientPointsFailure([
    super.message = 'Not enough points to redeem this reward.',
  ]);
}

// Specific failure for reward availability issues
class RewardNotAvailableFailure extends Failure {
  const RewardNotAvailableFailure([
    super.message = 'This reward is no longer available.',
  ]);
}
