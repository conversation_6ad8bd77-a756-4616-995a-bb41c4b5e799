{"@@locale": "ru", "appTitle": "DetoxMe", "settingsTitle": "Настройки", "@settingsTitle": {"description": "Title for the settings screen."}, "appTheme": "Тема приложения", "@appTheme": {"description": "Section title for theme settings."}, "themeSystem": "Системная", "@themeSystem": {"description": "Option for system theme."}, "themeLight": "Светлая", "@themeLight": {"description": "Option for light theme."}, "themeDark": "Тёмная", "@themeDark": {"description": "Option for dark theme."}, "language": "Язык", "@language": {"description": "Section title for language settings."}, "notifications": "Уведомления", "@notifications": {"description": "Section title for notification settings."}, "enableNotifications": "Включить уведомления", "@enableNotifications": {"description": "Title for enabling/disabling all notifications."}, "notificationsDescription": "Получайте уведомления о новых активностях, купонах и напоминаниях", "@notificationsDescription": {"description": "Description of what notifications include."}, "notifyAboutActivities": "Уведомления об активностях", "@notifyAboutActivities": {"description": "Subheading for activity notification settings."}, "activityMoodPreferences": "Настройки настроения", "@activityMoodPreferences": {"description": "Subheading for mood-specific notification settings."}, "otherNotifications": "Другие уведомления", "@otherNotifications": {"description": "Subheading for other notification settings."}, "newActivitiesPushNotif": "Новые активности", "@newActivitiesPushNotif": {"description": "Toggle for new activities notifications."}, "newCouponsPushNotif": "Новые купоны", "@newCouponsPushNotif": {"description": "Toggle for new coupons notifications."}, "activityRemindersPushNotif": "Напоминания", "@activityRemindersPushNotif": {"description": "Toggle for activity reminders notifications."}, "motivationalMessagesPushNotif": "Мотивационные сообщения", "@motivationalMessagesPushNotif": {"description": "Toggle for motivational message notifications."}, "commentLikesPushNotif": "Лайки комментариев", "@commentLikesPushNotif": {"description": "Toggle for comment like notifications."}, "comingSoon": "Скоро", "detoxRewards": "Награды Detox", "newLabel": "НОВОЕ", "rewardsDescription": "Выполняйте задания, чтобы получать очки и обменивать их на реальные награды", "goodMorning": "Доброе утро", "goodAfternoon": "Добрый день", "goodEvening": "Добрый вечер", "goodNight": "Спокойной ночи", "currentPointsLabel": "Текущие", "targetPointsLabel": "Цель", "maxPointsLabel": "Максимум", "detoxActivities": "Detox-активности", "defaultActivitiesDescription": "Попробуйте эти активности, чтобы улучшить ваше цифровое благополучие и заработать очки", "activitiesForMood": "Активности для вашего {mood} настроения", "beginnerActivities": "Начинающий", "intermediateActivities": "Средний", "expertActivities": "Эксперт", "loadingDashboard": "Загрузка вашего персонального дашборда...", "noActivitiesAvailable": "Пока нет активностей для этого настроения", "activityInProgressError": "У вас уже есть активность в процессе", "startActivity": "Начать", "moodOMeterTitle": "Как вы себя чувствуете сегодня?", "moodOMeterInstructions": "Перетаскивайте по шкале настроения или нажмите на эмодзи, чтобы выбрать ваше текущее настроение", "createActivityTitle": "Создать активность", "titleLabel": "Название", "titleHint": "Введите название для вашей активности", "titleRequired": "Пожалуйста, введите название", "descriptionLabel": "Описание", "descriptionHint": "Опишите вашу активность", "descriptionRequired": "Пожалуйста, введите описание", "durationLabel": "Продолжительность: {duration} минут", "durationMinutes": "{minutes} мин", "healthBenefitsLabel": "Польза для здоровья", "healthBenefitsHint": "Опишите пользу для здоровья от этой активности", "healthBenefitsRequired": "Пожалуйста, опишите пользу для здоровья", "healthBenefitsDialogTitle": "Польза для здоровья", "addNewHealthBenefitHint": "Добавить новую пользу для здоровья", "selectedBenefitsLabel": "Выбранные преимущества:", "suggestedBenefitsLabel": "Предлагаемые преимущества:", "categoryLabel": "Категория", "languageLabel": "Язык", "selectLanguageHint": "Выберите язык", "recommendedForMoodsLabel": "Рекомендуется для настроений", "selectAtLeastOneMood": "(выберите минимум 1)", "moodSelectionError": "Пожалуйста, выберите хотя бы 1 настроение", "healthBenefitsSelectionError": "Пожалуйста, выберите не менее 2 преимуществ для здоровья", "privacySettingsLabel": "Настройки конфиденциальности", "makeActivityPrivateLabel": "Сделать активность приватной", "privateActivityDescription": "Только вы можете видеть эту активность", "showYourNameLabel": "Показать ваше имя", "showNameDescription": "Ваше имя будет отображаться как создатель", "createActivityButton": "Создать активность", "activityCreatedSuccess": "Активность успешно создана", "loginRequiredError": "Вы должны быть авторизованы, чтобы создавать активности", "createActivityError": "Не удалось создать активность: {error}", "selectYourMood": "Выберите ваше настроение", "commonMoods": "Распространенные настроения:", "allMoodOptions": "Все варианты настроения:", "moreMoodsButton": "Больше", "lessMoodsButton": "Меньше", "noMoodSelected": "Настроение не выбрано", "selectMoodAbove": "Выберите настроение выше", "moodSelectionDescription": "Мы предложим активности, которые помогут вам чувствовать себя лучше или оставаться позитивным", "failedToLoadActivities": "Не удалось загрузить активности", "noActivitiesFound": "Активности не найдены", "noActivitiesForMoodDescription": "Мы не смогли найти активности для вашего текущего настроения. Попробуйте выбрать другое настроение.", "noUserActivities": "Нет активностей, созданных вами", "noUserActivitiesDescription": "Вы еще не создали активностей для этого настроения. Создайте одну или отключите фильтр.", "showAllActivities": "Показать все активности", "suggestionsForMood": "Предложения для {mood} {emoji}", "startButton": "Начать", "stopButton": "Остановить", "activityStarted": "Начато: {activityTitle}", "activityStartError": "Не удалось начать активность: {error}", "activityCancelled": "Активность отменена. Баллы не начислены.", "activityCancelError": "Ошибка при отмене активности: {error}", "completeCurrentActivityFirst": "Сначала завершите или отмените текущую активность", "cancelButton": "Отмена", "applyButton": "Применить", "healthBenefitStress": "Снижает стресс и тревожность", "healthBenefitCardio": "Улучшает сердечно-сосудистую систему", "healthBenefitClarity": "Повышает ясность ума", "healthBenefitImmune": "Укрепляет иммунную систему", "healthBenefitSleep": "Улучшает качество сна", "healthBenefitStrength": "Увеличивает физическую силу", "healthBenefitMindful": "Способствует осознанности", "healthBenefitDigital": "Снижает цифровую зависимость", "healthBenefitFocus": "Улучшает фокус и концентрацию", "healthBenefitEmotional": "Способствует эмоциональному благополучию", "healthBenefitEnergy": "Повышает уровень энергии", "healthBenefitCreativity": "Стимулирует творческие способности", "healthBenefitPosture": "Улучшает осанку", "healthBenefitWeight": "Помогает контролировать вес", "foodMoodExplanationTitle": "Знаете ли вы?", "foodMoodExplanationText": "Еда, которую вы потребляете, может существенно влиять на ваше настроение и психическое благополучие. Богатые питательными веществами продукты поддерживают функции мозга и помогают регулировать эмоции, в то время как обработанные продукты могут способствовать перепадам настроения.", "noRecommendationsFound": "Рекомендации не найдены", "tryDifferentMood": "Попробуйте выбрать другое настроение или изменить фильтры.", "description": "Описание", "benefits": "Преимущества", "categories": "Категории", "foodCategories": "Категории продуктов", "hideDownvotedItems": "Скрыть элементы с отрицательными оценками", "hideDownvotedDescription": "Скрыть элементы со слишком большим количеством отрицательных оценок", "clearAll": "Очистить всё", "recommendedForMoods": "Рекомендуется для настроений", "nutritionInfo": "Пищевая ценность", "calories": "Калории", "protein": "Белки", "carbs": "Углеводы", "fats": "<PERSON><PERSON><PERSON><PERSON>", "upvotes": "Положительные оценки", "downvotes": "Отрицательные оценки", "healthyFood": "Здоровая пища", "moodHappy": "Счастливый", "@moodHappy": {"description": "Mood label: Happy"}, "moodSad": "Грустный", "@moodSad": {"description": "Mood label: Sad"}, "moodAngry": "Злой", "@moodAngry": {"description": "Mood label: Angry"}, "moodAnxious": "Тревожный", "@moodAnxious": {"description": "Mood label: Anxious"}, "moodTired": "Уставший", "@moodTired": {"description": "Mood label: Tired"}, "moodStressed": "В стрессе", "@moodStressed": {"description": "Mood label: Stressed"}, "moodDepressed": "Депрессивный", "@moodDepressed": {"description": "Mood label: Depressed"}, "moodExcited": "Взволнованный", "@moodExcited": {"description": "Mood label: Excited"}, "moodCalm": "Спокойный", "@moodCalm": {"description": "Mood label: Calm"}, "moodBored": "Скучающий", "@moodBored": {"description": "Mood label: Bo<PERSON>"}, "foodCategoryProtein": "Белковая пища", "@foodCategoryProtein": {"description": "Категория продуктов: Белковая пища"}, "foodCategoryHealthyFats": "Полезные жиры", "@foodCategoryHealthyFats": {"description": "Категория продуктов: Полезные жиры"}, "foodCategoryComplexCarbs": "Сложные углеводы", "@foodCategoryComplexCarbs": {"description": "Категория продуктов: Сложные углеводы"}, "foodCategoryVitamins": "Витаминная пища", "@foodCategoryVitamins": {"description": "Категория продуктов: Витаминная пища"}, "foodCategoryMinerals": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "@foodCategoryMinerals": {"description": "Категория продуктов: Мин<PERSON><PERSON><PERSON><PERSON>ы"}, "foodCategoryOmega3": "Омега-3", "@foodCategoryOmega3": {"description": "Категория продуктов: Омега-3"}, "foodCategoryProbiotics": "Пробиотики", "@foodCategoryProbiotics": {"description": "Категория продуктов: Пробиотики"}, "foodCategoryCalming": "Успокаивающие", "@foodCategoryCalming": {"description": "Категория продуктов: Успокаивающие продукты"}, "foodCategoryEnergyBoosting": "Энергетические", "@foodCategoryEnergyBoosting": {"description": "Категория продуктов: Энергетические продукты"}, "foodCategoryAntioxidants": "Антиоксиданты", "@foodCategoryAntioxidants": {"description": "Категория продуктов: Антиоксиданты"}, "activitiesTitle": "Активности", "@activitiesTitle": {"description": "Заголовок для вкладки и экрана активностей."}, "helloUser": "Привет, {userName}", "@helloUser": {"description": "Приветствие для пользователя, параметр: userName", "placeholders": {"userName": {"type": "String"}}}, "goalTag": "{goal}", "@goalTag": {"description": "Метка цели, параметр: goal", "placeholders": {"goal": {"type": "String"}}}, "familyTag": "Семья", "@familyTag": {"description": "Метка для семьи"}, "childrenTag": "Дети", "@childrenTag": {"description": "Метка для детей"}, "motivationChildrenFocus": "Вы сегодня отличный пример для своих детей! Ваша сосредоточенность показывает им важность быть здесь и сейчас.", "@motivationChildrenFocus": {"description": "Мотивационное сообщение для детей и цели 'Фокус'."}, "motivationChildrenSleep": "Лучший сон — больше присутствия для вашей семьи. Продолжайте соблюдать цифровые границы!", "@motivationChildrenSleep": {"description": "Мотивационное сообщение для детей и цели 'Сон'."}, "motivationChildrenGeneric": "Ваши дети замечают, когда вы откладываете телефон. Каждый момент связи с ними важен!", "@motivationChildrenGeneric": {"description": "Мотивационное сообщение для детей, общее."}, "motivationFocusHigh": "Невероятная сосредоточенность сегодня! Ваш ум ясен, а продуктивность на высоте. Так держать!", "@motivationFocusHigh": {"description": "Мотивационное сообщение для высокого прогресса в фокусе."}, "motivationFocusLow": "Каждая минута вдали от экрана помогает вашему мозгу лучше концентрироваться. У вас получится!", "@motivationFocusLow": {"description": "Мотивационное сообщение для низкого прогресса в фокусе."}, "motivationSleep": "Качество вашего сна улучшается с каждым цифровым перерывом. Ваше тело и разум благодарят вас!", "@motivationSleep": {"description": "Мотивационное сообщение для цели 'Сон'."}, "motivationGeneric": "Маленькие шаги ведут к большим переменам. С каждым цифровым перерывом вы меняетесь к лучшему. Продолжайте!", "@motivationGeneric": {"description": "Общее мотивационное сообщение."}, "profileLoadFailure": "Не удалось загрузить профиль: ", "profileYourProgress": "Ваш прогресс", "@profileYourProgress": {"description": "Header for the progress section in the profile screen."}, "profilePoints": "Очки", "@profilePoints": {"description": "Title for the points speedometer chart in the profile screen."}, "profileMyGoals": "Мои цели", "@profileMyGoals": {"description": "Title for the goals section in the profile screen."}, "profileFavoriteActivities": "Избранные активности", "@profileFavoriteActivities": {"description": "Title for the favorite activities section in the profile screen."}, "profileNoFavoriteActivities": "Еще нет избранных активностей", "@profileNoFavoriteActivities": {"description": "Message shown when there are no favorite activities."}, "profileAddFavoriteActivitiesHint": "Отметьте активности как избранные, чтобы увидеть их здесь", "@profileAddFavoriteActivitiesHint": {"description": "Hint message shown when there are no favorite activities."}, "profileExploreActivities": "Изучить активности", "@profileExploreActivities": {"description": "Button text to explore activities when there are no favorites."}, "profileExploreMoreActivities": "Изучить больше активностей", "@profileExploreMoreActivities": {"description": "Button text to explore more activities at the bottom of the favorites section."}, "profileQuickActions": "Быстрые действия", "@profileQuickActions": {"description": "Title for the quick actions section in the profile screen."}, "profileActionActivityHistory": "История активностей", "@profileActionActivityHistory": {"description": "Quick action button for activity history."}, "profileActionUsedCoupons": "Использованные купоны", "@profileActionUsedCoupons": {"description": "Quick action button for used coupons."}, "profileActionRewardHistory": "История наград", "@profileActionRewardHistory": {"description": "Quick action button for reward history."}, "profileActionSettings": "Настройки", "@profileActionSettings": {"description": "Quick action button for settings."}, "profileViewAll": "Посмотреть все", "@profileViewAll": {"description": "Button text to view all favorite activities."}, "homeScreenError": "Ошибка загрузки задач: {error}", "sort": "Сортировка", "sortNotifications": "Сортировка уведомлений", "byDateNewestFirst": "По дате (сначала новые)", "unreadFirst": "Сначала непрочитанные", "unreadCount": "Непрочитанных: {count}", "@unreadCount": {"description": "Number of unread notifications", "placeholders": {"count": {"type": "int"}}}, "today": "Сегодня", "yesterday": "Вчера", "notificationDeleted": "Уведомление удалено", "notificationMarkedRead": "Отмечено как прочитанное", "notificationMarkedUnread": "Отмечено как непрочитанное", "delete": "Удалить", "markRead": "Отметить прочитанным", "markUnread": "Отметить непрочитанным", "undo": "Отменить", "noNotifications": "Нет уведомлений", "noNotificationsDescription": "У вас еще нет уведомлений. Мы сообщим вам о новых активностях, наградах и важных обновлениях.", "couponDetails": "Детали купона", "aboutPartner": "О партнере", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openInMaps": "Открыть в картах", "howToUse": "Как использовать", "termsAndConditions": "Условия использования", "validUntil": "Действует до", "redeemCoupon": "Использовать купон", "redeemFor": "Обменять на {points} очков", "@redeemFor": {"description": "<PERSON><PERSON> text for redeeming a coupon", "placeholders": {"points": {"type": "int"}}}, "redeemConfirmation": "Вы хотите использовать купон {discount} от {partner}? Это будет стоить вам {points} очков.", "@redeemConfirmation": {"description": "Confirmation message for redeeming a coupon", "placeholders": {"discount": {"type": "String"}, "partner": {"type": "String"}, "points": {"type": "int"}}}, "cancel": "Отмена", "redeem": "Использовать", "openingInMaps": "Открытие в Картах...", "shareFeatureComingSoon": "Функция обмена скоро появится!", "activityDetails": "Информация об активности", "activityCompleted": "Активность завершена!", "activityCompletedMessage": "Поздравляем! Вы завершили \"{title}\" и заработали очки!", "@activityCompletedMessage": {"description": "Message shown when an activity is completed", "placeholders": {"title": {"type": "String"}}}, "ok": "ОК", "addToFavorites": "Добавить в избранное", "removedFromFavorites": "Удалено из избранного", "addedToFavorites": "Добавлено в избранное", "commentAdded": "Комментарий добавлен", "activityInProgress": "Активность в процессе", "remaining": "осталось", "elapsed": "прошло", "stayFocused": "Сохраняйте фокус, чтобы заработать больше очков Detox!", "activityPoints": "Очки активности", "points": "очки", "completeToEarn": "Завершите эту активность, чтобы заработать очки!", "aboutThisActivity": "Об этой активности", "healthBenefits": "Польза для здоровья", "noHealthBenefitsListed": "Для этой активности не указана польза для здоровья.", "recommendedFor": "Рекомендуется для", "noCommentsYet": "Пока нет комментариев", "beFirstToComment": "Будьте первым, кто поделится своим опытом", "shareYourExperience": "Поделитесь своим опытом...", "justNow": "только что", "minutesAgo": "{minutes}м назад", "@minutesAgo": {"description": "Minutes ago text for comment timestamp", "placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "{hours}ч назад", "@hoursAgo": {"description": "Hours ago text for comment timestamp", "placeholders": {"hours": {"type": "int"}}}, "daysAgo": "{days}д назад", "@daysAgo": {"description": "Days ago text for comment timestamp", "placeholders": {"days": {"type": "int"}}}, "activityComplete": "Активность завершена!", "comments": "Комментарии", "createdBy": "Автор: {name}", "detoxBenefits": "Польза для здоровья", "targetedMoods": "Рекомендуется для", "started": "Начато: {name}", "failedToStartActivity": "Не удалось начать активность: {error}", "pointsValue": "{value} баллов", "termsConditions": "Условия и положения", "nutritionalInfo": "Пищевая ценность", "ingredients": "Ингредиенты", "preparationSteps": "Шаги приготовления", "foodBenefits": "Польза пищи", "details": "Детали", "discussion": "Обсуждение", "editComment": "Редактировать комментарий...", "commentLikeNotification": "Уведомления о лайках комментариев", "commentLikeNotificationDescription": "Получать уведомления, когда кто-то лайкает ваш комментарий"}