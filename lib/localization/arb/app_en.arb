{"@@locale": "en", "appTitle": "DetoxMe", "@appTitle": {"description": "The title of the DetoxMe app."}, "pinScreenSetupTitle": "Set up Admin PIN", "@pinScreenSetupTitle": {"description": "Title for the PIN setup screen."}, "pinScreenEnterTitle": "Enter Admin PIN", "@pinScreenEnterTitle": {"description": "Title for the PIN entry screen."}, "pinScreenSetupSubtitle": "Set up a 4-digit PIN", "@pinScreenSetupSubtitle": {"description": "Subtitle for the PIN setup screen."}, "pinScreenEnterSubtitle": "Enter your 4-digit PIN", "@pinScreenEnterSubtitle": {"description": "Subtitle for the PIN entry screen."}, "pinScreenSettingPin": "Setting PIN...", "@pinScreenSettingPin": {"description": "Loading message when setting a PIN."}, "pinScreenVerifyingPin": "Verifying PIN...", "@pinScreenVerifyingPin": {"description": "Loading message when verifying a PIN."}, "pinScreenSaving": "Saving...", "@pinScreenSaving": {"description": "Loading message when saving a PIN."}, "pinScreenChecking": "Checking...", "@pinScreenChecking": {"description": "Loading message when checking a PIN."}, "pinScreenSetupFailed": "PIN Setup Failed", "@pinScreenSetupFailed": {"description": "Error message when PIN setup fails."}, "pinScreenIncorrectPin": "Incorrect PIN", "@pinScreenIncorrectPin": {"description": "Error message when an incorrect PIN is entered."}, "pinScreenSuccessSet": "PIN Set Successfully!", "@pinScreenSuccessSet": {"description": "Success message when a PIN is set."}, "pinScreenSuccessVerified": "PIN Verified!", "@pinScreenSuccessVerified": {"description": "Success message when a PIN is verified."}, "pinScreenErrorSnackbarPrefix": "Error: ", "@pinScreenErrorSnackbarPrefix": {"description": "Prefix for error messages in PIN-related snackbars."}, "homeScreenTitle": "Home", "@homeScreenTitle": {"description": "Title for the home screen."}, "homeScreenWelcome": "Welcome back!", "@homeScreenWelcome": {"description": "Welcome message on the home screen."}, "homeScreenSubtitle": "Take a moment for yourself today", "@homeScreenSubtitle": {"description": "Subtitle message on the home screen."}, "profileScreenTitle": "Profile", "@profileScreenTitle": {"description": "Title for the profile screen."}, "profileEditTitle": "Edit Profile", "profileUsernameLabel": "Username", "profileAvatarSelectionLabel": "<PERSON><PERSON>", "profileSaveButton": "Save Profile", "profileSaveSuccess": "Profile saved successfully!", "profileSaveFailure": "Failed to save profile: ", "profileLoadFailure": "Failed to load profile: ", "profileYourProgress": "Your Progress", "@profileYourProgress": {"description": "Header for the progress section in the profile screen."}, "profilePoints": "Points", "@profilePoints": {"description": "Title for the points speedometer chart in the profile screen."}, "profileMyGoals": "My Goals", "@profileMyGoals": {"description": "Title for the goals section in the profile screen."}, "profileFavoriteActivities": "Favorite Activities", "@profileFavoriteActivities": {"description": "Title for the favorite activities section in the profile screen."}, "profileNoFavoriteActivities": "No favorite activities yet", "@profileNoFavoriteActivities": {"description": "Message shown when there are no favorite activities."}, "profileAddFavoriteActivitiesHint": "Mark activities as favorites to see them here", "@profileAddFavoriteActivitiesHint": {"description": "Hint message shown when there are no favorite activities."}, "profileExploreActivities": "Explore Activities", "@profileExploreActivities": {"description": "Button text to explore activities when there are no favorites."}, "profileExploreMoreActivities": "Explore More Activities", "@profileExploreMoreActivities": {"description": "Button text to explore more activities at the bottom of the favorites section."}, "profileQuickActions": "Quick Actions", "@profileQuickActions": {"description": "Title for the quick actions section in the profile screen."}, "profileActionActivityHistory": "Activity History", "@profileActionActivityHistory": {"description": "Quick action button for activity history."}, "profileActionUsedCoupons": "Used Coupons", "@profileActionUsedCoupons": {"description": "Quick action button for used coupons."}, "profileActionRewardHistory": "Reward History", "@profileActionRewardHistory": {"description": "Quick action button for reward history."}, "profileActionSettings": "Settings", "@profileActionSettings": {"description": "Quick action button for settings."}, "profileViewAll": "View All", "@profileViewAll": {"description": "Button text to view all favorite activities."}, "homeScreenError": "Error loading tasks: {error}", "@homeScreenError": {"description": "Error message when tasks fail to load.", "placeholders": {"error": {"type": "String"}}}, "homeScreenNoTasks": "No tasks available right now!", "homeScreenTaskDuration": "Duration: {duration}", "@homeScreenTaskDuration": {"description": "Label showing task duration", "placeholders": {"duration": {"type": "String"}}}, "homeScreenStartButton": "Begin Digital Detox", "homeScreenAnotherTaskButton": "Try a different challenge", "detoxPointsLabel": "Detox Points", "greetingMessage": "Good Day, {username}!", "@greetingMessage": {"description": "Greeting message on the home screen with the user's name.", "placeholders": {"username": {"type": "String", "example": "<PERSON>"}}}, "mindfulActivitySuggestions": "Mindful Activity Suggestions", "homeScreenProfileTooltip": "Profile", "@homeScreenProfileTooltip": {"description": "Tooltip text for the profile icon button."}, "rewardNotificationXp": "You earned {xp} Detox Points!", "@rewardNotificationXp": {"description": "Snackbar message shown when the user earns detox points.", "placeholders": {"xp": {"type": "int", "example": "25"}}}, "rewardNotificationLevelUp": "New Milestone! Reached Level {levelNumber}: {levelName}", "@rewardNotificationLevelUp": {"description": "Snackbar message shown when the user reaches a new milestone.", "placeholders": {"levelNumber": {"type": "int", "example": "5"}, "levelName": {"type": "String", "example": "Digital Minimalist"}}}, "rewardNotificationBadge": "Achievement Unlocked: {badgeName}", "@rewardNotificationBadge": {"description": "Snackbar message shown when the user earns a new achievement.", "placeholders": {"badgeName": {"type": "String", "example": "Focus Master"}}}, "rewardNotificationCard": "Reward Unlocked: {cardName} ({cardRarity})", "@rewardNotificationCard": {"description": "Snackbar message shown when the user unlocks a new reward.", "placeholders": {"cardName": {"type": "String", "example": "Mindfulness Session"}, "cardRarity": {"type": "String", "example": "Premium"}}}, "rewardErrorSnackbar": "Error processing rewards: {errorMessage}", "@rewardErrorSnackbar": {"description": "Snackbar message shown when there's an error loading or saving reward/player stats.", "placeholders": {"errorMessage": {"type": "String", "example": "Failed to connect to database"}}}, "rewardDialogTitle": "Challenge Complete!", "@rewardDialogTitle": {"description": "Title for the dialog shown after a challenge is completed and rewards are given."}, "dialogButtonOK": "OK", "@dialogButtonOK": {"description": "Generic OK button text for dialogs."}, "leaderboardScreenTitle": "Leaderboard", "@leaderboardScreenTitle": {"description": "Title for the leaderboard screen."}, "leaderboardLoadingError": "Failed to load leaderboard: {message}", "@leaderboardLoadingError": {"description": "Error message when leaderboard fails to load.", "placeholders": {"message": {"type": "String", "example": "Network error"}}}, "leaderboardEmpty": "No entries in the leaderboard yet", "@leaderboardEmpty": {"description": "Message shown when the leaderboard is empty."}, "unknownState": "Unknown state", "@unknownState": {"description": "Message shown for undefined states."}, "onboardingButtonNext": "Next", "@onboardingButtonNext": {"description": "Button text to go to the next onboarding page."}, "authLoginTitle": "<PERSON><PERSON>", "@authLoginTitle": {"description": "Title for the Authentication screen when in Login mode."}, "authSignUpTitle": "Sign Up", "@authSignUpTitle": {"description": "Title for the Authentication screen when in Sign Up mode."}, "authEmailLabel": "Email", "@authEmailLabel": {"description": "Label for the email input field on the Auth screen."}, "authPasswordLabel": "Password", "@authPasswordLabel": {"description": "Label for the password input field on the Auth screen."}, "authInvalidEmailError": "Please enter a valid email", "@authInvalidEmailError": {"description": "Validation error message for the email field."}, "authPasswordTooShortError": "Password must be at least 6 characters", "@authPasswordTooShortError": {"description": "Validation error message for the password field if it's too short."}, "authLoginButton": "<PERSON><PERSON>", "@authLoginButton": {"description": "Text for the main action button when in Login mode."}, "authSignUpButton": "Sign Up", "@authSignUpButton": {"description": "Text for the main action button when in Sign Up mode."}, "authToggleToSignUp": "Need an account? Sign Up", "@authToggleToSignUp": {"description": "Text for the button to switch from Login mode to Sign Up mode."}, "authToggleToLogin": "Have an account? Login", "@authToggleToLogin": {"description": "Text for the button to switch from Sign Up mode to Login mode."}, "adultOnboardingWelcomeTitle": "Welcome to DetoxMe!", "@adultOnboardingWelcomeTitle": {"description": "Title for the first screen of the adult onboarding."}, "adultOnboardingWelcomeBody": "Let's reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.", "@adultOnboardingWelcomeBody": {"description": "Body text for the first screen of the adult onboarding."}, "adultOnboardingExplanationTitle": "Why These Questions?", "@adultOnboardingExplanationTitle": {"description": "Title explaining why personal questions are asked."}, "adultOnboardingExplanationBody": "Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.", "@adultOnboardingExplanationBody": {"description": "Body text explaining the purpose of the questions and privacy."}, "adultOnboardingAgeTitle": "About You: Age Group", "@adultOnboardingAgeTitle": {"description": "Title for the age group selection screen."}, "adultOnboardingAgeBody": "Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.", "@adultOnboardingAgeBody": {"description": "Body text for the age group selection screen."}, "adultOnboardingAgeOption1": "18-25", "@adultOnboardingAgeOption1": {}, "adultOnboardingAgeOption2": "26-35", "@adultOnboardingAgeOption2": {}, "adultOnboardingAgeOption3": "36-45", "@adultOnboardingAgeOption3": {}, "adultOnboardingAgeOption4": "46-55", "@adultOnboardingAgeOption4": {}, "adultOnboardingAgeOption5": "56+", "@adultOnboardingAgeOption5": {}, "adultOnboardingGenderTitle": "About You: Gender", "@adultOnboardingGenderTitle": {"description": "Title for the gender selection screen."}, "adultOnboardingGenderBody": "This helps us use appropriate language and understand demographic patterns (optional).", "@adultOnboardingGenderBody": {"description": "Body text for the gender selection screen."}, "adultOnboardingGenderOptionMale": "Male", "@adultOnboardingGenderOptionMale": {}, "adultOnboardingGenderOptionFemale": "Female", "@adultOnboardingGenderOptionFemale": {}, "adultOnboardingGenderOptionNonBinary": "Non-binary", "@adultOnboardingGenderOptionNonBinary": {}, "adultOnboardingGenderOptionPreferNotToSay": "Prefer not to say", "@adultOnboardingGenderOptionPreferNotToSay": {}, "adultOnboardingFamilyTitle": "About You: Family Status", "@adultOnboardingFamilyTitle": {"description": "Title for the family status selection screen."}, "adultOnboardingFamilyBody": "Understanding your home life can help tailor challenges and goals (optional).", "@adultOnboardingFamilyBody": {"description": "Body text for the family status selection screen."}, "adultOnboardingFamilyOptionSingle": "Single", "@adultOnboardingFamilyOptionSingle": {}, "adultOnboardingFamilyOptionInRelationship": "In a relationship / Married", "@adultOnboardingFamilyOptionInRelationship": {}, "adultOnboardingFamilyOptionHaveChildren": "Have children", "@adultOnboardingFamilyOptionHaveChildren": {}, "adultOnboardingFamilyOptionPreferNotToSay": "Prefer not to say", "@adultOnboardingFamilyOptionPreferNotToSay": {}, "adultOnboardingGoalsTitle": "Your Main Goals", "@adultOnboardingGoalsTitle": {"description": "Title for the main goals selection screen."}, "adultOnboardingGoalsBody": "What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).", "@adultOnboardingGoalsBody": {"description": "Body text for the main goals selection screen."}, "adultOnboardingGoalReduceScreenTime": "Reduce overall screen time", "@adultOnboardingGoalReduceScreenTime": {}, "adultOnboardingGoalImproveFocus": "Improve focus & concentration", "@adultOnboardingGoalImproveFocus": {}, "adultOnboardingGoalBeMorePresent": "Be more present in daily life", "@adultOnboardingGoalBeMorePresent": {}, "adultOnboardingGoalSpendMoreTimeFamily": "Spend more quality time with family/friends", "@adultOnboardingGoalSpendMoreTimeFamily": {}, "adultOnboardingGoalDigitalDetox": "Perform a digital detox", "@adultOnboardingGoalDigitalDetox": {}, "adultOnboardingGoalImproveSleep": "Improve sleep quality", "@adultOnboardingGoalImproveSleep": {}, "adultOnboardingGoalOther": "Other", "@adultOnboardingGoalOther": {}, "adultOnboardingCompletionTitle": "All Set!", "@adultOnboardingCompletionTitle": {"description": "Title for the final onboarding completion screen."}, "adultOnboardingCompletionBody": "Thank you! You're ready to start your journey. Let's build healthier digital habits together.", "@adultOnboardingCompletionBody": {"description": "Body text for the final onboarding completion screen."}, "adultOnboardingButtonGetStarted": "Get Started", "@adultOnboardingButtonGetStarted": {"description": "<PERSON><PERSON> text on the final onboarding screen."}, "adultOnboardingButtonBack": "Back", "@adultOnboardingButtonBack": {"description": "<PERSON><PERSON> text to go back to the previous onboarding step."}, "requiredErrorText": "Please make a selection", "@requiredErrorText": {"description": "Generic error message when a selection is required."}, "phoneInputTitle": "Enter Phone Number", "@phoneInputTitle": {"description": "Title for the phone number input screen."}, "phoneInputInstructions": "Enter your phone number to continue", "@phoneInputInstructions": {"description": "Instructions text on the phone number input screen."}, "phoneInputLabel": "Phone Number", "@phoneInputLabel": {"description": "Label for the phone number input field."}, "phoneInputHint": "123456789", "@phoneInputHint": {"description": "Hint text for the phone number input field."}, "phoneInputEmptyError": "Please enter a phone number", "@phoneInputEmptyError": {"description": "Error message when phone number field is empty."}, "phoneInputInvalidError": "Please enter a valid phone number", "@phoneInputInvalidError": {"description": "Error message when phone number is invalid."}, "phoneInputContinueButton": "Continue", "@phoneInputContinueButton": {"description": "Text for the button to continue with phone verification."}, "otpVerificationTitle": "Verify Phone Number", "@otpVerificationTitle": {"description": "Title for the OTP verification screen."}, "otpVerificationCodeSent": "Code sent to {phoneNumber}", "@otpVerificationCodeSent": {"description": "Message showing the phone number where the code was sent.", "placeholders": {"phoneNumber": {"type": "String", "example": "*****1234"}}}, "otpVerificationInvalidCode": "Please enter a valid 6-digit code", "@otpVerificationInvalidCode": {"description": "Error message when the OTP code is invalid."}, "otpVerificationButton": "Verify Code", "@otpVerificationButton": {"description": "Text for the button to verify the OTP code."}, "otpVerificationDidntReceive": "Didn't receive the code?", "@otpVerificationDidntReceive": {"description": "Text asking if the user didn't receive the verification code."}, "otpVerificationResend": "Resend", "@otpVerificationResend": {"description": "Text for the button to resend the verification code."}, "otpVerificationResendTimer": "Resend in {seconds} s", "@otpVerificationResendTimer": {"description": "Text showing the countdown timer for resending the code.", "placeholders": {"seconds": {"type": "int", "example": "30"}}}, "otpVerifiedSnackbar": "Phone number verified successfully!", "continueWithPhone": "Continue with Phone", "@continueWithPhone": {"description": "Text for the button to continue with phone authentication."}, "continueWithGoogle": "Continue with Google", "@continueWithGoogle": {"description": "Text for the button to continue with Google authentication."}, "continueWithApple": "Continue with Apple", "@continueWithApple": {"description": "Text for the button to continue with Apple authentication."}, "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Text for the separator between authentication methods."}, "onboardingTellUsAboutYourself": "Tell us about yourself", "@onboardingTellUsAboutYourself": {"description": "Title for the user information screen during onboarding."}, "onboardingPersonalizeExperience": "We'll use this information to personalize your detox experience", "@onboardingPersonalizeExperience": {"description": "Subtitle explaining why we're collecting user information."}, "username": "Username", "@username": {"description": "Label for username input field."}, "enterYourUsername": "Enter your preferred username", "@enterYourUsername": {"description": "Hint text for the username input field."}, "usernameRequired": "Username is required", "@usernameRequired": {"description": "Validation error message when username is empty."}, "birthYear": "Birth Year", "@birthYear": {"description": "Label for birth year selection."}, "selectYourBirthYear": "Select your birth year", "@selectYourBirthYear": {"description": "Hint text for the birth year dropdown."}, "birthYearRequired": "Birth year is required", "@birthYearRequired": {"description": "Validation error message when birth year is not selected."}, "gender": "Gender", "@gender": {"description": "Label for gender selection."}, "male": "Male", "@male": {"description": "Option for male gender."}, "female": "Female", "@female": {"description": "Option for female gender."}, "nonBinary": "Non-binary", "@nonBinary": {"description": "Option for non-binary gender."}, "preferNotToSay": "Prefer not to say", "@preferNotToSay": {"description": "Option to not disclose gender."}, "next": "Next", "@next": {"description": "Text for the divider between email/password login and social login options."}, "authTitle": "Welcome Back", "@authTitle": {"description": "Title for the auth screen"}, "authSubtitle": "Sign in to continue your digital wellness journey", "@authSubtitle": {"description": "Subtitle for the auth screen"}, "loginWithPhone": "Login with Phone", "@loginWithPhone": {"description": "Text for phone login button"}, "loginWithEmail": "Login with <PERSON><PERSON>", "@loginWithEmail": {"description": "Text for email login button"}, "emailFieldLabel": "Email", "@emailFieldLabel": {"description": "Label for email input field"}, "emailRequiredError": "Email is required", "@emailRequiredError": {"description": "Error message when email is empty"}, "emailInvalidError": "Please enter a valid email", "@emailInvalidError": {"description": "Error message when email is invalid"}, "passwordFieldLabel": "Password", "@passwordFieldLabel": {"description": "Label for password input field"}, "passwordRequiredError": "Password is required", "@passwordRequiredError": {"description": "Error message when password is empty"}, "passwordLengthError": "Password must be at least 6 characters", "@passwordLengthError": {"description": "Error message when password is too short"}, "noAccountQuestion": "Don't have an account?", "@noAccountQuestion": {"description": "Text asking if user doesn't have an account"}, "hasAccountQuestion": "Already have an account?", "@hasAccountQuestion": {"description": "Text asking if user already has an account"}, "signupAction": "Sign Up", "@signupAction": {"description": "Action text for signup"}, "loginAction": "<PERSON><PERSON>", "@loginAction": {"description": "Action text for login"}, "loginButton": "<PERSON><PERSON>", "@loginButton": {"description": "Text for login button"}, "signupButton": "Create Account", "@signupButton": {"description": "Text for signup button"}, "phoneVerificationHeader": "Verify Your Phone", "@phoneVerificationHeader": {"description": "Header for phone verification screen"}, "phoneVerificationHint": "You'll receive a 6-digit code to verify your phone number", "@phoneVerificationHint": {"description": "Hint text on phone verification screen"}, "otpVerificationHeader": "Enter Verification Code", "@otpVerificationHeader": {"description": "Header for OTP verification screen"}, "otpVerificationResendCode": "Resend Code", "@otpVerificationResendCode": {"description": "Text for resending verification code"}, "otpVerificationResendIn": "Resend in", "@otpVerificationResendIn": {"description": "Text for countdown before resending code"}, "otpVerificationVerifyButton": "Verify & Continue", "@otpVerificationVerifyButton": {"description": "Text for verification button"}, "selectLifeSituation": "Select your life situation", "lifeSituationIndividual": "Individual", "lifeSituationIndividualDesc": "Focus on personal growth and wellbeing", "lifeSituationRelationship": "Relationship", "lifeSituationRelationshipDesc": "Balance screen time with your partner", "lifeSituationFamily": "Family", "lifeSituationFamilyDesc": "Create healthy digital habits for your family", "lifeSituationWork": "Work", "lifeSituationWorkDesc": "Improve productivity and reduce digital distractions", "lifeSituationErrorRequired": "Please select a life situation", "lifeSituationTitle": "Your Life Situation", "lifeSituationDescription": "Select the option that best describes your current situation", "lifeSituationCustom": "Custom", "rewardHistoryScreenTitle": "Reward History", "rewardHistoryEmptyTitle": "No Rewards Yet", "rewardHistoryEmptyMessage": "You haven't redeemed any rewards yet. Complete challenges to earn points and redeem rewards.", "loadRedemptionsButton": "<PERSON><PERSON>", "goBackButton": "Go Back", "activeActivityStatus": "{activityTitle} (Active)", "@activeActivityStatus": {"description": "Status shown when an activity is active in the app bar", "placeholders": {"activityTitle": {"type": "String", "example": "Reading"}}}, "pausedActivityStatus": "{activityTitle} (Paused)", "@pausedActivityStatus": {"description": "Status shown when an activity is paused in the app bar", "placeholders": {"activityTitle": {"type": "String", "example": "Reading"}}}, "activeActivityMotivation": "You're doing super! Stay strong and finish the task to earn valuable Detox Points.", "@activeActivityMotivation": {"description": "Motivational message shown in active activity bottom sheet"}, "activeActivityHealthBenefit": "This activity is good for your health: {benefit}", "@activeActivityHealthBenefit": {"description": "Health benefit message shown in active activity bottom sheet", "placeholders": {"benefit": {"type": "String", "example": "Reduces stress and anxiety"}}}, "activeActivityStopConfirmTitle": "Are you sure?", "@activeActivityStopConfirmTitle": {"description": "Title for the stop activity confirmation dialog"}, "activeActivityStopConfirmMessage": "You're so close to completing this activity! Stay strong and continue to earn valuable Detox Points.", "@activeActivityStopConfirmMessage": {"description": "Message shown when user tries to stop an activity"}, "activeActivityKeepGoingButton": "Keep Going", "@activeActivityKeepGoingButton": {"description": "Text for button to continue with the activity"}, "activeActivityStopButton": "Stop Activity", "@activeActivityStopButton": {"description": "Text for button to stop the activity"}, "activeActivityPauseButton": "Pause", "@activeActivityPauseButton": {"description": "Text for button to pause the activity"}, "activeActivityResumeButton": "Resume", "@activeActivityResumeButton": {"description": "Text for button to resume the activity"}, "settingsTitle": "Settings", "@settingsTitle": {"description": "Title for the settings screen."}, "appTheme": "App Theme", "@appTheme": {"description": "Section title for theme settings."}, "themeSystem": "System", "@themeSystem": {"description": "Option for system theme."}, "themeLight": "Light", "@themeLight": {"description": "Option for light theme."}, "themeDark": "Dark", "@themeDark": {"description": "Option for dark theme."}, "language": "Language", "@language": {"description": "Section title for language settings."}, "notifications": "Notifications", "@notifications": {"description": "Section title for notification settings."}, "enableNotifications": "Enable Notifications", "@enableNotifications": {"description": "Title for enabling/disabling all notifications."}, "notificationsDescription": "Receive notifications about new activities, coupons, and reminders", "@notificationsDescription": {"description": "Description of what notifications include."}, "notifyAboutActivities": "Activity Notifications", "@notifyAboutActivities": {"description": "Subheading for activity notification settings."}, "activityMoodPreferences": "Mood Preferences", "@activityMoodPreferences": {"description": "Subheading for mood-specific notification settings."}, "otherNotifications": "Other Notifications", "@otherNotifications": {"description": "Subheading for other notification settings."}, "newActivitiesPushNotif": "New Activities", "@newActivitiesPushNotif": {"description": "Toggle for new activities notifications."}, "newCouponsPushNotif": "New Coupons", "@newCouponsPushNotif": {"description": "Toggle for new coupons notifications."}, "activityRemindersPushNotif": "Activity Reminders", "@activityRemindersPushNotif": {"description": "Toggle for activity reminders notifications."}, "motivationalMessagesPushNotif": "Motivational Messages", "@motivationalMessagesPushNotif": {"description": "Toggle for motivational message notifications."}, "commentLikesPushNotif": "Comment Likes", "@commentLikesPushNotif": {"description": "Toggle for comment like notifications."}, "noNotifications": "No notifications", "@noNotifications": {"description": "Title shown when there are no notifications."}, "noNotificationsDescription": "You don't have any notifications yet. We'll notify you about new activities, rewards, and important updates.", "@noNotificationsDescription": {"description": "Description shown when there are no notifications."}, "notificationDeleted": "Notification deleted", "@notificationDeleted": {"description": "Message shown when a notification is deleted."}, "notificationMarkedRead": "Marked as read", "@notificationMarkedRead": {"description": "Message shown when a notification is marked as read."}, "notificationMarkedUnread": "Marked as unread", "@notificationMarkedUnread": {"description": "Message shown when a notification is marked as unread."}, "undo": "Undo", "@undo": {"description": "Text for undo action."}, "activitiesScreenTitle": "Mood & Activities", "@activitiesScreenTitle": {"description": "Title for the activities screen."}, "filterAndSort": "Filter & Sort", "@filterAndSort": {"description": "Title for the filter and sort bottom sheet."}, "activities": "Activities", "@activities": {"description": "Title for the activities section header."}, "allActivities": "All Activities", "@allActivities": {"description": "Filter chip text for showing all activities."}, "myActivities": "My Activities", "@myActivities": {"description": "Filter chip text for showing user's activities."}, "newest": "Newest", "@newest": {"description": "Label for newest sort option."}, "mostPopular": "Most Popular", "@mostPopular": {"description": "Label for most popular sort option."}, "mostUpvoted": "Most Upvoted", "@mostUpvoted": {"description": "Label for most upvoted sort option."}, "highestRated": "Highest Rated", "@highestRated": {"description": "Label for highest rated sort option."}, "languages": "Languages", "@languages": {"description": "Label for languages filter section."}, "applyFilters": "Apply Filters", "@applyFilters": {"description": "Button text to apply selected filters."}, "errorLoadingData": "Error loading data: {error}", "@errorLoadingData": {"description": "Error message when data fails to load.", "placeholders": {"error": {"type": "String"}}}, "errorRefreshingData": "Error refreshing data: {error}", "@errorRefreshingData": {"description": "Error message when data fails to refresh.", "placeholders": {"error": {"type": "String"}}}, "orWithEmail": "or with email", "@orWithEmail": {"description": "Text shown for email login option"}, "takeDeepBreath": "Take a deep breath", "@takeDeepBreath": {"description": "Text shown in fallback animation when <PERSON><PERSON> fails to load"}, "dashboardTitle": "Dashboard", "@dashboardTitle": {"description": "Title for the dashboard screen."}, "somethingWentWrong": "Something went wrong", "@somethingWentWrong": {"description": "Generic error message when something fails."}, "tryAgain": "Try Again", "@tryAgain": {"description": "<PERSON><PERSON> text to retry an action after failure."}, "dailyProgress": "Daily Progress", "@dailyProgress": {"description": "Title for daily progress section on dashboard."}, "failedToLoadProgressData": "Failed to load progress data", "@failedToLoadProgressData": {"description": "Error message when progress data fails to load."}, "connectionProblem": "There was a problem connecting to the server.", "@connectionProblem": {"description": "Error message for server connection issues."}, "noMotivationalContent": "No motivational content available", "@noMotivationalContent": {"description": "Message when no motivational content is available."}, "dailyPoints": "daily points", "@dailyPoints": {"description": "Label for daily points in the speedometer."}, "foodMoodTitle": "Food Mood", "@foodMoodTitle": {"description": "Title for the food mood section."}, "filter": "Filter", "@filter": {"description": "Label for filter button."}, "sortBy": "Sort By", "@sortBy": {"description": "Title for sort options menu."}, "defaultOption": "<PERSON><PERSON><PERSON>", "@defaultOption": {"description": "Label for default sort option."}, "topRated": "Top Rated", "@topRated": {"description": "Label for top rated sort option."}, "newestFirst": "Newest First", "@newestFirst": {"description": "Label for newest first sort option."}, "oldestFirst": "Oldest First", "@oldestFirst": {"description": "Label for oldest first sort option."}, "filterOptions": "Filter Options", "@filterOptions": {"description": "Title for filter options menu."}, "clearAll": "Clear All", "@clearAll": {"description": "Button to clear all selections or filters"}, "foodCategories": "Food Categories", "@foodCategories": {"description": "Label for food categories section"}, "hideHighlyDownvotedItems": "Hide Highly Downvoted Items", "@hideHighlyDownvotedItems": {"description": "Label for switch to hide highly downvoted items."}, "hideItemsWithTooManyNegativeVotes": "Hide items with too many negative votes", "@hideItemsWithTooManyNegativeVotes": {"description": "Subtitle for switch to hide highly downvoted items."}, "sort": "Sort", "@sort": {"description": "Label for sort button."}, "loadingYourProfile": "Loading your profile...", "@loadingYourProfile": {"description": "Message shown when profile is loading."}, "failedToLoadProfile": "Failed to load profile", "@failedToLoadProfile": {"description": "Error message when profile fails to load."}, "startMyDetoxJourney": "Start My Detox Journey", "@startMyDetoxJourney": {"description": "Button text for starting detox journey in onboarding."}, "startBeingPresent": "Start Being Present", "@startBeingPresent": {"description": "<PERSON><PERSON> text for being present goal in onboarding."}, "startMyDetox": "Start My Detox", "@startMyDetox": {"description": "Button text for digital detox goal in onboarding."}, "getStarted": "Get Started", "@getStarted": {"description": "Generic button text for getting started."}, "comingSoon": "Coming Soon", "@comingSoon": {"description": "Label for features that are coming soon."}, "detoxRewards": "Detox Rewards", "@detoxRewards": {"description": "Title for the rewards section."}, "newLabel": "NEW", "@newLabel": {"description": "Label to mark new features or items."}, "rewardsDescription": "Complete activities to earn points and redeem for real-world rewards", "@rewardsDescription": {"description": "Description of how the rewards system works."}, "goodMorning": "Good Morning", "@goodMorning": {"description": "Morning greeting."}, "goodAfternoon": "Good Afternoon", "@goodAfternoon": {"description": "Afternoon greeting."}, "goodEvening": "Good Evening", "@goodEvening": {"description": "Evening greeting."}, "goodNight": "Good Night", "@goodNight": {"description": "Night greeting."}, "currentPointsLabel": "Current", "@currentPointsLabel": {"description": "Label for current points statistic."}, "targetPointsLabel": "Target", "@targetPointsLabel": {"description": "Label for target points statistic."}, "maxPointsLabel": "Maximum", "@maxPointsLabel": {"description": "Label for maximum points statistic."}, "detoxActivities": "Detox Activities", "@detoxActivities": {"description": "Title for the detox activities section."}, "defaultActivitiesDescription": "Try these activities to improve your digital wellbeing and earn points", "@defaultActivitiesDescription": {"description": "Default description for activities without a mood selection."}, "activitiesForMood": "Activities tailored for your {mood} mood", "@activitiesForMood": {"description": "Description for mood-filtered activities.", "placeholders": {"mood": {"type": "String", "example": "happy"}}}, "beginnerActivities": "<PERSON><PERSON><PERSON>", "@beginnerActivities": {"description": "Label for beginner difficulty level activities."}, "intermediateActivities": "Intermediate", "@intermediateActivities": {"description": "Label for intermediate difficulty level activities."}, "expertActivities": "Expert", "@expertActivities": {"description": "Label for expert difficulty level activities."}, "loadingDashboard": "Loading your personalized dashboard...", "@loadingDashboard": {"description": "Message shown when dashboard is loading."}, "noActivitiesAvailable": "No activities available for this mood yet", "@noActivitiesAvailable": {"description": "Message shown when no activities are available for a mood."}, "activityInProgressError": "You already have an activity in progress", "@activityInProgressError": {"description": "Error message when user tries to start an activity while another is in progress."}, "startActivity": "Start", "@startActivity": {"description": "Label for button to start an activity."}, "moodOMeterTitle": "How are you feeling today?", "@moodOMeterTitle": {"description": "Title for the mood-o-meter widget."}, "moodOMeterInstructions": "Drag around the mood meter or tap an emoji to select your current mood", "@moodOMeterInstructions": {"description": "Instructions for using the mood-o-meter widget."}, "createActivityTitle": "Create Activity", "@createActivityTitle": {"description": "Title for the create activity screen."}, "titleLabel": "Title", "@titleLabel": {"description": "Label for the title input field."}, "titleHint": "Enter a title for your activity", "@titleHint": {"description": "Hint text for the title input field."}, "titleRequired": "Please enter a title", "@titleRequired": {"description": "Error message when title is empty."}, "descriptionLabel": "Description", "@descriptionLabel": {"description": "Label for the description input field."}, "descriptionHint": "Describe your activity", "@descriptionHint": {"description": "Hint text for the description input field."}, "descriptionRequired": "Please enter a description", "@descriptionRequired": {"description": "Error message when description is empty."}, "durationLabel": "Duration: {duration} minutes", "@durationLabel": {"description": "Label for the duration slider.", "placeholders": {"duration": {"type": "int", "example": "15"}}}, "durationMinutes": "{minutes} min", "@durationMinutes": {"description": "Text for duration in minutes.", "placeholders": {"minutes": {"type": "int", "example": "15"}}}, "healthBenefitsLabel": "Health Benefits", "@healthBenefitsLabel": {"description": "Label for the health benefits input field."}, "healthBenefitsHint": "Describe the health benefits of this activity", "@healthBenefitsHint": {"description": "Hint text for the health benefits input field."}, "healthBenefitsRequired": "Please describe the health benefits", "@healthBenefitsRequired": {"description": "Error message when health benefits is empty."}, "healthBenefitsDialogTitle": "Health Benefits", "@healthBenefitsDialogTitle": {"description": "Title for the health benefits dialog."}, "addNewHealthBenefitHint": "Add new health benefit", "@addNewHealthBenefitHint": {"description": "Hint text for adding a new health benefit."}, "selectedBenefitsLabel": "Selected Benefits:", "@selectedBenefitsLabel": {"description": "Label for the selected benefits section."}, "suggestedBenefitsLabel": "Suggested Benefits:", "@suggestedBenefitsLabel": {"description": "Label for the suggested benefits section."}, "categoryLabel": "Category", "@categoryLabel": {"description": "Label for the category dropdown."}, "languageLabel": "Language", "@languageLabel": {"description": "Label for the language dropdown."}, "selectLanguageHint": "Select language", "@selectLanguageHint": {"description": "Hint text for the language dropdown."}, "recommendedForMoodsLabel": "Recommended for moods", "@recommendedForMoodsLabel": {"description": "Label for the recommended moods section."}, "selectAtLeastOneMood": "(select at least 1)", "@selectAtLeastOneMood": {"description": "Instruction to select at least one mood."}, "moodSelectionError": "Please select at least 1 mood", "@moodSelectionError": {"description": "Error message when no mood is selected."}, "healthBenefitsSelectionError": "Please select at least 2 health benefits", "@healthBenefitsSelectionError": {"description": "Error message when not enough health benefits are selected."}, "privacySettingsLabel": "Privacy settings", "@privacySettingsLabel": {"description": "Label for the privacy settings section."}, "makeActivityPrivateLabel": "Make activity private", "@makeActivityPrivateLabel": {"description": "Label for the private activity toggle."}, "privateActivityDescription": "Only you can see this activity", "@privateActivityDescription": {"description": "Description for the private activity toggle."}, "showYourNameLabel": "Show your name", "@showYourNameLabel": {"description": "Label for the show name toggle."}, "showNameDescription": "Your name will be displayed as the creator", "@showNameDescription": {"description": "Description for the show name toggle."}, "createActivityButton": "Create Activity", "@createActivityButton": {"description": "Text for the create activity button."}, "activityCreatedSuccess": "Activity created successfully", "@activityCreatedSuccess": {"description": "Message shown when activity is created successfully."}, "loginRequiredError": "You must be logged in to create activities", "@loginRequiredError": {"description": "Error message when user is not logged in."}, "createActivityError": "Failed to create activity: {error}", "@createActivityError": {"description": "Error message when activity creation fails.", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "selectYourMood": "Select your mood", "@selectYourMood": {"description": "Title for the mood selector."}, "commonMoods": "Common moods:", "@commonMoods": {"description": "Label for common moods section."}, "allMoodOptions": "All mood options:", "@allMoodOptions": {"description": "Label for all mood options section."}, "moreMoodsButton": "More", "@moreMoodsButton": {"description": "Text for button to show more moods."}, "lessMoodsButton": "Less", "@lessMoodsButton": {"description": "Text for button to show fewer moods."}, "noMoodSelected": "No mood selected", "@noMoodSelected": {"description": "Message shown when no mood is selected."}, "selectMoodAbove": "Select a mood above", "@selectMoodAbove": {"description": "Message shown when no mood is selected in activity screen."}, "moodSelectionDescription": "We'll suggest activities to help you feel better or stay positive", "@moodSelectionDescription": {"description": "Description for mood selection."}, "failedToLoadActivities": "Failed to load activities", "@failedToLoadActivities": {"description": "Message shown when activities fail to load."}, "noActivitiesFound": "No activities found", "@noActivitiesFound": {"description": "Message shown when no activities are found."}, "noActivitiesForMoodDescription": "We couldn't find activities for your current mood. Try selecting a different mood.", "@noActivitiesForMoodDescription": {"description": "Description when no activities are found for the selected mood."}, "noUserActivities": "No activities created by you", "@noUserActivities": {"description": "Message shown when user has created no activities."}, "noUserActivitiesDescription": "You haven't created any activities for this mood yet. Create one or turn off the filter.", "@noUserActivitiesDescription": {"description": "Description when user has no activities for the selected mood."}, "showAllActivities": "Show All Activities", "@showAllActivities": {"description": "Button text to show all activities."}, "suggestionsForMood": "Suggestions for {mood} {emoji}", "@suggestionsForMood": {"description": "Title for mood-based suggestions.", "placeholders": {"mood": {"type": "String", "example": "happy"}, "emoji": {"type": "String", "example": "😊"}}}, "startButton": "Start", "@startButton": {"description": "Text for start button."}, "stopButton": "Stop", "@stopButton": {"description": "Text for stop button."}, "activityStarted": "Started: {activityTitle}", "@activityStarted": {"description": "Message shown when activity is started.", "placeholders": {"activityTitle": {"type": "String", "example": "Yoga"}}}, "activityStartError": "Failed to start activity: {error}", "@activityStartError": {"description": "Error message when activity fails to start.", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "activityCancelled": "Activity cancelled. No points awarded.", "@activityCancelled": {"description": "Message shown when activity is cancelled."}, "activityCancelError": "Error cancelling activity: {error}", "@activityCancelError": {"description": "Error message when activity fails to cancel.", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "completeCurrentActivityFirst": "Complete or cancel the current activity first", "@completeCurrentActivityFirst": {"description": "Message shown when user tries to start a new activity while one is active."}, "cancelButton": "Cancel", "@cancelButton": {"description": "Text for cancel button."}, "applyButton": "Apply", "@applyButton": {"description": "Text for apply button."}, "healthBenefitStress": "Reduces stress and anxiety", "@healthBenefitStress": {"description": "Health benefit related to stress reduction"}, "healthBenefitCardio": "Improves cardiovascular health", "@healthBenefitCardio": {"description": "Health benefit related to heart health"}, "healthBenefitClarity": "Enhances mental clarity", "@healthBenefitClarity": {"description": "Health benefit related to mental clarity"}, "healthBenefitImmune": "Boosts immune system", "@healthBenefitImmune": {"description": "Health benefit related to immune system"}, "healthBenefitSleep": "Improves sleep quality", "@healthBenefitSleep": {"description": "Health benefit related to sleep"}, "healthBenefitStrength": "Increases physical strength", "@healthBenefitStrength": {"description": "Health benefit related to physical strength"}, "healthBenefitMindful": "Promotes mindfulness", "@healthBenefitMindful": {"description": "Health benefit related to mindfulness"}, "healthBenefitDigital": "Reduces digital addiction", "@healthBenefitDigital": {"description": "Health benefit related to digital habits"}, "healthBenefitFocus": "Improves focus and concentration", "@healthBenefitFocus": {"description": "Health benefit related to focus"}, "healthBenefitEmotional": "Promotes emotional well-being", "@healthBenefitEmotional": {"description": "Health benefit related to emotional health"}, "healthBenefitEnergy": "Increases energy levels", "@healthBenefitEnergy": {"description": "Health benefit related to energy"}, "healthBenefitCreativity": "Enhances creativity", "@healthBenefitCreativity": {"description": "Health benefit related to creativity"}, "healthBenefitPosture": "Improves posture", "@healthBenefitPosture": {"description": "Health benefit related to posture"}, "healthBenefitWeight": "Helps with weight management", "@healthBenefitWeight": {"description": "Health benefit related to weight management"}, "foodMoodExplanationTitle": "Did you know?", "@foodMoodExplanationTitle": {"description": "Title for the food mood explanation card"}, "foodMoodExplanationText": "The food you eat can significantly impact your mood and mental well-being. Nutrient-rich foods support brain function and help regulate emotions, while processed foods may contribute to mood swings.", "@foodMoodExplanationText": {"description": "Text explaining the relationship between food and mood"}, "noRecommendationsFound": "No recommendations found", "@noRecommendationsFound": {"description": "Text shown when no food recommendations are found"}, "tryDifferentMood": "Try selecting a different mood or adjusting your filters.", "@tryDifferentMood": {"description": "Suggestion when no food recommendations are found"}, "description": "Description", "@description": {"description": "Header for description section"}, "benefits": "Benefits", "@benefits": {"description": "Title for the benefits section in food details"}, "categories": "Categories", "@categories": {"description": "Header for categories section"}, "hideDownvotedItems": "Hide Highly Downvoted Items", "@hideDownvotedItems": {"description": "Option to hide items with many negative votes"}, "hideDownvotedDescription": "Hide items with too many negative votes", "@hideDownvotedDescription": {"description": "Description for hiding downvoted items option"}, "recommendedForMoods": "Recommended for Moods", "@recommendedForMoods": {"description": "Header for moods that food is recommended for"}, "nutritionalInfo": "Nutritional Information", "@nutritionalInfo": {"description": "Title for the nutritional information section in food details"}, "ingredients": "Ingredients", "@ingredients": {"description": "Title for the ingredients section in food details"}, "preparationSteps": "Preparation Steps", "@preparationSteps": {"description": "Title for the preparation steps section in food details"}, "healthyFood": "Healthy Food", "@healthyFood": {"description": "Default category when no specific category is available"}, "moodHappy": "Happy", "@moodHappy": {"description": "Mood label: Happy"}, "moodSad": "Sad", "@moodSad": {"description": "Mood label: Sad"}, "moodAngry": "Angry", "@moodAngry": {"description": "Mood label: Angry"}, "moodAnxious": "Anxious", "@moodAnxious": {"description": "Mood label: Anxious"}, "moodTired": "Tired", "@moodTired": {"description": "Mood label: Tired"}, "moodStressed": "Stressed", "@moodStressed": {"description": "Mood label: Stressed"}, "moodDepressed": "Depressed", "@moodDepressed": {"description": "Mood label: Depressed"}, "moodExcited": "Excited", "@moodExcited": {"description": "Mood label: Excited"}, "moodCalm": "Calm", "@moodCalm": {"description": "Mood label: Calm"}, "moodBored": "<PERSON><PERSON>", "@moodBored": {"description": "Mood label: Bo<PERSON>"}, "foodCategoryProtein": "Protein-rich", "@foodCategoryProtein": {"description": "Food category: Protein-rich foods"}, "foodCategoryHealthyFats": "Healthy Fats", "@foodCategoryHealthyFats": {"description": "Food category: Healthy fats"}, "foodCategoryComplexCarbs": "Complex Carbs", "@foodCategoryComplexCarbs": {"description": "Food category: Complex carbohydrates"}, "foodCategoryVitamins": "Vitamin-rich", "@foodCategoryVitamins": {"description": "Food category: Vitamin-rich foods"}, "foodCategoryMinerals": "Minerals", "@foodCategoryMinerals": {"description": "Food category: Minerals"}, "foodCategoryOmega3": "Omega-3", "@foodCategoryOmega3": {"description": "Food category: Omega-3 rich foods"}, "foodCategoryProbiotics": "Probiotics", "@foodCategoryProbiotics": {"description": "Food category: Probiotics"}, "foodCategoryCalming": "Calming", "@foodCategoryCalming": {"description": "Food category: Calming foods"}, "foodCategoryEnergyBoosting": "Energy Boosting", "@foodCategoryEnergyBoosting": {"description": "Food category: Energy boosting foods"}, "foodCategoryAntioxidants": "Antioxidants", "@foodCategoryAntioxidants": {"description": "Food category: Antioxidants"}, "activitiesTitle": "Activities", "@activitiesTitle": {"description": "Title for the activities tab and screen."}, "helloUser": "Hello, {userName}", "@helloUser": {"description": "Greeting for the user, parameter: userName", "placeholders": {"userName": {"type": "String"}}}, "goalTag": "{goal}", "@goalTag": {"description": "Goal tag label, parameter: goal", "placeholders": {"goal": {"type": "String"}}}, "familyTag": "Family", "@familyTag": {"description": "Tag for family"}, "childrenTag": "Children", "@childrenTag": {"description": "Tag for children"}, "motivationChildrenFocus": "Setting a great example for your kids today! Your focus shows them the power of being present and engaged.", "@motivationChildrenFocus": {"description": "Motivational message for children and focus goal."}, "motivationChildrenSleep": "Better sleep means being more present for your family. Keep up with your digital boundaries!", "@motivationChildrenSleep": {"description": "Motivational message for children and sleep goal."}, "motivationChildrenGeneric": "Your children notice when you put down your phone. Every moment of connection with them matters!", "@motivationChildrenGeneric": {"description": "Motivational message for children, generic."}, "motivationFocusHigh": "Incredible focus today! Your mind is clear and your productivity is soaring. Keep it up!", "@motivationFocusHigh": {"description": "Motivational message for high focus progress."}, "motivationFocusLow": "Each moment away from your screen is helping your brain reset and focus better. You've got this!", "@motivationFocusLow": {"description": "Motivational message for low focus progress."}, "motivationSleep": "Your sleep quality improves with every digital break. Your body and mind thank you!", "@motivationSleep": {"description": "Motivational message for sleep goal."}, "motivationGeneric": "Small steps lead to big changes. You're rewiring your brain with each digital break. Keep going!", "@motivationGeneric": {"description": "Generic motivational message."}, "sortNotifications": "Sort Notifications", "byDateNewestFirst": "By Date (newest first)", "unreadFirst": "Unread First", "unreadCount": "{count} unread", "@unreadCount": {"description": "Number of unread notifications", "placeholders": {"count": {"type": "int"}}}, "today": "Today", "yesterday": "Yesterday", "delete": "Delete", "markRead": "<PERSON>", "markUnread": "<PERSON>", "couponDetails": "Coupon Details", "aboutPartner": "About Partner", "address": "Address", "openInMaps": "Open in Maps", "howToUse": "How to Use", "termsAndConditions": "Terms & Conditions", "validUntil": "Valid until", "redeemCoupon": "Redeem Coupon", "redeemFor": "Redeem for {points} Points", "@redeemFor": {"description": "<PERSON><PERSON> text for redeeming a coupon", "placeholders": {"points": {"type": "int"}}}, "redeemConfirmation": "Would you like to redeem the {discount} coupon from {partner}? This will cost you {points} points.", "@redeemConfirmation": {"description": "Confirmation message for redeeming a coupon", "placeholders": {"discount": {"type": "String"}, "partner": {"type": "String"}, "points": {"type": "int"}}}, "cancel": "Cancel", "redeem": "Redeem", "openingInMaps": "Opening in Maps...", "shareFeatureComingSoon": "Sharing feature coming soon!", "activityDetails": "Activity Details", "activityCompleted": "Activity Completed!", "activityCompletedMessage": "Congratulations! You completed \"{title}\" and earned points!", "@activityCompletedMessage": {"description": "Message shown when an activity is completed", "placeholders": {"title": {"type": "String"}}}, "ok": "OK", "addToFavorites": "Add to favorites", "removedFromFavorites": "Removed from favorites", "addedToFavorites": "Added to favorites", "commentAdded": "Comment added", "activityInProgress": "Activity in Progress", "remaining": "remaining", "elapsed": "elapsed", "stayFocused": "Stay focused to earn more Detox Points!", "activityPoints": "Activity Points", "points": "points", "completeToEarn": "Complete this activity to earn points!", "aboutThisActivity": "About this activity", "healthBenefits": "Health Benefits", "noHealthBenefitsListed": "No health benefits listed for this activity.", "recommendedFor": "Recommended for", "noCommentsYet": "No comments yet", "beFirstToComment": "Be the first to share your experience", "shareYourExperience": "Share your experience...", "justNow": "just now", "minutesAgo": "{minutes}m ago", "@minutesAgo": {"description": "Minutes ago text for comment timestamp", "placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "{hours}h ago", "@hoursAgo": {"description": "Hours ago text for comment timestamp", "placeholders": {"hours": {"type": "int"}}}, "daysAgo": "{days}d ago", "@daysAgo": {"description": "Days ago text for comment timestamp", "placeholders": {"days": {"type": "int"}}}, "activityComplete": "Activity Completed!", "comments": "Comments", "createdBy": "Created by: {name}", "@createdBy": {"description": "Shows who created the activity", "placeholders": {"name": {"type": "String"}}}, "detoxBenefits": "Detox Benefits", "targetedMoods": "Targeted Moods", "started": "Started: {name}", "@started": {"description": "Shows activity has started", "placeholders": {"name": {"type": "String"}}}, "failedToStartActivity": "Failed to start activity: {error}", "@failedToStartActivity": {"description": "Shows error when starting activity", "placeholders": {"error": {"type": "String"}}}, "pointsValue": "{value} points", "@pointsValue": {"description": "Shows point value", "placeholders": {"value": {"type": "int"}}}, "upvotes": "Upvotes", "downvotes": "Downvotes", "nutritionInfo": "Nutrition Info", "calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fats": "Fats", "moodFilter": "Filter by <PERSON><PERSON>", "networkStatusOnline": "Online", "@networkStatusOnline": {"description": "Tooltip for network online status."}, "networkStatusOffline": "Offline", "@networkStatusOffline": {"description": "Tooltip for network offline status."}, "networkStatusChecking": "Checking network...", "@networkStatusChecking": {"description": "Tooltip for network checking status."}, "commonLoading": "Loading...", "@commonLoading": {"description": "Text shown while content is loading."}, "foodImageLoadErrorTitle": "Image Not Available", "@foodImageLoadErrorTitle": {"description": "Title for the error message when a food image fails to load."}, "foodImageLoadErrorMessage": "Could not load image. Please try again later.", "@foodImageLoadErrorMessage": {"description": "Detailed message for the error when a food image fails to load."}, "suggestionsFor": "Suggestions for", "@suggestionsFor": {"description": "Label for activities suggestions for a specific mood."}, "tipsAndTricks": "Tips & Tricks", "@tipsAndTricks": {"description": "Title for the tips and tricks section in activities."}, "activityTips": "Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.", "@activityTips": {"description": "Content for the activity tips card."}, "loading": "Loading...", "@loading": {"description": "Generic loading text."}, "foodRecommendations": "Food Recommendations", "@foodRecommendations": {"description": "Title for the food recommendations section."}, "timeToDisconnect": "Time to Disconnect", "@timeToDisconnect": {"description": "Motivational header encouraging users to disconnect from devices."}, "activitiesMotivationalMessage": "Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.", "@activitiesMotivationalMessage": {"description": "Motivational message encouraging users to be active instead of using their phone."}, "earnPointsForActivities": "Earn points for every activity completed", "@earnPointsForActivities": {"description": "Badge message explaining how users earn points."}, "details": "Details", "@details": {"description": "Tab label for activity details"}, "discussion": "Discussion", "@discussion": {"description": "Tab label for comments and discussion"}, "editComment": "Edit your comment...", "@editComment": {"description": "Placeholder text for editing a comment"}, "commentLikeNotification": "Comment Like Notifications", "@commentLikeNotification": {"description": "Setting to enable/disable comment like notifications"}, "commentLikeNotificationDescription": "Get notified when someone likes your comment", "@commentLikeNotificationDescription": {"description": "Description for comment like notification setting"}}