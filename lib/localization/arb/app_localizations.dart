import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_tr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'arb/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('de'),
    Locale('en'),
    Locale('ru'),
    Locale('tr'),
  ];

  /// The title of the DetoxMe app.
  ///
  /// In en, this message translates to:
  /// **'DetoxMe'**
  String get appTitle;

  /// Title for the PIN setup screen.
  ///
  /// In en, this message translates to:
  /// **'Set up Admin PIN'**
  String get pinScreenSetupTitle;

  /// Title for the PIN entry screen.
  ///
  /// In en, this message translates to:
  /// **'Enter Admin PIN'**
  String get pinScreenEnterTitle;

  /// Subtitle for the PIN setup screen.
  ///
  /// In en, this message translates to:
  /// **'Set up a 4-digit PIN'**
  String get pinScreenSetupSubtitle;

  /// Subtitle for the PIN entry screen.
  ///
  /// In en, this message translates to:
  /// **'Enter your 4-digit PIN'**
  String get pinScreenEnterSubtitle;

  /// Loading message when setting a PIN.
  ///
  /// In en, this message translates to:
  /// **'Setting PIN...'**
  String get pinScreenSettingPin;

  /// Loading message when verifying a PIN.
  ///
  /// In en, this message translates to:
  /// **'Verifying PIN...'**
  String get pinScreenVerifyingPin;

  /// Loading message when saving a PIN.
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get pinScreenSaving;

  /// Loading message when checking a PIN.
  ///
  /// In en, this message translates to:
  /// **'Checking...'**
  String get pinScreenChecking;

  /// Error message when PIN setup fails.
  ///
  /// In en, this message translates to:
  /// **'PIN Setup Failed'**
  String get pinScreenSetupFailed;

  /// Error message when an incorrect PIN is entered.
  ///
  /// In en, this message translates to:
  /// **'Incorrect PIN'**
  String get pinScreenIncorrectPin;

  /// Success message when a PIN is set.
  ///
  /// In en, this message translates to:
  /// **'PIN Set Successfully!'**
  String get pinScreenSuccessSet;

  /// Success message when a PIN is verified.
  ///
  /// In en, this message translates to:
  /// **'PIN Verified!'**
  String get pinScreenSuccessVerified;

  /// Prefix for error messages in PIN-related snackbars.
  ///
  /// In en, this message translates to:
  /// **'Error: '**
  String get pinScreenErrorSnackbarPrefix;

  /// Title for the home screen.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get homeScreenTitle;

  /// Welcome message on the home screen.
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get homeScreenWelcome;

  /// Subtitle message on the home screen.
  ///
  /// In en, this message translates to:
  /// **'Take a moment for yourself today'**
  String get homeScreenSubtitle;

  /// Title for the profile screen.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileScreenTitle;

  /// No description provided for @profileEditTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get profileEditTitle;

  /// No description provided for @profileUsernameLabel.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get profileUsernameLabel;

  /// No description provided for @profileAvatarSelectionLabel.
  ///
  /// In en, this message translates to:
  /// **'Choose Avatar'**
  String get profileAvatarSelectionLabel;

  /// No description provided for @profileSaveButton.
  ///
  /// In en, this message translates to:
  /// **'Save Profile'**
  String get profileSaveButton;

  /// No description provided for @profileSaveSuccess.
  ///
  /// In en, this message translates to:
  /// **'Profile saved successfully!'**
  String get profileSaveSuccess;

  /// No description provided for @profileSaveFailure.
  ///
  /// In en, this message translates to:
  /// **'Failed to save profile: '**
  String get profileSaveFailure;

  /// No description provided for @profileLoadFailure.
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile: '**
  String get profileLoadFailure;

  /// Header for the progress section in the profile screen.
  ///
  /// In en, this message translates to:
  /// **'Your Progress'**
  String get profileYourProgress;

  /// Title for the points speedometer chart in the profile screen.
  ///
  /// In en, this message translates to:
  /// **'Points'**
  String get profilePoints;

  /// Title for the goals section in the profile screen.
  ///
  /// In en, this message translates to:
  /// **'My Goals'**
  String get profileMyGoals;

  /// Title for the favorite activities section in the profile screen.
  ///
  /// In en, this message translates to:
  /// **'Favorite Activities'**
  String get profileFavoriteActivities;

  /// Message shown when there are no favorite activities.
  ///
  /// In en, this message translates to:
  /// **'No favorite activities yet'**
  String get profileNoFavoriteActivities;

  /// Hint message shown when there are no favorite activities.
  ///
  /// In en, this message translates to:
  /// **'Mark activities as favorites to see them here'**
  String get profileAddFavoriteActivitiesHint;

  /// Button text to explore activities when there are no favorites.
  ///
  /// In en, this message translates to:
  /// **'Explore Activities'**
  String get profileExploreActivities;

  /// Button text to explore more activities at the bottom of the favorites section.
  ///
  /// In en, this message translates to:
  /// **'Explore More Activities'**
  String get profileExploreMoreActivities;

  /// Title for the quick actions section in the profile screen.
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get profileQuickActions;

  /// Quick action button for activity history.
  ///
  /// In en, this message translates to:
  /// **'Activity History'**
  String get profileActionActivityHistory;

  /// Quick action button for used coupons.
  ///
  /// In en, this message translates to:
  /// **'Used Coupons'**
  String get profileActionUsedCoupons;

  /// Quick action button for reward history.
  ///
  /// In en, this message translates to:
  /// **'Reward History'**
  String get profileActionRewardHistory;

  /// Quick action button for settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get profileActionSettings;

  /// Button text to view all favorite activities.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get profileViewAll;

  /// Error message when tasks fail to load.
  ///
  /// In en, this message translates to:
  /// **'Error loading tasks: {error}'**
  String homeScreenError(String error);

  /// No description provided for @homeScreenNoTasks.
  ///
  /// In en, this message translates to:
  /// **'No tasks available right now!'**
  String get homeScreenNoTasks;

  /// Label showing task duration
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration}'**
  String homeScreenTaskDuration(String duration);

  /// No description provided for @homeScreenStartButton.
  ///
  /// In en, this message translates to:
  /// **'Begin Digital Detox'**
  String get homeScreenStartButton;

  /// No description provided for @homeScreenAnotherTaskButton.
  ///
  /// In en, this message translates to:
  /// **'Try a different challenge'**
  String get homeScreenAnotherTaskButton;

  /// No description provided for @detoxPointsLabel.
  ///
  /// In en, this message translates to:
  /// **'Detox Points'**
  String get detoxPointsLabel;

  /// Greeting message on the home screen with the user's name.
  ///
  /// In en, this message translates to:
  /// **'Good Day, {username}!'**
  String greetingMessage(String username);

  /// No description provided for @mindfulActivitySuggestions.
  ///
  /// In en, this message translates to:
  /// **'Mindful Activity Suggestions'**
  String get mindfulActivitySuggestions;

  /// Tooltip text for the profile icon button.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get homeScreenProfileTooltip;

  /// Snackbar message shown when the user earns detox points.
  ///
  /// In en, this message translates to:
  /// **'You earned {xp} Detox Points!'**
  String rewardNotificationXp(int xp);

  /// Snackbar message shown when the user reaches a new milestone.
  ///
  /// In en, this message translates to:
  /// **'New Milestone! Reached Level {levelNumber}: {levelName}'**
  String rewardNotificationLevelUp(int levelNumber, String levelName);

  /// Snackbar message shown when the user earns a new achievement.
  ///
  /// In en, this message translates to:
  /// **'Achievement Unlocked: {badgeName}'**
  String rewardNotificationBadge(String badgeName);

  /// Snackbar message shown when the user unlocks a new reward.
  ///
  /// In en, this message translates to:
  /// **'Reward Unlocked: {cardName} ({cardRarity})'**
  String rewardNotificationCard(String cardName, String cardRarity);

  /// Snackbar message shown when there's an error loading or saving reward/player stats.
  ///
  /// In en, this message translates to:
  /// **'Error processing rewards: {errorMessage}'**
  String rewardErrorSnackbar(String errorMessage);

  /// Title for the dialog shown after a challenge is completed and rewards are given.
  ///
  /// In en, this message translates to:
  /// **'Challenge Complete!'**
  String get rewardDialogTitle;

  /// Generic OK button text for dialogs.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get dialogButtonOK;

  /// Title for the leaderboard screen.
  ///
  /// In en, this message translates to:
  /// **'Leaderboard'**
  String get leaderboardScreenTitle;

  /// Error message when leaderboard fails to load.
  ///
  /// In en, this message translates to:
  /// **'Failed to load leaderboard: {message}'**
  String leaderboardLoadingError(String message);

  /// Message shown when the leaderboard is empty.
  ///
  /// In en, this message translates to:
  /// **'No entries in the leaderboard yet'**
  String get leaderboardEmpty;

  /// Message shown for undefined states.
  ///
  /// In en, this message translates to:
  /// **'Unknown state'**
  String get unknownState;

  /// Button text to go to the next onboarding page.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get onboardingButtonNext;

  /// Title for the Authentication screen when in Login mode.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get authLoginTitle;

  /// Title for the Authentication screen when in Sign Up mode.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get authSignUpTitle;

  /// Label for the email input field on the Auth screen.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get authEmailLabel;

  /// Label for the password input field on the Auth screen.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get authPasswordLabel;

  /// Validation error message for the email field.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get authInvalidEmailError;

  /// Validation error message for the password field if it's too short.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get authPasswordTooShortError;

  /// Text for the main action button when in Login mode.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get authLoginButton;

  /// Text for the main action button when in Sign Up mode.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get authSignUpButton;

  /// Text for the button to switch from Login mode to Sign Up mode.
  ///
  /// In en, this message translates to:
  /// **'Need an account? Sign Up'**
  String get authToggleToSignUp;

  /// Text for the button to switch from Sign Up mode to Login mode.
  ///
  /// In en, this message translates to:
  /// **'Have an account? Login'**
  String get authToggleToLogin;

  /// Title for the first screen of the adult onboarding.
  ///
  /// In en, this message translates to:
  /// **'Welcome to DetoxMe!'**
  String get adultOnboardingWelcomeTitle;

  /// Body text for the first screen of the adult onboarding.
  ///
  /// In en, this message translates to:
  /// **'Let\'s reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.'**
  String get adultOnboardingWelcomeBody;

  /// Title explaining why personal questions are asked.
  ///
  /// In en, this message translates to:
  /// **'Why These Questions?'**
  String get adultOnboardingExplanationTitle;

  /// Body text explaining the purpose of the questions and privacy.
  ///
  /// In en, this message translates to:
  /// **'Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.'**
  String get adultOnboardingExplanationBody;

  /// Title for the age group selection screen.
  ///
  /// In en, this message translates to:
  /// **'About You: Age Group'**
  String get adultOnboardingAgeTitle;

  /// Body text for the age group selection screen.
  ///
  /// In en, this message translates to:
  /// **'Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.'**
  String get adultOnboardingAgeBody;

  /// No description provided for @adultOnboardingAgeOption1.
  ///
  /// In en, this message translates to:
  /// **'18-25'**
  String get adultOnboardingAgeOption1;

  /// No description provided for @adultOnboardingAgeOption2.
  ///
  /// In en, this message translates to:
  /// **'26-35'**
  String get adultOnboardingAgeOption2;

  /// No description provided for @adultOnboardingAgeOption3.
  ///
  /// In en, this message translates to:
  /// **'36-45'**
  String get adultOnboardingAgeOption3;

  /// No description provided for @adultOnboardingAgeOption4.
  ///
  /// In en, this message translates to:
  /// **'46-55'**
  String get adultOnboardingAgeOption4;

  /// No description provided for @adultOnboardingAgeOption5.
  ///
  /// In en, this message translates to:
  /// **'56+'**
  String get adultOnboardingAgeOption5;

  /// Title for the gender selection screen.
  ///
  /// In en, this message translates to:
  /// **'About You: Gender'**
  String get adultOnboardingGenderTitle;

  /// Body text for the gender selection screen.
  ///
  /// In en, this message translates to:
  /// **'This helps us use appropriate language and understand demographic patterns (optional).'**
  String get adultOnboardingGenderBody;

  /// No description provided for @adultOnboardingGenderOptionMale.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get adultOnboardingGenderOptionMale;

  /// No description provided for @adultOnboardingGenderOptionFemale.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get adultOnboardingGenderOptionFemale;

  /// No description provided for @adultOnboardingGenderOptionNonBinary.
  ///
  /// In en, this message translates to:
  /// **'Non-binary'**
  String get adultOnboardingGenderOptionNonBinary;

  /// No description provided for @adultOnboardingGenderOptionPreferNotToSay.
  ///
  /// In en, this message translates to:
  /// **'Prefer not to say'**
  String get adultOnboardingGenderOptionPreferNotToSay;

  /// Title for the family status selection screen.
  ///
  /// In en, this message translates to:
  /// **'About You: Family Status'**
  String get adultOnboardingFamilyTitle;

  /// Body text for the family status selection screen.
  ///
  /// In en, this message translates to:
  /// **'Understanding your home life can help tailor challenges and goals (optional).'**
  String get adultOnboardingFamilyBody;

  /// No description provided for @adultOnboardingFamilyOptionSingle.
  ///
  /// In en, this message translates to:
  /// **'Single'**
  String get adultOnboardingFamilyOptionSingle;

  /// No description provided for @adultOnboardingFamilyOptionInRelationship.
  ///
  /// In en, this message translates to:
  /// **'In a relationship / Married'**
  String get adultOnboardingFamilyOptionInRelationship;

  /// No description provided for @adultOnboardingFamilyOptionHaveChildren.
  ///
  /// In en, this message translates to:
  /// **'Have children'**
  String get adultOnboardingFamilyOptionHaveChildren;

  /// No description provided for @adultOnboardingFamilyOptionPreferNotToSay.
  ///
  /// In en, this message translates to:
  /// **'Prefer not to say'**
  String get adultOnboardingFamilyOptionPreferNotToSay;

  /// Title for the main goals selection screen.
  ///
  /// In en, this message translates to:
  /// **'Your Main Goals'**
  String get adultOnboardingGoalsTitle;

  /// Body text for the main goals selection screen.
  ///
  /// In en, this message translates to:
  /// **'What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).'**
  String get adultOnboardingGoalsBody;

  /// No description provided for @adultOnboardingGoalReduceScreenTime.
  ///
  /// In en, this message translates to:
  /// **'Reduce overall screen time'**
  String get adultOnboardingGoalReduceScreenTime;

  /// No description provided for @adultOnboardingGoalImproveFocus.
  ///
  /// In en, this message translates to:
  /// **'Improve focus & concentration'**
  String get adultOnboardingGoalImproveFocus;

  /// No description provided for @adultOnboardingGoalBeMorePresent.
  ///
  /// In en, this message translates to:
  /// **'Be more present in daily life'**
  String get adultOnboardingGoalBeMorePresent;

  /// No description provided for @adultOnboardingGoalSpendMoreTimeFamily.
  ///
  /// In en, this message translates to:
  /// **'Spend more quality time with family/friends'**
  String get adultOnboardingGoalSpendMoreTimeFamily;

  /// No description provided for @adultOnboardingGoalDigitalDetox.
  ///
  /// In en, this message translates to:
  /// **'Perform a digital detox'**
  String get adultOnboardingGoalDigitalDetox;

  /// No description provided for @adultOnboardingGoalImproveSleep.
  ///
  /// In en, this message translates to:
  /// **'Improve sleep quality'**
  String get adultOnboardingGoalImproveSleep;

  /// No description provided for @adultOnboardingGoalOther.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get adultOnboardingGoalOther;

  /// Title for the final onboarding completion screen.
  ///
  /// In en, this message translates to:
  /// **'All Set!'**
  String get adultOnboardingCompletionTitle;

  /// Body text for the final onboarding completion screen.
  ///
  /// In en, this message translates to:
  /// **'Thank you! You\'re ready to start your journey. Let\'s build healthier digital habits together.'**
  String get adultOnboardingCompletionBody;

  /// Button text on the final onboarding screen.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get adultOnboardingButtonGetStarted;

  /// Button text to go back to the previous onboarding step.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get adultOnboardingButtonBack;

  /// Generic error message when a selection is required.
  ///
  /// In en, this message translates to:
  /// **'Please make a selection'**
  String get requiredErrorText;

  /// Title for the phone number input screen.
  ///
  /// In en, this message translates to:
  /// **'Enter Phone Number'**
  String get phoneInputTitle;

  /// Instructions text on the phone number input screen.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number to continue'**
  String get phoneInputInstructions;

  /// Label for the phone number input field.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneInputLabel;

  /// Hint text for the phone number input field.
  ///
  /// In en, this message translates to:
  /// **'123456789'**
  String get phoneInputHint;

  /// Error message when phone number field is empty.
  ///
  /// In en, this message translates to:
  /// **'Please enter a phone number'**
  String get phoneInputEmptyError;

  /// Error message when phone number is invalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get phoneInputInvalidError;

  /// Text for the button to continue with phone verification.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get phoneInputContinueButton;

  /// Title for the OTP verification screen.
  ///
  /// In en, this message translates to:
  /// **'Verify Phone Number'**
  String get otpVerificationTitle;

  /// Message showing the phone number where the code was sent.
  ///
  /// In en, this message translates to:
  /// **'Code sent to {phoneNumber}'**
  String otpVerificationCodeSent(String phoneNumber);

  /// Error message when the OTP code is invalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid 6-digit code'**
  String get otpVerificationInvalidCode;

  /// Text for the button to verify the OTP code.
  ///
  /// In en, this message translates to:
  /// **'Verify Code'**
  String get otpVerificationButton;

  /// Text asking if the user didn't receive the verification code.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t receive the code?'**
  String get otpVerificationDidntReceive;

  /// Text for the button to resend the verification code.
  ///
  /// In en, this message translates to:
  /// **'Resend'**
  String get otpVerificationResend;

  /// Text showing the countdown timer for resending the code.
  ///
  /// In en, this message translates to:
  /// **'Resend in {seconds} s'**
  String otpVerificationResendTimer(int seconds);

  /// No description provided for @otpVerifiedSnackbar.
  ///
  /// In en, this message translates to:
  /// **'Phone number verified successfully!'**
  String get otpVerifiedSnackbar;

  /// Text for the button to continue with phone authentication.
  ///
  /// In en, this message translates to:
  /// **'Continue with Phone'**
  String get continueWithPhone;

  /// Text for the button to continue with Google authentication.
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get continueWithGoogle;

  /// Text for the button to continue with Apple authentication.
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get continueWithApple;

  /// Text for the separator between authentication methods.
  ///
  /// In en, this message translates to:
  /// **'Or continue with'**
  String get orContinueWith;

  /// Title for the user information screen during onboarding.
  ///
  /// In en, this message translates to:
  /// **'Tell us about yourself'**
  String get onboardingTellUsAboutYourself;

  /// Subtitle explaining why we're collecting user information.
  ///
  /// In en, this message translates to:
  /// **'We\'ll use this information to personalize your detox experience'**
  String get onboardingPersonalizeExperience;

  /// Label for username input field.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// Hint text for the username input field.
  ///
  /// In en, this message translates to:
  /// **'Enter your preferred username'**
  String get enterYourUsername;

  /// Validation error message when username is empty.
  ///
  /// In en, this message translates to:
  /// **'Username is required'**
  String get usernameRequired;

  /// Label for birth year selection.
  ///
  /// In en, this message translates to:
  /// **'Birth Year'**
  String get birthYear;

  /// Hint text for the birth year dropdown.
  ///
  /// In en, this message translates to:
  /// **'Select your birth year'**
  String get selectYourBirthYear;

  /// Validation error message when birth year is not selected.
  ///
  /// In en, this message translates to:
  /// **'Birth year is required'**
  String get birthYearRequired;

  /// Label for gender selection.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get gender;

  /// Option for male gender.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get male;

  /// Option for female gender.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get female;

  /// Option for non-binary gender.
  ///
  /// In en, this message translates to:
  /// **'Non-binary'**
  String get nonBinary;

  /// Option to not disclose gender.
  ///
  /// In en, this message translates to:
  /// **'Prefer not to say'**
  String get preferNotToSay;

  /// Text for the divider between email/password login and social login options.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Title for the auth screen
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get authTitle;

  /// Subtitle for the auth screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue your digital wellness journey'**
  String get authSubtitle;

  /// Text for phone login button
  ///
  /// In en, this message translates to:
  /// **'Login with Phone'**
  String get loginWithPhone;

  /// Text for email login button
  ///
  /// In en, this message translates to:
  /// **'Login with Email'**
  String get loginWithEmail;

  /// Label for email input field
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailFieldLabel;

  /// Error message when email is empty
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequiredError;

  /// Error message when email is invalid
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get emailInvalidError;

  /// Label for password input field
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordFieldLabel;

  /// Error message when password is empty
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequiredError;

  /// Error message when password is too short
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordLengthError;

  /// Text asking if user doesn't have an account
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get noAccountQuestion;

  /// Text asking if user already has an account
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get hasAccountQuestion;

  /// Action text for signup
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signupAction;

  /// Action text for login
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginAction;

  /// Text for login button
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginButton;

  /// Text for signup button
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get signupButton;

  /// Header for phone verification screen
  ///
  /// In en, this message translates to:
  /// **'Verify Your Phone'**
  String get phoneVerificationHeader;

  /// Hint text on phone verification screen
  ///
  /// In en, this message translates to:
  /// **'You\'ll receive a 6-digit code to verify your phone number'**
  String get phoneVerificationHint;

  /// Header for OTP verification screen
  ///
  /// In en, this message translates to:
  /// **'Enter Verification Code'**
  String get otpVerificationHeader;

  /// Text for resending verification code
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get otpVerificationResendCode;

  /// Text for countdown before resending code
  ///
  /// In en, this message translates to:
  /// **'Resend in'**
  String get otpVerificationResendIn;

  /// Text for verification button
  ///
  /// In en, this message translates to:
  /// **'Verify & Continue'**
  String get otpVerificationVerifyButton;

  /// No description provided for @selectLifeSituation.
  ///
  /// In en, this message translates to:
  /// **'Select your life situation'**
  String get selectLifeSituation;

  /// No description provided for @lifeSituationIndividual.
  ///
  /// In en, this message translates to:
  /// **'Individual'**
  String get lifeSituationIndividual;

  /// No description provided for @lifeSituationIndividualDesc.
  ///
  /// In en, this message translates to:
  /// **'Focus on personal growth and wellbeing'**
  String get lifeSituationIndividualDesc;

  /// No description provided for @lifeSituationRelationship.
  ///
  /// In en, this message translates to:
  /// **'Relationship'**
  String get lifeSituationRelationship;

  /// No description provided for @lifeSituationRelationshipDesc.
  ///
  /// In en, this message translates to:
  /// **'Balance screen time with your partner'**
  String get lifeSituationRelationshipDesc;

  /// No description provided for @lifeSituationFamily.
  ///
  /// In en, this message translates to:
  /// **'Family'**
  String get lifeSituationFamily;

  /// No description provided for @lifeSituationFamilyDesc.
  ///
  /// In en, this message translates to:
  /// **'Create healthy digital habits for your family'**
  String get lifeSituationFamilyDesc;

  /// No description provided for @lifeSituationWork.
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get lifeSituationWork;

  /// No description provided for @lifeSituationWorkDesc.
  ///
  /// In en, this message translates to:
  /// **'Improve productivity and reduce digital distractions'**
  String get lifeSituationWorkDesc;

  /// No description provided for @lifeSituationErrorRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a life situation'**
  String get lifeSituationErrorRequired;

  /// No description provided for @lifeSituationTitle.
  ///
  /// In en, this message translates to:
  /// **'Your Life Situation'**
  String get lifeSituationTitle;

  /// No description provided for @lifeSituationDescription.
  ///
  /// In en, this message translates to:
  /// **'Select the option that best describes your current situation'**
  String get lifeSituationDescription;

  /// No description provided for @lifeSituationCustom.
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get lifeSituationCustom;

  /// No description provided for @rewardHistoryScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Reward History'**
  String get rewardHistoryScreenTitle;

  /// No description provided for @rewardHistoryEmptyTitle.
  ///
  /// In en, this message translates to:
  /// **'No Rewards Yet'**
  String get rewardHistoryEmptyTitle;

  /// No description provided for @rewardHistoryEmptyMessage.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t redeemed any rewards yet. Complete challenges to earn points and redeem rewards.'**
  String get rewardHistoryEmptyMessage;

  /// No description provided for @loadRedemptionsButton.
  ///
  /// In en, this message translates to:
  /// **'Load Rewards'**
  String get loadRedemptionsButton;

  /// No description provided for @goBackButton.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBackButton;

  /// Status shown when an activity is active in the app bar
  ///
  /// In en, this message translates to:
  /// **'{activityTitle} (Active)'**
  String activeActivityStatus(String activityTitle);

  /// Status shown when an activity is paused in the app bar
  ///
  /// In en, this message translates to:
  /// **'{activityTitle} (Paused)'**
  String pausedActivityStatus(String activityTitle);

  /// Motivational message shown in active activity bottom sheet
  ///
  /// In en, this message translates to:
  /// **'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.'**
  String get activeActivityMotivation;

  /// Health benefit message shown in active activity bottom sheet
  ///
  /// In en, this message translates to:
  /// **'This activity is good for your health: {benefit}'**
  String activeActivityHealthBenefit(String benefit);

  /// Title for the stop activity confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get activeActivityStopConfirmTitle;

  /// Message shown when user tries to stop an activity
  ///
  /// In en, this message translates to:
  /// **'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.'**
  String get activeActivityStopConfirmMessage;

  /// Text for button to continue with the activity
  ///
  /// In en, this message translates to:
  /// **'Keep Going'**
  String get activeActivityKeepGoingButton;

  /// Text for button to stop the activity
  ///
  /// In en, this message translates to:
  /// **'Stop Activity'**
  String get activeActivityStopButton;

  /// Text for button to pause the activity
  ///
  /// In en, this message translates to:
  /// **'Pause'**
  String get activeActivityPauseButton;

  /// Text for button to resume the activity
  ///
  /// In en, this message translates to:
  /// **'Resume'**
  String get activeActivityResumeButton;

  /// Title for the settings screen.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsTitle;

  /// Section title for theme settings.
  ///
  /// In en, this message translates to:
  /// **'App Theme'**
  String get appTheme;

  /// Option for system theme.
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get themeSystem;

  /// Option for light theme.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get themeLight;

  /// Option for dark theme.
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get themeDark;

  /// Section title for language settings.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Section title for notification settings.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Title for enabling/disabling all notifications.
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get enableNotifications;

  /// Description of what notifications include.
  ///
  /// In en, this message translates to:
  /// **'Receive notifications about new activities, coupons, and reminders'**
  String get notificationsDescription;

  /// Subheading for activity notification settings.
  ///
  /// In en, this message translates to:
  /// **'Activity Notifications'**
  String get notifyAboutActivities;

  /// Subheading for mood-specific notification settings.
  ///
  /// In en, this message translates to:
  /// **'Mood Preferences'**
  String get activityMoodPreferences;

  /// Subheading for other notification settings.
  ///
  /// In en, this message translates to:
  /// **'Other Notifications'**
  String get otherNotifications;

  /// Toggle for new activities notifications.
  ///
  /// In en, this message translates to:
  /// **'New Activities'**
  String get newActivitiesPushNotif;

  /// Toggle for new coupons notifications.
  ///
  /// In en, this message translates to:
  /// **'New Coupons'**
  String get newCouponsPushNotif;

  /// Toggle for activity reminders notifications.
  ///
  /// In en, this message translates to:
  /// **'Activity Reminders'**
  String get activityRemindersPushNotif;

  /// Toggle for motivational message notifications.
  ///
  /// In en, this message translates to:
  /// **'Motivational Messages'**
  String get motivationalMessagesPushNotif;

  /// Toggle for comment like notifications.
  ///
  /// In en, this message translates to:
  /// **'Comment Likes'**
  String get commentLikesPushNotif;

  /// Title shown when there are no notifications.
  ///
  /// In en, this message translates to:
  /// **'No notifications'**
  String get noNotifications;

  /// Description shown when there are no notifications.
  ///
  /// In en, this message translates to:
  /// **'You don\'t have any notifications yet. We\'ll notify you about new activities, rewards, and important updates.'**
  String get noNotificationsDescription;

  /// Message shown when a notification is deleted.
  ///
  /// In en, this message translates to:
  /// **'Notification deleted'**
  String get notificationDeleted;

  /// Message shown when a notification is marked as read.
  ///
  /// In en, this message translates to:
  /// **'Marked as read'**
  String get notificationMarkedRead;

  /// Message shown when a notification is marked as unread.
  ///
  /// In en, this message translates to:
  /// **'Marked as unread'**
  String get notificationMarkedUnread;

  /// Text for undo action.
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// Title for the activities screen.
  ///
  /// In en, this message translates to:
  /// **'Mood & Activities'**
  String get activitiesScreenTitle;

  /// Title for the filter and sort bottom sheet.
  ///
  /// In en, this message translates to:
  /// **'Filter & Sort'**
  String get filterAndSort;

  /// Title for the activities section header.
  ///
  /// In en, this message translates to:
  /// **'Activities'**
  String get activities;

  /// Filter chip text for showing all activities.
  ///
  /// In en, this message translates to:
  /// **'All Activities'**
  String get allActivities;

  /// Filter chip text for showing user's activities.
  ///
  /// In en, this message translates to:
  /// **'My Activities'**
  String get myActivities;

  /// Label for newest sort option.
  ///
  /// In en, this message translates to:
  /// **'Newest'**
  String get newest;

  /// Label for most popular sort option.
  ///
  /// In en, this message translates to:
  /// **'Most Popular'**
  String get mostPopular;

  /// Label for most upvoted sort option.
  ///
  /// In en, this message translates to:
  /// **'Most Upvoted'**
  String get mostUpvoted;

  /// Label for highest rated sort option.
  ///
  /// In en, this message translates to:
  /// **'Highest Rated'**
  String get highestRated;

  /// Label for languages filter section.
  ///
  /// In en, this message translates to:
  /// **'Languages'**
  String get languages;

  /// Button text to apply selected filters.
  ///
  /// In en, this message translates to:
  /// **'Apply Filters'**
  String get applyFilters;

  /// Error message when data fails to load.
  ///
  /// In en, this message translates to:
  /// **'Error loading data: {error}'**
  String errorLoadingData(String error);

  /// Error message when data fails to refresh.
  ///
  /// In en, this message translates to:
  /// **'Error refreshing data: {error}'**
  String errorRefreshingData(String error);

  /// Text shown for email login option
  ///
  /// In en, this message translates to:
  /// **'or with email'**
  String get orWithEmail;

  /// Text shown in fallback animation when Lottie fails to load
  ///
  /// In en, this message translates to:
  /// **'Take a deep breath'**
  String get takeDeepBreath;

  /// Title for the dashboard screen.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboardTitle;

  /// Generic error message when something fails.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// Button text to retry an action after failure.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Title for daily progress section on dashboard.
  ///
  /// In en, this message translates to:
  /// **'Daily Progress'**
  String get dailyProgress;

  /// Error message when progress data fails to load.
  ///
  /// In en, this message translates to:
  /// **'Failed to load progress data'**
  String get failedToLoadProgressData;

  /// Error message for server connection issues.
  ///
  /// In en, this message translates to:
  /// **'There was a problem connecting to the server.'**
  String get connectionProblem;

  /// Message when no motivational content is available.
  ///
  /// In en, this message translates to:
  /// **'No motivational content available'**
  String get noMotivationalContent;

  /// Label for daily points in the speedometer.
  ///
  /// In en, this message translates to:
  /// **'daily points'**
  String get dailyPoints;

  /// Title for the food mood section.
  ///
  /// In en, this message translates to:
  /// **'Food Mood'**
  String get foodMoodTitle;

  /// Label for filter button.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Title for sort options menu.
  ///
  /// In en, this message translates to:
  /// **'Sort By'**
  String get sortBy;

  /// Label for default sort option.
  ///
  /// In en, this message translates to:
  /// **'Default'**
  String get defaultOption;

  /// Label for top rated sort option.
  ///
  /// In en, this message translates to:
  /// **'Top Rated'**
  String get topRated;

  /// Label for newest first sort option.
  ///
  /// In en, this message translates to:
  /// **'Newest First'**
  String get newestFirst;

  /// Label for oldest first sort option.
  ///
  /// In en, this message translates to:
  /// **'Oldest First'**
  String get oldestFirst;

  /// Title for filter options menu.
  ///
  /// In en, this message translates to:
  /// **'Filter Options'**
  String get filterOptions;

  /// Button to clear all selections or filters
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// Label for food categories section
  ///
  /// In en, this message translates to:
  /// **'Food Categories'**
  String get foodCategories;

  /// Label for switch to hide highly downvoted items.
  ///
  /// In en, this message translates to:
  /// **'Hide Highly Downvoted Items'**
  String get hideHighlyDownvotedItems;

  /// Subtitle for switch to hide highly downvoted items.
  ///
  /// In en, this message translates to:
  /// **'Hide items with too many negative votes'**
  String get hideItemsWithTooManyNegativeVotes;

  /// Label for sort button.
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// Message shown when profile is loading.
  ///
  /// In en, this message translates to:
  /// **'Loading your profile...'**
  String get loadingYourProfile;

  /// Error message when profile fails to load.
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile'**
  String get failedToLoadProfile;

  /// Button text for starting detox journey in onboarding.
  ///
  /// In en, this message translates to:
  /// **'Start My Detox Journey'**
  String get startMyDetoxJourney;

  /// Button text for being present goal in onboarding.
  ///
  /// In en, this message translates to:
  /// **'Start Being Present'**
  String get startBeingPresent;

  /// Button text for digital detox goal in onboarding.
  ///
  /// In en, this message translates to:
  /// **'Start My Detox'**
  String get startMyDetox;

  /// Generic button text for getting started.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Label for features that are coming soon.
  ///
  /// In en, this message translates to:
  /// **'Coming Soon'**
  String get comingSoon;

  /// Title for the rewards section.
  ///
  /// In en, this message translates to:
  /// **'Detox Rewards'**
  String get detoxRewards;

  /// Label to mark new features or items.
  ///
  /// In en, this message translates to:
  /// **'NEW'**
  String get newLabel;

  /// Description of how the rewards system works.
  ///
  /// In en, this message translates to:
  /// **'Complete activities to earn points and redeem for real-world rewards'**
  String get rewardsDescription;

  /// Morning greeting.
  ///
  /// In en, this message translates to:
  /// **'Good Morning'**
  String get goodMorning;

  /// Afternoon greeting.
  ///
  /// In en, this message translates to:
  /// **'Good Afternoon'**
  String get goodAfternoon;

  /// Evening greeting.
  ///
  /// In en, this message translates to:
  /// **'Good Evening'**
  String get goodEvening;

  /// Night greeting.
  ///
  /// In en, this message translates to:
  /// **'Good Night'**
  String get goodNight;

  /// Label for current points statistic.
  ///
  /// In en, this message translates to:
  /// **'Current'**
  String get currentPointsLabel;

  /// Label for target points statistic.
  ///
  /// In en, this message translates to:
  /// **'Target'**
  String get targetPointsLabel;

  /// Label for maximum points statistic.
  ///
  /// In en, this message translates to:
  /// **'Maximum'**
  String get maxPointsLabel;

  /// Title for the detox activities section.
  ///
  /// In en, this message translates to:
  /// **'Detox Activities'**
  String get detoxActivities;

  /// Default description for activities without a mood selection.
  ///
  /// In en, this message translates to:
  /// **'Try these activities to improve your digital wellbeing and earn points'**
  String get defaultActivitiesDescription;

  /// Description for mood-filtered activities.
  ///
  /// In en, this message translates to:
  /// **'Activities tailored for your {mood} mood'**
  String activitiesForMood(String mood);

  /// Label for beginner difficulty level activities.
  ///
  /// In en, this message translates to:
  /// **'Beginner'**
  String get beginnerActivities;

  /// Label for intermediate difficulty level activities.
  ///
  /// In en, this message translates to:
  /// **'Intermediate'**
  String get intermediateActivities;

  /// Label for expert difficulty level activities.
  ///
  /// In en, this message translates to:
  /// **'Expert'**
  String get expertActivities;

  /// Message shown when dashboard is loading.
  ///
  /// In en, this message translates to:
  /// **'Loading your personalized dashboard...'**
  String get loadingDashboard;

  /// Message shown when no activities are available for a mood.
  ///
  /// In en, this message translates to:
  /// **'No activities available for this mood yet'**
  String get noActivitiesAvailable;

  /// Error message when user tries to start an activity while another is in progress.
  ///
  /// In en, this message translates to:
  /// **'You already have an activity in progress'**
  String get activityInProgressError;

  /// Label for button to start an activity.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get startActivity;

  /// Title for the mood-o-meter widget.
  ///
  /// In en, this message translates to:
  /// **'How are you feeling today?'**
  String get moodOMeterTitle;

  /// Instructions for using the mood-o-meter widget.
  ///
  /// In en, this message translates to:
  /// **'Drag around the mood meter or tap an emoji to select your current mood'**
  String get moodOMeterInstructions;

  /// Title for the create activity screen.
  ///
  /// In en, this message translates to:
  /// **'Create Activity'**
  String get createActivityTitle;

  /// Label for the title input field.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get titleLabel;

  /// Hint text for the title input field.
  ///
  /// In en, this message translates to:
  /// **'Enter a title for your activity'**
  String get titleHint;

  /// Error message when title is empty.
  ///
  /// In en, this message translates to:
  /// **'Please enter a title'**
  String get titleRequired;

  /// Label for the description input field.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get descriptionLabel;

  /// Hint text for the description input field.
  ///
  /// In en, this message translates to:
  /// **'Describe your activity'**
  String get descriptionHint;

  /// Error message when description is empty.
  ///
  /// In en, this message translates to:
  /// **'Please enter a description'**
  String get descriptionRequired;

  /// Label for the duration slider.
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration} minutes'**
  String durationLabel(int duration);

  /// Text for duration in minutes.
  ///
  /// In en, this message translates to:
  /// **'{minutes} min'**
  String durationMinutes(int minutes);

  /// Label for the health benefits input field.
  ///
  /// In en, this message translates to:
  /// **'Health Benefits'**
  String get healthBenefitsLabel;

  /// Hint text for the health benefits input field.
  ///
  /// In en, this message translates to:
  /// **'Describe the health benefits of this activity'**
  String get healthBenefitsHint;

  /// Error message when health benefits is empty.
  ///
  /// In en, this message translates to:
  /// **'Please describe the health benefits'**
  String get healthBenefitsRequired;

  /// Title for the health benefits dialog.
  ///
  /// In en, this message translates to:
  /// **'Health Benefits'**
  String get healthBenefitsDialogTitle;

  /// Hint text for adding a new health benefit.
  ///
  /// In en, this message translates to:
  /// **'Add new health benefit'**
  String get addNewHealthBenefitHint;

  /// Label for the selected benefits section.
  ///
  /// In en, this message translates to:
  /// **'Selected Benefits:'**
  String get selectedBenefitsLabel;

  /// Label for the suggested benefits section.
  ///
  /// In en, this message translates to:
  /// **'Suggested Benefits:'**
  String get suggestedBenefitsLabel;

  /// Label for the category dropdown.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get categoryLabel;

  /// Label for the language dropdown.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get languageLabel;

  /// Hint text for the language dropdown.
  ///
  /// In en, this message translates to:
  /// **'Select language'**
  String get selectLanguageHint;

  /// Label for the recommended moods section.
  ///
  /// In en, this message translates to:
  /// **'Recommended for moods'**
  String get recommendedForMoodsLabel;

  /// Instruction to select at least one mood.
  ///
  /// In en, this message translates to:
  /// **'(select at least 1)'**
  String get selectAtLeastOneMood;

  /// Error message when no mood is selected.
  ///
  /// In en, this message translates to:
  /// **'Please select at least 1 mood'**
  String get moodSelectionError;

  /// Error message when not enough health benefits are selected.
  ///
  /// In en, this message translates to:
  /// **'Please select at least 2 health benefits'**
  String get healthBenefitsSelectionError;

  /// Label for the privacy settings section.
  ///
  /// In en, this message translates to:
  /// **'Privacy settings'**
  String get privacySettingsLabel;

  /// Label for the private activity toggle.
  ///
  /// In en, this message translates to:
  /// **'Make activity private'**
  String get makeActivityPrivateLabel;

  /// Description for the private activity toggle.
  ///
  /// In en, this message translates to:
  /// **'Only you can see this activity'**
  String get privateActivityDescription;

  /// Label for the show name toggle.
  ///
  /// In en, this message translates to:
  /// **'Show your name'**
  String get showYourNameLabel;

  /// Description for the show name toggle.
  ///
  /// In en, this message translates to:
  /// **'Your name will be displayed as the creator'**
  String get showNameDescription;

  /// Text for the create activity button.
  ///
  /// In en, this message translates to:
  /// **'Create Activity'**
  String get createActivityButton;

  /// Message shown when activity is created successfully.
  ///
  /// In en, this message translates to:
  /// **'Activity created successfully'**
  String get activityCreatedSuccess;

  /// Error message when user is not logged in.
  ///
  /// In en, this message translates to:
  /// **'You must be logged in to create activities'**
  String get loginRequiredError;

  /// Error message when activity creation fails.
  ///
  /// In en, this message translates to:
  /// **'Failed to create activity: {error}'**
  String createActivityError(String error);

  /// Title for the mood selector.
  ///
  /// In en, this message translates to:
  /// **'Select your mood'**
  String get selectYourMood;

  /// Label for common moods section.
  ///
  /// In en, this message translates to:
  /// **'Common moods:'**
  String get commonMoods;

  /// Label for all mood options section.
  ///
  /// In en, this message translates to:
  /// **'All mood options:'**
  String get allMoodOptions;

  /// Text for button to show more moods.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get moreMoodsButton;

  /// Text for button to show fewer moods.
  ///
  /// In en, this message translates to:
  /// **'Less'**
  String get lessMoodsButton;

  /// Message shown when no mood is selected.
  ///
  /// In en, this message translates to:
  /// **'No mood selected'**
  String get noMoodSelected;

  /// Message shown when no mood is selected in activity screen.
  ///
  /// In en, this message translates to:
  /// **'Select a mood above'**
  String get selectMoodAbove;

  /// Description for mood selection.
  ///
  /// In en, this message translates to:
  /// **'We\'ll suggest activities to help you feel better or stay positive'**
  String get moodSelectionDescription;

  /// Message shown when activities fail to load.
  ///
  /// In en, this message translates to:
  /// **'Failed to load activities'**
  String get failedToLoadActivities;

  /// Message shown when no activities are found.
  ///
  /// In en, this message translates to:
  /// **'No activities found'**
  String get noActivitiesFound;

  /// Description when no activities are found for the selected mood.
  ///
  /// In en, this message translates to:
  /// **'We couldn\'t find activities for your current mood. Try selecting a different mood.'**
  String get noActivitiesForMoodDescription;

  /// Message shown when user has created no activities.
  ///
  /// In en, this message translates to:
  /// **'No activities created by you'**
  String get noUserActivities;

  /// Description when user has no activities for the selected mood.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t created any activities for this mood yet. Create one or turn off the filter.'**
  String get noUserActivitiesDescription;

  /// Button text to show all activities.
  ///
  /// In en, this message translates to:
  /// **'Show All Activities'**
  String get showAllActivities;

  /// Title for mood-based suggestions.
  ///
  /// In en, this message translates to:
  /// **'Suggestions for {mood} {emoji}'**
  String suggestionsForMood(String mood, String emoji);

  /// Text for start button.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get startButton;

  /// Text for stop button.
  ///
  /// In en, this message translates to:
  /// **'Stop'**
  String get stopButton;

  /// Message shown when activity is started.
  ///
  /// In en, this message translates to:
  /// **'Started: {activityTitle}'**
  String activityStarted(String activityTitle);

  /// Error message when activity fails to start.
  ///
  /// In en, this message translates to:
  /// **'Failed to start activity: {error}'**
  String activityStartError(String error);

  /// Message shown when activity is cancelled.
  ///
  /// In en, this message translates to:
  /// **'Activity cancelled. No points awarded.'**
  String get activityCancelled;

  /// Error message when activity fails to cancel.
  ///
  /// In en, this message translates to:
  /// **'Error cancelling activity: {error}'**
  String activityCancelError(String error);

  /// Message shown when user tries to start a new activity while one is active.
  ///
  /// In en, this message translates to:
  /// **'Complete or cancel the current activity first'**
  String get completeCurrentActivityFirst;

  /// Text for cancel button.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// Text for apply button.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get applyButton;

  /// Health benefit related to stress reduction
  ///
  /// In en, this message translates to:
  /// **'Reduces stress and anxiety'**
  String get healthBenefitStress;

  /// Health benefit related to heart health
  ///
  /// In en, this message translates to:
  /// **'Improves cardiovascular health'**
  String get healthBenefitCardio;

  /// Health benefit related to mental clarity
  ///
  /// In en, this message translates to:
  /// **'Enhances mental clarity'**
  String get healthBenefitClarity;

  /// Health benefit related to immune system
  ///
  /// In en, this message translates to:
  /// **'Boosts immune system'**
  String get healthBenefitImmune;

  /// Health benefit related to sleep
  ///
  /// In en, this message translates to:
  /// **'Improves sleep quality'**
  String get healthBenefitSleep;

  /// Health benefit related to physical strength
  ///
  /// In en, this message translates to:
  /// **'Increases physical strength'**
  String get healthBenefitStrength;

  /// Health benefit related to mindfulness
  ///
  /// In en, this message translates to:
  /// **'Promotes mindfulness'**
  String get healthBenefitMindful;

  /// Health benefit related to digital habits
  ///
  /// In en, this message translates to:
  /// **'Reduces digital addiction'**
  String get healthBenefitDigital;

  /// Health benefit related to focus
  ///
  /// In en, this message translates to:
  /// **'Improves focus and concentration'**
  String get healthBenefitFocus;

  /// Health benefit related to emotional health
  ///
  /// In en, this message translates to:
  /// **'Promotes emotional well-being'**
  String get healthBenefitEmotional;

  /// Health benefit related to energy
  ///
  /// In en, this message translates to:
  /// **'Increases energy levels'**
  String get healthBenefitEnergy;

  /// Health benefit related to creativity
  ///
  /// In en, this message translates to:
  /// **'Enhances creativity'**
  String get healthBenefitCreativity;

  /// Health benefit related to posture
  ///
  /// In en, this message translates to:
  /// **'Improves posture'**
  String get healthBenefitPosture;

  /// Health benefit related to weight management
  ///
  /// In en, this message translates to:
  /// **'Helps with weight management'**
  String get healthBenefitWeight;

  /// Title for the food mood explanation card
  ///
  /// In en, this message translates to:
  /// **'Did you know?'**
  String get foodMoodExplanationTitle;

  /// Text explaining the relationship between food and mood
  ///
  /// In en, this message translates to:
  /// **'The food you eat can significantly impact your mood and mental well-being. Nutrient-rich foods support brain function and help regulate emotions, while processed foods may contribute to mood swings.'**
  String get foodMoodExplanationText;

  /// Text shown when no food recommendations are found
  ///
  /// In en, this message translates to:
  /// **'No recommendations found'**
  String get noRecommendationsFound;

  /// Suggestion when no food recommendations are found
  ///
  /// In en, this message translates to:
  /// **'Try selecting a different mood or adjusting your filters.'**
  String get tryDifferentMood;

  /// Header for description section
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Title for the benefits section in food details
  ///
  /// In en, this message translates to:
  /// **'Benefits'**
  String get benefits;

  /// Header for categories section
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// Option to hide items with many negative votes
  ///
  /// In en, this message translates to:
  /// **'Hide Highly Downvoted Items'**
  String get hideDownvotedItems;

  /// Description for hiding downvoted items option
  ///
  /// In en, this message translates to:
  /// **'Hide items with too many negative votes'**
  String get hideDownvotedDescription;

  /// Header for moods that food is recommended for
  ///
  /// In en, this message translates to:
  /// **'Recommended for Moods'**
  String get recommendedForMoods;

  /// Title for the nutritional information section in food details
  ///
  /// In en, this message translates to:
  /// **'Nutritional Information'**
  String get nutritionalInfo;

  /// Title for the ingredients section in food details
  ///
  /// In en, this message translates to:
  /// **'Ingredients'**
  String get ingredients;

  /// Title for the preparation steps section in food details
  ///
  /// In en, this message translates to:
  /// **'Preparation Steps'**
  String get preparationSteps;

  /// Default category when no specific category is available
  ///
  /// In en, this message translates to:
  /// **'Healthy Food'**
  String get healthyFood;

  /// Mood label: Happy
  ///
  /// In en, this message translates to:
  /// **'Happy'**
  String get moodHappy;

  /// Mood label: Sad
  ///
  /// In en, this message translates to:
  /// **'Sad'**
  String get moodSad;

  /// Mood label: Angry
  ///
  /// In en, this message translates to:
  /// **'Angry'**
  String get moodAngry;

  /// Mood label: Anxious
  ///
  /// In en, this message translates to:
  /// **'Anxious'**
  String get moodAnxious;

  /// Mood label: Tired
  ///
  /// In en, this message translates to:
  /// **'Tired'**
  String get moodTired;

  /// Mood label: Stressed
  ///
  /// In en, this message translates to:
  /// **'Stressed'**
  String get moodStressed;

  /// Mood label: Depressed
  ///
  /// In en, this message translates to:
  /// **'Depressed'**
  String get moodDepressed;

  /// Mood label: Excited
  ///
  /// In en, this message translates to:
  /// **'Excited'**
  String get moodExcited;

  /// Mood label: Calm
  ///
  /// In en, this message translates to:
  /// **'Calm'**
  String get moodCalm;

  /// Mood label: Bored
  ///
  /// In en, this message translates to:
  /// **'Bored'**
  String get moodBored;

  /// Food category: Protein-rich foods
  ///
  /// In en, this message translates to:
  /// **'Protein-rich'**
  String get foodCategoryProtein;

  /// Food category: Healthy fats
  ///
  /// In en, this message translates to:
  /// **'Healthy Fats'**
  String get foodCategoryHealthyFats;

  /// Food category: Complex carbohydrates
  ///
  /// In en, this message translates to:
  /// **'Complex Carbs'**
  String get foodCategoryComplexCarbs;

  /// Food category: Vitamin-rich foods
  ///
  /// In en, this message translates to:
  /// **'Vitamin-rich'**
  String get foodCategoryVitamins;

  /// Food category: Minerals
  ///
  /// In en, this message translates to:
  /// **'Minerals'**
  String get foodCategoryMinerals;

  /// Food category: Omega-3 rich foods
  ///
  /// In en, this message translates to:
  /// **'Omega-3'**
  String get foodCategoryOmega3;

  /// Food category: Probiotics
  ///
  /// In en, this message translates to:
  /// **'Probiotics'**
  String get foodCategoryProbiotics;

  /// Food category: Calming foods
  ///
  /// In en, this message translates to:
  /// **'Calming'**
  String get foodCategoryCalming;

  /// Food category: Energy boosting foods
  ///
  /// In en, this message translates to:
  /// **'Energy Boosting'**
  String get foodCategoryEnergyBoosting;

  /// Food category: Antioxidants
  ///
  /// In en, this message translates to:
  /// **'Antioxidants'**
  String get foodCategoryAntioxidants;

  /// Title for the activities tab and screen.
  ///
  /// In en, this message translates to:
  /// **'Activities'**
  String get activitiesTitle;

  /// Greeting for the user, parameter: userName
  ///
  /// In en, this message translates to:
  /// **'Hello, {userName}'**
  String helloUser(String userName);

  /// Goal tag label, parameter: goal
  ///
  /// In en, this message translates to:
  /// **'{goal}'**
  String goalTag(String goal);

  /// Tag for family
  ///
  /// In en, this message translates to:
  /// **'Family'**
  String get familyTag;

  /// Tag for children
  ///
  /// In en, this message translates to:
  /// **'Children'**
  String get childrenTag;

  /// Motivational message for children and focus goal.
  ///
  /// In en, this message translates to:
  /// **'Setting a great example for your kids today! Your focus shows them the power of being present and engaged.'**
  String get motivationChildrenFocus;

  /// Motivational message for children and sleep goal.
  ///
  /// In en, this message translates to:
  /// **'Better sleep means being more present for your family. Keep up with your digital boundaries!'**
  String get motivationChildrenSleep;

  /// Motivational message for children, generic.
  ///
  /// In en, this message translates to:
  /// **'Your children notice when you put down your phone. Every moment of connection with them matters!'**
  String get motivationChildrenGeneric;

  /// Motivational message for high focus progress.
  ///
  /// In en, this message translates to:
  /// **'Incredible focus today! Your mind is clear and your productivity is soaring. Keep it up!'**
  String get motivationFocusHigh;

  /// Motivational message for low focus progress.
  ///
  /// In en, this message translates to:
  /// **'Each moment away from your screen is helping your brain reset and focus better. You\'ve got this!'**
  String get motivationFocusLow;

  /// Motivational message for sleep goal.
  ///
  /// In en, this message translates to:
  /// **'Your sleep quality improves with every digital break. Your body and mind thank you!'**
  String get motivationSleep;

  /// Generic motivational message.
  ///
  /// In en, this message translates to:
  /// **'Small steps lead to big changes. You\'re rewiring your brain with each digital break. Keep going!'**
  String get motivationGeneric;

  /// No description provided for @sortNotifications.
  ///
  /// In en, this message translates to:
  /// **'Sort Notifications'**
  String get sortNotifications;

  /// No description provided for @byDateNewestFirst.
  ///
  /// In en, this message translates to:
  /// **'By Date (newest first)'**
  String get byDateNewestFirst;

  /// No description provided for @unreadFirst.
  ///
  /// In en, this message translates to:
  /// **'Unread First'**
  String get unreadFirst;

  /// Number of unread notifications
  ///
  /// In en, this message translates to:
  /// **'{count} unread'**
  String unreadCount(int count);

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @markRead.
  ///
  /// In en, this message translates to:
  /// **'Mark Read'**
  String get markRead;

  /// No description provided for @markUnread.
  ///
  /// In en, this message translates to:
  /// **'Mark Unread'**
  String get markUnread;

  /// No description provided for @couponDetails.
  ///
  /// In en, this message translates to:
  /// **'Coupon Details'**
  String get couponDetails;

  /// No description provided for @aboutPartner.
  ///
  /// In en, this message translates to:
  /// **'About Partner'**
  String get aboutPartner;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @openInMaps.
  ///
  /// In en, this message translates to:
  /// **'Open in Maps'**
  String get openInMaps;

  /// No description provided for @howToUse.
  ///
  /// In en, this message translates to:
  /// **'How to Use'**
  String get howToUse;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get termsAndConditions;

  /// No description provided for @validUntil.
  ///
  /// In en, this message translates to:
  /// **'Valid until'**
  String get validUntil;

  /// No description provided for @redeemCoupon.
  ///
  /// In en, this message translates to:
  /// **'Redeem Coupon'**
  String get redeemCoupon;

  /// Button text for redeeming a coupon
  ///
  /// In en, this message translates to:
  /// **'Redeem for {points} Points'**
  String redeemFor(int points);

  /// Confirmation message for redeeming a coupon
  ///
  /// In en, this message translates to:
  /// **'Would you like to redeem the {discount} coupon from {partner}? This will cost you {points} points.'**
  String redeemConfirmation(String discount, String partner, int points);

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @redeem.
  ///
  /// In en, this message translates to:
  /// **'Redeem'**
  String get redeem;

  /// No description provided for @openingInMaps.
  ///
  /// In en, this message translates to:
  /// **'Opening in Maps...'**
  String get openingInMaps;

  /// No description provided for @shareFeatureComingSoon.
  ///
  /// In en, this message translates to:
  /// **'Sharing feature coming soon!'**
  String get shareFeatureComingSoon;

  /// No description provided for @activityDetails.
  ///
  /// In en, this message translates to:
  /// **'Activity Details'**
  String get activityDetails;

  /// No description provided for @activityCompleted.
  ///
  /// In en, this message translates to:
  /// **'Activity Completed!'**
  String get activityCompleted;

  /// Message shown when an activity is completed
  ///
  /// In en, this message translates to:
  /// **'Congratulations! You completed \"{title}\" and earned points!'**
  String activityCompletedMessage(String title);

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @addToFavorites.
  ///
  /// In en, this message translates to:
  /// **'Add to favorites'**
  String get addToFavorites;

  /// No description provided for @removedFromFavorites.
  ///
  /// In en, this message translates to:
  /// **'Removed from favorites'**
  String get removedFromFavorites;

  /// No description provided for @addedToFavorites.
  ///
  /// In en, this message translates to:
  /// **'Added to favorites'**
  String get addedToFavorites;

  /// No description provided for @commentAdded.
  ///
  /// In en, this message translates to:
  /// **'Comment added'**
  String get commentAdded;

  /// No description provided for @activityInProgress.
  ///
  /// In en, this message translates to:
  /// **'Activity in Progress'**
  String get activityInProgress;

  /// No description provided for @remaining.
  ///
  /// In en, this message translates to:
  /// **'remaining'**
  String get remaining;

  /// No description provided for @elapsed.
  ///
  /// In en, this message translates to:
  /// **'elapsed'**
  String get elapsed;

  /// No description provided for @stayFocused.
  ///
  /// In en, this message translates to:
  /// **'Stay focused to earn more Detox Points!'**
  String get stayFocused;

  /// No description provided for @activityPoints.
  ///
  /// In en, this message translates to:
  /// **'Activity Points'**
  String get activityPoints;

  /// No description provided for @points.
  ///
  /// In en, this message translates to:
  /// **'points'**
  String get points;

  /// No description provided for @completeToEarn.
  ///
  /// In en, this message translates to:
  /// **'Complete this activity to earn points!'**
  String get completeToEarn;

  /// No description provided for @aboutThisActivity.
  ///
  /// In en, this message translates to:
  /// **'About this activity'**
  String get aboutThisActivity;

  /// No description provided for @healthBenefits.
  ///
  /// In en, this message translates to:
  /// **'Health Benefits'**
  String get healthBenefits;

  /// No description provided for @noHealthBenefitsListed.
  ///
  /// In en, this message translates to:
  /// **'No health benefits listed for this activity.'**
  String get noHealthBenefitsListed;

  /// No description provided for @recommendedFor.
  ///
  /// In en, this message translates to:
  /// **'Recommended for'**
  String get recommendedFor;

  /// No description provided for @noCommentsYet.
  ///
  /// In en, this message translates to:
  /// **'No comments yet'**
  String get noCommentsYet;

  /// No description provided for @beFirstToComment.
  ///
  /// In en, this message translates to:
  /// **'Be the first to share your experience'**
  String get beFirstToComment;

  /// No description provided for @shareYourExperience.
  ///
  /// In en, this message translates to:
  /// **'Share your experience...'**
  String get shareYourExperience;

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'just now'**
  String get justNow;

  /// Minutes ago text for comment timestamp
  ///
  /// In en, this message translates to:
  /// **'{minutes}m ago'**
  String minutesAgo(int minutes);

  /// Hours ago text for comment timestamp
  ///
  /// In en, this message translates to:
  /// **'{hours}h ago'**
  String hoursAgo(int hours);

  /// Days ago text for comment timestamp
  ///
  /// In en, this message translates to:
  /// **'{days}d ago'**
  String daysAgo(int days);

  /// No description provided for @activityComplete.
  ///
  /// In en, this message translates to:
  /// **'Activity Completed!'**
  String get activityComplete;

  /// No description provided for @comments.
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// Shows who created the activity
  ///
  /// In en, this message translates to:
  /// **'Created by: {name}'**
  String createdBy(String name);

  /// No description provided for @detoxBenefits.
  ///
  /// In en, this message translates to:
  /// **'Detox Benefits'**
  String get detoxBenefits;

  /// No description provided for @targetedMoods.
  ///
  /// In en, this message translates to:
  /// **'Targeted Moods'**
  String get targetedMoods;

  /// Shows activity has started
  ///
  /// In en, this message translates to:
  /// **'Started: {name}'**
  String started(String name);

  /// Shows error when starting activity
  ///
  /// In en, this message translates to:
  /// **'Failed to start activity: {error}'**
  String failedToStartActivity(String error);

  /// Shows point value
  ///
  /// In en, this message translates to:
  /// **'{value} points'**
  String pointsValue(int value);

  /// No description provided for @upvotes.
  ///
  /// In en, this message translates to:
  /// **'Upvotes'**
  String get upvotes;

  /// No description provided for @downvotes.
  ///
  /// In en, this message translates to:
  /// **'Downvotes'**
  String get downvotes;

  /// No description provided for @nutritionInfo.
  ///
  /// In en, this message translates to:
  /// **'Nutrition Info'**
  String get nutritionInfo;

  /// No description provided for @calories.
  ///
  /// In en, this message translates to:
  /// **'Calories'**
  String get calories;

  /// No description provided for @protein.
  ///
  /// In en, this message translates to:
  /// **'Protein'**
  String get protein;

  /// No description provided for @carbs.
  ///
  /// In en, this message translates to:
  /// **'Carbs'**
  String get carbs;

  /// No description provided for @fats.
  ///
  /// In en, this message translates to:
  /// **'Fats'**
  String get fats;

  /// No description provided for @moodFilter.
  ///
  /// In en, this message translates to:
  /// **'Filter by Mood'**
  String get moodFilter;

  /// Tooltip for network online status.
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get networkStatusOnline;

  /// Tooltip for network offline status.
  ///
  /// In en, this message translates to:
  /// **'Offline'**
  String get networkStatusOffline;

  /// Tooltip for network checking status.
  ///
  /// In en, this message translates to:
  /// **'Checking network...'**
  String get networkStatusChecking;

  /// Text shown while content is loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get commonLoading;

  /// Title for the error message when a food image fails to load.
  ///
  /// In en, this message translates to:
  /// **'Image Not Available'**
  String get foodImageLoadErrorTitle;

  /// Detailed message for the error when a food image fails to load.
  ///
  /// In en, this message translates to:
  /// **'Could not load image. Please try again later.'**
  String get foodImageLoadErrorMessage;

  /// Label for activities suggestions for a specific mood.
  ///
  /// In en, this message translates to:
  /// **'Suggestions for'**
  String get suggestionsFor;

  /// Title for the tips and tricks section in activities.
  ///
  /// In en, this message translates to:
  /// **'Tips & Tricks'**
  String get tipsAndTricks;

  /// Content for the activity tips card.
  ///
  /// In en, this message translates to:
  /// **'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.'**
  String get activityTips;

  /// Generic loading text.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Title for the food recommendations section.
  ///
  /// In en, this message translates to:
  /// **'Food Recommendations'**
  String get foodRecommendations;

  /// Motivational header encouraging users to disconnect from devices.
  ///
  /// In en, this message translates to:
  /// **'Time to Disconnect'**
  String get timeToDisconnect;

  /// Motivational message encouraging users to be active instead of using their phone.
  ///
  /// In en, this message translates to:
  /// **'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.'**
  String get activitiesMotivationalMessage;

  /// Badge message explaining how users earn points.
  ///
  /// In en, this message translates to:
  /// **'Earn points for every activity completed'**
  String get earnPointsForActivities;

  /// Tab label for activity details
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// Tab label for comments and discussion
  ///
  /// In en, this message translates to:
  /// **'Discussion'**
  String get discussion;

  /// Placeholder text for editing a comment
  ///
  /// In en, this message translates to:
  /// **'Edit your comment...'**
  String get editComment;

  /// Setting to enable/disable comment like notifications
  ///
  /// In en, this message translates to:
  /// **'Comment Like Notifications'**
  String get commentLikeNotification;

  /// Description for comment like notification setting
  ///
  /// In en, this message translates to:
  /// **'Get notified when someone likes your comment'**
  String get commentLikeNotificationDescription;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'de', 'en', 'ru', 'tr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'ru':
      return AppLocalizationsRu();
    case 'tr':
      return AppLocalizationsTr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
