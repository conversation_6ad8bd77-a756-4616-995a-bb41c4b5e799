// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'DetoxMe';

  @override
  String get pinScreenSetupTitle => 'Set up Admin PIN';

  @override
  String get pinScreenEnterTitle => 'Enter Admin PIN';

  @override
  String get pinScreenSetupSubtitle => 'Set up a 4-digit PIN';

  @override
  String get pinScreenEnterSubtitle => 'Enter your 4-digit PIN';

  @override
  String get pinScreenSettingPin => 'Setting PIN...';

  @override
  String get pinScreenVerifyingPin => 'Verifying PIN...';

  @override
  String get pinScreenSaving => 'Saving...';

  @override
  String get pinScreenChecking => 'Checking...';

  @override
  String get pinScreenSetupFailed => 'PIN Setup Failed';

  @override
  String get pinScreenIncorrectPin => 'Incorrect PIN';

  @override
  String get pinScreenSuccessSet => 'PIN Set Successfully!';

  @override
  String get pinScreenSuccessVerified => 'PIN Verified!';

  @override
  String get pinScreenErrorSnackbarPrefix => 'Error: ';

  @override
  String get homeScreenTitle => 'Home';

  @override
  String get homeScreenWelcome => 'Welcome back!';

  @override
  String get homeScreenSubtitle => 'Take a moment for yourself today';

  @override
  String get profileScreenTitle => 'Profile';

  @override
  String get profileEditTitle => 'Edit Profile';

  @override
  String get profileUsernameLabel => 'Username';

  @override
  String get profileAvatarSelectionLabel => 'Choose Avatar';

  @override
  String get profileSaveButton => 'Save Profile';

  @override
  String get profileSaveSuccess => 'Profile saved successfully!';

  @override
  String get profileSaveFailure => 'Failed to save profile: ';

  @override
  String get profileLoadFailure => 'Не удалось загрузить профиль: ';

  @override
  String get profileYourProgress => 'Ваш прогресс';

  @override
  String get profilePoints => 'Очки';

  @override
  String get profileMyGoals => 'Мои цели';

  @override
  String get profileFavoriteActivities => 'Избранные активности';

  @override
  String get profileNoFavoriteActivities => 'Еще нет избранных активностей';

  @override
  String get profileAddFavoriteActivitiesHint =>
      'Отметьте активности как избранные, чтобы увидеть их здесь';

  @override
  String get profileExploreActivities => 'Изучить активности';

  @override
  String get profileExploreMoreActivities => 'Изучить больше активностей';

  @override
  String get profileQuickActions => 'Быстрые действия';

  @override
  String get profileActionActivityHistory => 'История активностей';

  @override
  String get profileActionUsedCoupons => 'Использованные купоны';

  @override
  String get profileActionRewardHistory => 'История наград';

  @override
  String get profileActionSettings => 'Настройки';

  @override
  String get profileViewAll => 'Посмотреть все';

  @override
  String homeScreenError(String error) {
    return 'Ошибка загрузки задач: $error';
  }

  @override
  String get homeScreenNoTasks => 'No tasks available right now!';

  @override
  String homeScreenTaskDuration(String duration) {
    return 'Duration: $duration';
  }

  @override
  String get homeScreenStartButton => 'Begin Digital Detox';

  @override
  String get homeScreenAnotherTaskButton => 'Try a different challenge';

  @override
  String get detoxPointsLabel => 'Detox Points';

  @override
  String greetingMessage(String username) {
    return 'Good Day, $username!';
  }

  @override
  String get mindfulActivitySuggestions => 'Mindful Activity Suggestions';

  @override
  String get homeScreenProfileTooltip => 'Profile';

  @override
  String rewardNotificationXp(int xp) {
    return 'You earned $xp Detox Points!';
  }

  @override
  String rewardNotificationLevelUp(int levelNumber, String levelName) {
    return 'New Milestone! Reached Level $levelNumber: $levelName';
  }

  @override
  String rewardNotificationBadge(String badgeName) {
    return 'Achievement Unlocked: $badgeName';
  }

  @override
  String rewardNotificationCard(String cardName, String cardRarity) {
    return 'Reward Unlocked: $cardName ($cardRarity)';
  }

  @override
  String rewardErrorSnackbar(String errorMessage) {
    return 'Error processing rewards: $errorMessage';
  }

  @override
  String get rewardDialogTitle => 'Challenge Complete!';

  @override
  String get dialogButtonOK => 'OK';

  @override
  String get leaderboardScreenTitle => 'Leaderboard';

  @override
  String leaderboardLoadingError(String message) {
    return 'Failed to load leaderboard: $message';
  }

  @override
  String get leaderboardEmpty => 'No entries in the leaderboard yet';

  @override
  String get unknownState => 'Unknown state';

  @override
  String get onboardingButtonNext => 'Next';

  @override
  String get authLoginTitle => 'Login';

  @override
  String get authSignUpTitle => 'Sign Up';

  @override
  String get authEmailLabel => 'Email';

  @override
  String get authPasswordLabel => 'Password';

  @override
  String get authInvalidEmailError => 'Please enter a valid email';

  @override
  String get authPasswordTooShortError =>
      'Password must be at least 6 characters';

  @override
  String get authLoginButton => 'Login';

  @override
  String get authSignUpButton => 'Sign Up';

  @override
  String get authToggleToSignUp => 'Need an account? Sign Up';

  @override
  String get authToggleToLogin => 'Have an account? Login';

  @override
  String get adultOnboardingWelcomeTitle => 'Welcome to DetoxMe!';

  @override
  String get adultOnboardingWelcomeBody =>
      'Let\'s reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.';

  @override
  String get adultOnboardingExplanationTitle => 'Why These Questions?';

  @override
  String get adultOnboardingExplanationBody =>
      'Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.';

  @override
  String get adultOnboardingAgeTitle => 'About You: Age Group';

  @override
  String get adultOnboardingAgeBody =>
      'Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.';

  @override
  String get adultOnboardingAgeOption1 => '18-25';

  @override
  String get adultOnboardingAgeOption2 => '26-35';

  @override
  String get adultOnboardingAgeOption3 => '36-45';

  @override
  String get adultOnboardingAgeOption4 => '46-55';

  @override
  String get adultOnboardingAgeOption5 => '56+';

  @override
  String get adultOnboardingGenderTitle => 'About You: Gender';

  @override
  String get adultOnboardingGenderBody =>
      'This helps us use appropriate language and understand demographic patterns (optional).';

  @override
  String get adultOnboardingGenderOptionMale => 'Male';

  @override
  String get adultOnboardingGenderOptionFemale => 'Female';

  @override
  String get adultOnboardingGenderOptionNonBinary => 'Non-binary';

  @override
  String get adultOnboardingGenderOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingFamilyTitle => 'About You: Family Status';

  @override
  String get adultOnboardingFamilyBody =>
      'Understanding your home life can help tailor challenges and goals (optional).';

  @override
  String get adultOnboardingFamilyOptionSingle => 'Single';

  @override
  String get adultOnboardingFamilyOptionInRelationship =>
      'In a relationship / Married';

  @override
  String get adultOnboardingFamilyOptionHaveChildren => 'Have children';

  @override
  String get adultOnboardingFamilyOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingGoalsTitle => 'Your Main Goals';

  @override
  String get adultOnboardingGoalsBody =>
      'What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).';

  @override
  String get adultOnboardingGoalReduceScreenTime =>
      'Reduce overall screen time';

  @override
  String get adultOnboardingGoalImproveFocus => 'Improve focus & concentration';

  @override
  String get adultOnboardingGoalBeMorePresent =>
      'Be more present in daily life';

  @override
  String get adultOnboardingGoalSpendMoreTimeFamily =>
      'Spend more quality time with family/friends';

  @override
  String get adultOnboardingGoalDigitalDetox => 'Perform a digital detox';

  @override
  String get adultOnboardingGoalImproveSleep => 'Improve sleep quality';

  @override
  String get adultOnboardingGoalOther => 'Other';

  @override
  String get adultOnboardingCompletionTitle => 'All Set!';

  @override
  String get adultOnboardingCompletionBody =>
      'Thank you! You\'re ready to start your journey. Let\'s build healthier digital habits together.';

  @override
  String get adultOnboardingButtonGetStarted => 'Get Started';

  @override
  String get adultOnboardingButtonBack => 'Back';

  @override
  String get requiredErrorText => 'Please make a selection';

  @override
  String get phoneInputTitle => 'Enter Phone Number';

  @override
  String get phoneInputInstructions => 'Enter your phone number to continue';

  @override
  String get phoneInputLabel => 'Phone Number';

  @override
  String get phoneInputHint => '123456789';

  @override
  String get phoneInputEmptyError => 'Please enter a phone number';

  @override
  String get phoneInputInvalidError => 'Please enter a valid phone number';

  @override
  String get phoneInputContinueButton => 'Continue';

  @override
  String get otpVerificationTitle => 'Verify Phone Number';

  @override
  String otpVerificationCodeSent(String phoneNumber) {
    return 'Code sent to $phoneNumber';
  }

  @override
  String get otpVerificationInvalidCode => 'Please enter a valid 6-digit code';

  @override
  String get otpVerificationButton => 'Verify Code';

  @override
  String get otpVerificationDidntReceive => 'Didn\'t receive the code?';

  @override
  String get otpVerificationResend => 'Resend';

  @override
  String otpVerificationResendTimer(int seconds) {
    return 'Resend in $seconds s';
  }

  @override
  String get otpVerifiedSnackbar => 'Phone number verified successfully!';

  @override
  String get continueWithPhone => 'Continue with Phone';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get onboardingTellUsAboutYourself => 'Tell us about yourself';

  @override
  String get onboardingPersonalizeExperience =>
      'We\'ll use this information to personalize your detox experience';

  @override
  String get username => 'Username';

  @override
  String get enterYourUsername => 'Enter your preferred username';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectYourBirthYear => 'Select your birth year';

  @override
  String get birthYearRequired => 'Birth year is required';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get nonBinary => 'Non-binary';

  @override
  String get preferNotToSay => 'Prefer not to say';

  @override
  String get next => 'Next';

  @override
  String get authTitle => 'Welcome Back';

  @override
  String get authSubtitle =>
      'Sign in to continue your digital wellness journey';

  @override
  String get loginWithPhone => 'Login with Phone';

  @override
  String get loginWithEmail => 'Login with Email';

  @override
  String get emailFieldLabel => 'Email';

  @override
  String get emailRequiredError => 'Email is required';

  @override
  String get emailInvalidError => 'Please enter a valid email';

  @override
  String get passwordFieldLabel => 'Password';

  @override
  String get passwordRequiredError => 'Password is required';

  @override
  String get passwordLengthError => 'Password must be at least 6 characters';

  @override
  String get noAccountQuestion => 'Don\'t have an account?';

  @override
  String get hasAccountQuestion => 'Already have an account?';

  @override
  String get signupAction => 'Sign Up';

  @override
  String get loginAction => 'Login';

  @override
  String get loginButton => 'Login';

  @override
  String get signupButton => 'Create Account';

  @override
  String get phoneVerificationHeader => 'Verify Your Phone';

  @override
  String get phoneVerificationHint =>
      'You\'ll receive a 6-digit code to verify your phone number';

  @override
  String get otpVerificationHeader => 'Enter Verification Code';

  @override
  String get otpVerificationResendCode => 'Resend Code';

  @override
  String get otpVerificationResendIn => 'Resend in';

  @override
  String get otpVerificationVerifyButton => 'Verify & Continue';

  @override
  String get selectLifeSituation => 'Select your life situation';

  @override
  String get lifeSituationIndividual => 'Individual';

  @override
  String get lifeSituationIndividualDesc =>
      'Focus on personal growth and wellbeing';

  @override
  String get lifeSituationRelationship => 'Relationship';

  @override
  String get lifeSituationRelationshipDesc =>
      'Balance screen time with your partner';

  @override
  String get lifeSituationFamily => 'Family';

  @override
  String get lifeSituationFamilyDesc =>
      'Create healthy digital habits for your family';

  @override
  String get lifeSituationWork => 'Work';

  @override
  String get lifeSituationWorkDesc =>
      'Improve productivity and reduce digital distractions';

  @override
  String get lifeSituationErrorRequired => 'Please select a life situation';

  @override
  String get lifeSituationTitle => 'Your Life Situation';

  @override
  String get lifeSituationDescription =>
      'Select the option that best describes your current situation';

  @override
  String get lifeSituationCustom => 'Custom';

  @override
  String get rewardHistoryScreenTitle => 'Reward History';

  @override
  String get rewardHistoryEmptyTitle => 'No Rewards Yet';

  @override
  String get rewardHistoryEmptyMessage =>
      'You haven\'t redeemed any rewards yet. Complete challenges to earn points and redeem rewards.';

  @override
  String get loadRedemptionsButton => 'Load Rewards';

  @override
  String get goBackButton => 'Go Back';

  @override
  String activeActivityStatus(String activityTitle) {
    return '$activityTitle (Active)';
  }

  @override
  String pausedActivityStatus(String activityTitle) {
    return '$activityTitle (Paused)';
  }

  @override
  String get activeActivityMotivation =>
      'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.';

  @override
  String activeActivityHealthBenefit(String benefit) {
    return 'This activity is good for your health: $benefit';
  }

  @override
  String get activeActivityStopConfirmTitle => 'Are you sure?';

  @override
  String get activeActivityStopConfirmMessage =>
      'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.';

  @override
  String get activeActivityKeepGoingButton => 'Keep Going';

  @override
  String get activeActivityStopButton => 'Stop Activity';

  @override
  String get activeActivityPauseButton => 'Pause';

  @override
  String get activeActivityResumeButton => 'Resume';

  @override
  String get settingsTitle => 'Настройки';

  @override
  String get appTheme => 'Тема приложения';

  @override
  String get themeSystem => 'Системная';

  @override
  String get themeLight => 'Светлая';

  @override
  String get themeDark => 'Тёмная';

  @override
  String get language => 'Язык';

  @override
  String get notifications => 'Уведомления';

  @override
  String get enableNotifications => 'Включить уведомления';

  @override
  String get notificationsDescription =>
      'Получайте уведомления о новых активностях, купонах и напоминаниях';

  @override
  String get notifyAboutActivities => 'Уведомления об активностях';

  @override
  String get activityMoodPreferences => 'Настройки настроения';

  @override
  String get otherNotifications => 'Другие уведомления';

  @override
  String get newActivitiesPushNotif => 'Новые активности';

  @override
  String get newCouponsPushNotif => 'Новые купоны';

  @override
  String get activityRemindersPushNotif => 'Напоминания';

  @override
  String get motivationalMessagesPushNotif => 'Мотивационные сообщения';

  @override
  String get commentLikesPushNotif => 'Лайки комментариев';

  @override
  String get noNotifications => 'Нет уведомлений';

  @override
  String get noNotificationsDescription =>
      'У вас еще нет уведомлений. Мы сообщим вам о новых активностях, наградах и важных обновлениях.';

  @override
  String get notificationDeleted => 'Уведомление удалено';

  @override
  String get notificationMarkedRead => 'Отмечено как прочитанное';

  @override
  String get notificationMarkedUnread => 'Отмечено как непрочитанное';

  @override
  String get undo => 'Отменить';

  @override
  String get activitiesScreenTitle => 'Mood & Activities';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get activities => 'Activities';

  @override
  String get allActivities => 'All Activities';

  @override
  String get myActivities => 'My Activities';

  @override
  String get newest => 'Newest';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get mostUpvoted => 'Most Upvoted';

  @override
  String get highestRated => 'Highest Rated';

  @override
  String get languages => 'Languages';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String errorLoadingData(String error) {
    return 'Error loading data: $error';
  }

  @override
  String errorRefreshingData(String error) {
    return 'Error refreshing data: $error';
  }

  @override
  String get orWithEmail => 'or with email';

  @override
  String get takeDeepBreath => 'Take a deep breath';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get dailyProgress => 'Daily Progress';

  @override
  String get failedToLoadProgressData => 'Failed to load progress data';

  @override
  String get connectionProblem =>
      'There was a problem connecting to the server.';

  @override
  String get noMotivationalContent => 'No motivational content available';

  @override
  String get dailyPoints => 'daily points';

  @override
  String get foodMoodTitle => 'Food Mood';

  @override
  String get filter => 'Filter';

  @override
  String get sortBy => 'Sort By';

  @override
  String get defaultOption => 'Default';

  @override
  String get topRated => 'Top Rated';

  @override
  String get newestFirst => 'Newest First';

  @override
  String get oldestFirst => 'Oldest First';

  @override
  String get filterOptions => 'Filter Options';

  @override
  String get clearAll => 'Очистить всё';

  @override
  String get foodCategories => 'Категории продуктов';

  @override
  String get hideHighlyDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideItemsWithTooManyNegativeVotes =>
      'Hide items with too many negative votes';

  @override
  String get sort => 'Сортировка';

  @override
  String get loadingYourProfile => 'Loading your profile...';

  @override
  String get failedToLoadProfile => 'Failed to load profile';

  @override
  String get startMyDetoxJourney => 'Start My Detox Journey';

  @override
  String get startBeingPresent => 'Start Being Present';

  @override
  String get startMyDetox => 'Start My Detox';

  @override
  String get getStarted => 'Get Started';

  @override
  String get comingSoon => 'Скоро';

  @override
  String get detoxRewards => 'Награды Detox';

  @override
  String get newLabel => 'НОВОЕ';

  @override
  String get rewardsDescription =>
      'Выполняйте задания, чтобы получать очки и обменивать их на реальные награды';

  @override
  String get goodMorning => 'Доброе утро';

  @override
  String get goodAfternoon => 'Добрый день';

  @override
  String get goodEvening => 'Добрый вечер';

  @override
  String get goodNight => 'Спокойной ночи';

  @override
  String get currentPointsLabel => 'Текущие';

  @override
  String get targetPointsLabel => 'Цель';

  @override
  String get maxPointsLabel => 'Максимум';

  @override
  String get detoxActivities => 'Detox-активности';

  @override
  String get defaultActivitiesDescription =>
      'Попробуйте эти активности, чтобы улучшить ваше цифровое благополучие и заработать очки';

  @override
  String activitiesForMood(String mood) {
    return 'Активности для вашего $mood настроения';
  }

  @override
  String get beginnerActivities => 'Начинающий';

  @override
  String get intermediateActivities => 'Средний';

  @override
  String get expertActivities => 'Эксперт';

  @override
  String get loadingDashboard => 'Загрузка вашего персонального дашборда...';

  @override
  String get noActivitiesAvailable =>
      'Пока нет активностей для этого настроения';

  @override
  String get activityInProgressError => 'У вас уже есть активность в процессе';

  @override
  String get startActivity => 'Начать';

  @override
  String get moodOMeterTitle => 'Как вы себя чувствуете сегодня?';

  @override
  String get moodOMeterInstructions =>
      'Перетаскивайте по шкале настроения или нажмите на эмодзи, чтобы выбрать ваше текущее настроение';

  @override
  String get createActivityTitle => 'Создать активность';

  @override
  String get titleLabel => 'Название';

  @override
  String get titleHint => 'Введите название для вашей активности';

  @override
  String get titleRequired => 'Пожалуйста, введите название';

  @override
  String get descriptionLabel => 'Описание';

  @override
  String get descriptionHint => 'Опишите вашу активность';

  @override
  String get descriptionRequired => 'Пожалуйста, введите описание';

  @override
  String durationLabel(int duration) {
    return 'Продолжительность: $duration минут';
  }

  @override
  String durationMinutes(int minutes) {
    return '$minutes мин';
  }

  @override
  String get healthBenefitsLabel => 'Польза для здоровья';

  @override
  String get healthBenefitsHint =>
      'Опишите пользу для здоровья от этой активности';

  @override
  String get healthBenefitsRequired =>
      'Пожалуйста, опишите пользу для здоровья';

  @override
  String get healthBenefitsDialogTitle => 'Польза для здоровья';

  @override
  String get addNewHealthBenefitHint => 'Добавить новую пользу для здоровья';

  @override
  String get selectedBenefitsLabel => 'Выбранные преимущества:';

  @override
  String get suggestedBenefitsLabel => 'Предлагаемые преимущества:';

  @override
  String get categoryLabel => 'Категория';

  @override
  String get languageLabel => 'Язык';

  @override
  String get selectLanguageHint => 'Выберите язык';

  @override
  String get recommendedForMoodsLabel => 'Рекомендуется для настроений';

  @override
  String get selectAtLeastOneMood => '(выберите минимум 1)';

  @override
  String get moodSelectionError => 'Пожалуйста, выберите хотя бы 1 настроение';

  @override
  String get healthBenefitsSelectionError =>
      'Пожалуйста, выберите не менее 2 преимуществ для здоровья';

  @override
  String get privacySettingsLabel => 'Настройки конфиденциальности';

  @override
  String get makeActivityPrivateLabel => 'Сделать активность приватной';

  @override
  String get privateActivityDescription =>
      'Только вы можете видеть эту активность';

  @override
  String get showYourNameLabel => 'Показать ваше имя';

  @override
  String get showNameDescription => 'Ваше имя будет отображаться как создатель';

  @override
  String get createActivityButton => 'Создать активность';

  @override
  String get activityCreatedSuccess => 'Активность успешно создана';

  @override
  String get loginRequiredError =>
      'Вы должны быть авторизованы, чтобы создавать активности';

  @override
  String createActivityError(String error) {
    return 'Не удалось создать активность: $error';
  }

  @override
  String get selectYourMood => 'Выберите ваше настроение';

  @override
  String get commonMoods => 'Распространенные настроения:';

  @override
  String get allMoodOptions => 'Все варианты настроения:';

  @override
  String get moreMoodsButton => 'Больше';

  @override
  String get lessMoodsButton => 'Меньше';

  @override
  String get noMoodSelected => 'Настроение не выбрано';

  @override
  String get selectMoodAbove => 'Выберите настроение выше';

  @override
  String get moodSelectionDescription =>
      'Мы предложим активности, которые помогут вам чувствовать себя лучше или оставаться позитивным';

  @override
  String get failedToLoadActivities => 'Не удалось загрузить активности';

  @override
  String get noActivitiesFound => 'Активности не найдены';

  @override
  String get noActivitiesForMoodDescription =>
      'Мы не смогли найти активности для вашего текущего настроения. Попробуйте выбрать другое настроение.';

  @override
  String get noUserActivities => 'Нет активностей, созданных вами';

  @override
  String get noUserActivitiesDescription =>
      'Вы еще не создали активностей для этого настроения. Создайте одну или отключите фильтр.';

  @override
  String get showAllActivities => 'Показать все активности';

  @override
  String suggestionsForMood(String mood, String emoji) {
    return 'Предложения для $mood $emoji';
  }

  @override
  String get startButton => 'Начать';

  @override
  String get stopButton => 'Остановить';

  @override
  String activityStarted(String activityTitle) {
    return 'Начато: $activityTitle';
  }

  @override
  String activityStartError(String error) {
    return 'Не удалось начать активность: $error';
  }

  @override
  String get activityCancelled => 'Активность отменена. Баллы не начислены.';

  @override
  String activityCancelError(String error) {
    return 'Ошибка при отмене активности: $error';
  }

  @override
  String get completeCurrentActivityFirst =>
      'Сначала завершите или отмените текущую активность';

  @override
  String get cancelButton => 'Отмена';

  @override
  String get applyButton => 'Применить';

  @override
  String get healthBenefitStress => 'Снижает стресс и тревожность';

  @override
  String get healthBenefitCardio => 'Улучшает сердечно-сосудистую систему';

  @override
  String get healthBenefitClarity => 'Повышает ясность ума';

  @override
  String get healthBenefitImmune => 'Укрепляет иммунную систему';

  @override
  String get healthBenefitSleep => 'Улучшает качество сна';

  @override
  String get healthBenefitStrength => 'Увеличивает физическую силу';

  @override
  String get healthBenefitMindful => 'Способствует осознанности';

  @override
  String get healthBenefitDigital => 'Снижает цифровую зависимость';

  @override
  String get healthBenefitFocus => 'Улучшает фокус и концентрацию';

  @override
  String get healthBenefitEmotional =>
      'Способствует эмоциональному благополучию';

  @override
  String get healthBenefitEnergy => 'Повышает уровень энергии';

  @override
  String get healthBenefitCreativity => 'Стимулирует творческие способности';

  @override
  String get healthBenefitPosture => 'Улучшает осанку';

  @override
  String get healthBenefitWeight => 'Помогает контролировать вес';

  @override
  String get foodMoodExplanationTitle => 'Знаете ли вы?';

  @override
  String get foodMoodExplanationText =>
      'Еда, которую вы потребляете, может существенно влиять на ваше настроение и психическое благополучие. Богатые питательными веществами продукты поддерживают функции мозга и помогают регулировать эмоции, в то время как обработанные продукты могут способствовать перепадам настроения.';

  @override
  String get noRecommendationsFound => 'Рекомендации не найдены';

  @override
  String get tryDifferentMood =>
      'Попробуйте выбрать другое настроение или изменить фильтры.';

  @override
  String get description => 'Описание';

  @override
  String get benefits => 'Преимущества';

  @override
  String get categories => 'Категории';

  @override
  String get hideDownvotedItems => 'Скрыть элементы с отрицательными оценками';

  @override
  String get hideDownvotedDescription =>
      'Скрыть элементы со слишком большим количеством отрицательных оценок';

  @override
  String get recommendedForMoods => 'Рекомендуется для настроений';

  @override
  String get nutritionalInfo => 'Пищевая ценность';

  @override
  String get ingredients => 'Ингредиенты';

  @override
  String get preparationSteps => 'Шаги приготовления';

  @override
  String get healthyFood => 'Здоровая пища';

  @override
  String get moodHappy => 'Счастливый';

  @override
  String get moodSad => 'Грустный';

  @override
  String get moodAngry => 'Злой';

  @override
  String get moodAnxious => 'Тревожный';

  @override
  String get moodTired => 'Уставший';

  @override
  String get moodStressed => 'В стрессе';

  @override
  String get moodDepressed => 'Депрессивный';

  @override
  String get moodExcited => 'Взволнованный';

  @override
  String get moodCalm => 'Спокойный';

  @override
  String get moodBored => 'Скучающий';

  @override
  String get foodCategoryProtein => 'Белковая пища';

  @override
  String get foodCategoryHealthyFats => 'Полезные жиры';

  @override
  String get foodCategoryComplexCarbs => 'Сложные углеводы';

  @override
  String get foodCategoryVitamins => 'Витаминная пища';

  @override
  String get foodCategoryMinerals => 'Минералы';

  @override
  String get foodCategoryOmega3 => 'Омега-3';

  @override
  String get foodCategoryProbiotics => 'Пробиотики';

  @override
  String get foodCategoryCalming => 'Успокаивающие';

  @override
  String get foodCategoryEnergyBoosting => 'Энергетические';

  @override
  String get foodCategoryAntioxidants => 'Антиоксиданты';

  @override
  String get activitiesTitle => 'Активности';

  @override
  String helloUser(String userName) {
    return 'Привет, $userName';
  }

  @override
  String goalTag(String goal) {
    return '$goal';
  }

  @override
  String get familyTag => 'Семья';

  @override
  String get childrenTag => 'Дети';

  @override
  String get motivationChildrenFocus =>
      'Вы сегодня отличный пример для своих детей! Ваша сосредоточенность показывает им важность быть здесь и сейчас.';

  @override
  String get motivationChildrenSleep =>
      'Лучший сон — больше присутствия для вашей семьи. Продолжайте соблюдать цифровые границы!';

  @override
  String get motivationChildrenGeneric =>
      'Ваши дети замечают, когда вы откладываете телефон. Каждый момент связи с ними важен!';

  @override
  String get motivationFocusHigh =>
      'Невероятная сосредоточенность сегодня! Ваш ум ясен, а продуктивность на высоте. Так держать!';

  @override
  String get motivationFocusLow =>
      'Каждая минута вдали от экрана помогает вашему мозгу лучше концентрироваться. У вас получится!';

  @override
  String get motivationSleep =>
      'Качество вашего сна улучшается с каждым цифровым перерывом. Ваше тело и разум благодарят вас!';

  @override
  String get motivationGeneric =>
      'Маленькие шаги ведут к большим переменам. С каждым цифровым перерывом вы меняетесь к лучшему. Продолжайте!';

  @override
  String get sortNotifications => 'Сортировка уведомлений';

  @override
  String get byDateNewestFirst => 'По дате (сначала новые)';

  @override
  String get unreadFirst => 'Сначала непрочитанные';

  @override
  String unreadCount(int count) {
    return 'Непрочитанных: $count';
  }

  @override
  String get today => 'Сегодня';

  @override
  String get yesterday => 'Вчера';

  @override
  String get delete => 'Удалить';

  @override
  String get markRead => 'Отметить прочитанным';

  @override
  String get markUnread => 'Отметить непрочитанным';

  @override
  String get couponDetails => 'Детали купона';

  @override
  String get aboutPartner => 'О партнере';

  @override
  String get address => 'Адрес';

  @override
  String get openInMaps => 'Открыть в картах';

  @override
  String get howToUse => 'Как использовать';

  @override
  String get termsAndConditions => 'Условия использования';

  @override
  String get validUntil => 'Действует до';

  @override
  String get redeemCoupon => 'Использовать купон';

  @override
  String redeemFor(int points) {
    return 'Обменять на $points очков';
  }

  @override
  String redeemConfirmation(String discount, String partner, int points) {
    return 'Вы хотите использовать купон $discount от $partner? Это будет стоить вам $points очков.';
  }

  @override
  String get cancel => 'Отмена';

  @override
  String get redeem => 'Использовать';

  @override
  String get openingInMaps => 'Открытие в Картах...';

  @override
  String get shareFeatureComingSoon => 'Функция обмена скоро появится!';

  @override
  String get activityDetails => 'Информация об активности';

  @override
  String get activityCompleted => 'Активность завершена!';

  @override
  String activityCompletedMessage(String title) {
    return 'Поздравляем! Вы завершили \"$title\" и заработали очки!';
  }

  @override
  String get ok => 'ОК';

  @override
  String get addToFavorites => 'Добавить в избранное';

  @override
  String get removedFromFavorites => 'Удалено из избранного';

  @override
  String get addedToFavorites => 'Добавлено в избранное';

  @override
  String get commentAdded => 'Комментарий добавлен';

  @override
  String get activityInProgress => 'Активность в процессе';

  @override
  String get remaining => 'осталось';

  @override
  String get elapsed => 'прошло';

  @override
  String get stayFocused =>
      'Сохраняйте фокус, чтобы заработать больше очков Detox!';

  @override
  String get activityPoints => 'Очки активности';

  @override
  String get points => 'очки';

  @override
  String get completeToEarn =>
      'Завершите эту активность, чтобы заработать очки!';

  @override
  String get aboutThisActivity => 'Об этой активности';

  @override
  String get healthBenefits => 'Польза для здоровья';

  @override
  String get noHealthBenefitsListed =>
      'Для этой активности не указана польза для здоровья.';

  @override
  String get recommendedFor => 'Рекомендуется для';

  @override
  String get noCommentsYet => 'Пока нет комментариев';

  @override
  String get beFirstToComment => 'Будьте первым, кто поделится своим опытом';

  @override
  String get shareYourExperience => 'Поделитесь своим опытом...';

  @override
  String get justNow => 'только что';

  @override
  String minutesAgo(int minutes) {
    return '$minutesм назад';
  }

  @override
  String hoursAgo(int hours) {
    return '$hoursч назад';
  }

  @override
  String daysAgo(int days) {
    return '$daysд назад';
  }

  @override
  String get activityComplete => 'Активность завершена!';

  @override
  String get comments => 'Комментарии';

  @override
  String createdBy(String name) {
    return 'Автор: $name';
  }

  @override
  String get detoxBenefits => 'Польза для здоровья';

  @override
  String get targetedMoods => 'Рекомендуется для';

  @override
  String started(String name) {
    return 'Начато: $name';
  }

  @override
  String failedToStartActivity(String error) {
    return 'Не удалось начать активность: $error';
  }

  @override
  String pointsValue(int value) {
    return '$value баллов';
  }

  @override
  String get upvotes => 'Положительные оценки';

  @override
  String get downvotes => 'Отрицательные оценки';

  @override
  String get nutritionInfo => 'Пищевая ценность';

  @override
  String get calories => 'Калории';

  @override
  String get protein => 'Белки';

  @override
  String get carbs => 'Углеводы';

  @override
  String get fats => 'Жиры';

  @override
  String get moodFilter => 'Filter by Mood';

  @override
  String get networkStatusOnline => 'Online';

  @override
  String get networkStatusOffline => 'Offline';

  @override
  String get networkStatusChecking => 'Checking network...';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get foodImageLoadErrorTitle => 'Image Not Available';

  @override
  String get foodImageLoadErrorMessage =>
      'Could not load image. Please try again later.';

  @override
  String get suggestionsFor => 'Suggestions for';

  @override
  String get tipsAndTricks => 'Tips & Tricks';

  @override
  String get activityTips =>
      'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.';

  @override
  String get loading => 'Loading...';

  @override
  String get foodRecommendations => 'Food Recommendations';

  @override
  String get timeToDisconnect => 'Time to Disconnect';

  @override
  String get activitiesMotivationalMessage =>
      'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.';

  @override
  String get earnPointsForActivities =>
      'Earn points for every activity completed';

  @override
  String get details => 'Детали';

  @override
  String get discussion => 'Обсуждение';

  @override
  String get editComment => 'Редактировать комментарий...';

  @override
  String get commentLikeNotification => 'Уведомления о лайках комментариев';

  @override
  String get commentLikeNotificationDescription =>
      'Получать уведомления, когда кто-то лайкает ваш комментарий';
}
