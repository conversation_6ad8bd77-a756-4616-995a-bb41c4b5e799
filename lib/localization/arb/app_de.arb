{"@@locale": "de", "appTitle": "DetoxMe", "pinScreenSetupTitle": "Admin-P<PERSON> e<PERSON>", "pinScreenEnterTitle": "Admin-PIN e<PERSON>ben", "pinScreenSetupSubtitle": "Richte eine 4-stellige PIN ein", "pinScreenEnterSubtitle": "Gib deine 4-stellige P<PERSON> ein", "pinScreenSettingPin": "PIN wird gesetzt...", "pinScreenVerifyingPin": "PIN wird geprüft...", "pinScreenSaving": "Speichern...", "pinScreenChecking": "Prüfen...", "pinScreenSetupFailed": "PIN-Einrichtung fehlgeschlagen", "pinScreenIncorrectPin": "Falsche PIN", "pinScreenSuccessSet": "PIN erfolgreich gesetzt!", "pinScreenSuccessVerified": "PIN bestätigt!", "pinScreenErrorSnackbarPrefix": "<PERSON><PERSON>: ", "homeScreenTitle": "Startseite", "homeScreenWelcome": "Willkommen zurück!", "profileScreenTitle": "<PERSON><PERSON>", "profileEditTitle": "<PERSON><PERSON>", "profileUsernameLabel": "<PERSON><PERSON><PERSON><PERSON>", "profileAvatarSelectionLabel": "Avatar wählen", "profileSaveButton": "<PERSON><PERSON>", "profileSaveSuccess": "Profil erfolgreich gespeichert!", "profileSaveFailure": "Profil konnte nicht gespeichert werden: ", "profileLoadFailure": "Fehler beim Laden des Profils: ", "profileYourProgress": "<PERSON><PERSON>", "@profileYourProgress": {"description": "Header for the progress section in the profile screen."}, "profilePoints": "Punkte", "@profilePoints": {"description": "Title for the points speedometer chart in the profile screen."}, "profileMyGoals": "<PERSON><PERSON>", "@profileMyGoals": {"description": "Title for the goals section in the profile screen."}, "profileFavoriteActivities": "Lieblingsaktivitäten", "@profileFavoriteActivities": {"description": "Title for the favorite activities section in the profile screen."}, "profileNoFavoriteActivities": "Noch keine Lieblingsaktivitäten", "@profileNoFavoriteActivities": {"description": "Message shown when there are no favorite activities."}, "profileAddFavoriteActivitiesHint": "Markiere Aktivitäten als Favoriten, um sie hier zu sehen", "@profileAddFavoriteActivitiesHint": {"description": "Hint message shown when there are no favorite activities."}, "profileExploreActivities": "Aktivitäten erkunden", "@profileExploreActivities": {"description": "Button text to explore activities when there are no favorites."}, "profileExploreMoreActivities": "Mehr Aktivitäten erkunden", "@profileExploreMoreActivities": {"description": "Button text to explore more activities at the bottom of the favorites section."}, "profileQuickActions": "Schnellaktionen", "@profileQuickActions": {"description": "Title for the quick actions section in the profile screen."}, "profileActionActivityHistory": "Aktivitätsverlauf", "@profileActionActivityHistory": {"description": "Quick action button for activity history."}, "profileActionUsedCoupons": "Verwendete Gutscheine", "@profileActionUsedCoupons": {"description": "Quick action button for used coupons."}, "profileActionRewardHistory": "Belohnungsverlau<PERSON>", "@profileActionRewardHistory": {"description": "Quick action button for reward history."}, "profileActionSettings": "Einstellungen", "@profileActionSettings": {"description": "Quick action button for settings."}, "profileViewAll": "Alle anzeigen", "@profileViewAll": {"description": "Button text to view all favorite activities."}, "homeScreenError": "<PERSON><PERSON> beim Laden der Aufgaben: {error}", "@homeScreenError": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn Aufgaben nicht geladen werden können.", "placeholders": {"error": {"type": "String"}}}, "homeScreenNoTasks": "Momentan keine Aufgaben verfügbar!", "homeScreenTaskDuration": "Dauer: {duration}", "@homeScreenTaskDuration": {"description": "Label für Aufgabendauer", "placeholders": {"duration": {"type": "String"}}}, "homeScreenStartButton": "Digital Detox beginnen", "homeScreenAnotherTaskButton": "Andere Herausforderung versuchen", "homeScreenProfileTooltip": "Profil", "rewardNotificationXp": "Du hast {xp} Detox-<PERSON>te verdient!", "@rewardNotificationXp": {"description": "Snackbar message shown when the user gains detox points.", "placeholders": {"xp": {"type": "int", "example": "25"}}}, "rewardNotificationLevelUp": "Neuer Meilenstein! Level {levelNumber} erreicht: {levelName}", "@rewardNotificationLevelUp": {"description": "Snackbar message shown when the user reaches a new milestone.", "placeholders": {"levelNumber": {"type": "int", "example": "5"}, "levelName": {"type": "String", "example": "Digitaler Minimalist"}}}, "rewardNotificationBadge": "Erfolg freigeschaltet: {badgeName}", "@rewardNotificationBadge": {"description": "Snackbar message shown when the user earns a new achievement.", "placeholders": {"badgeName": {"type": "String", "example": "Fokus-<PERSON><PERSON>"}}}, "rewardNotificationCard": "Belohnung freigeschaltet: {cardName} ({cardRarity})", "@rewardNotificationCard": {"description": "Snackbar message shown when the user unlocks a new reward.", "placeholders": {"cardName": {"type": "String", "example": "Achtsamkeits-Session"}, "cardRarity": {"type": "String", "example": "Premium"}}}, "rewardErrorSnackbar": "Fehler beim Verarbeiten der Belohnungen: {errorMessage}", "@rewardErrorSnackbar": {"description": "Snackbar message shown when there's an error loading or saving reward/player stats.", "placeholders": {"errorMessage": {"type": "String", "example": "Verbindung zur Datenbank fehlgeschlagen"}}}, "rewardDialogTitle": "Herausforderung abgeschlossen!", "@rewardDialogTitle": {"description": "Title for the dialog shown after a challenge is completed and rewards are given."}, "dialogButtonOK": "OK", "@dialogButtonOK": {"description": "Generic OK button text for dialogs."}, "leaderboardScreenTitle": "Community-Erfolge", "@leaderboardScreenTitle": {"description": "Title for the community achievements screen."}, "leaderboardLoadingError": "Community-Daten konnten nicht geladen werden:", "@leaderboardLoadingError": {"description": "Error message shown if the community data fails to load.", "placeholders": {"errorMessage": {"type": "String", "example": "<PERSON>ine <PERSON>bindung"}}}, "leaderboardEmpty": "Noch keine Community-Erfolge. Sei der Erste!", "@leaderboardEmpty": {"description": "Message shown when the community achievements list has no entries."}, "onboardingButtonNext": "<PERSON><PERSON>", "@onboardingButtonNext": {"description": "Button text to go to the next onboarding page."}, "authLoginTitle": "Anmelden", "@authLoginTitle": {"description": "Title for the Authentication screen when in Login mode."}, "authSignUpTitle": "Registrieren", "@authSignUpTitle": {"description": "Title for the Authentication screen when in Sign Up mode."}, "authEmailLabel": "E-Mail", "@authEmailLabel": {"description": "Label for the email input field on the Auth screen."}, "authPasswordLabel": "Passwort", "@authPasswordLabel": {"description": "Label for the password input field on the Auth screen."}, "authInvalidEmailError": "<PERSON>te gib eine gültige E-Mail ein", "@authInvalidEmailError": {"description": "Validation error message for the email field."}, "authPasswordTooShortError": "Passwort muss mindestens 6 <PERSON>eichen lang sein", "@authPasswordTooShortError": {"description": "Validation error message for the password field if it's too short."}, "authLoginButton": "Anmelden", "@authLoginButton": {"description": "Text for the main action button when in Login mode."}, "authSignUpButton": "Registrieren", "@authSignUpButton": {"description": "Text for the main action button when in Sign Up mode."}, "authToggleToSignUp": "Noch kein Konto? Registrieren", "@authToggleToSignUp": {"description": "Text for the button to switch from Login mode to Sign Up mode."}, "authToggleToLogin": "Schon ein Konto? Anmelden", "@authToggleToLogin": {"description": "Text für den Button zum Wechseln vom Registrierungs- in den Anmeldemodus."}, "adultOnboardingWelcomeTitle": "Willkommen bei DetoxMe!", "@adultOnboardingWelcomeTitle": {"description": "Titel für den ersten Bildschirm des Erwachsenen-Onboardings."}, "adultOnboardingWelcomeBody": "Gewinnen wir gemeinsam Fokus und Zeit zurück. Ein paar schnelle Fragen helfen uns, deine Reise zu einem gesünderen digitalen Leben zu personalisieren.", "@adultOnboardingWelcomeBody": {"description": "Text für den ersten Bildschirm des Erwachsenen-Onboardings."}, "adultOnboardingExplanationTitle": "Warum diese Fragen?", "@adultOnboardingExplanationTitle": {"description": "<PERSON><PERSON><PERSON>, der erkl<PERSON>, warum persönliche Fragen gestellt werden."}, "adultOnboardingExplanationBody": "Ein wenig über dich zu erfahren, hilft uns, Vorschläge und Herausforderungen anzupassen. Deine Privatsphäre ist uns wichtig; diese Informationen bleiben auf deinem Gerät und helfen, deine Erfahrung zu personalisieren.", "@adultOnboardingExplanationBody": {"description": "Text, der den Zweck der Fragen und den Datenschutz erklärt."}, "adultOnboardingAgeTitle": "Über Dich: Altersgruppe", "@adultOnboardingAgeTitle": {"description": "Titel für den Bildschirm zur Auswahl der Altersgruppe."}, "adultOnboardingAgeBody": "Deine ungefähre Altersgruppe hilft uns, relevante Ziele und Inhalte vorzuschlagen. <PERSON><PERSON><PERSON><PERSON> den Bereich, der am besten passt.", "@adultOnboardingAgeBody": {"description": "Text für den Bildschirm zur Auswahl der Altersgruppe."}, "adultOnboardingAgeOption1": "18-25", "@adultOnboardingAgeOption1": {}, "adultOnboardingAgeOption2": "26-35", "@adultOnboardingAgeOption2": {}, "adultOnboardingAgeOption3": "36-45", "@adultOnboardingAgeOption3": {}, "adultOnboardingAgeOption4": "46-55", "@adultOnboardingAgeOption4": {}, "adultOnboardingAgeOption5": "56+", "@adultOnboardingAgeOption5": {}, "adultOnboardingGenderTitle": "Über Dich: Geschlecht", "@adultOnboardingGenderTitle": {"description": "Titel für den Bildschirm zur Auswahl des Geschlechts."}, "adultOnboardingGenderBody": "Dies hilft uns, eine passende Sprache zu verwenden und demografische Muster zu verstehen (optional).", "@adultOnboardingGenderBody": {"description": "Text für den Bildschirm zur Auswahl des Geschlechts."}, "adultOnboardingGenderOptionMale": "<PERSON><PERSON><PERSON><PERSON>", "@adultOnboardingGenderOptionMale": {}, "adultOnboardingGenderOptionFemale": "<PERSON><PERSON><PERSON>", "@adultOnboardingGenderOptionFemale": {}, "adultOnboardingGenderOptionNonBinary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@adultOnboardingGenderOptionNonBinary": {}, "adultOnboardingGenderOptionPreferNotToSay": "<PERSON><PERSON>", "@adultOnboardingGenderOptionPreferNotToSay": {}, "adultOnboardingFamilyTitle": "Über Dich: Familienstatus", "@adultOnboardingFamilyTitle": {"description": "Titel für den Bildschirm zur Auswahl des Familienstatus."}, "adultOnboardingFamilyBody": "<PERSON><PERSON> Familienleben zu verstehen, kann he<PERSON>, Herausforderungen und Ziele anzupassen (optional).", "@adultOnboardingFamilyBody": {"description": "Text für den Bildschirm zur Auswahl des Familienstatus."}, "adultOnboardingFamilyOptionSingle": "Single", "@adultOnboardingFamilyOptionSingle": {}, "adultOnboardingFamilyOptionInRelationship": "In Beziehung / Verheiratet", "@adultOnboardingFamilyOptionInRelationship": {}, "adultOnboardingFamilyOptionHaveChildren": "<PERSON>er", "@adultOnboardingFamilyOptionHaveChildren": {}, "adultOnboardingFamilyOptionPreferNotToSay": "<PERSON><PERSON>", "@adultOnboardingFamilyOptionPreferNotToSay": {}, "adultOnboardingGoalsTitle": "<PERSON><PERSON>", "@adultOnboardingGoalsTitle": {"description": "Titel für den Bildschirm zur Auswahl der Hauptziele."}, "adultOnboardingGoalsBody": "Was erhoffst du dir von DetoxMe? Wähle deine Hauptmotivationen (bis zu 3).", "@adultOnboardingGoalsBody": {"description": "Text für den Bildschirm zur Auswahl der Hauptziele."}, "adultOnboardingGoalReduceScreenTime": "Gesamte Bildschirmzeit reduzieren", "@adultOnboardingGoalReduceScreenTime": {}, "adultOnboardingGoalImproveFocus": "Fokus & Konzentration verbessern", "@adultOnboardingGoalImproveFocus": {}, "adultOnboardingGoalBeMorePresent": "<PERSON><PERSON> Alltag präsenter sein", "@adultOnboardingGoalBeMorePresent": {}, "adultOnboardingGoalSpendMoreTimeFamily": "Mehr Qualitätszeit mit Familie/Freunden verbringen", "@adultOnboardingGoalSpendMoreTimeFamily": {}, "adultOnboardingGoalDigitalDetox": "Einen digitalen Detox durchführen", "@adultOnboardingGoalDigitalDetox": {}, "adultOnboardingGoalImproveSleep": "Schlafqualität verbessern", "@adultOnboardingGoalImproveSleep": {}, "adultOnboardingGoalOther": "<PERSON><PERSON>", "@adultOnboardingGoalOther": {}, "adultOnboardingCompletionTitle": "Fertig!", "@adultOnboardingCompletionTitle": {"description": "Titel für den abschließenden Onboarding-Bildschirm."}, "adultOnboardingCompletionBody": "Danke! Du bist bereit, deine Reise zu beginnen. Lass uns gemeinsam gesündere digitale Gewohnheiten entwickeln.", "@adultOnboardingCompletionBody": {"description": "Text für den abschließenden Onboarding-Bildschirm."}, "adultOnboardingButtonGetStarted": "Loslegen", "@adultOnboardingButtonGetStarted": {"description": "Button-Text auf dem letzten Onboarding-Bildschirm."}, "adultOnboardingButtonBack": "Zurück", "@adultOnboardingButtonBack": {"description": "Button-Text, um zum vorherigen Onboarding-Schritt zurückzukehren."}, "requiredErrorText": "Bitte triff eine Auswahl", "@requiredErrorText": {"description": "Generische Fehlermeldung, wenn eine Auswahl erforderlich ist."}, "otpVerifiedSnackbar": "Telefonnummer erfolgreich verifiziert!", "selectLifeSituation": "Wähle deine Lebenssituation", "lifeSituationIndividual": "Individuum", "lifeSituationIndividualDesc": "Fokus auf persönliches Wachstum und Wohlbefinden", "lifeSituationRelationship": "Beziehung", "lifeSituationRelationshipDesc": "Bildschirmzeit mit deinem Partner ausbalancieren", "lifeSituationFamily": "Familie", "lifeSituationFamilyDesc": "Gesunde digitale Gewohnheiten für deine Familie schaffen", "lifeSituationWork": "Arbeit", "lifeSituationWorkDesc": "Produktivität steigern und digitale Ablenkungen reduzieren", "lifeSituationTitle": "<PERSON>ine <PERSON>tuation", "lifeSituationDescription": "<PERSON><PERSON><PERSON>e die Option, die deine aktuelle Situation am besten beschreibt", "lifeSituationCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeSituationErrorRequired": "Bitte wähle eine Lebenssituation", "authTitle": "Willkommen zurück", "@authTitle": {"description": "Titel für den Anmeldebildschirm"}, "authSubtitle": "Melde dich an, um deine digitale Wellness-Reise fortzusetzen", "@authSubtitle": {"description": "Untertitel für den Anmeldebildschirm"}, "loginWithPhone": "Mit Telefon anmelden", "@loginWithPhone": {"description": "Text für Telefonanmelde-Button"}, "loginWithEmail": "Mit E-Mail anmelden", "@loginWithEmail": {"description": "Text für E-Mail-Anmelde-Button"}, "emailFieldLabel": "E-Mail", "@emailFieldLabel": {"description": "Beschriftung für E-Mail-Eingabefeld"}, "emailRequiredError": "E-Mail ist erforderlich", "@emailRequiredError": {"description": "<PERSON><PERSON><PERSON><PERSON>ng, wenn E-Mail leer ist"}, "emailInvalidError": "<PERSON>te gib eine gültige E-Mail-Adresse ein", "@emailInvalidError": {"description": "<PERSON><PERSON>meldung, wenn E-Mail ungültig ist"}, "passwordFieldLabel": "Passwort", "@passwordFieldLabel": {"description": "Beschriftung für Passwort-Eingabefeld"}, "passwordRequiredError": "Passwort ist erforderlich", "@passwordRequiredError": {"description": "<PERSON><PERSON><PERSON><PERSON>ng, wenn Passwort leer ist"}, "passwordLengthError": "Passwort muss mindestens 6 <PERSON>eichen lang sein", "@passwordLengthError": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn Passwort zu kurz ist"}, "noAccountQuestion": "Noch kein Konto?", "@noAccountQuestion": {"description": "Text, der fragt, ob der Benutzer kein Konto hat"}, "hasAccountQuestion": "Bereits ein Konto?", "@hasAccountQuestion": {"description": "Text, der fragt, ob der Benutzer bereits ein Konto hat"}, "signupAction": "Registrieren", "@signupAction": {"description": "Aktionstext für Registrierung"}, "loginAction": "Anmelden", "@loginAction": {"description": "Aktionstext für Anmeldung"}, "loginButton": "Anmelden", "@loginButton": {"description": "Text für Anmelde-Button"}, "signupButton": "<PERSON><PERSON> er<PERSON>", "@signupButton": {"description": "Text für Registrierungs-<PERSON>ton"}, "phoneVerificationHeader": "Telefon verifizieren", "@phoneVerificationHeader": {"description": "Überschrift für Telefonverifizierungs-Bildschirm"}, "phoneVerificationHint": "Du erhältst einen 6-stelligen Code zur Verifizierung deiner Telefonnummer", "@phoneVerificationHint": {"description": "Hinweistext auf dem Telefonverifizierungs-Bildschirm"}, "otpVerificationHeader": "Verifizierungscode eingeben", "@otpVerificationHeader": {"description": "Überschrift für OTP-Verifizierungs-Bildschirm"}, "otpVerificationResendCode": "Code erneut senden", "@otpVerificationResendCode": {"description": "Text für erneutes Senden des Verifizierungscodes"}, "otpVerificationResendIn": "<PERSON><PERSON><PERSON> senden in", "@otpVerificationResendIn": {"description": "Text für Countdown vor erneutem Senden"}, "otpVerificationVerifyButton": "Verifizieren & Fortfahren", "@otpVerificationVerifyButton": {"description": "Text für Verifizierungs-Button"}, "phoneInputTitle": "Telefonnummer eingeben", "@phoneInputTitle": {"description": "Titel für den Bildschirm zur Eingabe der Telefonnummer"}, "phoneInputInstructions": "Gib deine Telefonnummer ein, um fortzufahren", "@phoneInputInstructions": {"description": "Anweisungstext auf dem Bildschirm zur Eingabe der Telefonnummer"}, "phoneInputLabel": "Telefonnummer", "@phoneInputLabel": {"description": "Beschriftung für das Telefonnummer-Eingabefeld"}, "phoneInputHint": "123456789", "@phoneInputHint": {"description": "Platzhaltertext für das Telefonnummer-Eingabefeld"}, "phoneInputEmptyError": "Bitte gib eine Telefonnummer ein", "@phoneInputEmptyError": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn das Feld für die Telefonnummer leer ist"}, "phoneInputInvalidError": "Bitte gib eine gültige Telefonnummer ein", "@phoneInputInvalidError": {"description": "<PERSON><PERSON>mel<PERSON>ng, wenn die Telefonnummer ungültig ist"}, "phoneInputContinueButton": "<PERSON><PERSON>", "@phoneInputContinueButton": {"description": "Text für die Schaltfläche zum Fortfahren mit der Telefonüberprüfung"}, "otpVerificationTitle": "Telefonnummer verifizieren", "@otpVerificationTitle": {"description": "Titel für den OTP-Verifizierungsbildschirm"}, "otpVerificationCodeSent": "Code gesendet an {phoneNumber}", "@otpVerificationCodeSent": {"description": "Meldung mit der Telefonnummer, an die der Code gesendet wurde", "placeholders": {"phoneNumber": {"type": "String", "example": "*****1234"}}}, "otpVerificationInvalidCode": "<PERSON>te gib einen gültigen 6-stelligen Code ein", "@otpVerificationInvalidCode": {"description": "Fehlermeldung, wenn der OTP-Code ungültig ist"}, "otpVerificationButton": "Code verifizieren", "@otpVerificationButton": {"description": "Text für die Schaltfläche zur Überprüfung des OTP-Codes"}, "otpVerificationDidntReceive": "Keinen Code erhalten?", "@otpVerificationDidntReceive": {"description": "Text, der fragt, ob der Benutzer den Verifizierungscode nicht erhalten hat"}, "otpVerificationResend": "<PERSON><PERSON><PERSON> senden", "@otpVerificationResend": {"description": "Text für die Schaltfläche zum erneuten Senden des Verifizierungscodes"}, "otpVerificationResendTimer": "<PERSON><PERSON><PERSON> senden in {seconds} s", "@otpVerificationResendTimer": {"description": "Text, der den Countdown-Timer für das erneute Senden des Codes anzeigt", "placeholders": {"seconds": {"type": "int", "example": "30"}}}, "continueWithPhone": "Mit Telefon fortfahren", "@continueWithPhone": {"description": "Text für die Schaltfläche zum Fortfahren mit der Telefonauthentifizierung"}, "continueWithGoogle": "Mit Google fortfahren", "@continueWithGoogle": {"description": "Text für die Schaltfläche zum Fortfahren mit der Google-Authentifizierung"}, "continueWithApple": "<PERSON>t <PERSON> fortfahren", "@continueWithApple": {"description": "Text für die Schaltfläche zum Fortfahren mit der Apple-Authentifizierung"}, "orContinueWith": "Oder fortfahren mit", "@orContinueWith": {"description": "Text für den Trenner zwischen E-Mail/Passwort-Login und Optionen für soziale Anmeldungen"}, "rewardHistoryScreenTitle": "Belohnungsverlau<PERSON>", "rewardHistoryEmptyTitle": "<PERSON>ch keine <PERSON>n", "rewardHistoryEmptyMessage": "Du hast noch keine Belohnungen eingelöst. Schließe Herausforderungen ab, um Punkte zu sammeln und Belohnungen einzulösen.", "loadRedemptionsButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> laden", "goBackButton": "Zurück", "settingsTitle": "Einstellungen", "appTheme": "App-Theme", "themeSystem": "System", "themeLight": "Hell", "themeDark": "<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "notifications": "Benachrichtigungen", "enableNotifications": "Benachrichtigungen aktivieren", "notificationsDescription": "Erhalten Sie Benachrichtigungen über neue Aktivitäten, Gutscheine und Erinnerungen", "notifyAboutActivities": "Aktivitätsbenachrichtigungen", "activityMoodPreferences": "Stimmungspräferenzen", "otherNotifications": "Andere Benachrichtigungen", "newActivitiesPushNotif": "Neue Aktivitäten", "newCouponsPushNotif": "Neue Gutscheine", "activityRemindersPushNotif": "Aktivitätserinnerungen", "motivationalMessagesPushNotif": "Motivierende Nachrichten", "commentLikesPushNotif": "Kommentar-Likes", "comingSoon": "Demnächst verfügbar", "detoxRewards": "Detox-<PERSON>n", "newLabel": "NEU", "rewardsDescription": "Absolviere Aktivitäten, um Punkte zu sammeln und gegen reale Belohnungen einzulösen", "goodMorning": "<PERSON><PERSON><PERSON>", "goodAfternoon": "Guten Tag", "goodEvening": "<PERSON><PERSON><PERSON>", "goodNight": "<PERSON><PERSON>", "currentPointsLabel": "Aktuell", "targetPointsLabel": "<PERSON><PERSON>", "maxPointsLabel": "Maximum", "detoxActivities": "Detox-Aktivitäten", "defaultActivitiesDescription": "Probiere diese Aktivitäten, um dein digitales Wohlbefinden zu verbessern und Punkte zu sammeln", "activitiesForMood": "Aktivitäten für deine {mood} Stimmung", "beginnerActivities": "<PERSON><PERSON><PERSON><PERSON>", "intermediateActivities": "Fortgeschritten", "expertActivities": "Experte", "loadingDashboard": "Dein per<PERSON>s Dashboard wird geladen...", "noActivitiesAvailable": "Noch keine Aktivitäten für diese Stimmung verfügbar", "activityInProgressError": "Du hast bereits eine Aktivität in Bearbeitung", "startActivity": "Starten", "moodOMeterTitle": "Wie fühlst du dich heute?", "moodOMeterInstructions": "Ziehe den Zeiger über die Stimmungsskala oder tippe auf ein Emoji, um deine aktuelle Stimmung auszuwählen", "createActivityTitle": "Aktivität erstellen", "titleLabel": "Titel", "titleHint": "G<PERSON> einen Titel für deine Aktivität ein", "titleRequired": "<PERSON>te gib einen Titel ein", "descriptionLabel": "Beschreibung", "descriptionHint": "Beschreibe deine Aktivität", "descriptionRequired": "<PERSON>te gib eine Beschreibung ein", "durationLabel": "Dauer: {duration} Minuten", "durationMinutes": "{minutes} Min", "healthBenefitsLabel": "Gesundheitliche Vorteile", "healthBenefitsHint": "Beschreibe die gesundheitlichen Vorteile dieser Aktivität", "healthBenefitsRequired": "Bitte beschreibe die gesundheitlichen Vorteile", "healthBenefitsDialogTitle": "Gesundheitliche Vorteile", "addNewHealthBenefitHint": "Neuen gesundheitlichen Vorteil hinzufügen", "selectedBenefitsLabel": "Ausgewählte Vorteile:", "suggestedBenefitsLabel": "Vorgeschlagene Vorteile:", "categoryLabel": "<PERSON><PERSON><PERSON>", "languageLabel": "<PERSON><PERSON><PERSON>", "selectLanguageHint": "Sprache auswählen", "recommendedForMoodsLabel": "Empfohlen für Stimmungen", "selectAtLeastOneMood": "(wähle mindestens 1)", "moodSelectionError": "Bitte wähle mindestens 1 Stimmung aus", "healthBenefitsSelectionError": "Bitte wähle mindestens 2 gesundheitliche Vorteile aus", "privacySettingsLabel": "Datenschutzeinstellungen", "makeActivityPrivateLabel": "Aktivität privat machen", "privateActivityDescription": "Nur du kannst diese Aktivität sehen", "showYourNameLabel": "Deinen Namen anzeigen", "showNameDescription": "Dein Name wird als Ersteller angezeigt", "createActivityButton": "Aktivität erstellen", "activityCreatedSuccess": "Aktivität erfolgreich erstellt", "loginRequiredError": "Du musst angemeldet sein, um Aktivitäten zu erstellen", "createActivityError": "Fehler beim Erstellen der Aktivität: {error}", "selectYourMood": "Wähle deine Stimmung", "commonMoods": "Häufige Stimmungen:", "allMoodOptions": "Alle Stimmungsoptionen:", "moreMoodsButton": "<PERSON><PERSON>", "lessMoodsButton": "<PERSON><PERSON>", "noMoodSelected": "<PERSON><PERSON>immung ausgewählt", "selectMoodAbove": "<PERSON><PERSON>hle oben eine Stimmung aus", "moodSelectionDescription": "Wir schlagen Aktivitäten vor, die dir helfen, dich besser zu fühlen oder positiv zu bleiben", "failedToLoadActivities": "Fehler beim Laden der Aktivitäten", "somethingWentWrong": "Etwas ist schiefgelaufen", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "dailyProgress": "Täglicher Fortschritt", "failedToLoadProgressData": "Fehler beim Laden der Fortschrittsdaten", "connectionProblem": "Es gab ein Problem bei der Verbindung zum Server.", "failedToLoadProfile": "Fehler beim Laden des Profils", "noMotivationalContent": "<PERSON><PERSON> motivierenden Inhalte verfügbar", "dailyPoints": "tägliche Punkte", "noActivitiesFound": "Keine Aktivitäten gefunden", "noActivitiesForMoodDescription": "Wir konnten keine Aktivitäten für deine aktuelle Stimmung finden. Versuche, eine andere Stimmung auszuwählen.", "noUserActivities": "<PERSON><PERSON> von dir erstellten Aktivitäten", "noUserActivitiesDescription": "Du hast noch keine Aktivitäten für diese Stimmung erstellt. Erstelle eine oder deaktiviere den Filter.", "showAllActivities": "Alle Aktivitäten anzeigen", "suggestionsForMood": "Vorschläge für {mood} {emoji}", "startButton": "Starten", "stopButton": "Stoppen", "activityStarted": "Gestartet: {activityTitle}", "activityStartError": "Fehler beim Starten der Aktivität: {error}", "activityCancelled": "Aktivität abgebrochen. Keine Punkte vergeben.", "activityCancelError": "Fehler beim Abbrechen der Aktivität: {error}", "completeCurrentActivityFirst": "<PERSON><PERSON><PERSON><PERSON> zuerst die aktuelle Aktivität ab oder breche sie ab", "cancelButton": "Abbrechen", "applyButton": "<PERSON><PERSON><PERSON>", "healthBenefitStress": "Reduziert Stress und Angst", "healthBenefitCardio": "Verbessert die Herzgesundheit", "healthBenefitClarity": "Steigert die geistige Klarheit", "healthBenefitImmune": "Stärkt das Immunsystem", "healthBenefitSleep": "Verbessert die Schlafqualität", "healthBenefitStrength": "Steigert die körperliche Kraft", "healthBenefitMindful": "Fördert die Achtsamkeit", "healthBenefitDigital": "Reduziert digitale Abhängigkeit", "healthBenefitFocus": "Verbessert Fokus und Konzentration", "healthBenefitEmotional": "<PERSON><PERSON><PERSON><PERSON> das emotionale Wohlbefinden", "healthBenefitEnergy": "Steigert das Energieniveau", "healthBenefitCreativity": "Fö<PERSON>rt die Kreativität", "healthBenefitPosture": "Verbessert die Körperhaltung", "healthBenefitWeight": "Unterstützt das Gewichtsmanagement", "foodMoodExplanationTitle": "Wusstest du?", "foodMoodExplanationText": "Die Nahrung, die du isst, kann deine Stimmung und dein psychisches Wohlbefinden erheblich beeinflussen. Nährstoffreiche Lebensmittel unterstützen die Gehirnfunktion und helfen, Emotionen zu regulieren, während verarbeitete Lebensmittel zu Stimmungsschwankungen beitragen können.", "noRecommendationsFound": "<PERSON><PERSON> Empfehlungen gefunden", "tryDifferentMood": "<PERSON><PERSON><PERSON>, eine andere Stimmung auszuwählen oder deine Filter anzupassen.", "description": "Beschreibung", "benefits": "<PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "foodCategories": "Lebensmittelkategorien", "hideDownvotedItems": "Stark herabgestufte Artikel ausblenden", "hideDownvotedDescription": "Artikel mit zu vielen negativen Bewertungen ausblenden", "clearAll": "Alles löschen", "recommendedForMoods": "Empfohlen für Stimmungen", "nutritionInfo": "Nährwertinformationen", "calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Kohlenhydrate", "fats": "<PERSON><PERSON>", "upvotes": "Positive Bewertungen", "downvotes": "Negative Bewertungen", "healthyFood": "Gesundes Essen", "moodHappy": "<PERSON><PERSON><PERSON><PERSON>", "@moodHappy": {"description": "Mood label: Happy"}, "moodSad": "<PERSON><PERSON><PERSON><PERSON>", "@moodSad": {"description": "Mood label: Sad"}, "moodAngry": "<PERSON><PERSON><PERSON>", "@moodAngry": {"description": "Mood label: Angry"}, "moodAnxious": "<PERSON><PERSON><PERSON><PERSON>", "@moodAnxious": {"description": "Mood label: Anxious"}, "moodTired": "<PERSON><PERSON><PERSON>", "@moodTired": {"description": "Mood label: Tired"}, "moodStressed": "<PERSON><PERSON><PERSON><PERSON>", "@moodStressed": {"description": "Mood label: Stressed"}, "moodDepressed": "Depressiv", "@moodDepressed": {"description": "Mood label: Depressed"}, "moodExcited": "Aufger<PERSON><PERSON>", "@moodExcited": {"description": "Mood label: Excited"}, "moodCalm": "<PERSON><PERSON><PERSON>", "@moodCalm": {"description": "Mood label: Calm"}, "moodBored": "Gelangweilt", "@moodBored": {"description": "Mood label: Bo<PERSON>"}, "foodCategoryProtein": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@foodCategoryProtein": {"description": "Lebensmittelkategorie: Eiweißreiche Lebensmittel"}, "foodCategoryHealthyFats": "Gesunde Fette", "@foodCategoryHealthyFats": {"description": "Lebensmittelkategorie: Gesunde Fette"}, "foodCategoryComplexCarbs": "Komplexe Kohlenhydrate", "@foodCategoryComplexCarbs": {"description": "Lebensmittelkategorie: Komplexe Kohlenhydrate"}, "foodCategoryVitamins": "<PERSON><PERSON><PERSON><PERSON>", "@foodCategoryVitamins": {"description": "Lebensmittelkategorie: Vitaminre<PERSON>ittel"}, "foodCategoryMinerals": "<PERSON><PERSON><PERSON>", "@foodCategoryMinerals": {"description": "Lebensmittelkategorie: Mineralien"}, "foodCategoryOmega3": "Omega-3", "@foodCategoryOmega3": {"description": "Lebensmittelkategorie: <PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, "foodCategoryProbiotics": "Probiotika", "@foodCategoryProbiotics": {"description": "Lebensmittelkategorie: Probiotika"}, "foodCategoryCalming": "Beruhigend", "@foodCategoryCalming": {"description": "Lebensmittelkategorie: Beruhigende Lebensmittel"}, "foodCategoryEnergyBoosting": "Energie spendend", "@foodCategoryEnergyBoosting": {"description": "Lebensmittelkategorie: Energie spendende Lebensmittel"}, "foodCategoryAntioxidants": "Antioxidantien", "@foodCategoryAntioxidants": {"description": "Lebensmittelkategorie: Antioxidantien"}, "activitiesTitle": "Aktivitäten", "@activitiesTitle": {"description": "Titel für den Aktivitäten-Tab und Bildschirm."}, "helloUser": "<PERSON><PERSON>, {userName}", "@helloUser": {"description": "Begrüßung für den Benutzer, Parameter: userName", "placeholders": {"userName": {"type": "String"}}}, "goalTag": "{goal}", "@goalTag": {"description": "Ziel-Tag-Label, Parameter: goal", "placeholders": {"goal": {"type": "String"}}}, "familyTag": "Familie", "@familyTag": {"description": "Tag für Familie"}, "childrenTag": "Kinder", "@childrenTag": {"description": "Tag für Kinder"}, "motivationChildrenFocus": "Du bist heute ein tolles Vorbild für deine Kinder! Deine Konzentration zeigt ihnen, wie wichtig es ist, prä<PERSON> zu sein.", "@motivationChildrenFocus": {"description": "Motivationsnachricht für Kinder und Fokus-Ziel."}, "motivationChildrenSleep": "<PERSON><PERSON><PERSON><PERSON>, für deine Familie präsenter zu sein. Bleib dran an deinen digitalen Grenzen!", "@motivationChildrenSleep": {"description": "Motivationsnachricht für Kinder und Schlaf-Ziel."}, "motivationChildrenGeneric": "<PERSON><PERSON>, wenn du das Handy weglegst. Jeder Moment der Verbindung zählt!", "@motivationChildrenGeneric": {"description": "Motivationsnachricht für Kinder, allgemein."}, "motivationFocusHigh": "Unglaubliche Konzentration heute! Dein Geist ist klar und deine Produktivität steigt. <PERSON><PERSON> so!", "@motivationFocusHigh": {"description": "Motivationsnachricht für hohe Konzentration."}, "motivationFocusLow": "Jeder Moment fern vom Bildschirm hilft deinem Gehirn, sich besser zu fokussieren. Du schaffst das!", "@motivationFocusLow": {"description": "Motivationsnachricht für niedrige Konzentration."}, "motivationSleep": "<PERSON>ine Schlafqualität verbessert sich mit jeder digitalen Pause. K<PERSON>rper und Geist danken es dir!", "@motivationSleep": {"description": "Motivationsnachricht für Schlaf-Ziel."}, "motivationGeneric": "Kleine Schritte führen zu großen Veränderungen. Mit jeder digitalen Pause veränderst du dich positiv. <PERSON><PERSON> so!", "@motivationGeneric": {"description": "Allgemeine Motivationsnachricht."}, "sort": "<PERSON><PERSON><PERSON><PERSON>", "sortNotifications": "Benachrichtigungen sortieren", "byDateNewestFirst": "<PERSON><PERSON> (neueste <PERSON>)", "unreadFirst": "<PERSON><PERSON><PERSON><PERSON>", "unreadCount": "{count} un<PERSON><PERSON>n", "@unreadCount": {"description": "Anzahl der ungelesenen Benachrichtigungen", "placeholders": {"count": {"type": "int"}}}, "today": "<PERSON><PERSON>", "yesterday": "Gestern", "notificationDeleted": "Benachrichtigung gelöscht", "notificationMarkedRead": "<PERSON>s gelesen markiert", "notificationMarkedUnread": "<PERSON><PERSON> ungelesen mark<PERSON>t", "delete": "Löschen", "markRead": "Als gelesen markieren", "markUnread": "<PERSON>s ungelesen markieren", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noNotifications": "<PERSON><PERSON>", "noNotificationsDescription": "Du hast noch keine Benachrichtigungen. Wir werden dich über neue Aktivitäten, Belohnungen und wichtige Updates informieren.", "couponDetails": "Gutschein-Details", "aboutPartner": "Über den Partner", "address": "<PERSON><PERSON><PERSON>", "openInMaps": "In Karten öffnen", "howToUse": "Verwendung", "termsAndConditions": "Allgemeine Geschäftsbedingungen", "validUntil": "Gültig bis", "redeemCoupon": "Gutschein e<PERSON><PERSON><PERSON>sen", "redeemFor": "<PERSON><PERSON>r {points} <PERSON><PERSON> e<PERSON>lösen", "@redeemFor": {"description": "Button-Text zum Einlösen eines Gutscheins", "placeholders": {"points": {"type": "int"}}}, "redeemConfirmation": "<PERSON><PERSON>cht<PERSON> Si<PERSON> einen {discount} <PERSON><PERSON><PERSON><PERSON> <PERSON> {partner} e<PERSON><PERSON><PERSON><PERSON>? Dies kostet Si<PERSON> {points} Punkte.", "@redeemConfirmation": {"description": "Bestätigungsnachricht zum Einlösen eines Gutscheins", "placeholders": {"discount": {"type": "String"}, "partner": {"type": "String"}, "points": {"type": "int"}}}, "cancel": "Abbrechen", "redeem": "<PERSON><PERSON><PERSON><PERSON>", "openingInMaps": "Öffne in Karten...", "shareFeatureComingSoon": "Teilen-<PERSON><PERSON> kommt bald!", "activityDetails": "Aktivitätsdetails", "activityCompleted": "Aktivität abgeschlossen!", "activityCompletedMessage": "Glückwunsch! Du hast \"{title}\" abgeschlossen und Punkte verdient!", "@activityCompletedMessage": {"description": "<PERSON><PERSON><PERSON><PERSON>, die angezeigt wird, wenn eine Aktivität abgeschlossen wurde", "placeholders": {"title": {"type": "String"}}}, "ok": "OK", "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "removedFromFavorites": "Aus Favoriten entfernt", "addedToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hi<PERSON>", "commentAdded": "<PERSON><PERSON><PERSON><PERSON>", "activityInProgress": "Aktivität läuft", "remaining": "verbleibend", "elapsed": "vergangen", "stayFocused": "<PERSON><PERSON><PERSON> fokussiert, um mehr Detox-Punk<PERSON> zu verdi<PERSON>n!", "activityPoints": "Aktivitätspunkte", "points": "Punkte", "completeToEarn": "Schl<PERSON>ße diese Aktivität ab, um Punkte zu verdienen!", "aboutThisActivity": "Über diese Aktivität", "healthBenefits": "Gesundheitliche Vorteile", "noHealthBenefitsListed": "<PERSON>ine gesundheitlichen Vorteile für diese Aktivität aufgeführt.", "recommendedFor": "Em<PERSON><PERSON><PERSON>", "noCommentsYet": "Noch keine Kommentare", "beFirstToComment": "Se<PERSON> der Erste, der seine Erfahrung teilt", "shareYourExperience": "Teile deine E<PERSON>rung...", "justNow": "gerade eben", "minutesAgo": "vor {minutes}m", "@minutesAgo": {"description": "Text für 'vor X Minuten' bei Kommentar-Zeitstempeln", "placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "vor {hours}h", "@hoursAgo": {"description": "Text für 'vor X Stunden' bei Kommentar-Zeitstempeln", "placeholders": {"hours": {"type": "int"}}}, "daysAgo": "vor {days}d", "@daysAgo": {"description": "Text für 'vor X Tagen' bei Kommentar-Zeitstempeln", "placeholders": {"days": {"type": "int"}}}, "activityComplete": "Aktivität abgeschlossen!", "comments": "Kommentare", "createdBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>: {name}", "detoxBenefits": "Gesundheitliche Vorteile", "targetedMoods": "Em<PERSON><PERSON><PERSON>", "started": "Gestartet: {name}", "failedToStartActivity": "Fehler beim Starten der Aktivität: {error}", "pointsValue": "{value} Punkte", "nutritionalInfo": "Nährwertinformationen", "ingredients": "<PERSON><PERSON><PERSON>", "preparationSteps": "Zubereitungsschritte", "foodBenefits": "Lebensmittelvorteile", "termsConditions": "Geschäftsbedingungen", "details": "Details", "discussion": "Diskussion", "editComment": "Bearbeite deinen Kommentar...", "commentLikeNotification": "Kommentar-Like-Benachrichtigungen", "commentLikeNotificationDescription": "Benach<PERSON><PERSON><PERSON><PERSON> erhalt<PERSON>, wenn jemand deinen <PERSON> liked"}