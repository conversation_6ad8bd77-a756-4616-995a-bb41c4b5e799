// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'DetoxMe';

  @override
  String get pinScreenSetupTitle => 'Set up Admin PIN';

  @override
  String get pinScreenEnterTitle => 'Enter Admin PIN';

  @override
  String get pinScreenSetupSubtitle => 'Set up a 4-digit PIN';

  @override
  String get pinScreenEnterSubtitle => 'Enter your 4-digit PIN';

  @override
  String get pinScreenSettingPin => 'Setting PIN...';

  @override
  String get pinScreenVerifyingPin => 'Verifying PIN...';

  @override
  String get pinScreenSaving => 'Saving...';

  @override
  String get pinScreenChecking => 'Checking...';

  @override
  String get pinScreenSetupFailed => 'PIN Setup Failed';

  @override
  String get pinScreenIncorrectPin => 'Incorrect PIN';

  @override
  String get pinScreenSuccessSet => 'PIN Set Successfully!';

  @override
  String get pinScreenSuccessVerified => 'PIN Verified!';

  @override
  String get pinScreenErrorSnackbarPrefix => 'Error: ';

  @override
  String get homeScreenTitle => 'Home';

  @override
  String get homeScreenWelcome => 'Welcome back!';

  @override
  String get homeScreenSubtitle => 'Take a moment for yourself today';

  @override
  String get profileScreenTitle => 'Profile';

  @override
  String get profileEditTitle => 'Edit Profile';

  @override
  String get profileUsernameLabel => 'Username';

  @override
  String get profileAvatarSelectionLabel => 'Choose Avatar';

  @override
  String get profileSaveButton => 'Save Profile';

  @override
  String get profileSaveSuccess => 'Profile saved successfully!';

  @override
  String get profileSaveFailure => 'Failed to save profile: ';

  @override
  String get profileLoadFailure => 'Failed to load profile: ';

  @override
  String get profileYourProgress => 'Your Progress';

  @override
  String get profilePoints => 'Points';

  @override
  String get profileMyGoals => 'My Goals';

  @override
  String get profileFavoriteActivities => 'Favorite Activities';

  @override
  String get profileNoFavoriteActivities => 'No favorite activities yet';

  @override
  String get profileAddFavoriteActivitiesHint =>
      'Mark activities as favorites to see them here';

  @override
  String get profileExploreActivities => 'Explore Activities';

  @override
  String get profileExploreMoreActivities => 'Explore More Activities';

  @override
  String get profileQuickActions => 'Quick Actions';

  @override
  String get profileActionActivityHistory => 'Activity History';

  @override
  String get profileActionUsedCoupons => 'Used Coupons';

  @override
  String get profileActionRewardHistory => 'Reward History';

  @override
  String get profileActionSettings => 'Settings';

  @override
  String get profileViewAll => 'View All';

  @override
  String homeScreenError(String error) {
    return 'Error loading tasks: $error';
  }

  @override
  String get homeScreenNoTasks => 'No tasks available right now!';

  @override
  String homeScreenTaskDuration(String duration) {
    return 'Duration: $duration';
  }

  @override
  String get homeScreenStartButton => 'Begin Digital Detox';

  @override
  String get homeScreenAnotherTaskButton => 'Try a different challenge';

  @override
  String get detoxPointsLabel => 'Detox Points';

  @override
  String greetingMessage(String username) {
    return 'Good Day, $username!';
  }

  @override
  String get mindfulActivitySuggestions => 'Mindful Activity Suggestions';

  @override
  String get homeScreenProfileTooltip => 'Profile';

  @override
  String rewardNotificationXp(int xp) {
    return 'You earned $xp Detox Points!';
  }

  @override
  String rewardNotificationLevelUp(int levelNumber, String levelName) {
    return 'New Milestone! Reached Level $levelNumber: $levelName';
  }

  @override
  String rewardNotificationBadge(String badgeName) {
    return 'Achievement Unlocked: $badgeName';
  }

  @override
  String rewardNotificationCard(String cardName, String cardRarity) {
    return 'Reward Unlocked: $cardName ($cardRarity)';
  }

  @override
  String rewardErrorSnackbar(String errorMessage) {
    return 'Error processing rewards: $errorMessage';
  }

  @override
  String get rewardDialogTitle => 'Challenge Complete!';

  @override
  String get dialogButtonOK => 'OK';

  @override
  String get leaderboardScreenTitle => 'Leaderboard';

  @override
  String leaderboardLoadingError(String message) {
    return 'Failed to load leaderboard: $message';
  }

  @override
  String get leaderboardEmpty => 'No entries in the leaderboard yet';

  @override
  String get unknownState => 'Unknown state';

  @override
  String get onboardingButtonNext => 'Next';

  @override
  String get authLoginTitle => 'Login';

  @override
  String get authSignUpTitle => 'Sign Up';

  @override
  String get authEmailLabel => 'Email';

  @override
  String get authPasswordLabel => 'Password';

  @override
  String get authInvalidEmailError => 'Please enter a valid email';

  @override
  String get authPasswordTooShortError =>
      'Password must be at least 6 characters';

  @override
  String get authLoginButton => 'Login';

  @override
  String get authSignUpButton => 'Sign Up';

  @override
  String get authToggleToSignUp => 'Need an account? Sign Up';

  @override
  String get authToggleToLogin => 'Have an account? Login';

  @override
  String get adultOnboardingWelcomeTitle => 'Welcome to DetoxMe!';

  @override
  String get adultOnboardingWelcomeBody =>
      'Let\'s reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.';

  @override
  String get adultOnboardingExplanationTitle => 'Why These Questions?';

  @override
  String get adultOnboardingExplanationBody =>
      'Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.';

  @override
  String get adultOnboardingAgeTitle => 'About You: Age Group';

  @override
  String get adultOnboardingAgeBody =>
      'Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.';

  @override
  String get adultOnboardingAgeOption1 => '18-25';

  @override
  String get adultOnboardingAgeOption2 => '26-35';

  @override
  String get adultOnboardingAgeOption3 => '36-45';

  @override
  String get adultOnboardingAgeOption4 => '46-55';

  @override
  String get adultOnboardingAgeOption5 => '56+';

  @override
  String get adultOnboardingGenderTitle => 'About You: Gender';

  @override
  String get adultOnboardingGenderBody =>
      'This helps us use appropriate language and understand demographic patterns (optional).';

  @override
  String get adultOnboardingGenderOptionMale => 'Male';

  @override
  String get adultOnboardingGenderOptionFemale => 'Female';

  @override
  String get adultOnboardingGenderOptionNonBinary => 'Non-binary';

  @override
  String get adultOnboardingGenderOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingFamilyTitle => 'About You: Family Status';

  @override
  String get adultOnboardingFamilyBody =>
      'Understanding your home life can help tailor challenges and goals (optional).';

  @override
  String get adultOnboardingFamilyOptionSingle => 'Single';

  @override
  String get adultOnboardingFamilyOptionInRelationship =>
      'In a relationship / Married';

  @override
  String get adultOnboardingFamilyOptionHaveChildren => 'Have children';

  @override
  String get adultOnboardingFamilyOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingGoalsTitle => 'Your Main Goals';

  @override
  String get adultOnboardingGoalsBody =>
      'What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).';

  @override
  String get adultOnboardingGoalReduceScreenTime =>
      'Reduce overall screen time';

  @override
  String get adultOnboardingGoalImproveFocus => 'Improve focus & concentration';

  @override
  String get adultOnboardingGoalBeMorePresent =>
      'Be more present in daily life';

  @override
  String get adultOnboardingGoalSpendMoreTimeFamily =>
      'Spend more quality time with family/friends';

  @override
  String get adultOnboardingGoalDigitalDetox => 'Perform a digital detox';

  @override
  String get adultOnboardingGoalImproveSleep => 'Improve sleep quality';

  @override
  String get adultOnboardingGoalOther => 'Other';

  @override
  String get adultOnboardingCompletionTitle => 'All Set!';

  @override
  String get adultOnboardingCompletionBody =>
      'Thank you! You\'re ready to start your journey. Let\'s build healthier digital habits together.';

  @override
  String get adultOnboardingButtonGetStarted => 'Get Started';

  @override
  String get adultOnboardingButtonBack => 'Back';

  @override
  String get requiredErrorText => 'Please make a selection';

  @override
  String get phoneInputTitle => 'Enter Phone Number';

  @override
  String get phoneInputInstructions => 'Enter your phone number to continue';

  @override
  String get phoneInputLabel => 'Phone Number';

  @override
  String get phoneInputHint => '123456789';

  @override
  String get phoneInputEmptyError => 'Please enter a phone number';

  @override
  String get phoneInputInvalidError => 'Please enter a valid phone number';

  @override
  String get phoneInputContinueButton => 'Continue';

  @override
  String get otpVerificationTitle => 'Verify Phone Number';

  @override
  String otpVerificationCodeSent(String phoneNumber) {
    return 'Code sent to $phoneNumber';
  }

  @override
  String get otpVerificationInvalidCode => 'Please enter a valid 6-digit code';

  @override
  String get otpVerificationButton => 'Verify Code';

  @override
  String get otpVerificationDidntReceive => 'Didn\'t receive the code?';

  @override
  String get otpVerificationResend => 'Resend';

  @override
  String otpVerificationResendTimer(int seconds) {
    return 'Resend in $seconds s';
  }

  @override
  String get otpVerifiedSnackbar => 'Phone number verified successfully!';

  @override
  String get continueWithPhone => 'Continue with Phone';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get onboardingTellUsAboutYourself => 'Tell us about yourself';

  @override
  String get onboardingPersonalizeExperience =>
      'We\'ll use this information to personalize your detox experience';

  @override
  String get username => 'Username';

  @override
  String get enterYourUsername => 'Enter your preferred username';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectYourBirthYear => 'Select your birth year';

  @override
  String get birthYearRequired => 'Birth year is required';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get nonBinary => 'Non-binary';

  @override
  String get preferNotToSay => 'Prefer not to say';

  @override
  String get next => 'Next';

  @override
  String get authTitle => 'Welcome Back';

  @override
  String get authSubtitle =>
      'Sign in to continue your digital wellness journey';

  @override
  String get loginWithPhone => 'Login with Phone';

  @override
  String get loginWithEmail => 'Login with Email';

  @override
  String get emailFieldLabel => 'Email';

  @override
  String get emailRequiredError => 'Email is required';

  @override
  String get emailInvalidError => 'Please enter a valid email';

  @override
  String get passwordFieldLabel => 'Password';

  @override
  String get passwordRequiredError => 'Password is required';

  @override
  String get passwordLengthError => 'Password must be at least 6 characters';

  @override
  String get noAccountQuestion => 'Don\'t have an account?';

  @override
  String get hasAccountQuestion => 'Already have an account?';

  @override
  String get signupAction => 'Sign Up';

  @override
  String get loginAction => 'Login';

  @override
  String get loginButton => 'Login';

  @override
  String get signupButton => 'Create Account';

  @override
  String get phoneVerificationHeader => 'Verify Your Phone';

  @override
  String get phoneVerificationHint =>
      'You\'ll receive a 6-digit code to verify your phone number';

  @override
  String get otpVerificationHeader => 'Enter Verification Code';

  @override
  String get otpVerificationResendCode => 'Resend Code';

  @override
  String get otpVerificationResendIn => 'Resend in';

  @override
  String get otpVerificationVerifyButton => 'Verify & Continue';

  @override
  String get selectLifeSituation => 'Select your life situation';

  @override
  String get lifeSituationIndividual => 'Individual';

  @override
  String get lifeSituationIndividualDesc =>
      'Focus on personal growth and wellbeing';

  @override
  String get lifeSituationRelationship => 'Relationship';

  @override
  String get lifeSituationRelationshipDesc =>
      'Balance screen time with your partner';

  @override
  String get lifeSituationFamily => 'Family';

  @override
  String get lifeSituationFamilyDesc =>
      'Create healthy digital habits for your family';

  @override
  String get lifeSituationWork => 'Work';

  @override
  String get lifeSituationWorkDesc =>
      'Improve productivity and reduce digital distractions';

  @override
  String get lifeSituationErrorRequired => 'Please select a life situation';

  @override
  String get lifeSituationTitle => 'Your Life Situation';

  @override
  String get lifeSituationDescription =>
      'Select the option that best describes your current situation';

  @override
  String get lifeSituationCustom => 'Custom';

  @override
  String get rewardHistoryScreenTitle => 'Reward History';

  @override
  String get rewardHistoryEmptyTitle => 'No Rewards Yet';

  @override
  String get rewardHistoryEmptyMessage =>
      'You haven\'t redeemed any rewards yet. Complete challenges to earn points and redeem rewards.';

  @override
  String get loadRedemptionsButton => 'Load Rewards';

  @override
  String get goBackButton => 'Go Back';

  @override
  String activeActivityStatus(String activityTitle) {
    return '$activityTitle (Active)';
  }

  @override
  String pausedActivityStatus(String activityTitle) {
    return '$activityTitle (Paused)';
  }

  @override
  String get activeActivityMotivation =>
      'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.';

  @override
  String activeActivityHealthBenefit(String benefit) {
    return 'This activity is good for your health: $benefit';
  }

  @override
  String get activeActivityStopConfirmTitle => 'Are you sure?';

  @override
  String get activeActivityStopConfirmMessage =>
      'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.';

  @override
  String get activeActivityKeepGoingButton => 'Keep Going';

  @override
  String get activeActivityStopButton => 'Stop Activity';

  @override
  String get activeActivityPauseButton => 'Pause';

  @override
  String get activeActivityResumeButton => 'Resume';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get appTheme => 'App Theme';

  @override
  String get themeSystem => 'System';

  @override
  String get themeLight => 'Light';

  @override
  String get themeDark => 'Dark';

  @override
  String get language => 'Language';

  @override
  String get notifications => 'Notifications';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get notificationsDescription =>
      'Receive notifications about new activities, coupons, and reminders';

  @override
  String get notifyAboutActivities => 'Activity Notifications';

  @override
  String get activityMoodPreferences => 'Mood Preferences';

  @override
  String get otherNotifications => 'Other Notifications';

  @override
  String get newActivitiesPushNotif => 'New Activities';

  @override
  String get newCouponsPushNotif => 'New Coupons';

  @override
  String get activityRemindersPushNotif => 'Activity Reminders';

  @override
  String get motivationalMessagesPushNotif => 'Motivational Messages';

  @override
  String get commentLikesPushNotif => 'Comment Likes';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get noNotificationsDescription =>
      'You don\'t have any notifications yet. We\'ll notify you about new activities, rewards, and important updates.';

  @override
  String get notificationDeleted => 'Notification deleted';

  @override
  String get notificationMarkedRead => 'Marked as read';

  @override
  String get notificationMarkedUnread => 'Marked as unread';

  @override
  String get undo => 'Undo';

  @override
  String get activitiesScreenTitle => 'Mood & Activities';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get activities => 'Activities';

  @override
  String get allActivities => 'All Activities';

  @override
  String get myActivities => 'My Activities';

  @override
  String get newest => 'Newest';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get mostUpvoted => 'Most Upvoted';

  @override
  String get highestRated => 'Highest Rated';

  @override
  String get languages => 'Languages';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String errorLoadingData(String error) {
    return 'Error loading data: $error';
  }

  @override
  String errorRefreshingData(String error) {
    return 'Error refreshing data: $error';
  }

  @override
  String get orWithEmail => 'or with email';

  @override
  String get takeDeepBreath => 'Take a deep breath';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get dailyProgress => 'Daily Progress';

  @override
  String get failedToLoadProgressData => 'Failed to load progress data';

  @override
  String get connectionProblem =>
      'There was a problem connecting to the server.';

  @override
  String get noMotivationalContent => 'No motivational content available';

  @override
  String get dailyPoints => 'daily points';

  @override
  String get foodMoodTitle => 'Food Mood';

  @override
  String get filter => 'Filter';

  @override
  String get sortBy => 'Sort By';

  @override
  String get defaultOption => 'Default';

  @override
  String get topRated => 'Top Rated';

  @override
  String get newestFirst => 'Newest First';

  @override
  String get oldestFirst => 'Oldest First';

  @override
  String get filterOptions => 'Filter Options';

  @override
  String get clearAll => 'Clear All';

  @override
  String get foodCategories => 'Food Categories';

  @override
  String get hideHighlyDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideItemsWithTooManyNegativeVotes =>
      'Hide items with too many negative votes';

  @override
  String get sort => 'Sort';

  @override
  String get loadingYourProfile => 'Loading your profile...';

  @override
  String get failedToLoadProfile => 'Failed to load profile';

  @override
  String get startMyDetoxJourney => 'Start My Detox Journey';

  @override
  String get startBeingPresent => 'Start Being Present';

  @override
  String get startMyDetox => 'Start My Detox';

  @override
  String get getStarted => 'Get Started';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get detoxRewards => 'Detox Rewards';

  @override
  String get newLabel => 'NEW';

  @override
  String get rewardsDescription =>
      'Complete activities to earn points and redeem for real-world rewards';

  @override
  String get goodMorning => 'Good Morning';

  @override
  String get goodAfternoon => 'Good Afternoon';

  @override
  String get goodEvening => 'Good Evening';

  @override
  String get goodNight => 'Good Night';

  @override
  String get currentPointsLabel => 'Current';

  @override
  String get targetPointsLabel => 'Target';

  @override
  String get maxPointsLabel => 'Maximum';

  @override
  String get detoxActivities => 'Detox Activities';

  @override
  String get defaultActivitiesDescription =>
      'Try these activities to improve your digital wellbeing and earn points';

  @override
  String activitiesForMood(String mood) {
    return 'Activities tailored for your $mood mood';
  }

  @override
  String get beginnerActivities => 'Beginner';

  @override
  String get intermediateActivities => 'Intermediate';

  @override
  String get expertActivities => 'Expert';

  @override
  String get loadingDashboard => 'Loading your personalized dashboard...';

  @override
  String get noActivitiesAvailable =>
      'No activities available for this mood yet';

  @override
  String get activityInProgressError =>
      'You already have an activity in progress';

  @override
  String get startActivity => 'Start';

  @override
  String get moodOMeterTitle => 'How are you feeling today?';

  @override
  String get moodOMeterInstructions =>
      'Drag around the mood meter or tap an emoji to select your current mood';

  @override
  String get createActivityTitle => 'Create Activity';

  @override
  String get titleLabel => 'Title';

  @override
  String get titleHint => 'Enter a title for your activity';

  @override
  String get titleRequired => 'Please enter a title';

  @override
  String get descriptionLabel => 'Description';

  @override
  String get descriptionHint => 'Describe your activity';

  @override
  String get descriptionRequired => 'Please enter a description';

  @override
  String durationLabel(int duration) {
    return 'Duration: $duration minutes';
  }

  @override
  String durationMinutes(int minutes) {
    return '$minutes min';
  }

  @override
  String get healthBenefitsLabel => 'Health Benefits';

  @override
  String get healthBenefitsHint =>
      'Describe the health benefits of this activity';

  @override
  String get healthBenefitsRequired => 'Please describe the health benefits';

  @override
  String get healthBenefitsDialogTitle => 'Health Benefits';

  @override
  String get addNewHealthBenefitHint => 'Add new health benefit';

  @override
  String get selectedBenefitsLabel => 'Selected Benefits:';

  @override
  String get suggestedBenefitsLabel => 'Suggested Benefits:';

  @override
  String get categoryLabel => 'Category';

  @override
  String get languageLabel => 'Language';

  @override
  String get selectLanguageHint => 'Select language';

  @override
  String get recommendedForMoodsLabel => 'Recommended for moods';

  @override
  String get selectAtLeastOneMood => '(select at least 1)';

  @override
  String get moodSelectionError => 'Please select at least 1 mood';

  @override
  String get healthBenefitsSelectionError =>
      'Please select at least 2 health benefits';

  @override
  String get privacySettingsLabel => 'Privacy settings';

  @override
  String get makeActivityPrivateLabel => 'Make activity private';

  @override
  String get privateActivityDescription => 'Only you can see this activity';

  @override
  String get showYourNameLabel => 'Show your name';

  @override
  String get showNameDescription =>
      'Your name will be displayed as the creator';

  @override
  String get createActivityButton => 'Create Activity';

  @override
  String get activityCreatedSuccess => 'Activity created successfully';

  @override
  String get loginRequiredError => 'You must be logged in to create activities';

  @override
  String createActivityError(String error) {
    return 'Failed to create activity: $error';
  }

  @override
  String get selectYourMood => 'Select your mood';

  @override
  String get commonMoods => 'Common moods:';

  @override
  String get allMoodOptions => 'All mood options:';

  @override
  String get moreMoodsButton => 'More';

  @override
  String get lessMoodsButton => 'Less';

  @override
  String get noMoodSelected => 'No mood selected';

  @override
  String get selectMoodAbove => 'Select a mood above';

  @override
  String get moodSelectionDescription =>
      'We\'ll suggest activities to help you feel better or stay positive';

  @override
  String get failedToLoadActivities => 'Failed to load activities';

  @override
  String get noActivitiesFound => 'No activities found';

  @override
  String get noActivitiesForMoodDescription =>
      'We couldn\'t find activities for your current mood. Try selecting a different mood.';

  @override
  String get noUserActivities => 'No activities created by you';

  @override
  String get noUserActivitiesDescription =>
      'You haven\'t created any activities for this mood yet. Create one or turn off the filter.';

  @override
  String get showAllActivities => 'Show All Activities';

  @override
  String suggestionsForMood(String mood, String emoji) {
    return 'Suggestions for $mood $emoji';
  }

  @override
  String get startButton => 'Start';

  @override
  String get stopButton => 'Stop';

  @override
  String activityStarted(String activityTitle) {
    return 'Started: $activityTitle';
  }

  @override
  String activityStartError(String error) {
    return 'Failed to start activity: $error';
  }

  @override
  String get activityCancelled => 'Activity cancelled. No points awarded.';

  @override
  String activityCancelError(String error) {
    return 'Error cancelling activity: $error';
  }

  @override
  String get completeCurrentActivityFirst =>
      'Complete or cancel the current activity first';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get applyButton => 'Apply';

  @override
  String get healthBenefitStress => 'Reduces stress and anxiety';

  @override
  String get healthBenefitCardio => 'Improves cardiovascular health';

  @override
  String get healthBenefitClarity => 'Enhances mental clarity';

  @override
  String get healthBenefitImmune => 'Boosts immune system';

  @override
  String get healthBenefitSleep => 'Improves sleep quality';

  @override
  String get healthBenefitStrength => 'Increases physical strength';

  @override
  String get healthBenefitMindful => 'Promotes mindfulness';

  @override
  String get healthBenefitDigital => 'Reduces digital addiction';

  @override
  String get healthBenefitFocus => 'Improves focus and concentration';

  @override
  String get healthBenefitEmotional => 'Promotes emotional well-being';

  @override
  String get healthBenefitEnergy => 'Increases energy levels';

  @override
  String get healthBenefitCreativity => 'Enhances creativity';

  @override
  String get healthBenefitPosture => 'Improves posture';

  @override
  String get healthBenefitWeight => 'Helps with weight management';

  @override
  String get foodMoodExplanationTitle => 'Did you know?';

  @override
  String get foodMoodExplanationText =>
      'The food you eat can significantly impact your mood and mental well-being. Nutrient-rich foods support brain function and help regulate emotions, while processed foods may contribute to mood swings.';

  @override
  String get noRecommendationsFound => 'No recommendations found';

  @override
  String get tryDifferentMood =>
      'Try selecting a different mood or adjusting your filters.';

  @override
  String get description => 'Description';

  @override
  String get benefits => 'Benefits';

  @override
  String get categories => 'Categories';

  @override
  String get hideDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideDownvotedDescription =>
      'Hide items with too many negative votes';

  @override
  String get recommendedForMoods => 'Recommended for Moods';

  @override
  String get nutritionalInfo => 'Nutritional Information';

  @override
  String get ingredients => 'Ingredients';

  @override
  String get preparationSteps => 'Preparation Steps';

  @override
  String get healthyFood => 'Healthy Food';

  @override
  String get moodHappy => 'Happy';

  @override
  String get moodSad => 'Sad';

  @override
  String get moodAngry => 'Angry';

  @override
  String get moodAnxious => 'Anxious';

  @override
  String get moodTired => 'Tired';

  @override
  String get moodStressed => 'Stressed';

  @override
  String get moodDepressed => 'Depressed';

  @override
  String get moodExcited => 'Excited';

  @override
  String get moodCalm => 'Calm';

  @override
  String get moodBored => 'Bored';

  @override
  String get foodCategoryProtein => 'Protein-rich';

  @override
  String get foodCategoryHealthyFats => 'Healthy Fats';

  @override
  String get foodCategoryComplexCarbs => 'Complex Carbs';

  @override
  String get foodCategoryVitamins => 'Vitamin-rich';

  @override
  String get foodCategoryMinerals => 'Minerals';

  @override
  String get foodCategoryOmega3 => 'Omega-3';

  @override
  String get foodCategoryProbiotics => 'Probiotics';

  @override
  String get foodCategoryCalming => 'Calming';

  @override
  String get foodCategoryEnergyBoosting => 'Energy Boosting';

  @override
  String get foodCategoryAntioxidants => 'Antioxidants';

  @override
  String get activitiesTitle => 'Activities';

  @override
  String helloUser(String userName) {
    return 'Hello, $userName';
  }

  @override
  String goalTag(String goal) {
    return '$goal';
  }

  @override
  String get familyTag => 'Family';

  @override
  String get childrenTag => 'Children';

  @override
  String get motivationChildrenFocus =>
      'Setting a great example for your kids today! Your focus shows them the power of being present and engaged.';

  @override
  String get motivationChildrenSleep =>
      'Better sleep means being more present for your family. Keep up with your digital boundaries!';

  @override
  String get motivationChildrenGeneric =>
      'Your children notice when you put down your phone. Every moment of connection with them matters!';

  @override
  String get motivationFocusHigh =>
      'Incredible focus today! Your mind is clear and your productivity is soaring. Keep it up!';

  @override
  String get motivationFocusLow =>
      'Each moment away from your screen is helping your brain reset and focus better. You\'ve got this!';

  @override
  String get motivationSleep =>
      'Your sleep quality improves with every digital break. Your body and mind thank you!';

  @override
  String get motivationGeneric =>
      'Small steps lead to big changes. You\'re rewiring your brain with each digital break. Keep going!';

  @override
  String get sortNotifications => 'Sort Notifications';

  @override
  String get byDateNewestFirst => 'By Date (newest first)';

  @override
  String get unreadFirst => 'Unread First';

  @override
  String unreadCount(int count) {
    return '$count unread';
  }

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get delete => 'Delete';

  @override
  String get markRead => 'Mark Read';

  @override
  String get markUnread => 'Mark Unread';

  @override
  String get couponDetails => 'Coupon Details';

  @override
  String get aboutPartner => 'About Partner';

  @override
  String get address => 'Address';

  @override
  String get openInMaps => 'Open in Maps';

  @override
  String get howToUse => 'How to Use';

  @override
  String get termsAndConditions => 'Terms & Conditions';

  @override
  String get validUntil => 'Valid until';

  @override
  String get redeemCoupon => 'Redeem Coupon';

  @override
  String redeemFor(int points) {
    return 'Redeem for $points Points';
  }

  @override
  String redeemConfirmation(String discount, String partner, int points) {
    return 'Would you like to redeem the $discount coupon from $partner? This will cost you $points points.';
  }

  @override
  String get cancel => 'Cancel';

  @override
  String get redeem => 'Redeem';

  @override
  String get openingInMaps => 'Opening in Maps...';

  @override
  String get shareFeatureComingSoon => 'Sharing feature coming soon!';

  @override
  String get activityDetails => 'Activity Details';

  @override
  String get activityCompleted => 'Activity Completed!';

  @override
  String activityCompletedMessage(String title) {
    return 'Congratulations! You completed \"$title\" and earned points!';
  }

  @override
  String get ok => 'OK';

  @override
  String get addToFavorites => 'Add to favorites';

  @override
  String get removedFromFavorites => 'Removed from favorites';

  @override
  String get addedToFavorites => 'Added to favorites';

  @override
  String get commentAdded => 'Comment added';

  @override
  String get activityInProgress => 'Activity in Progress';

  @override
  String get remaining => 'remaining';

  @override
  String get elapsed => 'elapsed';

  @override
  String get stayFocused => 'Stay focused to earn more Detox Points!';

  @override
  String get activityPoints => 'Activity Points';

  @override
  String get points => 'points';

  @override
  String get completeToEarn => 'Complete this activity to earn points!';

  @override
  String get aboutThisActivity => 'About this activity';

  @override
  String get healthBenefits => 'Health Benefits';

  @override
  String get noHealthBenefitsListed =>
      'No health benefits listed for this activity.';

  @override
  String get recommendedFor => 'Recommended for';

  @override
  String get noCommentsYet => 'No comments yet';

  @override
  String get beFirstToComment => 'Be the first to share your experience';

  @override
  String get shareYourExperience => 'Share your experience...';

  @override
  String get justNow => 'just now';

  @override
  String minutesAgo(int minutes) {
    return '${minutes}m ago';
  }

  @override
  String hoursAgo(int hours) {
    return '${hours}h ago';
  }

  @override
  String daysAgo(int days) {
    return '${days}d ago';
  }

  @override
  String get activityComplete => 'Activity Completed!';

  @override
  String get comments => 'Comments';

  @override
  String createdBy(String name) {
    return 'Created by: $name';
  }

  @override
  String get detoxBenefits => 'Detox Benefits';

  @override
  String get targetedMoods => 'Targeted Moods';

  @override
  String started(String name) {
    return 'Started: $name';
  }

  @override
  String failedToStartActivity(String error) {
    return 'Failed to start activity: $error';
  }

  @override
  String pointsValue(int value) {
    return '$value points';
  }

  @override
  String get upvotes => 'Upvotes';

  @override
  String get downvotes => 'Downvotes';

  @override
  String get nutritionInfo => 'Nutrition Info';

  @override
  String get calories => 'Calories';

  @override
  String get protein => 'Protein';

  @override
  String get carbs => 'Carbs';

  @override
  String get fats => 'Fats';

  @override
  String get moodFilter => 'Filter by Mood';

  @override
  String get networkStatusOnline => 'Online';

  @override
  String get networkStatusOffline => 'Offline';

  @override
  String get networkStatusChecking => 'Checking network...';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get foodImageLoadErrorTitle => 'Image Not Available';

  @override
  String get foodImageLoadErrorMessage =>
      'Could not load image. Please try again later.';

  @override
  String get suggestionsFor => 'Suggestions for';

  @override
  String get tipsAndTricks => 'Tips & Tricks';

  @override
  String get activityTips =>
      'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.';

  @override
  String get loading => 'Loading...';

  @override
  String get foodRecommendations => 'Food Recommendations';

  @override
  String get timeToDisconnect => 'Time to Disconnect';

  @override
  String get activitiesMotivationalMessage =>
      'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.';

  @override
  String get earnPointsForActivities =>
      'Earn points for every activity completed';

  @override
  String get details => 'Details';

  @override
  String get discussion => 'Discussion';

  @override
  String get editComment => 'Edit your comment...';

  @override
  String get commentLikeNotification => 'Comment Like Notifications';

  @override
  String get commentLikeNotificationDescription =>
      'Get notified when someone likes your comment';
}
