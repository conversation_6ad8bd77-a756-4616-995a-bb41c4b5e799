{"@@locale": "tr", "appTitle": "DetoxMe", "settingsTitle": "<PERSON><PERSON><PERSON>", "@settingsTitle": {"description": "Title for the settings screen."}, "appTheme": "Uygulama <PERSON>ı", "@appTheme": {"description": "Section title for theme settings."}, "themeSystem": "Sistem", "@themeSystem": {"description": "Option for system theme."}, "themeLight": "Açık", "@themeLight": {"description": "Option for light theme."}, "themeDark": "<PERSON><PERSON>", "@themeDark": {"description": "Option for dark theme."}, "language": "Dil", "@language": {"description": "Section title for language settings."}, "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@notifications": {"description": "Section title for notification settings."}, "enableNotifications": "Bildirimleri Etkinleştir", "@enableNotifications": {"description": "Title for enabling/disabling all notifications."}, "notificationsDescription": "<PERSON><PERSON> a<PERSON>, k<PERSON>lar ve hatırlatıcılar hakkında bildirimler alın", "@notificationsDescription": {"description": "Description of what notifications include."}, "notifyAboutActivities": "Aktivite Bildirimleri", "@notifyAboutActivities": {"description": "Subheading for activity notification settings."}, "activityMoodPreferences": "<PERSON><PERSON> <PERSON>", "@activityMoodPreferences": {"description": "Subheading for mood-specific notification settings."}, "otherNotifications": "<PERSON><PERSON><PERSON>", "@otherNotifications": {"description": "Subheading for other notification settings."}, "newActivitiesPushNotif": "<PERSON><PERSON> Aktiv<PERSON>ler", "@newActivitiesPushNotif": {"description": "Toggle for new activities notifications."}, "newCouponsPushNotif": "<PERSON><PERSON>", "@newCouponsPushNotif": {"description": "Toggle for new coupons notifications."}, "activityRemindersPushNotif": "Hatırlatıcılar", "@activityRemindersPushNotif": {"description": "Toggle for activity reminders notifications."}, "motivationalMessagesPushNotif": "Motive Edici <PERSON>", "@motivationalMessagesPushNotif": {"description": "Toggle for motivational message notifications."}, "commentLikesPushNotif": "<PERSON><PERSON>", "@commentLikesPushNotif": {"description": "Toggle for comment like notifications."}, "comingSoon": "Çok Yakında", "detoxRewards": "Detox Ödülleri", "newLabel": "YENİ", "rewardsDescription": "Etkinlikleri tamamlayarak puan kazanın ve gerçek ödüller için kullanın", "goodMorning": "Günaydın", "goodAfternoon": "Tünaydın", "goodEvening": "<PERSON>yi Akşamlar", "goodNight": "<PERSON><PERSON>", "currentPointsLabel": "Mevcut", "targetPointsLabel": "<PERSON><PERSON><PERSON>", "maxPointsLabel": "<PERSON><PERSON><PERSON><PERSON>", "detoxActivities": "Detox Etkinlikleri", "defaultActivitiesDescription": "Dijital refahınızı artırmak ve puan kazanmak için bu etkinlikleri deneyin", "activitiesForMood": "{mood} ruh haliniz i<PERSON>", "beginnerActivities": "Başlangıç", "intermediateActivities": "<PERSON><PERSON>", "expertActivities": "<PERSON><PERSON>", "loadingDashboard": "Kişiselleş<PERSON>rilmiş kontrol paneliniz yükleniyor...", "noActivitiesAvailable": "Bu ruh hali i<PERSON>in hen<PERSON>z et<PERSON> bulunmuyor", "activityInProgressError": "Zaten devam eden bir etkinliğiniz var", "startActivity": "Başla", "moodOMeterTitle": "<PERSON><PERSON><PERSON>n nasıl hissediyorsunuz?", "moodOMeterInstructions": "Ruh hali ölçeğinde sürükleyin veya mevcut ruh halinizi seçmek için bir emojiyi tıklayın", "createActivityTitle": "Etkinlik Oluştur", "titleLabel": "Başlık", "titleHint": "Etkinliğiniz için bir başlık girin", "titleRequired": "Lütfen bir başlık girin", "descriptionLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "descriptionHint": "Etkinliğinizi açıklayın", "descriptionRequired": "Lütfen bir açıklama girin", "durationLabel": "Süre: {duration} dakika", "durationMinutes": "{minutes} dk", "healthBenefitsLabel": "Sağlık Faydaları", "healthBenefitsHint": "Bu etkinliğin sağlık faydalarını açıklayın", "healthBenefitsRequired": "Lütfen sağlık faydalarını açıklayın", "healthBenefitsDialogTitle": "Sağlık Faydaları", "addNewHealthBenefitHint": "Yeni sağlık faydası ekle", "selectedBenefitsLabel": "Seçilen <PERSON>:", "suggestedBenefitsLabel": "<PERSON><PERSON><PERSON><PERSON>:", "categoryLabel": "<PERSON><PERSON><PERSON>", "languageLabel": "Dil", "selectLanguageHint": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "recommendedForMoodsLabel": "Şu ruh halleri i<PERSON><PERSON>", "selectAtLeastOneMood": "(en az 1 seçin)", "moodSelectionError": "Lütfen en az 1 ruh hali seçin", "healthBenefitsSelectionError": "Lütfen en az 2 sağlık faydası seçin", "privacySettingsLabel": "Gizlilik ayarları", "makeActivityPrivateLabel": "Etkinliği ö<PERSON> yap", "privateActivityDescription": "<PERSON><PERSON> et<PERSON>ğ<PERSON> sadece siz görebilirsiniz", "showYourNameLabel": "İsminizi gösterin", "showNameDescription": "İsminiz oluşturucu olarak görüntülenecek", "createActivityButton": "Etkinlik Oluştur", "activityCreatedSuccess": "Etkinlik başarıyla oluşturuldu", "loginRequiredError": "Etkinlik oluşturmak için giriş yapmalısınız", "createActivityError": "Etkinlik oluşturma başarısız: {error}", "selectYourMood": "<PERSON><PERSON> halinizi seçin", "commonMoods": "<PERSON>ygın ruh halleri:", "allMoodOptions": "Tüm ruh hali se<PERSON>ri:", "moreMoodsButton": "<PERSON><PERSON>", "lessMoodsButton": "<PERSON><PERSON>", "noMoodSelected": "<PERSON>uh hali se<PERSON><PERSON><PERSON>i", "selectMoodAbove": "<PERSON><PERSON><PERSON>dan bir ruh hali seçin", "moodSelectionDescription": "Kendinizi daha iyi hissetmenize veya pozitif kalmanıza yardımcı olacak etkinlikler önereceğiz", "failedToLoadActivities": "Etkinlikler yüklenemedi", "noActivitiesFound": "Etkinlik bulunamadı", "noActivitiesForMoodDescription": "Mevcut ruh haliniz için etkinlik bulamadık. Farklı bir ruh hali seçmeyi deneyin.", "noUserActivities": "Sizin tarafınızdan oluşturulan etkinlik yok", "noUserActivitiesDescription": "Bu ruh hali için henüz etkinlik oluşturmadınız. Bir tane oluşturun veya filtreyi kapatın.", "showAllActivities": "<PERSON><PERSON><PERSON> Etkinlikleri Göster", "suggestionsForMood": "{mood} {emoji} i<PERSON><PERSON>", "startButton": "Aktiv<PERSON><PERSON>", "stopButton": "Aktiv<PERSON><PERSON>", "activityStarted": "Başlatıldı: {activityTitle}", "activityStartError": "Etkinlik başlatılamadı: {error}", "activityCancelled": "Aktivite iptal edildi. <PERSON>uan kazanılmadı.", "activityCancelError": "Etkinlik iptal edilirken hata: {error}", "completeCurrentActivityFirst": "Önce mevcut etkinliği tamamlayın veya iptal edin", "cancelButton": "İptal", "applyButton": "<PERSON><PERSON><PERSON><PERSON>", "healthBenefitStress": "Stres ve kaygıyı azaltır", "healthBenefitCardio": "Kardiyovasküler sağlığı geliştirir", "healthBenefitClarity": "Zihinsel netliği artırır", "healthBenefitImmune": "Bağışıklık sistemini güçlendirir", "healthBenefitSleep": "<PERSON><PERSON><PERSON> ka<PERSON>i artı<PERSON>ır", "healthBenefitStrength": "Fiziksel gücü artırır", "healthBenefitMindful": "Farkındalığı teşvik eder", "healthBenefitDigital": "Dijital bağımlılığı azaltır", "healthBenefitFocus": "Odak ve konsantrasyonu artırır", "healthBenefitEmotional": "Duygusal refahı teşvik eder", "healthBenefitEnergy": "Enerji seviyelerini artırır", "healthBenefitCreativity": "Yaratıcılığı teşvik eder", "healthBenefitPosture": "<PERSON><PERSON><PERSON><PERSON>", "healthBenefitWeight": "<PERSON><PERSON>", "foodMoodExplanationTitle": "<PERSON><PERSON><PERSON><PERSON> muydun?", "foodMoodExplanationText": "<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON>, ruh halinizi ve zihinsel refahınızı önemli ölçüde etkileyebilir. Besin değeri yüksek gıdalar beyin fonksiyonunu destekler ve duyguları düzenlemeye yardımcı olurken, işlenmiş gıdalar ruh hali dalgalanmalarına katkıda bulunabilir.", "noRecommendationsFound": "Öneri bulunamadı", "tryDifferentMood": "Farklı bir ruh hali seçmeyi veya filtrelerinizi ayarlamayı deneyin.", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "benefits": "<PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "foodCategories": "Yiyecek Kategorileri", "hideDownvotedItems": "Çok Fazla Olumsuz Oy Alan Öğeleri <PERSON>izle", "hideDownvotedDescription": "Çok fazla olumsuz oy alan <PERSON> gizle", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recommendedForMoods": "<PERSON><PERSON><PERSON><PERSON>", "nutritionInfo": "<PERSON><PERSON>", "calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fats": "<PERSON><PERSON><PERSON>", "upvotes": "<PERSON><PERSON><PERSON><PERSON>", "downvotes": "Beğenmeyenler", "healthyFood": "Sağlıklı Yiyecek", "moodHappy": "<PERSON><PERSON><PERSON>", "@moodHappy": {"description": "Mood label: Happy"}, "moodSad": "Üzgün", "@moodSad": {"description": "Mood label: Sad"}, "moodAngry": "Kızgın", "@moodAngry": {"description": "Mood label: Angry"}, "moodAnxious": "<PERSON><PERSON><PERSON><PERSON>", "@moodAnxious": {"description": "Mood label: Anxious"}, "moodTired": "<PERSON><PERSON><PERSON>", "@moodTired": {"description": "Mood label: Tired"}, "moodStressed": "<PERSON><PERSON><PERSON>", "@moodStressed": {"description": "Mood label: Stressed"}, "moodDepressed": "Dep<PERSON><PERSON>", "@moodDepressed": {"description": "Mood label: Depressed"}, "moodExcited": "Heyecanlı", "@moodExcited": {"description": "Mood label: Excited"}, "moodCalm": "<PERSON><PERSON>", "@moodCalm": {"description": "Mood label: Calm"}, "moodBored": "Sıkılmış", "@moodBored": {"description": "Mood label: Bo<PERSON>"}, "foodCategoryProtein": "<PERSON><PERSON> açısından zengin", "@foodCategoryProtein": {"description": "Yiyecek kategorisi: <PERSON>tein açısından zengin yi<PERSON>r"}, "foodCategoryHealthyFats": "Sağlıklı Yağlar", "@foodCategoryHealthyFats": {"description": "Yiyecek kategorisi: Sağlıklı yağlar"}, "foodCategoryComplexCarbs": "Kompleks Karbonhidratlar", "@foodCategoryComplexCarbs": {"description": "Yiyecek kategorisi: Kompleks karbonhidratlar"}, "foodCategoryVitamins": "<PERSON><PERSON> zeng<PERSON>", "@foodCategoryVitamins": {"description": "Yiyecek kategorisi: <PERSON><PERSON> zengin y<PERSON>"}, "foodCategoryMinerals": "<PERSON><PERSON><PERSON>", "@foodCategoryMinerals": {"description": "Yiyecek kategorisi: <PERSON><PERSON><PERSON>"}, "foodCategoryOmega3": "Omega-3", "@foodCategoryOmega3": {"description": "Yiyecek kategorisi: Omega-3 açısından zengin yiye<PERSON>kler"}, "foodCategoryProbiotics": "Probiyotikler", "@foodCategoryProbiotics": {"description": "Yiyecek kategorisi: Probiyotikler"}, "foodCategoryCalming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@foodCategoryCalming": {"description": "Yiyecek kategorisi: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yiyecekler"}, "foodCategoryEnergyBoosting": "<PERSON><PERSON><PERSON>", "@foodCategoryEnergyBoosting": {"description": "Yiyecek kategorisi: <PERSON><PERSON><PERSON> veren yi<PERSON><PERSON>r"}, "foodCategoryAntioxidants": "Antioksidanlar", "@foodCategoryAntioxidants": {"description": "Yiyecek kategorisi: Antioksidanlar"}, "activitiesTitle": "Aktiviteler", "@activitiesTitle": {"description": "Aktiviteler sekmesi ve ekranı için başlık."}, "helloUser": "<PERSON><PERSON><PERSON><PERSON>, {userName}", "@helloUser": {"description": "Kullanıcı i<PERSON><PERSON>, parametre: userName", "placeholders": {"userName": {"type": "String"}}}, "goalTag": "{goal}", "@goalTag": {"description": "<PERSON><PERSON><PERSON>, parametre: goal", "placeholders": {"goal": {"type": "String"}}}, "familyTag": "<PERSON><PERSON>", "@familyTag": {"description": "<PERSON><PERSON>"}, "childrenTag": "Çocuklar", "@childrenTag": {"description": "Çocuk<PERSON> etike<PERSON>"}, "motivationChildrenFocus": "Bugün ç<PERSON>ukların için harika bir örneksin! <PERSON><PERSON><PERSON>, onlara anda o<PERSON>ın gücünü gösteriyor.", "@motivationChildrenFocus": {"description": "Çocuklar ve odak hedefi için motivasyon mesajı."}, "motivationChildrenSleep": "<PERSON><PERSON> i<PERSON>, a<PERSON><PERSON> i<PERSON>in daha fazla var olman demek. Dijital sınırlarına devam et!", "@motivationChildrenSleep": {"description": "Çocuklar ve uyku hedefi için motivasyon mesajı."}, "motivationChildrenGeneric": "Çocukların telefonu bıraktığını fark ediyor. <PERSON><PERSON><PERSON> her anın önemi büyük!", "@motivationChildrenGeneric": {"description": "Çocuklar için genel motivasyon mesajı."}, "motivationFocusHigh": "Bugün inanılmaz bir odaklanma! <PERSON><PERSON>nin berrak ve verimliliğin artıyor. Böyle devam et!", "@motivationFocusHigh": {"description": "Yüksek odak ilerlemesi için motivasyon mesajı."}, "motivationFocusLow": "<PERSON><PERSON><PERSON>n uzak her an beyninin daha iyi odaklanmasına yardımcı oluyor. <PERSON><PERSON>u başarabil<PERSON>in!", "@motivationFocusLow": {"description": "Düşük odak ilerlemesi için motivasyon mesajı."}, "motivationSleep": "Her dijital mola ile uyku kaliten artıyor. Bedenin ve zihnin sana teşekkür ediyor!", "@motivationSleep": {"description": "<PERSON><PERSON><PERSON> hede<PERSON> için motivasyon mesajı."}, "motivationGeneric": "Küçük adımlar büyük değişimlere yol açar. Her dijital mola ile beynini yeniden şekillendiriyorsun. Devam et!", "@motivationGeneric": {"description": "Genel motivasyon mesajı."}, "profileLoadFailure": "Profil <PERSON>: ", "profileYourProgress": "İlerlemeniz", "@profileYourProgress": {"description": "Header for the progress section in the profile screen."}, "profilePoints": "<PERSON><PERSON><PERSON>", "@profilePoints": {"description": "Title for the points speedometer chart in the profile screen."}, "profileMyGoals": "<PERSON><PERSON><PERSON><PERSON>", "@profileMyGoals": {"description": "Title for the goals section in the profile screen."}, "profileFavoriteActivities": "<PERSON><PERSON><PERSON>", "@profileFavoriteActivities": {"description": "Title for the favorite activities section in the profile screen."}, "profileNoFavoriteActivities": "Henüz favori aktivite yok", "@profileNoFavoriteActivities": {"description": "Message shown when there are no favorite activities."}, "profileAddFavoriteActivitiesHint": "Burada görmek için aktiviteleri favori olarak işaretleyin", "@profileAddFavoriteActivitiesHint": {"description": "Hint message shown when there are no favorite activities."}, "profileExploreActivities": "Aktiviteleri Keşfet", "@profileExploreActivities": {"description": "Button text to explore activities when there are no favorites."}, "profileExploreMoreActivities": "Daha Fazla Aktivite Keşfet", "@profileExploreMoreActivities": {"description": "Button text to explore more activities at the bottom of the favorites section."}, "profileQuickActions": "Hızlı İşlemler", "@profileQuickActions": {"description": "Title for the quick actions section in the profile screen."}, "profileActionActivityHistory": "Aktivite Geçmişi", "@profileActionActivityHistory": {"description": "Quick action button for activity history."}, "profileActionUsedCoupons": "Kullanılan Ku<PERSON>nlar", "@profileActionUsedCoupons": {"description": "Quick action button for used coupons."}, "profileActionRewardHistory": "Ödül Geçmişi", "@profileActionRewardHistory": {"description": "Quick action button for reward history."}, "profileActionSettings": "<PERSON><PERSON><PERSON>", "@profileActionSettings": {"description": "Quick action button for settings."}, "profileViewAll": "Tümünü <PERSON>", "@profileViewAll": {"description": "Button text to view all favorite activities."}, "homeScreenError": "Görevler yüklenirken hata oluştu: {error}", "sort": "S<PERSON>rala", "sortNotifications": "Bildirimleri Sırala", "byDateNewestFirst": "<PERSON><PERSON><PERSON> (yeniden eskiye)", "unreadFirst": "Okunmayanlar Önce", "unreadCount": "{count} okunmamış", "@unreadCount": {"description": "Number of unread notifications", "placeholders": {"count": {"type": "int"}}}, "today": "<PERSON><PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "notificationDeleted": "<PERSON><PERSON><PERSON><PERSON>", "notificationMarkedRead": "Okundu olarak işaretlendi", "notificationMarkedUnread": "Okunmadı olarak işaretlendi", "delete": "Sil", "markRead": "Okundu İşaretle", "markUnread": "Okunmadı İşaretle", "undo": "<PERSON><PERSON>", "noNotifications": "<PERSON><PERSON><PERSON><PERSON> yok", "noNotificationsDescription": "Henüz bildiriminiz yok. <PERSON><PERSON> a<PERSON>, ödüller ve önemli güncellemeler hakkında sizi bilgilendireceğiz.", "couponDetails": "<PERSON><PERSON><PERSON>", "aboutPartner": "İş Ortağı Hakkında", "address": "<PERSON><PERSON>", "openInMaps": "Haritalarda Aç", "howToUse": "<PERSON><PERSON><PERSON><PERSON>", "termsAndConditions": "Şartlar ve <PERSON>ar", "validUntil": "Geçerlilik tarihi", "redeemCoupon": "<PERSON><PERSON><PERSON>", "redeemFor": "{points} <PERSON><PERSON>", "@redeemFor": {"description": "<PERSON><PERSON> text for redeeming a coupon", "placeholders": {"points": {"type": "int"}}}, "redeemConfirmation": "{partner} firmasının {discount} indirim kuponunu kullanmak istiyor musunuz? Bu size {points} puana mal olacak.", "@redeemConfirmation": {"description": "Confirmation message for redeeming a coupon", "placeholders": {"discount": {"type": "String"}, "partner": {"type": "String"}, "points": {"type": "int"}}}, "cancel": "İptal", "redeem": "<PERSON><PERSON>", "openingInMaps": "Haritalarda açılıyor...", "shareFeatureComingSoon": "Paylaşım özelliği yakında geliyor!", "activityDetails": "Aktivite Detayları", "activityComplete": "Aktivite Tamamlandı", "activityCompletedMessage": "Tebrikler! \"{title}\" aktivitesini tamamladınız ve puan kazandınız!", "@activityCompletedMessage": {"description": "Message shown when an activity is completed", "placeholders": {"title": {"type": "String"}}}, "ok": "<PERSON><PERSON>", "addToFavorites": "<PERSON>av<PERSON><PERSON>e ekle", "removedFromFavorites": "Favorilerden çıkarıldı", "addedToFavorites": "Favorilere e<PERSON>ndi", "commentAdded": "<PERSON><PERSON> e<PERSON>", "activityInProgress": "Aktivite <PERSON>", "remaining": "kalan", "elapsed": "geçen", "stayFocused": "Daha fazla Detox Puanı kazanmak için odaklanmaya devam edin!", "activityPoints": "Aktivite Puanları", "points": "puan", "completeToEarn": "<PERSON>uan kazanmak için bu aktiviteyi tamamlayın!", "aboutThisActivity": "Bu aktivite hakkında", "healthBenefits": "Sağlık Faydaları", "noHealthBenefitsListed": "Bu aktivite için listelenmiş sağlık faydası yok.", "recommendedFor": "<PERSON><PERSON><PERSON> i<PERSON>", "noCommentsYet": "<PERSON><PERSON><PERSON><PERSON> yorum yok", "beFirstToComment": "Deneyiminizi pay<PERSON>şan ilk kişi olun", "shareYourExperience": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "justNow": "az önce", "minutesAgo": "{minutes}d önce", "@minutesAgo": {"description": "Minutes ago text for comment timestamp", "placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "{hours}s önce", "@hoursAgo": {"description": "Hours ago text for comment timestamp", "placeholders": {"hours": {"type": "int"}}}, "daysAgo": "{days}g önce", "@daysAgo": {"description": "Days ago text for comment timestamp", "placeholders": {"days": {"type": "int"}}}, "comments": "<PERSON><PERSON><PERSON>", "createdBy": "Oluşturan: {name}", "detoxBenefits": "Detoks Faydaları", "targetedMoods": "<PERSON><PERSON><PERSON><PERSON>", "started": "Başladı: {name}", "failedToStartActivity": "Aktivite başlatılamadı: {error}", "pointsValue": "{value} puan", "nutritionalInfo": "<PERSON><PERSON>", "ingredients": "Mal<PERSON>mel<PERSON>", "preparationSteps": "Hazırlık <PERSON>ı<PERSON>ları", "foodBenefits": "Yiyecek Faydaları", "termsConditions": "Şartlar ve <PERSON>ar", "details": "Detaylar", "discussion": "Tartışma", "editComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>...", "commentLikeNotification": "<PERSON>rum Beğeni Bildirimleri", "commentLikeNotificationDescription": "<PERSON><PERSON><PERSON> yorum<PERSON>uzu beğendiğinde bildirim alın"}