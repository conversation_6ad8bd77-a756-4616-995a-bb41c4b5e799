// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appTitle => 'DetoxMe';

  @override
  String get pinScreenSetupTitle => 'Set up Admin PIN';

  @override
  String get pinScreenEnterTitle => 'Enter Admin PIN';

  @override
  String get pinScreenSetupSubtitle => 'Set up a 4-digit PIN';

  @override
  String get pinScreenEnterSubtitle => 'Enter your 4-digit PIN';

  @override
  String get pinScreenSettingPin => 'Setting PIN...';

  @override
  String get pinScreenVerifyingPin => 'Verifying PIN...';

  @override
  String get pinScreenSaving => 'Saving...';

  @override
  String get pinScreenChecking => 'Checking...';

  @override
  String get pinScreenSetupFailed => 'PIN Setup Failed';

  @override
  String get pinScreenIncorrectPin => 'Incorrect PIN';

  @override
  String get pinScreenSuccessSet => 'PIN Set Successfully!';

  @override
  String get pinScreenSuccessVerified => 'PIN Verified!';

  @override
  String get pinScreenErrorSnackbarPrefix => 'Error: ';

  @override
  String get homeScreenTitle => 'Home';

  @override
  String get homeScreenWelcome => 'Welcome back!';

  @override
  String get homeScreenSubtitle => 'Take a moment for yourself today';

  @override
  String get profileScreenTitle => 'Profile';

  @override
  String get profileEditTitle => 'Edit Profile';

  @override
  String get profileUsernameLabel => 'Username';

  @override
  String get profileAvatarSelectionLabel => 'Choose Avatar';

  @override
  String get profileSaveButton => 'Save Profile';

  @override
  String get profileSaveSuccess => 'Profile saved successfully!';

  @override
  String get profileSaveFailure => 'Failed to save profile: ';

  @override
  String get profileLoadFailure => 'Profil yüklenemedi: ';

  @override
  String get profileYourProgress => 'İlerlemeniz';

  @override
  String get profilePoints => 'Puanlar';

  @override
  String get profileMyGoals => 'Hedeflerim';

  @override
  String get profileFavoriteActivities => 'Favori Aktiviteler';

  @override
  String get profileNoFavoriteActivities => 'Henüz favori aktivite yok';

  @override
  String get profileAddFavoriteActivitiesHint =>
      'Burada görmek için aktiviteleri favori olarak işaretleyin';

  @override
  String get profileExploreActivities => 'Aktiviteleri Keşfet';

  @override
  String get profileExploreMoreActivities => 'Daha Fazla Aktivite Keşfet';

  @override
  String get profileQuickActions => 'Hızlı İşlemler';

  @override
  String get profileActionActivityHistory => 'Aktivite Geçmişi';

  @override
  String get profileActionUsedCoupons => 'Kullanılan Kuponlar';

  @override
  String get profileActionRewardHistory => 'Ödül Geçmişi';

  @override
  String get profileActionSettings => 'Ayarlar';

  @override
  String get profileViewAll => 'Tümünü Gör';

  @override
  String homeScreenError(String error) {
    return 'Görevler yüklenirken hata oluştu: $error';
  }

  @override
  String get homeScreenNoTasks => 'No tasks available right now!';

  @override
  String homeScreenTaskDuration(String duration) {
    return 'Duration: $duration';
  }

  @override
  String get homeScreenStartButton => 'Begin Digital Detox';

  @override
  String get homeScreenAnotherTaskButton => 'Try a different challenge';

  @override
  String get detoxPointsLabel => 'Detox Points';

  @override
  String greetingMessage(String username) {
    return 'Good Day, $username!';
  }

  @override
  String get mindfulActivitySuggestions => 'Mindful Activity Suggestions';

  @override
  String get homeScreenProfileTooltip => 'Profile';

  @override
  String rewardNotificationXp(int xp) {
    return 'You earned $xp Detox Points!';
  }

  @override
  String rewardNotificationLevelUp(int levelNumber, String levelName) {
    return 'New Milestone! Reached Level $levelNumber: $levelName';
  }

  @override
  String rewardNotificationBadge(String badgeName) {
    return 'Achievement Unlocked: $badgeName';
  }

  @override
  String rewardNotificationCard(String cardName, String cardRarity) {
    return 'Reward Unlocked: $cardName ($cardRarity)';
  }

  @override
  String rewardErrorSnackbar(String errorMessage) {
    return 'Error processing rewards: $errorMessage';
  }

  @override
  String get rewardDialogTitle => 'Challenge Complete!';

  @override
  String get dialogButtonOK => 'OK';

  @override
  String get leaderboardScreenTitle => 'Leaderboard';

  @override
  String leaderboardLoadingError(String message) {
    return 'Failed to load leaderboard: $message';
  }

  @override
  String get leaderboardEmpty => 'No entries in the leaderboard yet';

  @override
  String get unknownState => 'Unknown state';

  @override
  String get onboardingButtonNext => 'Next';

  @override
  String get authLoginTitle => 'Login';

  @override
  String get authSignUpTitle => 'Sign Up';

  @override
  String get authEmailLabel => 'Email';

  @override
  String get authPasswordLabel => 'Password';

  @override
  String get authInvalidEmailError => 'Please enter a valid email';

  @override
  String get authPasswordTooShortError =>
      'Password must be at least 6 characters';

  @override
  String get authLoginButton => 'Login';

  @override
  String get authSignUpButton => 'Sign Up';

  @override
  String get authToggleToSignUp => 'Need an account? Sign Up';

  @override
  String get authToggleToLogin => 'Have an account? Login';

  @override
  String get adultOnboardingWelcomeTitle => 'Welcome to DetoxMe!';

  @override
  String get adultOnboardingWelcomeBody =>
      'Let\'s reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.';

  @override
  String get adultOnboardingExplanationTitle => 'Why These Questions?';

  @override
  String get adultOnboardingExplanationBody =>
      'Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.';

  @override
  String get adultOnboardingAgeTitle => 'About You: Age Group';

  @override
  String get adultOnboardingAgeBody =>
      'Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.';

  @override
  String get adultOnboardingAgeOption1 => '18-25';

  @override
  String get adultOnboardingAgeOption2 => '26-35';

  @override
  String get adultOnboardingAgeOption3 => '36-45';

  @override
  String get adultOnboardingAgeOption4 => '46-55';

  @override
  String get adultOnboardingAgeOption5 => '56+';

  @override
  String get adultOnboardingGenderTitle => 'About You: Gender';

  @override
  String get adultOnboardingGenderBody =>
      'This helps us use appropriate language and understand demographic patterns (optional).';

  @override
  String get adultOnboardingGenderOptionMale => 'Male';

  @override
  String get adultOnboardingGenderOptionFemale => 'Female';

  @override
  String get adultOnboardingGenderOptionNonBinary => 'Non-binary';

  @override
  String get adultOnboardingGenderOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingFamilyTitle => 'About You: Family Status';

  @override
  String get adultOnboardingFamilyBody =>
      'Understanding your home life can help tailor challenges and goals (optional).';

  @override
  String get adultOnboardingFamilyOptionSingle => 'Single';

  @override
  String get adultOnboardingFamilyOptionInRelationship =>
      'In a relationship / Married';

  @override
  String get adultOnboardingFamilyOptionHaveChildren => 'Have children';

  @override
  String get adultOnboardingFamilyOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingGoalsTitle => 'Your Main Goals';

  @override
  String get adultOnboardingGoalsBody =>
      'What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).';

  @override
  String get adultOnboardingGoalReduceScreenTime =>
      'Reduce overall screen time';

  @override
  String get adultOnboardingGoalImproveFocus => 'Improve focus & concentration';

  @override
  String get adultOnboardingGoalBeMorePresent =>
      'Be more present in daily life';

  @override
  String get adultOnboardingGoalSpendMoreTimeFamily =>
      'Spend more quality time with family/friends';

  @override
  String get adultOnboardingGoalDigitalDetox => 'Perform a digital detox';

  @override
  String get adultOnboardingGoalImproveSleep => 'Improve sleep quality';

  @override
  String get adultOnboardingGoalOther => 'Other';

  @override
  String get adultOnboardingCompletionTitle => 'All Set!';

  @override
  String get adultOnboardingCompletionBody =>
      'Thank you! You\'re ready to start your journey. Let\'s build healthier digital habits together.';

  @override
  String get adultOnboardingButtonGetStarted => 'Get Started';

  @override
  String get adultOnboardingButtonBack => 'Back';

  @override
  String get requiredErrorText => 'Please make a selection';

  @override
  String get phoneInputTitle => 'Enter Phone Number';

  @override
  String get phoneInputInstructions => 'Enter your phone number to continue';

  @override
  String get phoneInputLabel => 'Phone Number';

  @override
  String get phoneInputHint => '123456789';

  @override
  String get phoneInputEmptyError => 'Please enter a phone number';

  @override
  String get phoneInputInvalidError => 'Please enter a valid phone number';

  @override
  String get phoneInputContinueButton => 'Continue';

  @override
  String get otpVerificationTitle => 'Verify Phone Number';

  @override
  String otpVerificationCodeSent(String phoneNumber) {
    return 'Code sent to $phoneNumber';
  }

  @override
  String get otpVerificationInvalidCode => 'Please enter a valid 6-digit code';

  @override
  String get otpVerificationButton => 'Verify Code';

  @override
  String get otpVerificationDidntReceive => 'Didn\'t receive the code?';

  @override
  String get otpVerificationResend => 'Resend';

  @override
  String otpVerificationResendTimer(int seconds) {
    return 'Resend in $seconds s';
  }

  @override
  String get otpVerifiedSnackbar => 'Phone number verified successfully!';

  @override
  String get continueWithPhone => 'Continue with Phone';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get onboardingTellUsAboutYourself => 'Tell us about yourself';

  @override
  String get onboardingPersonalizeExperience =>
      'We\'ll use this information to personalize your detox experience';

  @override
  String get username => 'Username';

  @override
  String get enterYourUsername => 'Enter your preferred username';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectYourBirthYear => 'Select your birth year';

  @override
  String get birthYearRequired => 'Birth year is required';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get nonBinary => 'Non-binary';

  @override
  String get preferNotToSay => 'Prefer not to say';

  @override
  String get next => 'Next';

  @override
  String get authTitle => 'Welcome Back';

  @override
  String get authSubtitle =>
      'Sign in to continue your digital wellness journey';

  @override
  String get loginWithPhone => 'Login with Phone';

  @override
  String get loginWithEmail => 'Login with Email';

  @override
  String get emailFieldLabel => 'Email';

  @override
  String get emailRequiredError => 'Email is required';

  @override
  String get emailInvalidError => 'Please enter a valid email';

  @override
  String get passwordFieldLabel => 'Password';

  @override
  String get passwordRequiredError => 'Password is required';

  @override
  String get passwordLengthError => 'Password must be at least 6 characters';

  @override
  String get noAccountQuestion => 'Don\'t have an account?';

  @override
  String get hasAccountQuestion => 'Already have an account?';

  @override
  String get signupAction => 'Sign Up';

  @override
  String get loginAction => 'Login';

  @override
  String get loginButton => 'Login';

  @override
  String get signupButton => 'Create Account';

  @override
  String get phoneVerificationHeader => 'Verify Your Phone';

  @override
  String get phoneVerificationHint =>
      'You\'ll receive a 6-digit code to verify your phone number';

  @override
  String get otpVerificationHeader => 'Enter Verification Code';

  @override
  String get otpVerificationResendCode => 'Resend Code';

  @override
  String get otpVerificationResendIn => 'Resend in';

  @override
  String get otpVerificationVerifyButton => 'Verify & Continue';

  @override
  String get selectLifeSituation => 'Select your life situation';

  @override
  String get lifeSituationIndividual => 'Individual';

  @override
  String get lifeSituationIndividualDesc =>
      'Focus on personal growth and wellbeing';

  @override
  String get lifeSituationRelationship => 'Relationship';

  @override
  String get lifeSituationRelationshipDesc =>
      'Balance screen time with your partner';

  @override
  String get lifeSituationFamily => 'Family';

  @override
  String get lifeSituationFamilyDesc =>
      'Create healthy digital habits for your family';

  @override
  String get lifeSituationWork => 'Work';

  @override
  String get lifeSituationWorkDesc =>
      'Improve productivity and reduce digital distractions';

  @override
  String get lifeSituationErrorRequired => 'Please select a life situation';

  @override
  String get lifeSituationTitle => 'Your Life Situation';

  @override
  String get lifeSituationDescription =>
      'Select the option that best describes your current situation';

  @override
  String get lifeSituationCustom => 'Custom';

  @override
  String get rewardHistoryScreenTitle => 'Reward History';

  @override
  String get rewardHistoryEmptyTitle => 'No Rewards Yet';

  @override
  String get rewardHistoryEmptyMessage =>
      'You haven\'t redeemed any rewards yet. Complete challenges to earn points and redeem rewards.';

  @override
  String get loadRedemptionsButton => 'Load Rewards';

  @override
  String get goBackButton => 'Go Back';

  @override
  String activeActivityStatus(String activityTitle) {
    return '$activityTitle (Active)';
  }

  @override
  String pausedActivityStatus(String activityTitle) {
    return '$activityTitle (Paused)';
  }

  @override
  String get activeActivityMotivation =>
      'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.';

  @override
  String activeActivityHealthBenefit(String benefit) {
    return 'This activity is good for your health: $benefit';
  }

  @override
  String get activeActivityStopConfirmTitle => 'Are you sure?';

  @override
  String get activeActivityStopConfirmMessage =>
      'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.';

  @override
  String get activeActivityKeepGoingButton => 'Keep Going';

  @override
  String get activeActivityStopButton => 'Stop Activity';

  @override
  String get activeActivityPauseButton => 'Pause';

  @override
  String get activeActivityResumeButton => 'Resume';

  @override
  String get settingsTitle => 'Ayarlar';

  @override
  String get appTheme => 'Uygulama Teması';

  @override
  String get themeSystem => 'Sistem';

  @override
  String get themeLight => 'Açık';

  @override
  String get themeDark => 'Koyu';

  @override
  String get language => 'Dil';

  @override
  String get notifications => 'Bildirimler';

  @override
  String get enableNotifications => 'Bildirimleri Etkinleştir';

  @override
  String get notificationsDescription =>
      'Yeni aktiviteler, kuponlar ve hatırlatıcılar hakkında bildirimler alın';

  @override
  String get notifyAboutActivities => 'Aktivite Bildirimleri';

  @override
  String get activityMoodPreferences => 'Ruh Hali Tercihleri';

  @override
  String get otherNotifications => 'Diğer Bildirimler';

  @override
  String get newActivitiesPushNotif => 'Yeni Aktiviteler';

  @override
  String get newCouponsPushNotif => 'Yeni Kuponlar';

  @override
  String get activityRemindersPushNotif => 'Hatırlatıcılar';

  @override
  String get motivationalMessagesPushNotif => 'Motive Edici Mesajlar';

  @override
  String get commentLikesPushNotif => 'Yorum Beğenileri';

  @override
  String get noNotifications => 'Bildirim yok';

  @override
  String get noNotificationsDescription =>
      'Henüz bildiriminiz yok. Yeni aktiviteler, ödüller ve önemli güncellemeler hakkında sizi bilgilendireceğiz.';

  @override
  String get notificationDeleted => 'Bildirim silindi';

  @override
  String get notificationMarkedRead => 'Okundu olarak işaretlendi';

  @override
  String get notificationMarkedUnread => 'Okunmadı olarak işaretlendi';

  @override
  String get undo => 'Geri Al';

  @override
  String get activitiesScreenTitle => 'Mood & Activities';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get activities => 'Activities';

  @override
  String get allActivities => 'All Activities';

  @override
  String get myActivities => 'My Activities';

  @override
  String get newest => 'Newest';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get mostUpvoted => 'Most Upvoted';

  @override
  String get highestRated => 'Highest Rated';

  @override
  String get languages => 'Languages';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String errorLoadingData(String error) {
    return 'Error loading data: $error';
  }

  @override
  String errorRefreshingData(String error) {
    return 'Error refreshing data: $error';
  }

  @override
  String get orWithEmail => 'or with email';

  @override
  String get takeDeepBreath => 'Take a deep breath';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get dailyProgress => 'Daily Progress';

  @override
  String get failedToLoadProgressData => 'Failed to load progress data';

  @override
  String get connectionProblem =>
      'There was a problem connecting to the server.';

  @override
  String get noMotivationalContent => 'No motivational content available';

  @override
  String get dailyPoints => 'daily points';

  @override
  String get foodMoodTitle => 'Food Mood';

  @override
  String get filter => 'Filter';

  @override
  String get sortBy => 'Sort By';

  @override
  String get defaultOption => 'Default';

  @override
  String get topRated => 'Top Rated';

  @override
  String get newestFirst => 'Newest First';

  @override
  String get oldestFirst => 'Oldest First';

  @override
  String get filterOptions => 'Filter Options';

  @override
  String get clearAll => 'Tümünü Temizle';

  @override
  String get foodCategories => 'Yiyecek Kategorileri';

  @override
  String get hideHighlyDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideItemsWithTooManyNegativeVotes =>
      'Hide items with too many negative votes';

  @override
  String get sort => 'Sırala';

  @override
  String get loadingYourProfile => 'Loading your profile...';

  @override
  String get failedToLoadProfile => 'Failed to load profile';

  @override
  String get startMyDetoxJourney => 'Start My Detox Journey';

  @override
  String get startBeingPresent => 'Start Being Present';

  @override
  String get startMyDetox => 'Start My Detox';

  @override
  String get getStarted => 'Get Started';

  @override
  String get comingSoon => 'Çok Yakında';

  @override
  String get detoxRewards => 'Detox Ödülleri';

  @override
  String get newLabel => 'YENİ';

  @override
  String get rewardsDescription =>
      'Etkinlikleri tamamlayarak puan kazanın ve gerçek ödüller için kullanın';

  @override
  String get goodMorning => 'Günaydın';

  @override
  String get goodAfternoon => 'Tünaydın';

  @override
  String get goodEvening => 'İyi Akşamlar';

  @override
  String get goodNight => 'İyi Geceler';

  @override
  String get currentPointsLabel => 'Mevcut';

  @override
  String get targetPointsLabel => 'Hedef';

  @override
  String get maxPointsLabel => 'Maksimum';

  @override
  String get detoxActivities => 'Detox Etkinlikleri';

  @override
  String get defaultActivitiesDescription =>
      'Dijital refahınızı artırmak ve puan kazanmak için bu etkinlikleri deneyin';

  @override
  String activitiesForMood(String mood) {
    return '$mood ruh haliniz için özel etkinlikler';
  }

  @override
  String get beginnerActivities => 'Başlangıç';

  @override
  String get intermediateActivities => 'Orta Seviye';

  @override
  String get expertActivities => 'Uzman';

  @override
  String get loadingDashboard =>
      'Kişiselleştirilmiş kontrol paneliniz yükleniyor...';

  @override
  String get noActivitiesAvailable =>
      'Bu ruh hali için henüz etkinlik bulunmuyor';

  @override
  String get activityInProgressError => 'Zaten devam eden bir etkinliğiniz var';

  @override
  String get startActivity => 'Başla';

  @override
  String get moodOMeterTitle => 'Bugün nasıl hissediyorsunuz?';

  @override
  String get moodOMeterInstructions =>
      'Ruh hali ölçeğinde sürükleyin veya mevcut ruh halinizi seçmek için bir emojiyi tıklayın';

  @override
  String get createActivityTitle => 'Etkinlik Oluştur';

  @override
  String get titleLabel => 'Başlık';

  @override
  String get titleHint => 'Etkinliğiniz için bir başlık girin';

  @override
  String get titleRequired => 'Lütfen bir başlık girin';

  @override
  String get descriptionLabel => 'Açıklama';

  @override
  String get descriptionHint => 'Etkinliğinizi açıklayın';

  @override
  String get descriptionRequired => 'Lütfen bir açıklama girin';

  @override
  String durationLabel(int duration) {
    return 'Süre: $duration dakika';
  }

  @override
  String durationMinutes(int minutes) {
    return '$minutes dk';
  }

  @override
  String get healthBenefitsLabel => 'Sağlık Faydaları';

  @override
  String get healthBenefitsHint => 'Bu etkinliğin sağlık faydalarını açıklayın';

  @override
  String get healthBenefitsRequired => 'Lütfen sağlık faydalarını açıklayın';

  @override
  String get healthBenefitsDialogTitle => 'Sağlık Faydaları';

  @override
  String get addNewHealthBenefitHint => 'Yeni sağlık faydası ekle';

  @override
  String get selectedBenefitsLabel => 'Seçilen Faydalar:';

  @override
  String get suggestedBenefitsLabel => 'Önerilen Faydalar:';

  @override
  String get categoryLabel => 'Kategori';

  @override
  String get languageLabel => 'Dil';

  @override
  String get selectLanguageHint => 'Dil seçin';

  @override
  String get recommendedForMoodsLabel => 'Şu ruh halleri için önerilir';

  @override
  String get selectAtLeastOneMood => '(en az 1 seçin)';

  @override
  String get moodSelectionError => 'Lütfen en az 1 ruh hali seçin';

  @override
  String get healthBenefitsSelectionError =>
      'Lütfen en az 2 sağlık faydası seçin';

  @override
  String get privacySettingsLabel => 'Gizlilik ayarları';

  @override
  String get makeActivityPrivateLabel => 'Etkinliği özel yap';

  @override
  String get privateActivityDescription =>
      'Bu etkinliği sadece siz görebilirsiniz';

  @override
  String get showYourNameLabel => 'İsminizi gösterin';

  @override
  String get showNameDescription => 'İsminiz oluşturucu olarak görüntülenecek';

  @override
  String get createActivityButton => 'Etkinlik Oluştur';

  @override
  String get activityCreatedSuccess => 'Etkinlik başarıyla oluşturuldu';

  @override
  String get loginRequiredError =>
      'Etkinlik oluşturmak için giriş yapmalısınız';

  @override
  String createActivityError(String error) {
    return 'Etkinlik oluşturma başarısız: $error';
  }

  @override
  String get selectYourMood => 'Ruh halinizi seçin';

  @override
  String get commonMoods => 'Yaygın ruh halleri:';

  @override
  String get allMoodOptions => 'Tüm ruh hali seçenekleri:';

  @override
  String get moreMoodsButton => 'Daha Fazla';

  @override
  String get lessMoodsButton => 'Daha Az';

  @override
  String get noMoodSelected => 'Ruh hali seçilmedi';

  @override
  String get selectMoodAbove => 'Yukarıdan bir ruh hali seçin';

  @override
  String get moodSelectionDescription =>
      'Kendinizi daha iyi hissetmenize veya pozitif kalmanıza yardımcı olacak etkinlikler önereceğiz';

  @override
  String get failedToLoadActivities => 'Etkinlikler yüklenemedi';

  @override
  String get noActivitiesFound => 'Etkinlik bulunamadı';

  @override
  String get noActivitiesForMoodDescription =>
      'Mevcut ruh haliniz için etkinlik bulamadık. Farklı bir ruh hali seçmeyi deneyin.';

  @override
  String get noUserActivities => 'Sizin tarafınızdan oluşturulan etkinlik yok';

  @override
  String get noUserActivitiesDescription =>
      'Bu ruh hali için henüz etkinlik oluşturmadınız. Bir tane oluşturun veya filtreyi kapatın.';

  @override
  String get showAllActivities => 'Tüm Etkinlikleri Göster';

  @override
  String suggestionsForMood(String mood, String emoji) {
    return '$mood $emoji için öneriler';
  }

  @override
  String get startButton => 'Aktiviteyi Başlat';

  @override
  String get stopButton => 'Aktiviteyi Durdur';

  @override
  String activityStarted(String activityTitle) {
    return 'Başlatıldı: $activityTitle';
  }

  @override
  String activityStartError(String error) {
    return 'Etkinlik başlatılamadı: $error';
  }

  @override
  String get activityCancelled => 'Aktivite iptal edildi. Puan kazanılmadı.';

  @override
  String activityCancelError(String error) {
    return 'Etkinlik iptal edilirken hata: $error';
  }

  @override
  String get completeCurrentActivityFirst =>
      'Önce mevcut etkinliği tamamlayın veya iptal edin';

  @override
  String get cancelButton => 'İptal';

  @override
  String get applyButton => 'Uygula';

  @override
  String get healthBenefitStress => 'Stres ve kaygıyı azaltır';

  @override
  String get healthBenefitCardio => 'Kardiyovasküler sağlığı geliştirir';

  @override
  String get healthBenefitClarity => 'Zihinsel netliği artırır';

  @override
  String get healthBenefitImmune => 'Bağışıklık sistemini güçlendirir';

  @override
  String get healthBenefitSleep => 'Uyku kalitesini artırır';

  @override
  String get healthBenefitStrength => 'Fiziksel gücü artırır';

  @override
  String get healthBenefitMindful => 'Farkındalığı teşvik eder';

  @override
  String get healthBenefitDigital => 'Dijital bağımlılığı azaltır';

  @override
  String get healthBenefitFocus => 'Odak ve konsantrasyonu artırır';

  @override
  String get healthBenefitEmotional => 'Duygusal refahı teşvik eder';

  @override
  String get healthBenefitEnergy => 'Enerji seviyelerini artırır';

  @override
  String get healthBenefitCreativity => 'Yaratıcılığı teşvik eder';

  @override
  String get healthBenefitPosture => 'Duruşu iyileştirir';

  @override
  String get healthBenefitWeight => 'Kilo yönetimini destekler';

  @override
  String get foodMoodExplanationTitle => 'Biliyor muydun?';

  @override
  String get foodMoodExplanationText =>
      'Yediğiniz yiyecekler, ruh halinizi ve zihinsel refahınızı önemli ölçüde etkileyebilir. Besin değeri yüksek gıdalar beyin fonksiyonunu destekler ve duyguları düzenlemeye yardımcı olurken, işlenmiş gıdalar ruh hali dalgalanmalarına katkıda bulunabilir.';

  @override
  String get noRecommendationsFound => 'Öneri bulunamadı';

  @override
  String get tryDifferentMood =>
      'Farklı bir ruh hali seçmeyi veya filtrelerinizi ayarlamayı deneyin.';

  @override
  String get description => 'Açıklama';

  @override
  String get benefits => 'Faydalar';

  @override
  String get categories => 'Kategoriler';

  @override
  String get hideDownvotedItems => 'Çok Fazla Olumsuz Oy Alan Öğeleri Gizle';

  @override
  String get hideDownvotedDescription =>
      'Çok fazla olumsuz oy alan öğeleri gizle';

  @override
  String get recommendedForMoods => 'Önerilen Ruh Halleri';

  @override
  String get nutritionalInfo => 'Besin Değerleri';

  @override
  String get ingredients => 'Malzemeler';

  @override
  String get preparationSteps => 'Hazırlık Adımları';

  @override
  String get healthyFood => 'Sağlıklı Yiyecek';

  @override
  String get moodHappy => 'Mutlu';

  @override
  String get moodSad => 'Üzgün';

  @override
  String get moodAngry => 'Kızgın';

  @override
  String get moodAnxious => 'Endişeli';

  @override
  String get moodTired => 'Yorgun';

  @override
  String get moodStressed => 'Stresli';

  @override
  String get moodDepressed => 'Depresif';

  @override
  String get moodExcited => 'Heyecanlı';

  @override
  String get moodCalm => 'Sakin';

  @override
  String get moodBored => 'Sıkılmış';

  @override
  String get foodCategoryProtein => 'Protein açısından zengin';

  @override
  String get foodCategoryHealthyFats => 'Sağlıklı Yağlar';

  @override
  String get foodCategoryComplexCarbs => 'Kompleks Karbonhidratlar';

  @override
  String get foodCategoryVitamins => 'Vitamin açısından zengin';

  @override
  String get foodCategoryMinerals => 'Mineraller';

  @override
  String get foodCategoryOmega3 => 'Omega-3';

  @override
  String get foodCategoryProbiotics => 'Probiyotikler';

  @override
  String get foodCategoryCalming => 'Sakinleştirici';

  @override
  String get foodCategoryEnergyBoosting => 'Enerji Veren';

  @override
  String get foodCategoryAntioxidants => 'Antioksidanlar';

  @override
  String get activitiesTitle => 'Aktiviteler';

  @override
  String helloUser(String userName) {
    return 'Merhaba, $userName';
  }

  @override
  String goalTag(String goal) {
    return '$goal';
  }

  @override
  String get familyTag => 'Aile';

  @override
  String get childrenTag => 'Çocuklar';

  @override
  String get motivationChildrenFocus =>
      'Bugün çocukların için harika bir örneksin! Dikkatin, onlara anda olmanın gücünü gösteriyor.';

  @override
  String get motivationChildrenSleep =>
      'Daha iyi uyku, ailen için daha fazla var olman demek. Dijital sınırlarına devam et!';

  @override
  String get motivationChildrenGeneric =>
      'Çocukların telefonu bıraktığını fark ediyor. Onlarla her anın önemi büyük!';

  @override
  String get motivationFocusHigh =>
      'Bugün inanılmaz bir odaklanma! Zihnin berrak ve verimliliğin artıyor. Böyle devam et!';

  @override
  String get motivationFocusLow =>
      'Ekrandan uzak her an beyninin daha iyi odaklanmasına yardımcı oluyor. Bunu başarabilirsin!';

  @override
  String get motivationSleep =>
      'Her dijital mola ile uyku kaliten artıyor. Bedenin ve zihnin sana teşekkür ediyor!';

  @override
  String get motivationGeneric =>
      'Küçük adımlar büyük değişimlere yol açar. Her dijital mola ile beynini yeniden şekillendiriyorsun. Devam et!';

  @override
  String get sortNotifications => 'Bildirimleri Sırala';

  @override
  String get byDateNewestFirst => 'Tarihe Göre (yeniden eskiye)';

  @override
  String get unreadFirst => 'Okunmayanlar Önce';

  @override
  String unreadCount(int count) {
    return '$count okunmamış';
  }

  @override
  String get today => 'Bugün';

  @override
  String get yesterday => 'Dün';

  @override
  String get delete => 'Sil';

  @override
  String get markRead => 'Okundu İşaretle';

  @override
  String get markUnread => 'Okunmadı İşaretle';

  @override
  String get couponDetails => 'Kupon Detayları';

  @override
  String get aboutPartner => 'İş Ortağı Hakkında';

  @override
  String get address => 'Adres';

  @override
  String get openInMaps => 'Haritalarda Aç';

  @override
  String get howToUse => 'Nasıl Kullanılır';

  @override
  String get termsAndConditions => 'Şartlar ve Koşullar';

  @override
  String get validUntil => 'Geçerlilik tarihi';

  @override
  String get redeemCoupon => 'Kuponu Kullan';

  @override
  String redeemFor(int points) {
    return '$points Puan Karşılığında Kullan';
  }

  @override
  String redeemConfirmation(String discount, String partner, int points) {
    return '$partner firmasının $discount indirim kuponunu kullanmak istiyor musunuz? Bu size $points puana mal olacak.';
  }

  @override
  String get cancel => 'İptal';

  @override
  String get redeem => 'Kullan';

  @override
  String get openingInMaps => 'Haritalarda açılıyor...';

  @override
  String get shareFeatureComingSoon => 'Paylaşım özelliği yakında geliyor!';

  @override
  String get activityDetails => 'Aktivite Detayları';

  @override
  String get activityCompleted => 'Activity Completed!';

  @override
  String activityCompletedMessage(String title) {
    return 'Tebrikler! \"$title\" aktivitesini tamamladınız ve puan kazandınız!';
  }

  @override
  String get ok => 'Tamam';

  @override
  String get addToFavorites => 'Favorilere ekle';

  @override
  String get removedFromFavorites => 'Favorilerden çıkarıldı';

  @override
  String get addedToFavorites => 'Favorilere eklendi';

  @override
  String get commentAdded => 'Yorum eklendi';

  @override
  String get activityInProgress => 'Aktivite Devam Ediyor';

  @override
  String get remaining => 'kalan';

  @override
  String get elapsed => 'geçen';

  @override
  String get stayFocused =>
      'Daha fazla Detox Puanı kazanmak için odaklanmaya devam edin!';

  @override
  String get activityPoints => 'Aktivite Puanları';

  @override
  String get points => 'puan';

  @override
  String get completeToEarn => 'Puan kazanmak için bu aktiviteyi tamamlayın!';

  @override
  String get aboutThisActivity => 'Bu aktivite hakkında';

  @override
  String get healthBenefits => 'Sağlık Faydaları';

  @override
  String get noHealthBenefitsListed =>
      'Bu aktivite için listelenmiş sağlık faydası yok.';

  @override
  String get recommendedFor => 'Şunlar için önerilir';

  @override
  String get noCommentsYet => 'Henüz yorum yok';

  @override
  String get beFirstToComment => 'Deneyiminizi paylaşan ilk kişi olun';

  @override
  String get shareYourExperience => 'Deneyiminizi paylaşın...';

  @override
  String get justNow => 'az önce';

  @override
  String minutesAgo(int minutes) {
    return '${minutes}d önce';
  }

  @override
  String hoursAgo(int hours) {
    return '${hours}s önce';
  }

  @override
  String daysAgo(int days) {
    return '${days}g önce';
  }

  @override
  String get activityComplete => 'Aktivite Tamamlandı';

  @override
  String get comments => 'Yorumlar';

  @override
  String createdBy(String name) {
    return 'Oluşturan: $name';
  }

  @override
  String get detoxBenefits => 'Detoks Faydaları';

  @override
  String get targetedMoods => 'Hedeflenen Ruh Halleri';

  @override
  String started(String name) {
    return 'Başladı: $name';
  }

  @override
  String failedToStartActivity(String error) {
    return 'Aktivite başlatılamadı: $error';
  }

  @override
  String pointsValue(int value) {
    return '$value puan';
  }

  @override
  String get upvotes => 'Beğeniler';

  @override
  String get downvotes => 'Beğenmeyenler';

  @override
  String get nutritionInfo => 'Besin Değerleri';

  @override
  String get calories => 'Kaloriler';

  @override
  String get protein => 'Protein';

  @override
  String get carbs => 'Karbonhidratlar';

  @override
  String get fats => 'Yağlar';

  @override
  String get moodFilter => 'Filter by Mood';

  @override
  String get networkStatusOnline => 'Online';

  @override
  String get networkStatusOffline => 'Offline';

  @override
  String get networkStatusChecking => 'Checking network...';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get foodImageLoadErrorTitle => 'Image Not Available';

  @override
  String get foodImageLoadErrorMessage =>
      'Could not load image. Please try again later.';

  @override
  String get suggestionsFor => 'Suggestions for';

  @override
  String get tipsAndTricks => 'Tips & Tricks';

  @override
  String get activityTips =>
      'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.';

  @override
  String get loading => 'Loading...';

  @override
  String get foodRecommendations => 'Food Recommendations';

  @override
  String get timeToDisconnect => 'Time to Disconnect';

  @override
  String get activitiesMotivationalMessage =>
      'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.';

  @override
  String get earnPointsForActivities =>
      'Earn points for every activity completed';

  @override
  String get details => 'Detaylar';

  @override
  String get discussion => 'Tartışma';

  @override
  String get editComment => 'Yorumunuzu düzenleyin...';

  @override
  String get commentLikeNotification => 'Yorum Beğeni Bildirimleri';

  @override
  String get commentLikeNotificationDescription =>
      'Birisi yorumunuzu beğendiğinde bildirim alın';
}
