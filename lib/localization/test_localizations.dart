import 'package:flutter/material.dart';
import 'package:detoxme/localization/app_localizations.dart';

class LocalizationTest extends StatelessWidget {
  const LocalizationTest({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Print all the new translations to check if they're accessible
    debugPrint('comingSoon: ${l10n.comingSoon}');
    debugPrint('detoxRewards: ${l10n.detoxRewards}');
    debugPrint('newLabel: ${l10n.newLabel}');
    debugPrint('rewardsDescription: ${l10n.rewardsDescription}');
    debugPrint('goodMorning: ${l10n.goodMorning}');
    debugPrint('goodAfternoon: ${l10n.goodAfternoon}');
    debugPrint('goodEvening: ${l10n.goodEvening}');
    debugPrint('currentPointsLabel: ${l10n.currentPointsLabel}');
    debugPrint('targetPointsLabel: ${l10n.targetPointsLabel}');
    debugPrint('maxPointsLabel: ${l10n.maxPointsLabel}');
    debugPrint('detoxActivities: ${l10n.detoxActivities}');
    debugPrint(
      'defaultActivitiesDescription: ${l10n.defaultActivitiesDescription}',
    );
    debugPrint('beginnerActivities: ${l10n.beginnerActivities}');
    debugPrint('intermediateActivities: ${l10n.intermediateActivities}');
    debugPrint('expertActivities: ${l10n.expertActivities}');
    debugPrint('loadingDashboard: ${l10n.loadingDashboard}');
    debugPrint('noActivitiesAvailable: ${l10n.noActivitiesAvailable}');
    debugPrint('activityInProgressError: ${l10n.activityInProgressError}');
    debugPrint('startActivity: ${l10n.startActivity}');
    debugPrint('moodOMeterTitle: ${l10n.moodOMeterTitle}');
    debugPrint('moodOMeterInstructions: ${l10n.moodOMeterInstructions}');

    return const Placeholder();
  }
}
