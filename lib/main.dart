import 'package:detoxme/core/init/app_initializer.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/core/themes/theme_cubit.dart';
import 'package:detoxme/core/themes/app_themes.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/core/constants/app_constants.dart';
import 'package:detoxme/core/network/network_status_cubit.dart';
import 'package:detoxme/core/network/network_status_state.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:detoxme/core/services/logger_service.dart';
import 'package:detoxme/core/services/analytics_service.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/core/services/sync_service.dart';

// Global variables are now managed by ServiceInitializer class
// No need for redundant global declarations

/// Entry point of the application
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Set status bar to show white icons
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light, // For Android (dark icons)
      statusBarBrightness: Brightness.dark, // For iOS (dark icons)
    ),
  );
  
  // Initialize application (this also initializes services internally)
  await AppInitializer.initialize();
  
  // Run the app
  runApp(const MyApp());
}

/// Main application widget
class MyApp extends StatelessWidget {
  /// Creates the main application widget
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AppLocaleProvider>.value(
      value: serviceLocator<AppLocaleProvider>(),
      child: MultiBlocProvider(
        providers: [
          // Provide all Cubits/Blocs using serviceLocator
          BlocProvider(create: (_) => serviceLocator<NetworkStatusCubit>()),
          BlocProvider(create: (_) => serviceLocator<AuthCubit>()),
          BlocProvider(create: (_) => serviceLocator<ProfileCubit>()),
          BlocProvider(create: (_) => serviceLocator<DetoxPointsCubit>()),
          BlocProvider(create: (_) => serviceLocator<ActivityCubit>()),
          BlocProvider(create: (_) => serviceLocator<FoodMoodCubit>()),
          BlocProvider(create: (_) => serviceLocator<DashboardCubit>()),
          BlocProvider(create: (_) => serviceLocator<RewardCubit>()),
          BlocProvider(create: (_) => serviceLocator<MoodQuoteCubit>()),
          BlocProvider<ThemeCubit>(create: (_) => ThemeCubit()),
        ],
        child: Builder(
          builder: (context) {
            return MultiBlocListener(
              listeners: [
                BlocListener<AuthCubit, auth_state.AuthState>(
                  listener: (context, authState) {
                    // When authenticated, load user data
                    if (authState is auth_state.Authenticated) {
                      final userId = authState.user.id;

                      // Set user ID for analytics
                      serviceLocator<AnalyticsService>().setUserId(userId);

                      // Load initial data via Cubits
                      context.read<DetoxPointsCubit>().loadPointsAndHistory();
                      context.read<DetoxPointsCubit>().loadAvailableRewards();
                      context.read<ProfileCubit>().loadProfile(userId);
                      context.read<ActivityCubit>().loadActivities();
                      context.read<FoodMoodCubit>().loadFoodRecommendations();
                      
                      // Ensure cubits are connected
                      final dashboardCubit = context.read<DashboardCubit>();
                      final activityCubit = context.read<ActivityCubit>();
                      final foodMoodCubit = context.read<FoodMoodCubit>();

                      // Connect cubits if not already connected
                      dashboardCubit.setActivityCubit(activityCubit);
                      dashboardCubit.setFoodMoodCubit(foodMoodCubit);

                      // Load dashboard data (after connections are established)
                      dashboardCubit.loadDashboard();
                      
                      context.read<RewardCubit>().loadPlayerStats();

                    } else if (authState is auth_state.Unauthenticated) {
                      // Clear user ID for analytics on logout
                      serviceLocator<AnalyticsService>().clearUserId();
                    }
                  },
                ),
                BlocListener<NetworkStatusCubit, NetworkStatusState>(
                  listener: (context, state) {
                    // Process sync queue when network reconnects
                    if (state is NetworkOnline) {
                      // Use GetIt to access SharedPreferences and AuthCubit state
                      final prefs = serviceLocator<SharedPreferences>();
                      final onboardingComplete =
                          prefs.getBool(AppConstants.onboardingCompleteKey) ??
                          false;
                      final currentAuthState =
                          serviceLocator<AuthCubit>().state;

                      if (onboardingComplete &&
                          (currentAuthState is auth_state.Authenticated ||
                              currentAuthState is auth_state.AuthAnonymous)) {
                        serviceLocator<LoggerService>().info(
                          'NetworkStatus',
                          'Network reconnected. Triggering sync queue processing...',
                        );
                        // Use GetIt to access SyncService
                        serviceLocator<SyncService>().processUpdateQueue();

                        // Refresh food recommendations when back online
                        serviceLocator<FoodMoodCubit>()
                            .loadFoodRecommendations();
                      }
                    }
                  },
                ),
              ],
              child: BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  // Use Consumer to access AppLocaleProvider
                  return Consumer<AppLocaleProvider>(
                    builder: (context, localeProvider, _) {
                      return MaterialApp.router(
                        debugShowCheckedModeBanner: false,
                        key: ValueKey(
                          'app_${localeProvider.locale.languageCode}',
                        ),
                        routerConfig: router,
                        onGenerateTitle:
                            (context) => AppLocalizations.of(context)!.appTitle,
                        theme: AppThemes.lightTheme,
                        darkTheme: AppThemes.darkTheme,
                        themeMode: context.read<ThemeCubit>().themeMode,
                        locale: localeProvider.locale,
                        localizationsDelegates: const [
                          AppLocalizations.delegate,
                          GlobalMaterialLocalizations.delegate,
                          GlobalWidgetsLocalizations.delegate,
                          GlobalCupertinoLocalizations.delegate,
                        ],
                        supportedLocales: AppLocaleProvider.supportedLocales,
                      );
                    },
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
