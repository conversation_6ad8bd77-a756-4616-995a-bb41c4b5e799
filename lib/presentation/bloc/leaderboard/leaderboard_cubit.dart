import 'package:detoxme/domain/repositories/leaderboard_repository.dart';
import 'package:detoxme/presentation/bloc/leaderboard/leaderboard_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LeaderboardCubit extends Cubit<LeaderboardState> {
  final LeaderboardRepository leaderboardRepository;

  LeaderboardCubit({required this.leaderboardRepository})
    : super(LeaderboardInitial());

  Future<void> fetchLeaderboard() async {
    emit(LeaderboardLoading());
    final result = await leaderboardRepository.getLeaderboard();
    result.fold(
      (failure) => emit(LeaderboardError(failure)),
      (entries) => emit(LeaderboardLoaded(entries)),
    );
  }
}
