import 'package:detoxme/domain/entities/leaderboard_entry.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:equatable/equatable.dart';

abstract class LeaderboardState extends Equatable {
  const LeaderboardState();

  @override
  List<Object> get props => [];
}

class LeaderboardInitial extends LeaderboardState {}

class LeaderboardLoading extends LeaderboardState {}

class LeaderboardLoaded extends LeaderboardState {
  final List<LeaderboardEntry> entries;

  const LeaderboardLoaded(this.entries);

  @override
  List<Object> get props => [entries];
}

class LeaderboardError extends LeaderboardState {
  final Failure failure;

  const LeaderboardError(this.failure);

  @override
  List<Object> get props => [failure];
}
