part of 'settings_cubit.dart';

/// State for settings cubit (excluding theme which is handled by ThemeCubit)
class SettingsState {
  /// Whether all notifications are enabled
  final bool allNotificationsEnabled;

  /// Map of mood to notification preference
  final Map<String, bool> moodNotificationPreferences;

  /// Map of notification type to preference
  final Map<NotificationType, bool> notificationTypePreferences;

  /// Creates a [SettingsState]
  const SettingsState({
    this.allNotificationsEnabled = true,
    this.moodNotificationPreferences = const {},
    this.notificationTypePreferences = const {},
  });

  /// Create a copy with updated fields
  SettingsState copyWith({
    bool? allNotificationsEnabled,
    Map<String, bool>? moodNotificationPreferences,
    Map<NotificationType, bool>? notificationTypePreferences,
  }) {
    return SettingsState(
      allNotificationsEnabled:
          allNotificationsEnabled ?? this.allNotificationsEnabled,
      moodNotificationPreferences:
          moodNotificationPreferences ?? this.moodNotificationPreferences,
      notificationTypePreferences:
          notificationTypePreferences ?? this.notificationTypePreferences,
    );
  }
}
