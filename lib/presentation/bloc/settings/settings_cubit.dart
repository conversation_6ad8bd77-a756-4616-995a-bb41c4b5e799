import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'settings_state.dart';

/// Notification types that can be toggled
enum NotificationType {
  /// New activities
  activities,

  /// New coupons
  coupons,

  /// Reminders to complete activities
  reminders,

  /// Daily motivational messages
  motivation,

  /// Comment likes on user posts
  commentLike,
}

/// Cubit for managing app settings (excluding theme which is handled by ThemeCubit)
class SettingsCubit extends Cubit<SettingsState> {
  /// Shared preferences instance for persistent storage
  final SharedPreferences _prefs;

  /// Key for storing all notifications enabled flag
  static const String _allNotificationsKey = 'all_notifications_enabled';

  /// Prefix for storing mood notification preferences
  static const String _moodNotificationPrefix = 'mood_notification_';

  /// Prefix for storing notification type preferences
  static const String _notificationTypePrefix = 'notification_type_';

  /// Creates a [SettingsCubit]
  SettingsCubit(this._prefs) : super(const SettingsState()) {
    _loadSettings();
  }

  /// Load all settings from storage
  void _loadSettings() {
    // Load notifications settings
    final allNotificationsEnabled =
        _prefs.getBool(_allNotificationsKey) ?? true;

    // Load mood notification preferences
    final moodNotificationPreferences = <String, bool>{};
    final moodTypes = ['happy', 'sad', 'anxious', 'tired', 'focused'];

    for (final mood in moodTypes) {
      final prefKey = '$_moodNotificationPrefix$mood';
      moodNotificationPreferences[mood] = _prefs.getBool(prefKey) ?? true;
    }

    // Load notification type preferences
    final notificationTypePreferences = <NotificationType, bool>{};
    for (final type in NotificationType.values) {
      final prefKey = '$_notificationTypePrefix${type.name}';
      notificationTypePreferences[type] = _prefs.getBool(prefKey) ?? true;
    }

    emit(
      SettingsState(
        allNotificationsEnabled: allNotificationsEnabled,
        moodNotificationPreferences: moodNotificationPreferences,
        notificationTypePreferences: notificationTypePreferences,
      ),
    );
  }

  /// Toggle all notifications
  Future<void> toggleAllNotifications(bool enabled) async {
    if (state.allNotificationsEnabled == enabled) return;

    // Save to preferences
    await _prefs.setBool(_allNotificationsKey, enabled);

    // Update state
    emit(state.copyWith(allNotificationsEnabled: enabled));
  }

  /// Toggle notification for a specific mood
  Future<void> toggleMoodNotification(String mood, bool enabled) async {
    final currentPreferences = Map<String, bool>.from(
      state.moodNotificationPreferences,
    );

    if (currentPreferences[mood] == enabled) return;

    // Update preferences map
    currentPreferences[mood] = enabled;

    // Save to preferences
    await _prefs.setBool('$_moodNotificationPrefix$mood', enabled);

    // Update state
    emit(state.copyWith(moodNotificationPreferences: currentPreferences));
  }

  /// Toggle notification for a specific type
  Future<void> toggleNotificationType(
    NotificationType type,
    bool enabled,
  ) async {
    final currentPreferences = Map<NotificationType, bool>.from(
      state.notificationTypePreferences,
    );

    if (currentPreferences[type] == enabled) return;

    // Update preferences map
    currentPreferences[type] = enabled;

    // Save to preferences
    await _prefs.setBool('$_notificationTypePrefix${type.name}', enabled);

    // Update state
    emit(state.copyWith(notificationTypePreferences: currentPreferences));
  }
}
