import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../domain/entities/activity.dart';
import '../../../domain/entities/completed_activity.dart';
import '../../../domain/entities/mood.dart';
import '../../../domain/repositories/activity_repository.dart' as repo;

/// Status of the activity feature
enum ActivityStatus {
  /// Initial state
  initial,

  /// Loading data
  loading,

  /// Success, data loaded
  success,

  /// Error occurred
  error,
}

/// Sorting options for activities
enum ActivitySortOption {
  /// Sort by newest
  newest,

  /// Sort by most popular
  mostPopular,

  /// Sort by most upvoted
  mostUpvoted,

  /// Sort by highest rated
  highestRated,

  /// Sort by votes ascending
  votesAscending,

  /// Sort by votes descending
  votesDescending,
}

/// Convert from UI sort option to repository sort option
repo.ActivitySortOption convertToRepoSortOption(ActivitySortOption sortOption) {
  switch (sortOption) {
    case ActivitySortOption.newest:
      return repo.ActivitySortOption.newest;
    case ActivitySortOption.mostPopular:
      return repo.ActivitySortOption.mostPopular;
    case ActivitySortOption.mostUpvoted:
      return repo.ActivitySortOption.mostUpvoted;
    case ActivitySortOption.highestRated:
      return repo.ActivitySortOption.highestRated;
    // Fallback to newest for the new options until repository supports them
    default:
      return repo.ActivitySortOption.newest;
  }
}

/// State for the Activity feature
class ActivityState extends Equatable {
  /// Current status
  final ActivityStatus status;

  /// Error message if status is error
  final String? errorMessage;

  /// List of all activities
  final List<Activity>? activities;

  /// List of activities filtered by mood
  final List<Activity>? filteredActivities;

  /// Activities created by the user
  final List<Activity>? userActivities;

  /// List of completed activities
  final List<CompletedActivity>? completedActivities;

  /// ID of the currently active activity
  final String? activeActivityId;

  /// ID of the most recently completed activity
  final String? completedActivityId;

  /// Current mood selection
  final MoodType? selectedMood;

  /// List of mood history entries
  final List<Mood>? moodHistory;

  /// Sorting option
  final ActivitySortOption sortOption;

  /// Current search query for filtering
  final String? searchQuery;

  /// Category filter
  final ActivityCategory? categoryFilter;

  /// Country codes for filtering
  final List<String>? countryCodes;

  /// Remaining duration of the active activity
  final Duration? remainingDuration;

  /// Whether the active activity is paused
  final bool isPaused;

  /// Duration range for filtering (min, max) in minutes
  final RangeValues? durationRange;

  /// Creates an ActivityState instance
  const ActivityState({
    this.status = ActivityStatus.initial,
    this.errorMessage,
    this.activities,
    this.filteredActivities,
    this.userActivities,
    this.completedActivities,
    this.activeActivityId,
    this.completedActivityId,
    this.selectedMood,
    this.moodHistory,
    this.sortOption = ActivitySortOption.newest,
    this.searchQuery,
    this.categoryFilter,
    this.countryCodes,
    this.remainingDuration,
    this.isPaused = false,
    this.durationRange,
  });

  /// Creates a copy of this state with the given fields replaced with new values
  ActivityState copyWith({
    ActivityStatus? status,
    String? errorMessage,
    List<Activity>? activities,
    List<Activity>? filteredActivities,
    List<Activity>? userActivities,
    List<CompletedActivity>? completedActivities,
    String? activeActivityId,
    String? completedActivityId,
    MoodType? selectedMood,
    List<Mood>? moodHistory,
    ActivitySortOption? sortOption,
    String? searchQuery,
    ActivityCategory? categoryFilter,
    List<String>? countryCodes,
    Duration? remainingDuration,
    bool? isPaused,
    RangeValues? durationRange,
  }) {
    return ActivityState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      activities: activities ?? this.activities,
      filteredActivities: filteredActivities ?? this.filteredActivities,
      userActivities: userActivities ?? this.userActivities,
      completedActivities: completedActivities ?? this.completedActivities,
      activeActivityId: activeActivityId ?? this.activeActivityId,
      completedActivityId: completedActivityId ?? this.completedActivityId,
      selectedMood: selectedMood ?? this.selectedMood,
      moodHistory: moodHistory ?? this.moodHistory,
      sortOption: sortOption ?? this.sortOption,
      searchQuery: searchQuery ?? this.searchQuery,
      categoryFilter: categoryFilter ?? this.categoryFilter,
      countryCodes: countryCodes ?? this.countryCodes,
      remainingDuration: remainingDuration ?? this.remainingDuration,
      isPaused: isPaused ?? this.isPaused,
      durationRange: durationRange ?? this.durationRange,
    );
  }

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    activities,
    filteredActivities,
    userActivities,
    completedActivities,
    activeActivityId,
    completedActivityId,
    selectedMood,
    moodHistory,
    sortOption,
    searchQuery,
    categoryFilter,
    countryCodes,
    remainingDuration,
    isPaused,
    durationRange,
  ];
}
