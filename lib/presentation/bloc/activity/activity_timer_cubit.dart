import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/goal_status.dart';
import 'package:detoxme/domain/usecases/complete_activity_usecase.dart';
import 'package:detoxme/domain/usecases/get_current_user_usecase.dart';

/// State for activity timer
class ActivityTimerState extends Equatable {
  /// The current activity being performed
  final Activity? currentActivity;

  /// Whether the timer is currently running
  final bool isRunning;

  /// Time elapsed since starting the activity
  final Duration elapsedTime;

  /// Whether the activity is completed
  final bool isCompleted;

  /// Status of goal achievement after completing an activity
  final GoalStatus goalStatus;

  /// Points earned from the activity
  final int earnedPoints;

  /// Creates a new activity timer state
  const ActivityTimerState({
    this.currentActivity,
    this.isRunning = false,
    this.elapsedTime = Duration.zero,
    this.isCompleted = false,
    this.goalStatus = GoalStatus.none,
    this.earnedPoints = 0,
  });

  /// Whether an activity is currently active (not necessarily running)
  bool get isActive => currentActivity != null;

  /// Creates a copy of this state with the specified fields replaced
  ActivityTimerState copyWith({
    Activity? currentActivity,
    bool? isRunning,
    Duration? elapsedTime,
    bool? isCompleted,
    GoalStatus? goalStatus,
    int? earnedPoints,
  }) {
    return ActivityTimerState(
      currentActivity: currentActivity ?? this.currentActivity,
      isRunning: isRunning ?? this.isRunning,
      elapsedTime: elapsedTime ?? this.elapsedTime,
      isCompleted: isCompleted ?? this.isCompleted,
      goalStatus: goalStatus ?? this.goalStatus,
      earnedPoints: earnedPoints ?? this.earnedPoints,
    );
  }

  @override
  List<Object?> get props => [
    currentActivity,
    isRunning,
    elapsedTime,
    isCompleted,
    goalStatus,
    earnedPoints,
  ];
}

/// Cubit for managing activity timer
class ActivityTimerCubit extends Cubit<ActivityTimerState> {
  /// Use case for completing an activity
  final CompleteActivityUseCase _completeActivityUseCase;

  /// Use case for getting the current user
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  /// Timer for tracking elapsed time
  Timer? _timer;

  /// Creates a new activity timer cubit
  ActivityTimerCubit({
    required CompleteActivityUseCase completeActivityUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  }) : _completeActivityUseCase = completeActivityUseCase,
       _getCurrentUserUseCase = getCurrentUserUseCase,
       super(const ActivityTimerState());

  /// Starts an activity timer
  void startActivity(Activity activity) {
    // Cancel any existing timer
    _timer?.cancel();

    emit(
      ActivityTimerState(
        currentActivity: activity,
        isRunning: true,
        elapsedTime: Duration.zero,
      ),
    );

    // Start timer to track elapsed time
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      emit(
        state.copyWith(
          elapsedTime: state.elapsedTime + const Duration(seconds: 1),
        ),
      );
    });
  }

  /// Pauses the activity timer
  void pauseActivity() {
    if (!state.isRunning) return;

    _timer?.cancel();
    emit(state.copyWith(isRunning: false));
  }

  /// Resumes the activity timer
  void resumeActivity() {
    if (state.isRunning || !state.isActive) return;

    emit(state.copyWith(isRunning: true));

    // Restart timer
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      emit(
        state.copyWith(
          elapsedTime: state.elapsedTime + const Duration(seconds: 1),
        ),
      );
    });
  }

  /// Completes the current activity
  Future<void> completeActivity() async {
    if (!state.isActive) return;

    _timer?.cancel();

    // Get the current user
    final userResult = await _getCurrentUserUseCase();

    await userResult.fold(
      (failure) {
        emit(
          state.copyWith(
            isRunning: false,
            isCompleted: true,
            goalStatus: GoalStatus.none,
          ),
        );
      },
      (user) async {
        // Mark activity as completed
        final result = await _completeActivityUseCase(
          userId: user.id,
          activity: state.currentActivity!,
          timeSpent: state.elapsedTime,
          wasAborted: false,
        );

        result.fold(
          (failure) {
            emit(
              state.copyWith(
                isRunning: false,
                isCompleted: true,
                goalStatus: GoalStatus.none,
              ),
            );
          },
          (goalStatus) {
            // Calculate earned points (1 point per minute)
            final earnedPoints = state.elapsedTime.inMinutes;

            emit(
              state.copyWith(
                isRunning: false,
                isCompleted: true,
                goalStatus: goalStatus,
                earnedPoints: earnedPoints,
              ),
            );
          },
        );
      },
    );
  }

  /// Aborts the current activity
  Future<void> abortActivity() async {
    if (!state.isActive) return;

    _timer?.cancel();

    // Only calculate points if at least 15 minutes spent
    final int earnedPoints =
        state.elapsedTime.inMinutes > 15 ? state.elapsedTime.inMinutes : 0;

    if (earnedPoints > 0) {
      // Get the current user
      final userResult = await _getCurrentUserUseCase();

      await userResult.fold(
        (failure) {
          emit(const ActivityTimerState());
        },
        (user) async {
          // Mark activity as aborted but still add points
          final result = await _completeActivityUseCase(
            userId: user.id,
            activity: state.currentActivity!,
            timeSpent: state.elapsedTime,
            wasAborted: true,
          );

          result.fold(
            (failure) {
              emit(const ActivityTimerState());
            },
            (goalStatus) {
              emit(
                ActivityTimerState(
                  isCompleted: true,
                  goalStatus: goalStatus,
                  earnedPoints: earnedPoints,
                ),
              );
            },
          );
        },
      );
    } else {
      // No points earned, just reset
      emit(const ActivityTimerState());
    }
  }

  /// Resets the timer state
  void resetState() {
    _timer?.cancel();
    emit(const ActivityTimerState());
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
