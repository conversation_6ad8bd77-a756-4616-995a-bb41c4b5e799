import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart'; // Import for RangeValues

import '../../../core/services/notification_service.dart';
import '../../../core/services/service_locator.dart';
import '../../../core/services/logger_service.dart';
import '../../../core/utils/app_locale_provider.dart';
import '../../../domain/entities/activity.dart';
import '../../../domain/entities/activity_comment.dart';
import '../../../domain/entities/completed_activity.dart';

import '../../../domain/entities/mood.dart';
import '../../../domain/failure/failure.dart';
import '../../../domain/repositories/activity_repository.dart'
    hide ActivitySortOption;
import '../../../domain/usecases/complete_activity_usecase.dart';
import 'activity_state.dart';
import 'package:uuid/uuid.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;

/// Cubit to manage activities and mood state
class ActivityCubit extends Cubit<ActivityState> {
  /// Repository to access activity data
  final ActivityRepository _activityRepository;

  /// Notification service for showing persistent notifications
  final NotificationService? _notificationService;

  /// App locale provider for language settings
  final AppLocaleProvider? _localeProvider;

  /// Cubit to handle detox points updates
  final DetoxPointsCubit _detoxPointsCubit;

  /// Cubit to handle authentication state
  final AuthCubit _authCubit;
  
  /// Creates a new ActivityCubit
  ActivityCubit({
    required ActivityRepository activityRepository,
    NotificationService? notificationService,
    AppLocaleProvider? localeProvider,
    required DetoxPointsCubit detoxPointsCubit,
    required AuthCubit authCubit,
  }) : _activityRepository = activityRepository,
       _notificationService = notificationService,
       _localeProvider = localeProvider,
       _detoxPointsCubit = detoxPointsCubit,
       _authCubit = authCubit,
       super(const ActivityState());

  Activity? _activeActivity;
  Timer? _activityTimer;
  DateTime? _activityStartTime;
  Duration _remainingDuration = Duration.zero;
  bool _isPaused = false;

  // Store user favorites in memory (would be in repository in a real implementation)
  final Set<String> _favorites = {};

  // Store user votes in memory (would be in repository in a real implementation)
  final Map<String, bool> _userVotes = {}; // activityId -> isUpvote

  Activity? get activeActivity => _activeActivity;
  bool get hasActiveActivity => _activeActivity != null;
  Duration get remainingDuration => _remainingDuration;
  DateTime? get activityStartTime => _activityStartTime;
  bool get isActivityPaused => _isPaused;

  /// Load all activities
  Future<void> loadActivities({
    ActivitySortOption sortOption = ActivitySortOption.newest,
    bool hideDownvoted = true,
  }) async {
    emit(state.copyWith(status: ActivityStatus.loading));

    // Get current language code from locale provider
    final String languageCode = _localeProvider?.locale.languageCode ?? 'en';
    final List<String> countryCodes = [languageCode];

    final result = await _activityRepository.getFilteredActivities(
      sortOption: convertToRepoSortOption(sortOption),
      hideDownvoted: hideDownvoted,
      countryCodes: countryCodes,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (activities) {
        // Update activities with user votes and favorites
        final updatedActivities =
            activities.map((activity) {
              final hasUserUpvoted = _userVotes[activity.id] == true;
              final hasUserDownvoted = _userVotes[activity.id] == false;
              final isFavorite = _favorites.contains(activity.id);

              return activity.copyWith(
                hasUserUpvoted: hasUserUpvoted,
                hasUserDownvoted: hasUserDownvoted,
                isFavorite: isFavorite,
              );
            }).toList();
        emit(
          state.copyWith(
            status: ActivityStatus.success,
            activities: updatedActivities,
            sortOption: sortOption,
          ),
        );
      },
    );
  }

  /// Select a mood and load recommended activities
  Future<void> selectMood(MoodType mood) async {
    emit(state.copyWith(status: ActivityStatus.loading, selectedMood: mood));

    // Get current language code from locale provider or use the one from state
    final List<String> countryCodes =
        state.countryCodes ?? [_localeProvider?.locale.languageCode ?? 'en'];

    final result = await _activityRepository.getFilteredActivities(
      mood: mood,
      countryCodes: countryCodes,
      hideDownvoted: true,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (activities) => emit(
        state.copyWith(
          status: ActivityStatus.success,
          filteredActivities: activities,
          countryCodes: countryCodes, // Store the country codes in state
        ),
      ),
    );
  }

  /// Log the user's mood
  Future<void> logMood(String userId, MoodType mood) async {
    final moodEntry = Mood(type: mood, timestamp: DateTime.now());

    final result = await _activityRepository.logMood(userId, moodEntry);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (_) => loadMoodHistory(userId),
    );
  }

  /// Load the user's mood history
  Future<void> loadMoodHistory(String userId) async {
    final result = await _activityRepository.getMoodHistory(userId);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (moodHistory) => emit(state.copyWith(moodHistory: moodHistory)),
    );
  }

  /// Clear selected mood and filtered activities
  void clearMoodSelection() {
    emit(state.copyWith(selectedMood: null, filteredActivities: null));
  }

  /// Create a new user activity
  Future<Either<Failure, Activity>> createActivity({
    required String userId,
    required String title,
    required String description,
    required ActivityCategory category,
    required int duration,
    required List<MoodType> recommendedFor,
    List<String> healthBenefits = const [],
    String? imageUrl,
    String? creatorName,
    bool showCreatorName = true,
    bool isPrivate = false,
    String countryCode = 'en',
  }) async {
    final result = await _activityRepository.createActivity(
      userId: userId,
      title: title,
      description: description,
      category: category,
      duration: duration,
      recommendedFor: recommendedFor,
      healthBenefits: healthBenefits,
      imageUrl: imageUrl,
      creatorName: creatorName,
      showCreatorName: showCreatorName,
      isPrivate: isPrivate,
      countryCode: countryCode,
    );

    // Refresh activities list on success
    result.fold(
      (failure) => null,
      (activity) => loadActivities(sortOption: state.sortOption),
    );

    return result;
  }









  /// Apply sorting and filtering to activities
  Future<void> filterActivities({
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    String? userId,
    List<String>? countryCodes,
    bool hideDownvoted = true,
  }) async {
    // If countryCodes is not provided, use the ones from state or default to current locale
    final effectiveCountryCodes =
        countryCodes ??
        state.countryCodes ??
        [_localeProvider?.locale.languageCode ?? 'en'];

    // Update state to indicate loading and store filter parameters
    emit(
      state.copyWith(
        status: ActivityStatus.loading,
        sortOption: sortOption,
        searchQuery: searchQuery,
        categoryFilter: category,
        countryCodes: effectiveCountryCodes,
      ),
    );

    final result = await _activityRepository.getFilteredActivities(
      mood: state.selectedMood,
      searchQuery: searchQuery,
      category: category,
      sortOption: convertToRepoSortOption(sortOption),
      includePrivate: userId != null,
      userId: userId,
      hideDownvoted: hideDownvoted,
      countryCodes: effectiveCountryCodes,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (activities) => emit(
        state.copyWith(
          status: ActivityStatus.success,
          activities: activities,
          countryCodes:
              effectiveCountryCodes, // Store the country codes in state
        ),
      ),
    );
  }

  /// Start an activity timer
  void startActivity(Activity activity) {
    if (_activeActivity != null) {
      cancelActivity();
    }

    _activeActivity = activity;

    // Special handling for debug activity - if title contains "Debug Activity" and "seconds"
    // then use seconds instead of minutes for the duration
    if (activity.title.contains('Debug Activity') &&
        activity.title.contains('seconds')) {
      // Extract seconds from the title if possible
      final RegExp regex = RegExp(r'(\d+)\s*seconds');
      final match = regex.firstMatch(activity.title);
      if (match != null && match.groupCount >= 1) {
        final seconds = int.tryParse(match.group(1) ?? '') ?? 20;
        _remainingDuration = Duration(seconds: seconds);
        if (kDebugMode) {
          print('Debug activity detected! Setting timer to $seconds seconds');
        }
      } else {
        // Default to 20 seconds for debug activity
        _remainingDuration = const Duration(seconds: 20);
        if (kDebugMode) {
          print('Debug activity detected! Setting timer to 20 seconds');
        }
      }
    } else {
      // Normal activity - use minutes
      _remainingDuration = Duration(minutes: activity.duration);
    }

    _activityStartTime = DateTime.now();
    _isPaused = false;

    // Start the timer
    _activityTimer = Timer.periodic(
      const Duration(seconds: 1),
      _updateActivityTimer,
    );

    // Show notification once at the start
    _showActivityNotification();

    emit(
      state.copyWith(
        activeActivityId: activity.id,
        remainingDuration: _remainingDuration,
      ),
    );
  }

  /// Update the activity timer
  void _updateActivityTimer(Timer timer) {
    if (_remainingDuration.inSeconds <= 0) {
      if (kDebugMode) {
        print('Timer reached zero! Completing activity...');
      }
      completeActivity();
      return;
    }

    _remainingDuration = _remainingDuration - const Duration(seconds: 1);

    // Debug logging for debug activities
    if (_activeActivity?.title.contains('Debug Activity') == true &&
        kDebugMode) {
      if (_remainingDuration.inSeconds <= 5 ||
          _remainingDuration.inSeconds % 5 == 0) {
        serviceLocator<LoggerService>().debug(
          'ActivityTimer',
          'Debug activity timer: ${_remainingDuration.inSeconds} seconds remaining',
        );
      }
    }

    // Only update notification for significant changes (every 5 minutes)
    // This prevents constant vibration or sound alerts
    if (_remainingDuration.inMinutes % 5 == 0 &&
        _remainingDuration.inSeconds % 60 == 0) {
      _showActivityNotification();
    }

    emit(state.copyWith(remainingDuration: _remainingDuration));
  }

  /// Complete the current activity and award points
  Future<void> completeActivity() async {
    if (_activeActivity == null) return;

    _activityTimer?.cancel();
    _activityTimer = null;

    final completedActivity = _activeActivity;
    final activityDuration = DateTime.now().difference(_activityStartTime!);

    _activeActivity = null;
    _activityStartTime = null;
    _remainingDuration = Duration.zero;
    _isPaused = false;

    // Cancel notification
    _notificationService?.cancelActivityNotification();

    // Get the current user ID
    final authState = _authCubit.state;
    if (authState is auth_state.Authenticated && completedActivity != null) {
      final userId = authState.user.id;

      // Use the CompleteActivityUseCase to properly update both activity status and points
      final completeActivityUseCase = serviceLocator<CompleteActivityUseCase>();
      final result = await completeActivityUseCase(
        userId: userId,
        activity: completedActivity,
        timeSpent: activityDuration,
        wasAborted: false,
      );

      // Log the result for debugging
      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Failed to complete activity: ${failure.message}');
          }
        },
        (goalStatus) {
          if (kDebugMode) {
            print('Activity completed with status: $goalStatus');
          }
        },
      );
    } else {
      // Fallback to the old method if authentication state is not available
      if (completedActivity != null) {
        await _detoxPointsCubit.addPoints(
          completedActivity.duration,
          'Completed: ${completedActivity.title}', // Reason for points
        );
      }
    }

    emit(
      state.copyWith(
        activeActivityId: null,
        remainingDuration: Duration.zero,
        completedActivityId: completedActivity?.id,
      ),
    );
  }

  /// Load user's completed activities
  Future<void> loadCompletedActivities(String userId) async {
    emit(state.copyWith(status: ActivityStatus.loading));

    // This would normally call the repository, but for now we'll create mock data
    // In a real implementation, you would call:
    // final result = await _activityRepository.getCompletedActivities(userId);

    // For now, create some sample completed activities
    await Future.delayed(const Duration(milliseconds: 500));

    // Get activities from state or load them if not available
    List<Activity> activities = state.activities ?? [];
    if (activities.isEmpty) {
      await loadActivities();
      activities = state.activities ?? [];
    }

    if (activities.isEmpty) {
      emit(
        state.copyWith(status: ActivityStatus.success, completedActivities: []),
      );
      return;
    }

    // Create sample completed activities using available activities
    final now = DateTime.now();
    final completedActivities = <CompletedActivity>[];

    // Add some completed activities from the past week
    for (int i = 0; i < 5; i++) {
      if (activities.length > i) {
        completedActivities.add(
          CompletedActivity(
            id: const Uuid().v4(),
            userId: userId,
            activity: activities[i],
            completedAt: now.subtract(
              Duration(days: i ~/ 2, hours: (i * 3) % 24),
            ),
            rating: 4 + (i % 2),
          ),
        );
      }
    }

    // Sort by completion date (newest first)
    completedActivities.sort((a, b) => b.completedAt.compareTo(a.completedAt));

    emit(
      state.copyWith(
        status: ActivityStatus.success,
        completedActivities: completedActivities,
      ),
    );
  }

  /// Cancel the current activity
  void cancelActivity() {
    if (_activeActivity == null) return;

    _activityTimer?.cancel();
    _activityTimer = null;
    _activeActivity = null;
    _activityStartTime = null;
    _remainingDuration = Duration.zero;
    _isPaused = false;

    // Cancel notification
    _notificationService?.cancelActivityNotification();

    emit(
      state.copyWith(activeActivityId: null, remainingDuration: Duration.zero),
    );
  }



  /// Show or update the activity notification
  void _showActivityNotification() {
    if (_activeActivity == null || _notificationService == null) return;

    final minutes = _remainingDuration.inMinutes;
    final seconds = _remainingDuration.inSeconds % 60;

    final String statusText =
        _isPaused ? 'Activity paused' : 'Activity in progress';

    _notificationService.showActivityNotification(
      title: _activeActivity!.title,
      body: statusText,
      remainingMinutes: minutes,
      remainingSeconds: seconds,
      isPaused: _isPaused,
      payload: _activeActivity!.id,
    );
  }



  /// Get the remaining time for the current activity
  Duration getRemainingTime() {
    if (_activeActivity == null) return Duration.zero;
    return _remainingDuration;
  }

  /// Get the elapsed time for the current activity
  Duration getElapsedTime() {
    if (_activeActivity == null || _activityStartTime == null) {
      return Duration.zero;
    }

    final totalDuration = Duration(minutes: _activeActivity!.duration);
    return totalDuration - _remainingDuration;
  }

  /// Check if a specific activity is active
  bool isActivityActive(String activityId) {
    return _activeActivity?.id == activityId;
  }

  /// Toggle favorite status for an activity
  Future<void> toggleFavorite(Activity activity) async {
    final isFavorite = !activity.isFavorite;

    if (isFavorite) {
      _favorites.add(activity.id);
    } else {
      _favorites.remove(activity.id);
    }

    // Update activity in state
    final List<Activity> updatedActivities = [];
    if (state.activities != null) {
      updatedActivities.addAll(
        state.activities!.map((a) {
          if (a.id == activity.id) {
            return a.copyWith(isFavorite: isFavorite);
          }
          return a;
        }),
      );
    }

    final List<Activity>? updatedFilteredActivities;
    if (state.filteredActivities != null) {
      updatedFilteredActivities =
          state.filteredActivities!.map((a) {
            if (a.id == activity.id) {
              return a.copyWith(isFavorite: isFavorite);
            }
            return a;
          }).toList();
    } else {
      updatedFilteredActivities = null;
    }

    emit(
      state.copyWith(
        activities: updatedActivities.isEmpty ? null : updatedActivities,
        filteredActivities: updatedFilteredActivities,
      ),
    );

    // Save favorite status to repository
    // Get a user ID - in a real implementation, this would come from auth
    const String userId = 'current-user-id';
    await _activityRepository.setFavorite(activity.id, userId, isFavorite);
  }

  /// Vote on an activity (upvote or downvote)
  void voteActivity(Activity activity, {required bool isUpvote}) {
    final bool hasVoted =
        isUpvote ? activity.hasUserUpvoted : activity.hasUserDownvoted;

    bool hasUserUpvoted = activity.hasUserUpvoted;
    bool hasUserDownvoted = activity.hasUserDownvoted;
    int upvotes = activity.upvotes;
    int downvotes = activity.downvotes;

    // User is removing their existing vote
    if (hasVoted) {
      if (isUpvote) {
        hasUserUpvoted = false;
        upvotes--;
      } else {
        hasUserDownvoted = false;
        downvotes--;
      }
      _userVotes.remove(activity.id);
    }
    // User is changing vote from up to down or vice versa
    else if ((isUpvote && hasUserDownvoted) || (!isUpvote && hasUserUpvoted)) {
      if (isUpvote) {
        hasUserUpvoted = true;
        hasUserDownvoted = false;
        upvotes++;
        downvotes--;
      } else {
        hasUserUpvoted = false;
        hasUserDownvoted = true;
        upvotes--;
        downvotes++;
      }
      _userVotes[activity.id] = isUpvote;
    }
    // User is voting for the first time
    else {
      if (isUpvote) {
        hasUserUpvoted = true;
        upvotes++;
      } else {
        hasUserDownvoted = true;
        downvotes++;
      }
      _userVotes[activity.id] = isUpvote;
    }

    // Update activity in state
    final List<Activity> updatedActivities = [];
    if (state.activities != null) {
      updatedActivities.addAll(
        state.activities!.map((a) {
          if (a.id == activity.id) {
            return a.copyWith(
              hasUserUpvoted: hasUserUpvoted,
              hasUserDownvoted: hasUserDownvoted,
              upvotes: upvotes,
              downvotes: downvotes,
            );
          }
          return a;
        }),
      );
    }

    final List<Activity>? updatedFilteredActivities;
    if (state.filteredActivities != null) {
      updatedFilteredActivities =
          state.filteredActivities!.map((a) {
            if (a.id == activity.id) {
              return a.copyWith(
                hasUserUpvoted: hasUserUpvoted,
                hasUserDownvoted: hasUserDownvoted,
                upvotes: upvotes,
                downvotes: downvotes,
              );
            }
            return a;
          }).toList();
    } else {
      updatedFilteredActivities = null;
    }

    emit(
      state.copyWith(
        activities: updatedActivities.isEmpty ? null : updatedActivities,
        filteredActivities: updatedFilteredActivities,
      ),
    );

    // In a real implementation, update in repository
    // _activityRepository.voteActivity(activity.id, isUpvote);
  }

  /// Get comments for an activity
  Future<List<ActivityComment>> getCommentsForActivity(
    String activityId,
  ) async {
    try {
      final result = await _activityRepository.getComments(activityId);
      return result.fold((failure) {
        if (kDebugMode) {
          print('Failed to load comments: ${failure.message}');
        }
        return <ActivityComment>[];
      }, (comments) => comments);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading comments: $e');
      }
      return <ActivityComment>[];
    }
  }

  /// Get comments for an activity as a stream
  Stream<List<ActivityComment>> getCommentsStreamForActivity(
    String activityId,
  ) {
    return _activityRepository.getCommentsStream(activityId);
  }

  /// Add a comment to an activity
  Future<void> addComment({
    required String activityId,
    required String text,
  }) async {
    final userId = _authCubit.getCurrentUserId();
    final userEmail = _authCubit.getCurrentUserEmail();
    final userPhone = _authCubit.getCurrentUserPhone();
    
    // Create a display name from available information
    String userName = 'Anonymous';
    if (userEmail != null && userEmail.isNotEmpty) {
      // Extract username from email (part before @)
      userName = userEmail.split('@').first;
    } else if (userPhone != null && userPhone.isNotEmpty) {
      // Use last 4 digits of phone number
      userName = 'User${userPhone.substring(userPhone.length - 4)}';
    }

    if (userId == null) return;

    final result = await _activityRepository.addComment(
      activityId: activityId,
      userId: userId,
      userName: userName,
      text: text,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (comment) {
        // Emit a state update to trigger UI refresh
        emit(state.copyWith(status: ActivityStatus.success));
      },
    );
  }

  /// Edit a comment
  Future<void> editComment({
    required String commentId,
    required String activityId,
    required String newText,
  }) async {
    final userId = _authCubit.getCurrentUserId();
    if (userId == null) return;

    final result = await _activityRepository.editComment(
      commentId: commentId,
      userId: userId,
      newText: newText,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (updatedComment) {
        // Emit a state update to trigger UI refresh
        emit(state.copyWith(status: ActivityStatus.success));
      },
    );
  }

  /// Delete a comment
  Future<void> deleteComment({
    required String commentId,
    required String activityId,
  }) async {
    final userId = _authCubit.getCurrentUserId();
    if (userId == null) return;

    final result = await _activityRepository.deleteComment(
      commentId: commentId,
      userId: userId,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (_) {
        // Emit a state update to trigger UI refresh
        emit(state.copyWith(status: ActivityStatus.success));
      },
    );
  }

  /// Toggle like on a comment
  Future<void> toggleCommentLike({
    required String commentId,
    required String activityId,
  }) async {
    final userId = _authCubit.getCurrentUserId();
    if (userId == null) return;

    final result = await _activityRepository.toggleCommentLike(
      commentId: commentId,
      userId: userId,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (updatedComment) {
        // Emit a state update to trigger UI refresh
        emit(state.copyWith(status: ActivityStatus.success));
        
        // Note: Notification will be created automatically by the database trigger
        // when a like is added to the activity_comment_likes table
      },
    );
  }

  /// Report a comment
  Future<void> reportComment({
    required String commentId,
    required String reason,
  }) async {
    final userId = _authCubit.getCurrentUserId();
    if (userId == null) return;

    final result = await _activityRepository.reportComment(
      commentId: commentId,
      userId: userId,
      reason: reason,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (_) {
        // Could show a success message or update UI state
      },
    );
  }

  /// Get all favorite activities
  List<Activity> getFavoriteActivities() {
    if (state.activities == null) return [];

    return state.activities!.where((activity) => activity.isFavorite).toList();
  }

  /// Load favorite activities from the repository
  Future<void> loadFavoriteActivities() async {
    emit(state.copyWith(status: ActivityStatus.loading));

    // Get a user ID - in a real implementation, this would come from auth
    const String userId = 'current-user-id';

    final result = await _activityRepository.getFavoriteActivities(userId);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ActivityStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (favoriteActivities) {
        // Update the _favorites set
        _favorites.clear();
        for (final activity in favoriteActivities) {
          _favorites.add(activity.id);
        }

        emit(
          state.copyWith(
            status: ActivityStatus.success,
            // Update activities with favorite info
            activities:
                state.activities?.map((activity) {
                  if (_favorites.contains(activity.id)) {
                    return activity.copyWith(isFavorite: true);
                  }
                  return activity;
                }).toList(),
          ),
        );
      },
    );
  }

  /// Get activities filtered by difficulty level
  List<Activity> getActivitiesByDifficulty(DifficultyLevel difficultyLevel) {
    // Ensure activities are loaded
    if (state.activities == null) {
      return [];
    }

    // Filter activities by the specified difficulty level
    return state.activities!
        .where((activity) => activity.difficultyLevel == difficultyLevel)
        .toList();
  }





  /// Set the duration range for filtering
  void setDurationRange(RangeValues range) {
    emit(state.copyWith(durationRange: range));

    // If we have activities, apply the filter immediately
    if (state.activities != null && state.activities!.isNotEmpty) {
      final filtered =
          state.activities!.where((activity) {
            return activity.duration >= range.start.round() &&
                activity.duration <= range.end.round();
          }).toList();

      // Apply the duration filter on top of any existing mood filter
      if (state.filteredActivities != null) {
        final moodFiltered =
            state.filteredActivities!.where((activity) {
              return activity.duration >= range.start.round() &&
                  activity.duration <= range.end.round();
            }).toList();

        emit(state.copyWith(filteredActivities: moodFiltered));
      } else {
        emit(state.copyWith(filteredActivities: filtered));
      }
    }
  }



  @override
  Future<void> close() {
    _activityTimer?.cancel();
    _notificationService?.cancelActivityNotification();
    return super.close();
  }
}
