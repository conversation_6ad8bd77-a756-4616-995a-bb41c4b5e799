import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/mood.dart';

import 'package:detoxme/domain/repositories/mood_quote_repository.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_state.dart';
import 'package:flutter/foundation.dart';

/// Cubit for managing mood-based quotes
class MoodQuoteCubit extends Cubit<MoodQuoteState> {
  final MoodQuoteRepository _moodQuoteRepository;

  MoodQuoteCubit({
    required MoodQuoteRepository moodQuoteRepository,
  })  : _moodQuoteRepository = moodQuoteRepository,
        super(const MoodQuoteState());

  /// Gets quotes for the specified mood and language
  Future<void> getQuotesForMood({
    required MoodType moodType,
    required String language,
    int? limit,
  }) async {
    emit(state.copyWith(status: MoodQuoteStatus.loading));

    try {
      final moodString = _moodTypeToString(moodType);
      final result = await _moodQuoteRepository.getQuotesByMood(
        moodType: moodString,
        language: language,
        limit: limit,
      );

      result.fold(
        (failure) {
          debugPrint('Failed to get quotes for mood: ${failure.message}');
          emit(state.copyWith(
            status: MoodQuoteStatus.failure,
            errorMessage: failure.message,
          ));
        },
        (quotes) {
          emit(state.copyWith(
            status: MoodQuoteStatus.success,
            quotes: quotes,
            currentMood: moodType,
          ));
        },
      );
    } catch (e) {
      debugPrint('Error getting quotes for mood: $e');
      emit(state.copyWith(
        status: MoodQuoteStatus.failure,
        errorMessage: 'Failed to load quotes: ${e.toString()}',
      ));
    }
  }

  /// Gets a random quote for the specified mood and language
  Future<void> getRandomQuoteForMood({
    required MoodType moodType,
    required String language,
  }) async {
    emit(state.copyWith(status: MoodQuoteStatus.loading));

    try {
      final moodString = _moodTypeToString(moodType);
      final result = await _moodQuoteRepository.getRandomQuoteByMood(
        moodType: moodString,
        language: language,
      );

      result.fold(
        (failure) {
          debugPrint('Failed to get random quote for mood: ${failure.message}');
          emit(state.copyWith(
            status: MoodQuoteStatus.failure,
            errorMessage: failure.message,
          ));
        },
        (quote) {
          emit(state.copyWith(
            status: MoodQuoteStatus.success,
            quotes: [quote],
            currentMood: moodType,
          ));
        },
      );
    } catch (e) {
      debugPrint('Error getting random quote for mood: $e');
      emit(state.copyWith(
        status: MoodQuoteStatus.failure,
        errorMessage: 'Failed to load quote: ${e.toString()}',
      ));
    }
  }

  /// Updates quotes when mood changes
  Future<void> onMoodChanged({
    required MoodType newMood,
    required String language,
    int? limit,
  }) async {
    if (state.currentMood != newMood) {
      await getQuotesForMood(
        moodType: newMood,
        language: language,
        limit: limit,
      );
    }
  }

  /// Converts MoodType enum to string for API calls
  String _moodTypeToString(MoodType moodType) {
    switch (moodType) {
      case MoodType.happy:
        return 'happy';
      case MoodType.sad:
        return 'sad';
      case MoodType.angry:
        return 'angry';
      case MoodType.anxious:
        return 'anxious';
      case MoodType.calm:
        return 'calm';
      case MoodType.excited:
        return 'excited'; // Now using dedicated excited quotes
      case MoodType.inspired:
        return 'inspired'; // Now using dedicated inspired quotes
      case MoodType.tired:
        return 'tired'; // Now using dedicated tired quotes
      case MoodType.neutral:
        return 'neutral';
    }
  }

  /// Clears the current state
  void clearQuotes() {
    emit(const MoodQuoteState());
  }
}