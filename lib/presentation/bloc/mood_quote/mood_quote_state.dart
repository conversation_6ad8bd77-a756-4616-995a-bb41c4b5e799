import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/mood_quote.dart';

/// Status of mood quote operations
enum MoodQuoteStatus {
  /// Initial state
  initial,
  /// Loading quotes
  loading,
  /// Successfully loaded quotes
  success,
  /// Failed to load quotes
  failure,
}

/// State for mood quote management
class MoodQuoteState extends Equatable {
  /// Current status
  final MoodQuoteStatus status;
  
  /// List of quotes for the current mood
  final List<MoodQuote> quotes;
  
  /// Current mood type
  final MoodType? currentMood;
  
  /// Error message if any
  final String? errorMessage;

  /// Creates a mood quote state
  const MoodQuoteState({
    this.status = MoodQuoteStatus.initial,
    this.quotes = const [],
    this.currentMood,
    this.errorMessage,
  });

  /// Creates a copy of this state with updated values
  MoodQuoteState copyWith({
    MoodQuoteStatus? status,
    List<MoodQuote>? quotes,
    MoodType? currentMood,
    String? errorMessage,
  }) {
    return MoodQuoteState(
      status: status ?? this.status,
      quotes: quotes ?? this.quotes,
      currentMood: currentMood ?? this.currentMood,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        quotes,
        currentMood,
        errorMessage,
      ];
}