import 'package:detoxme/domain/entities/badge.dart';
import 'package:detoxme/domain/entities/collectible_card.dart';
import 'package:detoxme/domain/entities/level.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:equatable/equatable.dart';

abstract class RewardState extends Equatable {
  const RewardState();

  @override
  List<Object?> get props => [];
}

class RewardInitial extends RewardState {}

class RewardLoading extends RewardState {}

// Holds the current player stats and level info
class RewardLoaded extends RewardState {
  final PlayerStats stats;
  final Level currentLevelInfo; // Derived from stats.currentXp
  final int xpForNextLevel; // Derived from stats.currentLevel
  final List<Reward> redeemedRewards; // List of rewards that have been redeemed

  // Optional: Info about recently earned rewards for UI feedback (e.g., showing popups)
  final int? xpGained;
  final Level? levelReached;
  final Badge? badgeEarned;
  final CollectibleCard? cardEarned;

  const RewardLoaded({
    required this.stats,
    required this.currentLevelInfo,
    required this.xpForNextLevel,
    this.redeemedRewards = const [],
    this.xpGained,
    this.levelReached,
    this.badgeEarned,
    this.cardEarned,
  });

  // Helper constructor to clear reward notifications
  RewardLoaded copyWithoutNotifications() => RewardLoaded(
    stats: stats,
    currentLevelInfo: currentLevelInfo,
    xpForNextLevel: xpForNextLevel,
    redeemedRewards: redeemedRewards,
    // All notification fields set to null
  );

  @override
  List<Object?> get props => [
    stats,
    currentLevelInfo,
    xpForNextLevel,
    redeemedRewards,
    xpGained,
    levelReached,
    badgeEarned,
    cardEarned,
  ];
}

class RewardError extends RewardState {
  final Failure failure;
  const RewardError(this.failure);

  @override
  List<Object?> get props => [failure];
}
