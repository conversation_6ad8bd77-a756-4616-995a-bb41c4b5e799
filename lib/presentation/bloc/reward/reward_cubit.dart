import 'package:detoxme/core/game_data.dart'; // Import game data (levels, badges, cards)
import 'package:detoxme/domain/entities/activity.dart'; // Add import for Activity
import 'package:detoxme/domain/entities/badge.dart';
import 'package:detoxme/domain/entities/collectible_card.dart';
import 'package:detoxme/domain/entities/level.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/task.dart';
import 'package:detoxme/domain/repositories/reward_repository.dart';
import 'package:detoxme/presentation/bloc/reward/reward_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:math'; // For random card chance
import 'package:detoxme/domain/failure/failure.dart';

class RewardCubit extends Cubit<RewardState> {
  final RewardRepository rewardRepository;
  final Random _random = Random();

  RewardCubit({required this.rewardRepository}) : super(RewardInitial());

  Future<void> loadPlayerStats() async {
    emit(RewardLoading());
    final result = await rewardRepository.getPlayerStats();
    result.fold(
      (failure) => emit(RewardError(failure)),
      (stats) => _emitLoadedState(stats), // Use helper to emit loaded state
    );
  }

  // Helper to calculate level info and emit RewardLoaded state
  void _emitLoadedState(
    PlayerStats stats, {
    int? xpGained,
    Level? levelReached,
    Badge? badgeEarned,
    CollectibleCard? cardEarned,
  }) {
    final levelInfo = getLevelForXp(stats.currentXp);
    final nextLevelXp = getXpForNextLevel(levelInfo.levelNumber);
    emit(
      RewardLoaded(
        stats: stats,
        currentLevelInfo: levelInfo,
        xpForNextLevel: nextLevelXp,
        xpGained: xpGained,
        levelReached: levelReached,
        badgeEarned: badgeEarned,
        cardEarned: cardEarned,
      ),
    );
  }

  // Call this after a task is successfully completed
  Future<void> taskCompleted(Task completedTask) async {
    if (state is! RewardLoaded) {
      if (kDebugMode) {
        print("Cannot process reward, stats not loaded.");
      }
      return; // Don't process if stats aren't loaded
    }

    final currentLoadedState = state as RewardLoaded;
    PlayerStats currentStats = currentLoadedState.stats;

    // --- Calculate Rewards ---
    int xpEarned = _calculateXpForTask(completedTask); // Calculate XP
    Level? newlyReachedLevel;
    Badge? newlyEarnedBadge;
    CollectibleCard? newlyEarnedCard;

    // Update XP
    int newXp = currentStats.currentXp + xpEarned;

    // Check for Level Up
    final previousLevel = getLevelForXp(currentStats.currentXp);
    final newLevel = getLevelForXp(newXp);
    if (newLevel.levelNumber > previousLevel.levelNumber) {
      newlyReachedLevel = newLevel;
      if (kDebugMode) {
        print(
          'Level Up! Reached Level ${newLevel.levelNumber}: ${newLevel.name}',
        );
      }
      // TODO: Award level-up specific rewards?
    }

    // --- Check for Badges (Simple examples) ---
    List<String> updatedBadgeIds = List.from(currentStats.earnedBadgeIds);

    // Example: First task badge
    if (currentStats.earnedBadgeIds.isEmpty) {
      final firstTaskBadge = gameBadges.firstWhere(
        (b) => b.id == 'badge_first_task',
        orElse: () => gameBadges.first,
      ); // Find badge
      if (!updatedBadgeIds.contains(firstTaskBadge.id)) {
        updatedBadgeIds.add(firstTaskBadge.id);
        newlyEarnedBadge = firstTaskBadge;
        if (kDebugMode) {
          print('Badge Earned: ${firstTaskBadge.name}');
        }
      }
    }
    // Example: Specific task badge
    if (completedTask.id == 'task_01') {
      // Example: Hug task
      final huggerBadge = gameBadges.firstWhere(
        (b) => b.id == 'badge_hugger',
        orElse: () => gameBadges.first,
      );
      if (!updatedBadgeIds.contains(huggerBadge.id)) {
        updatedBadgeIds.add(huggerBadge.id);
        newlyEarnedBadge =
            huggerBadge; // Overwrites previous if multiple earned at once - handle better later?
        if (kDebugMode) {
          print('Badge Earned: ${huggerBadge.name}');
        }
      }
    }
    // TODO: Add checks for other badges (e.g., based on task count, level reached)

    // --- Check for Cards (Simple random chance example) ---
    List<String> updatedCardIds = List.from(currentStats.earnedCardIds);
    if (_random.nextDouble() < 0.25) {
      // 25% chance to get a card
      final availableCards =
          gameCards.where((card) => !updatedCardIds.contains(card.id)).toList();
      if (availableCards.isNotEmpty) {
        // Simple: pick a random available card
        final earnedCard =
            availableCards[_random.nextInt(availableCards.length)];
        updatedCardIds.add(earnedCard.id);
        newlyEarnedCard = earnedCard;
        if (kDebugMode) {
          print('Card Earned: ${earnedCard.name} (${earnedCard.rarity})');
        }
      }
    }

    // --- Update and Save Stats ---
    PlayerStats updatedStats = currentStats.copyWith(
      currentLevel: newLevel.levelNumber,
      currentXp: newXp,
      earnedBadgeIds: updatedBadgeIds,
      earnedCardIds: updatedCardIds,
      // Ensure userId and username are carried over
      userId: currentStats.userId,
      username: currentStats.username,
    );

    final saveResult = await rewardRepository.savePlayerStats(updatedStats);

    saveResult.fold(
      (failure) {
        // Handle save failure (local save failed)
        if (kDebugMode) {
          print("Error saving player stats: ${failure.message}");
        }
        emit(RewardError(failure)); // Emit error state
        // Optionally revert to previous loaded state after a delay?
        // Or just show error and keep the UI state as it was before taskCompleted was called?
        // For now, just emitting error is simplest.
      },
      (syncStatus) {
        // Local save was successful, syncStatus indicates remote status
        if (syncStatus == SyncStatus.syncedLocally) {
          // Log or potentially show a subtle indicator later
          if (kDebugMode) {
            print('Player stats saved locally, pending remote sync.');
          }
        }
        // Emit loaded state *with* the reward notifications
        // regardless of sync status (as local save succeeded)
        _emitLoadedState(
          updatedStats,
          xpGained: xpEarned,
          levelReached: newlyReachedLevel,
          badgeEarned: newlyEarnedBadge,
          cardEarned: newlyEarnedCard,
        );
      },
    );
  }

  // Simple XP calculation - 1 point per minute
  int _calculateXpForTask(Task task) {
    // 1 point per minute of duration as per requirements
    return task.duration.inMinutes;
  }

  // Call this after the UI has shown the reward notification
  void clearRewardNotifications() {
    if (state is RewardLoaded) {
      final loadedState = state as RewardLoaded;
      // Emit the same state but without the notification fields
      emit(loadedState.copyWithoutNotifications());
    }
  }

  /// Loads player reward redemption history
  Future<void> loadRedemptionHistory() async {
    emit(RewardLoading());
    try {
      // Get the user's reward history
      final rewards = await rewardRepository.getRedeemedRewards();

      // Check if we already have stats loaded, if so preserve them
      if (state is RewardLoaded) {
        final currentState = state as RewardLoaded;
        emit(
          RewardLoaded(
            stats: currentState.stats,
            currentLevelInfo: currentState.currentLevelInfo,
            xpForNextLevel: currentState.xpForNextLevel,
            redeemedRewards: rewards,
          ),
        );
      } else {
        // Load stats and reward history
        await loadPlayerStats();
      }
    } catch (e) {
      emit(RewardError(CacheFailure('Failed to load reward history: $e')));
    }
  }

  // Call this after an activity is successfully completed
  Future<void> activityCompleted(Activity activity) async {
    // Get current state (we need the player stats)
    if (state is! RewardLoaded) {
      await loadPlayerStats(); // Make sure stats are loaded
      if (state is! RewardLoaded) {
        emit(RewardError(ServerFailure('Failed to load player stats')));
        return;
      }
    }

    final loadedState = state as RewardLoaded;
    final currentStats = loadedState.stats;

    // Calculate rewards for completing the activity
    // 1 point per minute of activity as per requirements
    final int xpEarned = activity.duration;

    // Create updated stats
    final updatedStats = PlayerStats(
      userId: currentStats.userId,
      username: currentStats.username,
      currentXp: currentStats.currentXp + xpEarned,
      currentLevel: currentStats.currentLevel,
      earnedBadgeIds: currentStats.earnedBadgeIds,
      earnedCardIds: currentStats.earnedCardIds,
    );

    // Check for level up
    final currentLevelInfo = getLevelForXp(currentStats.currentXp);
    final newLevelInfo = getLevelForXp(updatedStats.currentXp);

    Level? newlyReachedLevel;
    Badge? newlyEarnedBadge;
    CollectibleCard? newlyEarnedCard;

    // Level up check
    if (newLevelInfo.levelNumber > currentLevelInfo.levelNumber) {
      newlyReachedLevel = newLevelInfo;

      // Award badges for specific level achievements
      final List<String> updatedBadgeIds = List.from(
        currentStats.earnedBadgeIds,
      );

      // Check for level-specific badges
      if (newLevelInfo.levelNumber >= 3) {
        newlyEarnedBadge = _awardBadgeIfNotEarned(
          updatedBadgeIds,
          'badge_level_3',
          newlyEarnedBadge,
        );
      }

      if (newLevelInfo.levelNumber >= 5) {
        newlyEarnedBadge = _awardBadgeIfNotEarned(
          updatedBadgeIds,
          'badge_level_5',
          newlyEarnedBadge,
        );
      }

      if (newLevelInfo.levelNumber >= 7) {
        newlyEarnedBadge = _awardBadgeIfNotEarned(
          updatedBadgeIds,
          'badge_level_7',
          newlyEarnedBadge,
        );
      }

      if (newLevelInfo.levelNumber >= 10) {
        newlyEarnedBadge = _awardBadgeIfNotEarned(
          updatedBadgeIds,
          'badge_level_10',
          newlyEarnedBadge,
        );
      }

      // Update badge IDs in player stats
      updatedStats.earnedBadgeIds.clear();
      updatedStats.earnedBadgeIds.addAll(updatedBadgeIds);
    }

    // Random card reward chance (simplified)
    if (_random.nextDouble() < 0.10) {
      // 10% chance
      // Find a card the player doesn't already have
      final allCards = getAllCards(); // Assuming this exists in GameData
      final unownedCards =
          allCards
              .where((card) => !currentStats.earnedCardIds.contains(card.id))
              .toList();

      if (unownedCards.isNotEmpty) {
        newlyEarnedCard = unownedCards[_random.nextInt(unownedCards.length)];
        updatedStats.earnedCardIds.add(newlyEarnedCard.id);
      }
    }

    // Save updated stats
    final saveResult = await rewardRepository.savePlayerStats(updatedStats);

    saveResult.fold(
      (failure) {
        // Handle save failure
        if (kDebugMode) {
          print("Error saving player stats: ${failure.message}");
        }
        emit(RewardError(failure));
      },
      (syncStatus) {
        // Emit loaded state with the reward notifications
        _emitLoadedState(
          updatedStats,
          xpGained: xpEarned,
          levelReached: newlyReachedLevel,
          badgeEarned: newlyEarnedBadge,
          cardEarned: newlyEarnedCard,
        );
      },
    );
  }

  // Helper method to get all collectible cards
  List<CollectibleCard> getAllCards() {
    // Use the gameCards list from game_data.dart
    return gameCards;
  }

  // Helper method to award a badge if not already earned and return the badge
  Badge? _awardBadgeIfNotEarned(
    List<String> badgeIds,
    String badgeId,
    Badge? currentBadge,
  ) {
    if (!badgeIds.contains(badgeId)) {
      // Find the badge in the game badges list
      final badge = gameBadges.firstWhere(
        (b) => b.id == badgeId,
        orElse: () => gameBadges.first,
      );

      // Add the badge ID to the list
      badgeIds.add(badgeId);

      if (kDebugMode) {
        print('Badge Earned: ${badge.name}');
      }

      // Return the newly earned badge
      return badge;
    }

    // Return the current badge if no new badge was earned
    return currentBadge;
  }
}
