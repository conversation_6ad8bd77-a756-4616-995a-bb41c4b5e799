import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';
import 'package:detoxme/presentation/bloc/profile/profile_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Cubit for managing user profile data
class ProfileCubit extends Cubit<ProfileState> {
  final UserRepository _profileRepository;

  /// Flag to track if this cubit is closed
  bool _isClosed = false;

  /// Creates a profile cubit with the necessary repository
  ProfileCubit({required UserRepository profileRepository})
    : _profileRepository = profileRepository,
      super(const ProfileInitial());

  @override
  Future<void> close() {
    _isClosed = true;
    return super.close();
  }

  /// Safely emit a state only if the cubit is not closed
  void safeEmit(ProfileState newState) {
    if (!_isClosed) {
      emit(newState);
    } else if (kDebugMode) {
      print('Attempted to emit on closed ProfileCubit, operation skipped');
    }
  }

  /// Loads a user profile from the repository
  Future<void> loadProfile([String? userId]) async {
    // Skip if cubit is closed
    if (_isClosed) {
      if (kDebugMode) {
        print('loadProfile called on closed cubit, skipping');
      }
      return;
    }

    safeEmit(const ProfileLoading());

    try {
      final profileResult =
          userId != null
              ? await _profileRepository.getProfileById(userId)
              : await _profileRepository.getProfile();

      // Skip if cubit was closed while waiting for the result
      if (_isClosed) return;

      profileResult.fold(
        (failure) => safeEmit(
          ProfileError('Failed to load profile: ${failure.message}'),
        ),
        (profile) {
          if (profile != null) {
            safeEmit(ProfileLoaded(profile));
          } else {
            // Create an empty profile if none exists
            final currentUserId = userId ?? _getCurrentUserId();
            if (currentUserId != null) {
              final emptyProfile = UserProfile.empty(currentUserId);
              safeEmit(ProfileLoaded(emptyProfile));
            } else {
              safeEmit(const ProfileError('Failed to get current user ID'));
            }
          }
        },
      );
    } catch (e) {
      if (!_isClosed) {
        safeEmit(ProfileError('Failed to load profile: ${e.toString()}'));
      }
    }
  }

  /// Saves the user profile to the repository
  Future<void> saveProfile(UserProfile profile) async {
    // Skip if cubit is closed
    if (_isClosed) {
      if (kDebugMode) {
        print('saveProfile called on closed cubit, skipping');
      }
      return;
    }

    // Get the current state to restore if save fails
    final currentState = state;

    try {
      safeEmit(const ProfileLoading());
      final saveResult = await _profileRepository.saveProfile(profile);

      // Skip if cubit was closed while waiting for the result
      if (_isClosed) return;

      saveResult.fold(
        (failure) => safeEmit(
          ProfileError('Failed to save profile: ${failure.message}'),
        ),
        (_) => safeEmit(ProfileLoaded(profile)),
      );
    } catch (e) {
      if (!_isClosed) {
        safeEmit(ProfileError('Failed to save profile: ${e.toString()}'));
        // Restore previous state if it was loaded
        if (currentState is ProfileLoaded) {
          safeEmit(currentState);
        }
      }
    }
  }

  /// Updates specific user profile fields and saves the updated profile
  Future<void> updateProfile({
    String? email,
    String? username,
    int? birthYear,
    String? gender,
    String? lifeSituation,
    List<String>? goals,
    String? avatarUrl,
    String? bio,
  }) async {
    // Skip if cubit is closed
    if (_isClosed) {
      if (kDebugMode) {
        print('updateProfile called on closed cubit, skipping');
      }
      return;
    }

    UserProfile profileToUpdate;

    if (state is ProfileLoaded) {
      // Use existing profile if available
      profileToUpdate = (state as ProfileLoaded).profile;
    } else {
      try {
        // If not loaded yet, load the default profile
        final result = await _profileRepository.getProfile();

        // Skip if cubit was closed while waiting for the result
        if (_isClosed) return;

        if (result.isLeft()) {
          // If we can't get a profile, create a new empty one
          final userId = _getCurrentUserId();
          if (userId == null) {
            safeEmit(const ProfileError('Failed to get current user ID'));
            return;
          }
          profileToUpdate = UserProfile.empty(userId);
        } else {
          // Use the profile we just loaded
          profileToUpdate = result.getOrElse(
            () => throw Exception('Failed to get profile'),
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error getting profile for update: $e');
        }
        safeEmit(ProfileError('Failed to prepare profile for update: $e'));
        return;
      }
    }

    // Update the profile with new values
    final updatedProfile = profileToUpdate.copyWith(
      email: email ?? profileToUpdate.email,
      username: username,
      birthYear: birthYear,
      gender: gender,
      lifeSituation: lifeSituation,
      goals: goals,
      avatarUrl: avatarUrl,
      bio: bio,
    );

    await saveProfile(updatedProfile);
  }

  /// Helper method to get current user ID
  String? _getCurrentUserId() {
    try {
      // This assumes you're using Supabase auth
      final supabase = Supabase.instance.client;
      return supabase.auth.currentUser?.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current user ID: $e');
      }
      return null;
    }
  }
}
