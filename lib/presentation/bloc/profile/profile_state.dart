import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:equatable/equatable.dart';

/// Base state for the profile cubit
abstract class ProfileState extends Equatable {
  const ProfileState();
  
  @override
  List<Object?> get props => [];
}

/// Initial state before any profile operations
class ProfileInitial extends ProfileState {
  const ProfileInitial();
}

/// Loading state while fetching or saving profile
class ProfileLoading extends ProfileState {
  const ProfileLoading();
}

/// State representing a loaded user profile
class ProfileLoaded extends ProfileState {
  final UserProfile profile;
  
  const ProfileLoaded(this.profile);
  
  @override
  List<Object?> get props => [profile];
  
  ProfileLoaded copyWith({UserProfile? profile}) {
    return ProfileLoaded(profile ?? this.profile);
  }
}

/// State representing error during profile operations
class ProfileError extends ProfileState {
  final String message;
  
  const ProfileError(this.message);
  
  @override
  List<Object?> get props => [message];
}
