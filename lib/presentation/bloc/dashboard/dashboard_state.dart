import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/weather_data.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Status of the dashboard data loading
enum DashboardStatus {
  /// Initial state when no data is loaded
  initial,

  /// Data is being loaded
  loading,

  /// Data is loaded successfully
  success,

  /// Error occurred while loading data
  failure,
}

/// State for the dashboard
class DashboardState extends Equatable {
  /// Current loading status
  final DashboardStatus status;

  /// Error message if status is failure
  final String? errorMessage;

  /// User's name
  final String userName;

  /// Whether the user has a family
  final bool hasFamily;

  /// Whether the user has children
  final bool hasChildren;

  /// User's main detox goal
  final String goal;

  /// Current detox points
  final int currentPoints;

  /// Maximum possible detox points
  final int maxPoints;

  /// Target detox points
  final int targetPoints;

  /// Current weather condition
  final WeatherCondition weatherCondition;

  /// Current temperature in Celsius
  final int temperature;

  /// Current location name
  final String location;

  /// Current time of day
  final TimeOfDay timeOfDay;
  
  /// Current mood selected by the user
  final MoodType? currentMood;

  /// Creates a [DashboardState]
  const DashboardState({
    this.status = DashboardStatus.initial,
    this.errorMessage,
    this.userName = '',
    this.hasFamily = false,
    this.hasChildren = false,
    this.goal = '',
    this.currentPoints = 0,
    this.maxPoints = 1000,
    this.targetPoints = 750,
    this.weatherCondition = WeatherCondition.sunny,
    this.temperature = 0,
    this.location = '',
    TimeOfDay? timeOfDay,
    this.currentMood,
  }) : timeOfDay = timeOfDay ?? const TimeOfDay(hour: 12, minute: 0);

  /// Creates a copy of this state with the given fields replaced
  DashboardState copyWith({
    DashboardStatus? status,
    String? errorMessage,
    String? userName,
    bool? hasFamily,
    bool? hasChildren,
    String? goal,
    int? currentPoints,
    int? maxPoints,
    int? targetPoints,
    WeatherCondition? weatherCondition,
    int? temperature,
    String? location,
    TimeOfDay? timeOfDay,
    MoodType? currentMood,
  }) {
    return DashboardState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      userName: userName ?? this.userName,
      hasFamily: hasFamily ?? this.hasFamily,
      hasChildren: hasChildren ?? this.hasChildren,
      goal: goal ?? this.goal,
      currentPoints: currentPoints ?? this.currentPoints,
      maxPoints: maxPoints ?? this.maxPoints,
      targetPoints: targetPoints ?? this.targetPoints,
      weatherCondition: weatherCondition ?? this.weatherCondition,
      temperature: temperature ?? this.temperature,
      location: location ?? this.location,
      timeOfDay: timeOfDay ?? this.timeOfDay,
      currentMood: currentMood ?? this.currentMood,
    );
  }

  /// Progress percentage (0.0 to 1.0)
  double get progressPercentage => currentPoints / maxPoints;

  /// Whether the target has been reached
  bool get isTargetReached => currentPoints >= targetPoints;

  /// Points remaining to reach target
  int get remainingPoints => targetPoints - currentPoints;

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    userName,
    hasFamily,
    hasChildren,
    goal,
    currentPoints,
    maxPoints,
    targetPoints,
    weatherCondition,
    temperature,
    location,
    timeOfDay.hour,
    timeOfDay.minute,
    currentMood,
  ];
}
