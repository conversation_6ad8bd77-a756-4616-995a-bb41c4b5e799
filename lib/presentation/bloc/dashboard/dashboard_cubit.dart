import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/data/repositories/dashboard_repository.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';

/// Cubit to manage dashboard data
class DashboardCubit extends Cubit<DashboardState> {
  /// Repository for dashboard data
  final DashboardRepository _repository;
  
  /// Activity cubit reference for updating mood
  ActivityCubit? _activityCubit;

  /// Food mood cubit reference for updating mood
  FoodMoodCubit? _foodMoodCubit;

  /// Flag to prevent recursive updates
  bool _isUpdatingMood = false;

  /// Creates a [DashboardCubit]
  DashboardCubit({
    DashboardRepository? repository,
    ActivityCubit? activityCubit,
    FoodMoodCubit? foodMoodCubit,
  }) : _repository = repository ?? DashboardRepository(),
       _activityCubit = activityCubit,
       _foodMoodCubit = foodMoodCubit,
       super(const DashboardState());
  
  /// Set activity cubit for synchronization
  void setActivityCubit(ActivityCubit cubit) {
    _activityCubit = cubit;
  }

  /// Set food mood cubit for synchronization
  void setFoodMoodCubit(FoodMoodCubit cubit) {
    _foodMoodCubit = cubit;
  }

  /// Load all dashboard data
  Future<void> loadDashboard() async {
    emit(state.copyWith(status: DashboardStatus.loading));

    try {
      // In a real app, these would be fetched from repositories
      await Future.wait([
        _loadUserProfile(),
        _loadDetoxPoints(),
        _loadWeatherData(),
        _loadLastMood(),
      ]);

      // Update time of day
      emit(
        state.copyWith(
          status: DashboardStatus.success,
          timeOfDay: TimeOfDay.now(),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: DashboardStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  /// Load user profile data
  Future<void> _loadUserProfile() async {
    final userName = await _repository.getUserName();
    final hasFamily = await _repository.getUserHasFamily();
    final hasChildren = await _repository.getUserHasChildren();
    final goal = await _repository.getUserGoal();

    emit(
      state.copyWith(
        userName: userName,
        hasFamily: hasFamily,
        hasChildren: hasChildren,
        goal: goal,
      ),
    );
  }

  /// Load detox points data
  Future<void> _loadDetoxPoints() async {
    final currentPoints = await _repository.getDetoxPoints();
    final maxPoints = await _repository.getMaxPoints();
    final targetPoints = await _repository.getTargetPoints();

    emit(
      state.copyWith(
        currentPoints: currentPoints,
        maxPoints: maxPoints,
        targetPoints: targetPoints,
      ),
    );
  }

  /// Load weather data
  Future<void> _loadWeatherData() async {
    final weatherData = await _repository.getWeatherData();
    final weatherCondition = _repository.getWeatherCondition(weatherData);

    emit(
      state.copyWith(
        weatherCondition: weatherCondition,
        temperature: weatherData.temperature.round(),
        location: weatherData.location,
      ),
    );
  }
  
  /// Load the last saved mood if available
  Future<void> _loadLastMood() async {
    try {
      // Get the last saved mood from shared preferences
      final lastMood = await _repository.getLastMood();
      if (lastMood != null) {
        emit(state.copyWith(currentMood: lastMood));
      }
    } catch (e) {
      // Ignore errors loading the mood - not critical
      debugPrint('Error loading last mood: $e');
    }
  }

  /// Update points when a task is completed
  Future<void> updatePoints(int additionalPoints) async {
    final newPoints = state.currentPoints + additionalPoints;
    final finalPoints =
        newPoints > state.maxPoints ? state.maxPoints : newPoints;

    // Update local state
    emit(state.copyWith(currentPoints: finalPoints));

    // Persist to storage
    await _repository.updateDetoxPoints(finalPoints);
  }
  
  /// Set the user's current mood - this is the central point of mood management
  /// across the app, used by all screens/tabs to maintain consistent mood state
  Future<void> setCurrentMood(MoodType mood) async {
    // If the mood is already set to this value, don't update
    if (state.currentMood == mood) return;
    
    // Update state with the new mood
    emit(state.copyWith(currentMood: mood));

    // Persist the mood to repository
    await _repository.saveMood(mood);

    // Flag to prevent recursive updates
    _isUpdatingMood = true;

    try {
      // Propagate the mood to ActivityCubit if available
      if (_activityCubit != null) {
        _activityCubit!.selectMood(mood);
      }

      // Propagate the mood to FoodMoodCubit if available
      if (_foodMoodCubit != null) {
        _foodMoodCubit!.loadFoodRecommendationsForMood(mood);
      }
    } finally {
      // Reset the flag
      _isUpdatingMood = false;
    }
  }

  /// Get the current mood
  MoodType? getCurrentMood() {
    return state.currentMood;
  }

  /// Clear the current mood
  Future<void> clearCurrentMood() async {
    emit(state.copyWith(currentMood: null));
    // Persist the change
    await _repository.clearMood();
  }
  
  /// Sync mood from other cubits (e.g., ActivityCubit or CouponCubit)
  /// This allows mood selection in any tab to be reflected in all tabs
  Future<void> syncMoodFromOtherCubit(MoodType? mood) async {
    // Prevent recursive updates
    if (_isUpdatingMood) return;
    
    if (mood != state.currentMood) {
      if (mood == null) {
        await clearCurrentMood();
      } else {
        emit(state.copyWith(currentMood: mood));
        // Persist the mood to repository
        await _repository.saveMood(mood);
      }
    }
  }
}
