import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/redemption.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:meta/meta.dart';

/// Status of the coupon state
enum CouponStatus {
  /// Initial state
  initial,
  
  /// Loading data
  loading,
  
  /// Successfully loaded data
  success,
  
  /// Error occurred
  failure,
}

/// Sort options for coupons
enum CouponSortOption {
  /// Sort by newest first
  newest,

  /// Sort by popularity
  mostPopular,

  /// Sort by highest discount
  highestDiscount,

  /// Sort by nearest location
  nearest,

  /// Sort by most relevant to selected mood
  moodRelevance,
}

/// Represents the state of the coupon feature
@immutable
class CouponState extends Equatable {
  /// Current status of the coupon feature
  final CouponStatus status;
  
  /// List of available coupons
  final List<Coupon> coupons;
  
  /// List of partners
  final List<Partner> partners;
  
  /// List of the user's redeemed coupons
  final List<Redemption> redemptions;
  
  /// Currently selected coupon for viewing details
  final Coupon? selectedCoupon;
  
  /// Currently selected redemption for viewing QR code
  final Redemption? selectedRedemption;
  
  /// Currently selected mood for filtering
  final MoodType? selectedMood;

  /// Currently selected sort option
  final CouponSortOption sortOption;

  /// Whether to only show nearby coupons
  final bool showOnlyNearby;

  /// Whether to show only user's redeemed coupons
  final bool showOnlyRedeemed;

  /// Selected language/country filters
  final List<String> selectedLanguages;
  
  /// Error message if an error occurred
  final Failure? failure;

  /// Constructor for CouponState
  const CouponState({
    this.status = CouponStatus.initial,
    this.coupons = const [],
    this.partners = const [],
    this.redemptions = const [],
    this.selectedCoupon,
    this.selectedRedemption,
    this.selectedMood,
    this.sortOption = CouponSortOption.newest,
    this.showOnlyNearby = false,
    this.showOnlyRedeemed = false,
    this.selectedLanguages = const ['en'],
    this.failure,
  });

  @override
  List<Object?> get props => [
        status,
        coupons,
        partners,
        redemptions,
        selectedCoupon,
        selectedRedemption,
    selectedMood,
    sortOption,
    showOnlyNearby,
    showOnlyRedeemed,
    selectedLanguages,
        failure,
      ];

  /// Creates a copy of this CouponState with the given fields replaced with the new values
  CouponState copyWith({
    CouponStatus? status,
    List<Coupon>? coupons,
    List<Partner>? partners,
    List<Redemption>? redemptions,
    Coupon? selectedCoupon,
    bool clearSelectedCoupon = false,
    Redemption? selectedRedemption,
    bool clearSelectedRedemption = false,
    MoodType? selectedMood,
    bool clearSelectedMood = false,
    CouponSortOption? sortOption,
    bool? showOnlyNearby,
    bool? showOnlyRedeemed,
    List<String>? selectedLanguages,
    Failure? failure,
    bool clearFailure = false,
  }) {
    return CouponState(
      status: status ?? this.status,
      coupons: coupons ?? this.coupons,
      partners: partners ?? this.partners,
      redemptions: redemptions ?? this.redemptions,
      selectedCoupon: clearSelectedCoupon ? null : selectedCoupon ?? this.selectedCoupon,
      selectedRedemption: clearSelectedRedemption ? null : selectedRedemption ?? this.selectedRedemption,
      selectedMood:
          clearSelectedMood ? null : selectedMood ?? this.selectedMood,
      sortOption: sortOption ?? this.sortOption,
      showOnlyNearby: showOnlyNearby ?? this.showOnlyNearby,
      showOnlyRedeemed: showOnlyRedeemed ?? this.showOnlyRedeemed,
      selectedLanguages: selectedLanguages ?? this.selectedLanguages,
      failure: clearFailure ? null : failure ?? this.failure,
    );
  }
}
