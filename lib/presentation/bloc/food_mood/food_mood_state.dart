import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';

/// Status of the food mood state
enum FoodMoodStatus {
  /// Initial state
  initial,

  /// Loading data
  loading,

  /// Loading more data (pagination)
  loadingMore,

  /// Loading completed successfully
  loaded,

  /// Error occurred
  error,
}

/// State for the FoodMood feature
@immutable
class FoodMoodState extends Equatable {
  /// Current status
  final FoodMoodStatus status;

  /// List of food recommendations
  final List<FoodRecommendation> foodRecommendations;

  /// Selected food recommendation
  final FoodRecommendation? selectedFoodRecommendation;

  /// Current failure
  final Failure? failure;

  /// Current mood filter
  final MoodType? selectedMood;

  /// Current sort option
  final FoodSortOption sortOption;

  /// Whether to exclude recommendations with too many downvotes
  final bool excludeTooManyDownvotes;

  /// Whether the food recommendations are filtered by mood
  final bool isFilteredByMood;

  /// Selected country code for filtering (or null for all)
  final String? countryCode;

  /// Whether vote action is in progress
  final bool isVoteInProgress;

  /// Food recommendation ID that is being voted on
  final String? votingRecommendationId;

  /// Duration range for filtering (min, max) in minutes
  final RangeValues? durationRange;

  /// Creates a new [FoodMoodState]
  const FoodMoodState({
    this.status = FoodMoodStatus.initial,
    this.foodRecommendations = const [],
    this.selectedFoodRecommendation,
    this.failure,
    this.selectedMood,
    this.sortOption = FoodSortOption.default_,
    this.excludeTooManyDownvotes = true,
    this.isFilteredByMood = false,
    this.countryCode,
    this.isVoteInProgress = false,
    this.votingRecommendationId,
    this.durationRange,
  });

  @override
  List<Object?> get props => [
    status,
    foodRecommendations,
    selectedFoodRecommendation,
    failure,
    selectedMood,
    sortOption,
    excludeTooManyDownvotes,
    isFilteredByMood,
    countryCode,
    isVoteInProgress,
    votingRecommendationId,
    durationRange,
  ];

  /// Creates a copy of this state with the given fields replaced with new values
  FoodMoodState copyWith({
    FoodMoodStatus? status,
    List<FoodRecommendation>? foodRecommendations,
    FoodRecommendation? selectedFoodRecommendation,
    Failure? failure,
    MoodType? selectedMood,
    FoodSortOption? sortOption,
    bool? excludeTooManyDownvotes,
    bool? isFilteredByMood,
    String? countryCode,
    bool? isVoteInProgress,
    String? votingRecommendationId,
    RangeValues? durationRange,
  }) {
    return FoodMoodState(
      status: status ?? this.status,
      foodRecommendations: foodRecommendations ?? this.foodRecommendations,
      selectedFoodRecommendation:
          selectedFoodRecommendation ?? this.selectedFoodRecommendation,
      failure: failure ?? this.failure,
      selectedMood: selectedMood ?? this.selectedMood,
      sortOption: sortOption ?? this.sortOption,
      excludeTooManyDownvotes:
          excludeTooManyDownvotes ?? this.excludeTooManyDownvotes,
      isFilteredByMood: isFilteredByMood ?? this.isFilteredByMood,
      countryCode: countryCode ?? this.countryCode,
      isVoteInProgress: isVoteInProgress ?? this.isVoteInProgress,
      votingRecommendationId:
          votingRecommendationId ?? this.votingRecommendationId,
      durationRange: durationRange ?? this.durationRange,
    );
  }

  /// Creates a copy with a cleared filter
  FoodMoodState clearFilter() {
    return copyWith(
      selectedMood: null,
      isFilteredByMood: false,
      durationRange: null,
    );
  }

  /// Update a specific food recommendation in the list
  FoodMoodState updateFoodRecommendation(
    FoodRecommendation updatedRecommendation,
  ) {
    final index = foodRecommendations.indexWhere(
      (r) => r.id == updatedRecommendation.id,
    );

    if (index < 0) {
      return this;
    }

    final updatedList = List<FoodRecommendation>.from(foodRecommendations);
    updatedList[index] = updatedRecommendation;

    // If this is the selected recommendation, update it too
    FoodRecommendation? newSelectedRecommendation = selectedFoodRecommendation;
    if (selectedFoodRecommendation?.id == updatedRecommendation.id) {
      newSelectedRecommendation = updatedRecommendation;
    }

    return copyWith(
      foodRecommendations: updatedList,
      selectedFoodRecommendation: newSelectedRecommendation,
    );
  }

  /// Get filtered recommendations based on current filters and sort options
  List<FoodRecommendation> get filteredRecommendations {
    List<FoodRecommendation> result = List.from(foodRecommendations);

    // Apply mood filter
    // NOTE: Mood and country code filtering are handled in the cubit and repository
    // before the data reaches the state.
    // if (isFilteredByMood && selectedMood != null) {
    //   result =
    //       result.where((r) {
    //         if (r.moodTypes == null || r.moodTypes!.isEmpty) {
    //           return false;
    //         }

    //         // Convert mood types to strings for comparison
    //         final moodStr = selectedMood.toString().split('.').last;
    //         final moodTypeStrs =
    //             r.moodTypes!.map((m) => m.toString().split('.').last).toList();

    //         return moodTypeStrs.contains(moodStr);
    //       }).toList();
    // }

    // Apply country code filter
    // NOTE: Country code filtering is handled in the cubit and repository
    // before the data reaches the state.
    // if (countryCodes.isNotEmpty) {
    //   final countryCode = countryCodes.first;
    //   result = result.where((r) => r.countryCode == countryCode).toList();
    // }

    // Apply downvotes filter
    if (excludeTooManyDownvotes) {
      result = result.where((r) => r.downvotes < 5).toList();
    }

    // Apply duration filter if set
    // Since FoodRecommendation doesn't have a duration property,
    // we'll use a random value based on the food's ID for demonstration
    // In a real app, you would use the actual duration property
    if (durationRange != null) {
      result =
          result.where((r) {
            // Generate a pseudo-random duration between 5-120 minutes based on the food's ID
            // This is just for demonstration - in a real app, you'd use the actual duration
            final pseudoDuration =
                5 +
                (r.id.hashCode % 24) *
                    5; // 5 to 120 minutes in 5-minute increments
            return pseudoDuration >= durationRange!.start &&
                pseudoDuration <= durationRange!.end;
          }).toList();
    }

    // Apply sorting
    switch (sortOption) {
      case FoodSortOption.highestVotes:
        result.sort((a, b) => b.totalVotes.compareTo(a.totalVotes));
        break;
      case FoodSortOption.newest:
        result.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case FoodSortOption.oldest:
        result.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case FoodSortOption.default_:
        // Keep original order or rely on data source ordering
        break;
    }

    return result;
  }
}
