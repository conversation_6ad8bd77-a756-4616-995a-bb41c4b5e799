import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/repositories/reward_history_repository.dart';
import 'package:detoxme/presentation/bloc/reward_history/reward_history_state.dart';

/// Cubit for managing reward history data and state
class RewardHistoryCubit extends Cubit<RewardHistoryState> {
  /// Repository for reward history data
  final RewardHistoryRepository _rewardHistoryRepository;

  /// Constructor
  RewardHistoryCubit({required RewardHistoryRepository rewardHistoryRepository})
    : _rewardHistoryRepository = rewardHistoryRepository,
      super(const RewardHistoryState());

  /// Load reward history
  Future<void> loadRewardHistory() async {
    emit(state.copyWith(status: RewardHistoryStatus.loading));

    final result = await _rewardHistoryRepository.getRewardHistory();

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: RewardHistoryStatus.failure,
          errorMessage: failure.message,
        ),
      ),
      (rewards) => emit(
        state.copyWith(
          status: RewardHistoryStatus.success,
          rewards: rewards,
          hasMore: rewards.length >= 100, // Assuming default page size of 100
        ),
      ),
    );
  }

  /// Load reward summary
  Future<void> loadRewardSummary() async {
    // Don't change the status to loading to avoid UI flicker if history is already loaded
    final initialStatus =
        state.status == RewardHistoryStatus.initial
            ? RewardHistoryStatus.loading
            : state.status;

    emit(state.copyWith(status: initialStatus));

    final result = await _rewardHistoryRepository.getRewardSummary();

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: RewardHistoryStatus.failure,
          errorMessage: failure.message,
        ),
      ),
      (summary) => emit(
        state.copyWith(
          summary: summary,
          // Only change status to success if currently loading
          status:
              state.status == RewardHistoryStatus.loading
                  ? RewardHistoryStatus.success
                  : state.status,
        ),
      ),
    );
  }

  /// Load both reward history and summary
  Future<void> loadAll() async {
    emit(state.copyWith(status: RewardHistoryStatus.loading));

    try {
      await Future.wait([loadRewardHistory(), loadRewardSummary()]);
    } catch (e) {
      debugPrint('Error loading reward data: $e');
      // The individual methods will handle their own errors and update state
    }
  }

  /// Add a custom reward (mainly for testing)
  Future<void> addCustomReward({
    required String title,
    required String description,
    required int pointsEarned,
    required String rewardType,
    Map<String, dynamic>? metadata,
  }) async {
    final result = await _rewardHistoryRepository.addCustomReward(
      title: title,
      description: description,
      pointsEarned: pointsEarned,
      rewardType: rewardType,
      metadata: metadata,
    );

    result.fold(
      (failure) => emit(state.copyWith(errorMessage: failure.message)),
      (reward) {
        final updatedRewards = [reward, ...state.rewards];
        emit(state.copyWith(rewards: updatedRewards));

        // Refresh summary
        loadRewardSummary();
      },
    );
  }
}
