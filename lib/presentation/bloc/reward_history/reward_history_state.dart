import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/repositories/reward_history_repository.dart';

/// Status of reward history loading
enum RewardHistoryStatus {
  /// Initial state
  initial,

  /// Loading in progress
  loading,

  /// Successfully loaded
  success,

  /// Error occurred
  failure,
}

/// State for reward history
class RewardHistoryState extends Equatable {
  /// Current status
  final RewardHistoryStatus status;

  /// List of all rewards in history
  final List<Reward> rewards;

  /// Summary by reward type
  final Map<String, RewardSummary> summary;

  /// Error message if any
  final String? errorMessage;

  /// Whether there are more rewards to load
  final bool hasMore;

  /// Total points earned from all rewards
  int get totalPointsEarned {
    return rewards.fold(0, (sum, reward) => sum + reward.pointsEarned);
  }

  /// Constructor
  const RewardHistoryState({
    this.status = RewardHistoryStatus.initial,
    this.rewards = const [],
    this.summary = const {},
    this.errorMessage,
    this.hasMore = false,
  });

  @override
  List<Object?> get props => [status, rewards, summary, errorMessage, hasMore];

  /// Create a copy with modified fields
  RewardHistoryState copyWith({
    RewardHistoryStatus? status,
    List<Reward>? rewards,
    Map<String, RewardSummary>? summary,
    String? errorMessage,
    bool? hasMore,
  }) {
    return RewardHistoryState(
      status: status ?? this.status,
      rewards: rewards ?? this.rewards,
      summary: summary ?? this.summary,
      errorMessage: errorMessage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}
