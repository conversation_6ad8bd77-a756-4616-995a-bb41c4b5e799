import 'package:detoxme/domain/repositories/auth_repository.dart';
import 'package:detoxme/presentation/bloc/auth/pin_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PinCubit extends Cubit<PinState> {
  final AuthRepository authRepository;
  final int _pinLength = 4;
  String _currentPin = '';
  bool _isSetupMode = false; // Will be determined by checkPinStatus

  PinCubit({required this.authRepository}) : super(PinInitial());

  Future<void> checkPinStatus() async {
    final isSet = await authRepository.isPinSet();
    _isSetupMode = !isSet;
    emit(PinStatusChecked(_isSetupMode)); // Initial state after checking
    emit(PinInputUpdate(_currentPin, _isSetupMode)); // Show empty input state
  }

  void digitEntered(String digit) {
    if (_currentPin.length < _pinLength) {
      _currentPin += digit;
      emit(PinInputUpdate(_currentPin, _isSetupMode));

      if (_currentPin.length == _pinLength) {
        _submitCurrentPin();
      }
    }
  }

  void backspacePressed() {
    if (_currentPin.isNotEmpty) {
      _currentPin = _currentPin.substring(0, _currentPin.length - 1);
      emit(PinInputUpdate(_currentPin, _isSetupMode));
    }
  }

  Future<void> _submitCurrentPin() async {
    emit(PinVerificationInProgress(_isSetupMode));
    final pinToSubmit = _currentPin;
    _currentPin = ''; // Clear internal pin immediately for next attempt

    if (_isSetupMode) {
      // Setting PIN Flow
      final result = await authRepository.setPin(pinToSubmit);
      result.fold(
        (failure) => emit(
          PinVerificationFailure(failure, '', _isSetupMode),
        ), // Failure state, clear input visually
        (_) {
          emit(const PinVerificationSuccess(true)); // Setup success
          _isSetupMode = false; // No longer in setup mode
        },
      );
    } else {
      // Verifying PIN Flow
      final result = await authRepository.verifyPin(pinToSubmit);
      result.fold(
        (failure) => emit(
          PinVerificationFailure(failure, '', _isSetupMode),
        ), // Failure state, clear input visually
        (_) =>
            emit(const PinVerificationSuccess(false)), // Verification success
      );
    }
    // After failure, reset to input state to allow re-entry
    if (state is PinVerificationFailure) {
      emit(PinInputUpdate('', _isSetupMode));
    }
  }
}
