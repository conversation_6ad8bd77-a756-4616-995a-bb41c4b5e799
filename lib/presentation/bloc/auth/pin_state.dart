import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/failure/failure.dart';

abstract class PinState extends Equatable {
  const PinState();

  @override
  List<Object?> get props => [];
}

class PinInitial extends PinState {}

// Indicates whether the user needs to set up a PIN or enter an existing one
class PinStatusChecked extends PinState {
  final bool isPinAlreadySet;
  const PinStatusChecked(this.isPinAlreadySet);

  @override
  List<Object?> get props => [isPinAlreadySet];
}

class PinInputUpdate extends PinState {
  final String enteredPin;
  final bool isSetupMode; // Needed to know context for submission
  const PinInputUpdate(this.enteredPin, this.isSetupMode);

  @override
  List<Object?> get props => [enteredPin, isSetupMode];
}

class PinVerificationInProgress extends PinState {
  final bool isSetupMode; // Keep track of mode during verification
  const PinVerificationInProgress(this.isSetupMode);
  @override
  List<Object?> get props => [isSetupMode];
}

class PinVerificationSuccess extends PinState {
  final bool wasSetupFlow; // Differentiate setup success from login success
  const PinVerificationSuccess(this.wasSetupFlow);
  @override
  List<Object?> get props => [wasSetupFlow];
}

class PinVerificationFailure extends PinState {
  final AuthFailure failure;
  final String currentPinInput; // Keep entered PIN to show indicators
  final bool isSetupMode; // Keep track of mode during failure

  const PinVerificationFailure(
    this.failure,
    this.currentPinInput,
    this.isSetupMode,
  );

  @override
  List<Object?> get props => [failure, currentPinInput, isSetupMode];
}
