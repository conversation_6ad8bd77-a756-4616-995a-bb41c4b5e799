import 'dart:async';
import 'dart:io'; // Import dart:io for platform check
import 'package:detoxme/presentation/bloc/auth/auth_state.dart'
    as app_auth_state; // Use our state type
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart'; // Import Apple Sign In
// Import supabase with prefix to avoid name collision
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:flutter/foundation.dart';
import 'package:detoxme/core/config/supabase_config.dart';
import 'package:detoxme/domain/repositories/device_token_repository.dart';
import 'package:detoxme/domain/repositories/auth_repository.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/core/services/notification_service.dart';

// Key for storing anonymous mode preference
const String _anonymousModeKey = 'anonymousModeActive';

// Use our app_auth_state.AuthState throughout the Cubit definition
class AuthCubit extends Cubit<app_auth_state.AuthState> {
  final supabase.SupabaseClient _supabaseClient;
  final DeviceTokenRepository? _deviceTokenRepository; // Keep for logout
  final NotificationService?
  _notificationService; // Keep for token registration
  final UserRepository _userRepository;
  // The listener stream emits Supabase's AuthState
  late final StreamSubscription<supabase.AuthState> _authStateSubscription;
  bool _isAnonymous = false; // Local flag for anonymous status

  AuthCubit({
    required supabase.SupabaseClient supabaseClient,
    required AuthRepository authRepository,
    DeviceTokenRepository? deviceTokenRepository,
    NotificationService? notificationService,
    required UserRepository userRepository,
  }) : _supabaseClient = supabaseClient,
       _deviceTokenRepository = deviceTokenRepository,
       _notificationService = notificationService,
       _userRepository = userRepository,
       super(app_auth_state.AuthInitial()) {
    _initialize();
  }

  Future<void> _initialize() async {
    // Listen to Supabase auth state changes
    _authStateSubscription = _supabaseClient.auth.onAuthStateChange.listen((
      data,
    ) async {
      // Make listener async
      // data is supabase.AuthState
      final supabase.Session? session = data.session;
      if (session != null) {
        // User logged in via Supabase
        _isAnonymous = false; // Ensure anonymous flag is off
        await _clearAnonymousPreference(); // Clear pref if user logs in

        // In debug mode, we'll always try to create a user profile
        // without checking if it exists first
        try {
          final userId = session.user.id;

          // Always try to create/update the user profile
          try {
            final metadata = session.user.userMetadata;
            final fullName =
                (metadata != null && metadata['full_name'] is String)
                    ? metadata['full_name'] as String
                    : null;
            final defaultProfile = UserProfile(
              id: userId,
              email: session.user.email ?? '',
              username: fullName,
            );
            await _userRepository.saveProfile(defaultProfile);
            if (kDebugMode) {
              print('[AuthCubit] Created/updated user profile for: $userId');
            }
          } catch (e) {
            if (kDebugMode) {
              print('[AuthCubit] Failed to create/update user profile: $e');
            }
            // Continue with authentication even if profile creation fails
            // This prevents users from being locked out
          }

          // Register FCM token HERE after successful login
          await _registerFcmToken(userId);

          // Check if onboarding is completed
          final prefs = await SharedPreferences.getInstance();
          final bool onboardingCompleted =
              prefs.getBool('onboarding_complete') ?? false;

          // Emit our Authenticated state
          emit(
            app_auth_state.Authenticated(
              user: session.user,
              needsOnboarding: !onboardingCompleted,
            ),
          );
        } catch (e) {
          if (kDebugMode) {
            print(
              '[AuthCubit] Error checking if user exists during auth state change: $e',
            );
          }
          // On error checking user existence, default to authenticated
          // This prevents locking users out due to network issues

          // Try to create a user profile anyway
          try {
            final userId = session.user.id;
            final metadata = session.user.userMetadata;
            final fullName =
                (metadata != null && metadata['full_name'] is String)
                    ? metadata['full_name'] as String
                    : null;
            final defaultProfile = UserProfile(
              id: userId,
              email: session.user.email ?? '',
              username: fullName,
            );
            await _userRepository.saveProfile(defaultProfile);
            if (kDebugMode) {
              print(
                '[AuthCubit] Created new user profile for: $userId (after error)',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              print(
                '[AuthCubit] Failed to create user profile after error: $e',
              );
            }
            // Continue with authentication even if profile creation fails
          }

          // Register FCM token
          await _registerFcmToken(session.user.id);

          // Check if onboarding is completed
          final prefs = await SharedPreferences.getInstance();
          final bool onboardingCompleted =
              prefs.getBool('onboarding_complete') ?? false;

          // Emit our Authenticated state
          emit(
            app_auth_state.Authenticated(
              user: session.user,
              needsOnboarding: !onboardingCompleted,
            ),
          );
        }
      } else {
        // User logged out from Supabase
        if (!_isAnonymous) {
          // Only emit Unauthenticated if not in anonymous mode
          // Emit our Unauthenticated state
          emit(app_auth_state.Unauthenticated());
        }
      }
    });

    // Check initial state on startup
    await checkAuthState();
  }

  Future<void> checkAuthState() async {
    emit(app_auth_state.AuthLoading());
    try {
      final prefs = await SharedPreferences.getInstance();
      _isAnonymous = prefs.getBool(_anonymousModeKey) ?? false;
      final currentSession = _supabaseClient.auth.currentSession;

      if (currentSession != null) {
        // In debug mode, we'll always try to create a user profile
        // without checking if it exists first
        final userId = currentSession.user.id;
        try {
          // Always try to create/update the user profile
          try {
            final metadata = currentSession.user.userMetadata;
            final fullName =
                (metadata != null && metadata['full_name'] is String)
                    ? metadata['full_name'] as String
                    : null;
            final defaultProfile = UserProfile(
              id: userId,
              email: currentSession.user.email ?? '',
              username: fullName,
            );
            await _userRepository.saveProfile(defaultProfile);
            if (kDebugMode) {
              print(
                '[AuthCubit] Created/updated user profile for: $userId in checkAuthState',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              print(
                '[AuthCubit] Failed to create/update user profile in checkAuthState: $e',
              );
            }
            // Continue with authentication even if profile creation fails
            // This prevents users from being locked out
          }

          // User exists, proceed with authentication
          _isAnonymous = false; // Clear anonymous flag if session exists
          await _clearAnonymousPreference();
          emit(app_auth_state.Authenticated(user: currentSession.user));
        } catch (e) {
          if (kDebugMode) {
            print('[AuthCubit] Error checking if user exists: $e');
          }
          // On error checking user existence, default to authenticated
          // This prevents locking users out due to network issues

          // Try to create a user profile anyway
          try {
            final userId = currentSession.user.id;
            final metadata = currentSession.user.userMetadata;
            final fullName =
                (metadata != null && metadata['full_name'] is String)
                    ? metadata['full_name'] as String
                    : null;
            final defaultProfile = UserProfile(
              id: userId,
              email: currentSession.user.email ?? '',
              username: fullName,
            );
            await _userRepository.saveProfile(defaultProfile);
            if (kDebugMode) {
              print(
                '[AuthCubit] Created new user profile for: $userId (after error in checkAuthState)',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              print(
                '[AuthCubit] Failed to create user profile after error in checkAuthState: $e',
              );
            }
            // Continue with authentication even if profile creation fails
          }

          _isAnonymous = false;
          await _clearAnonymousPreference();
          emit(app_auth_state.Authenticated(user: currentSession.user));
        }
      } else if (_isAnonymous) {
        emit(app_auth_state.AuthAnonymous());
      } else {
        emit(app_auth_state.Unauthenticated());
      }
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "Failed to check initial auth state: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated()); // Fallback to unauthenticated
    }
  }

  /// Check if the user exists in the database
  /// Note: This method is currently not used as we're bypassing the check in debug mode
  /// and always creating/updating the user profile. Kept for future reference.
  // ignore: unused_element
  Future<bool> _checkUserExistsInDatabase(String userId) async {
    // In debug mode, always return true to bypass the check
    // This allows login/signup even if the account was deleted
    if (kDebugMode) {
      print(
        '[AuthCubit] DEBUG MODE: Bypassing user existence check for: $userId',
      );
      return true;
    }

    try {
      // Check if user profile exists in user_profiles table
      final response =
          await _supabaseClient
              .from('user_profiles')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      if (kDebugMode) {
        print('[AuthCubit] Error checking if user exists in database: $e');
      }
      // On error, rethrow to be handled by the caller
      rethrow;
    }
  }

  Future<void> signUp({required String email, required String password}) async {
    emit(app_auth_state.AuthLoading());
    try {
      final supabase.AuthResponse response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
      );
      // Supabase handles emitting Authenticated via the listener
      if (response.user == null) {
        emit(
          app_auth_state.AuthError(
            "Signup successful, but user data is missing.",
          ),
        );
        emit(app_auth_state.Unauthenticated());
      }
      // Else: The listener will emit Authenticated
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError(e.message));
      emit(
        app_auth_state.Unauthenticated(),
      ); // Revert to unauthenticated after error
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during sign up: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated());
    }
  }

  Future<void> signIn({required String email, required String password}) async {
    emit(app_auth_state.AuthLoading());
    try {
      final supabase.AuthResponse response = await _supabaseClient.auth
          .signInWithPassword(email: email, password: password);
      // Supabase handles emitting Authenticated via the listener
      if (response.user == null) {
        emit(
          app_auth_state.AuthError(
            "Signin successful, but user data is missing.",
          ),
        );
        emit(app_auth_state.Unauthenticated());
      }
      // Else: The listener will emit Authenticated
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError(e.message));
      emit(
        app_auth_state.Unauthenticated(),
      ); // Revert to unauthenticated after error
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during sign in: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated());
    }
  }

  // --- Google Sign In ---
  Future<void> signInWithGoogle() async {
    emit(app_auth_state.AuthLoading());
    try {
      SupabaseAuthConfig.logAuthEvent('Starting Google Sign-In with OAuth');

      // Use Supabase's built-in Google provider
      final bool success = await _supabaseClient.auth.signInWithOAuth(
        supabase.OAuthProvider.google,
        redirectTo: null, // Let Supabase handle the redirect
      );

      if (!success) {
        emit(app_auth_state.AuthError("Google Sign-In failed or cancelled."));
        emit(
          app_auth_state.Unauthenticated(),
        ); // Reset state to fix stuck loading
      }
      // On success, Supabase listener will emit Authenticated state
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError("Google Sign-In Error: ${e.message}"));
      emit(
        app_auth_state.Unauthenticated(),
      ); // Reset state to fix stuck loading
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during Google Sign-In: ${e.toString()}",
        ),
      );
      emit(
        app_auth_state.Unauthenticated(),
      ); // Reset state to fix stuck loading
    }
  }

  // --- Apple Sign In ---
  Future<void> signInWithApple() async {
    // Platform check
    if (!Platform.isIOS && !Platform.isMacOS) {
      emit(
        app_auth_state.AuthError(
          "Apple Sign-In is only available on iOS and macOS.",
        ),
      );
      emit(app_auth_state.Unauthenticated());
      return;
    }

    emit(app_auth_state.AuthLoading());
    try {
      // Add more debug logging
      SupabaseAuthConfig.logAuthEvent('Starting Apple Sign-In');

      // Try with explicit scopes and webAuthenticationOptions
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        // Add web authentication for iOS simulators/testing
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.innovatio.detoxme.service', // Your app's bundle ID
          redirectUri: Uri.parse(
            'https://vbsdxnhkpoipturpmshl.supabase.co/auth/v1/callback',
          ),
        ),
      );

      SupabaseAuthConfig.logAuthEvent(
        'Apple credential received: ${credential.identityToken != null}',
      );

      final idToken = credential.identityToken;
      if (idToken == null) {
        throw const supabase.AuthException(
          'Missing identity token from Apple Sign In',
        );
      }

      // Sign in to Supabase with the Apple ID token
      SupabaseAuthConfig.logAuthEvent('Calling Supabase with Apple ID token');
      final authResponse = await _supabaseClient.auth.signInWithIdToken(
        provider: supabase.OAuthProvider.apple,
        idToken: idToken,
      );

      // Check if the response contains a valid user
      if (authResponse.user == null) {
        SupabaseAuthConfig.logAuthEvent(
          'Error: No user returned from Supabase',
        );
        emit(
          app_auth_state.AuthError(
            "Apple Sign-In failed: No user returned from Supabase",
          ),
        );
        emit(app_auth_state.Unauthenticated());
      }

      // On success, the Supabase auth state listener will emit Authenticated state

    } on SignInWithAppleAuthorizationException catch (e) {
      String errorMessage = "Apple Sign-In Error: ${e.code}";

      // More detailed error handling
      if (e.code == AuthorizationErrorCode.canceled) {
        errorMessage = "Apple Sign-In cancelled by user.";
      } else if (e.code == AuthorizationErrorCode.failed) {
        errorMessage = "Apple Sign-In failed: ${e.message}";
      } else if (e.code == AuthorizationErrorCode.invalidResponse) {
        errorMessage =
            "Apple Sign-In received an invalid response: ${e.message}";
      } else if (e.code == AuthorizationErrorCode.notHandled) {
        errorMessage = "Apple Sign-In was not handled: ${e.message}";
      } else if (e.code == AuthorizationErrorCode.unknown) {
        // Special handling for unknown errors (like error 1000)
        errorMessage =
            "Apple Sign-In system error (possibly configuration issue): ${e.message}";
        SupabaseAuthConfig.logAuthEvent(
          'Detailed Apple unknown error: ${e.toString()}',
        );
      }

      _logError('Apple Sign-In error', e);
      emit(app_auth_state.AuthError(errorMessage));
      emit(
        app_auth_state.Unauthenticated(),
      ); // Reset state to fix stuck loading
    } on supabase.AuthException catch (e) {
      _logError('Supabase Apple Sign-In error', e);
      emit(
        app_auth_state.AuthError("Supabase Apple Sign-In Error: ${e.message}"),
      );
      emit(
        app_auth_state.Unauthenticated(),
      ); // Reset state to fix stuck loading
    } catch (e) {
      _logError('Unexpected Apple Sign-In error', e);
      // More detailed error information
      SupabaseAuthConfig.logAuthEvent(
        'Detailed unexpected error: ${e.toString()}',
      );
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during Apple Sign-In: ${e.toString()}",
        ),
      );
      emit(
        app_auth_state.Unauthenticated(),
      ); // Reset state to fix stuck loading
    }
  }

  Future<void> playAnonymously() async {
    emit(app_auth_state.AuthLoading());
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_anonymousModeKey, true);
      _isAnonymous = true;
      // Ensure Supabase user is signed out if they choose anonymous
      if (_supabaseClient.auth.currentUser != null) {
        await _supabaseClient.auth.signOut();
        // Listener will trigger Unauthenticated, but we immediately emit AuthAnonymous
      }
      emit(app_auth_state.AuthAnonymous());
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "Failed to set anonymous mode: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated()); // Fallback
    }
  }

  Future<void> signOut() async {
    // Renamed from logout
    emit(app_auth_state.AuthLoading());
    try {
      final currentState = state;
      if (currentState is app_auth_state.Authenticated) {
        // Clear FCM tokens for this user BEFORE signing out
        await _clearFcmTokens(currentState.user.id);
      }

      await _supabaseClient.auth.signOut();
      await _clearAnonymousPreference(); // Clear anonymous pref on explicit logout
      _isAnonymous = false;
      // The listener will emit Unauthenticated
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError(e.message));
      // Don't revert state here, listener handles it
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during sign out: ${e.toString()}",
        ),
      );
    }
  }

  Future<void> _clearAnonymousPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_anonymousModeKey);
    } catch (_) {
      // Ignore errors clearing preference
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription.cancel();
    return super.close();
  }

  // Register FCM token for this user (called by listener)
  Future<void> _registerFcmToken(String userId) async {
    try {
      if (_notificationService != null) {
        // Request notification permissions
        final bool hasPermission =
            await _notificationService.requestPermissions();

        if (hasPermission) {
          // Register device token
          await _notificationService.registerDeviceToken(userId);
        }
      }
    } catch (e) {
      // Log but don't fail authentication if FCM registration fails
      _logError('FCM registration failed', e);
    }
  }

  // Clear FCM tokens on logout (called by signOut)
  Future<void> _clearFcmTokens(String userId) async {
    try {
      if (_deviceTokenRepository != null) {
        await _deviceTokenRepository.deleteAllUserTokens(userId);
      }
    } catch (e) {
      // Log but don't fail logout if FCM token removal fails
      _logError('FCM token removal failed', e);
    }
  }

  // Helper method to log errors in development mode
  void _logError(String message, Object e) {
    if (kDebugMode) {
      print('[AuthCubit] $message: $e');
    }
  }

  // --- Phone Sign In ---
  Future<void> signInWithPhone(String phoneNumber) async {
    emit(app_auth_state.AuthCodeSending());
    try {
      // Format phone number to E.164 format if not already formatted
      String formattedPhone = phoneNumber;
      if (!phoneNumber.startsWith('+')) {
        formattedPhone = '+$phoneNumber';
      }

      await _supabaseClient.auth.signInWithOtp(
        phone: formattedPhone,
        // channel: supabase.OtpChannel.sms, // Optional: specify channel if needed
        shouldCreateUser: true, // Creates user if doesn't exist
      );

      // Supabase client doesn't return verificationId directly, but we still need to
      // emit a state that signals successful OTP sending to trigger navigation.
      // With Supabase, we'll just pass the phone number which is required for verification later.
      // The actual verification ID is managed internally by Supabase.
      emit(
        app_auth_state.AuthCodeSent(
          verificationId: '',
          phoneNumber: formattedPhone,
        ),
      );
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError("Failed to send OTP: ${e.message}"));
      // Revert to Unauthenticated or a specific phone input error state?
      emit(app_auth_state.Unauthenticated());
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated());
    }
  }

  Future<void> verifyPhoneNumber(String phoneNumber, String otp) async {
    emit(app_auth_state.AuthOtpVerificationInProgress());
    try {
      // Format phone number to E.164 format if not already formatted
      String formattedPhone = phoneNumber;
      if (!phoneNumber.startsWith('+')) {
        formattedPhone = '+$phoneNumber';
      }

      final response = await _supabaseClient.auth.verifyOTP(
        type: supabase.OtpType.sms,
        phone: formattedPhone,
        token: otp,
      );

      // If successful, the onAuthStateChange listener will detect the session
      // and emit the Authenticated state, which includes token registration.
      if (response.session == null) {
        // This case might happen if OTP is wrong but doesn't throw immediately
        emit(app_auth_state.AuthError("Incorrect OTP entered."));
        emit(app_auth_state.Unauthenticated()); // Go back
      }
      // On success, listener handles state change.
    } on supabase.AuthException catch (e) {
      emit(app_auth_state.AuthError("OTP Verification Failed: ${e.message}"));
      emit(app_auth_state.Unauthenticated()); // Go back
    } catch (e) {
      emit(
        app_auth_state.AuthError(
          "An unexpected error occurred during OTP verification: ${e.toString()}",
        ),
      );
      emit(app_auth_state.Unauthenticated()); // Go back
    }
  }

  /// Get the current user's ID
  String? getCurrentUserId() {
    // If we're in the authenticated state, return the user ID
    if (state is app_auth_state.Authenticated) {
      return (state as app_auth_state.Authenticated).user.id;
    }
    return null;
  }

  /// Get the current user's email
  String? getCurrentUserEmail() {
    // If we're in the authenticated state, return the user email
    if (state is app_auth_state.Authenticated) {
      return _supabaseClient.auth.currentUser?.email;
    }
    return null;
  }

  /// Get the current user's phone number
  String? getCurrentUserPhone() {
    // If we're in the authenticated state, return the user phone
    if (state is app_auth_state.Authenticated) {
      return _supabaseClient.auth.currentUser?.phone;
    }
    return null;
  }
}
