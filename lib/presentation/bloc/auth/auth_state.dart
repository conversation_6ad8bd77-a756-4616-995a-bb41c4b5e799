import 'package:equatable/equatable.dart';
import 'package:supabase_flutter/supabase_flutter.dart' show User;

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

// Initial state, before checking auth status
class AuthInitial extends AuthState {}

// State while checking auth status, signing in, signing up, signing out
class AuthLoading extends AuthState {}

// User is authenticated via Supabase
class Authenticated extends AuthState {
  final User user;
  final bool needsOnboarding;

  const Authenticated({required this.user, this.needsOnboarding = false});

  @override
  List<Object?> get props => [user, needsOnboarding];
}

// User is explicitly unauthenticated (checked, not logged in)
class Unauthenticated extends AuthState {}

// User has chosen to play anonymously
class AuthAnonymous extends AuthState {}

// -- Phone Auth States --

// State when initiating phone sign-in (sending code)
class AuthCodeSending extends AuthState {}

// State after code has been successfully sent
class AuthCodeSent extends AuthState {
  final String verificationId; // Needed to verify the OTP
  final String phoneNumber; // Store the phone number used

  const AuthCodeSent({required this.verificationId, required this.phoneNumber});

  @override
  List<Object?> get props => [verificationId, phoneNumber];
}

// State when verifying the entered OTP
class AuthOtpVerificationInProgress extends AuthState {}

// -- End Phone Auth States --

// An error occurred during an auth operation
class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}
