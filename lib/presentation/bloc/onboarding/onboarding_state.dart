import 'package:equatable/equatable.dart';

/// Base class for all onboarding states
abstract class OnboardingState extends Equatable {
  final int currentPageIndex;
  final String? selectedLifeSituation;
  final List<String> selectedGoals;
  final Map<String, String?> validationErrors;
  final String? username;
  final int? birthYear;
  final String? gender;

  const OnboardingState({
    required this.currentPageIndex,
    this.selectedLifeSituation,
    required this.selectedGoals,
    required this.validationErrors,
    this.username,
    this.birthYear,
    this.gender,
  });

  OnboardingState copyWith({
    int? currentPageIndex,
    String? selectedLifeSituation,
    List<String>? selectedGoals,
    Map<String, String?>? validationErrors,
    String? username,
    int? birthYear,
    String? gender,
  });

  @override
  List<Object?> get props => [
        currentPageIndex,
        selectedLifeSituation,
        selectedGoals,
        validationErrors,
        username,
        birthYear,
        gender,
      ];
}

/// State when the onboarding is loading initial data
class OnboardingLoadingState extends OnboardingState {
  const OnboardingLoadingState()
      : super(
          currentPageIndex: 0,
          selectedGoals: const [],
          validationErrors: const {},
          username: null,
          birthYear: null,
          gender: null,
        );

  @override
  OnboardingState copyWith({
    int? currentPageIndex,
    String? selectedLifeSituation,
    List<String>? selectedGoals,
    Map<String, String?>? validationErrors,
    String? username,
    int? birthYear,
    String? gender,
  }) {
    return OnboardingContentState(
      currentPageIndex: currentPageIndex ?? this.currentPageIndex,
      selectedLifeSituation: selectedLifeSituation ?? this.selectedLifeSituation,
      selectedGoals: selectedGoals ?? this.selectedGoals,
      validationErrors: validationErrors ?? this.validationErrors,
      username: username ?? this.username,
      birthYear: birthYear ?? this.birthYear,
      gender: gender ?? this.gender,
    );
  }
}

/// State when the onboarding is actively showing content
class OnboardingContentState extends OnboardingState {
  const OnboardingContentState({
    required super.currentPageIndex,
    super.selectedLifeSituation,
    required super.selectedGoals,
    required super.validationErrors,
    super.username,
    super.birthYear,
    super.gender,
  });

  @override
  OnboardingState copyWith({
    int? currentPageIndex,
    String? selectedLifeSituation,
    List<String>? selectedGoals,
    Map<String, String?>? validationErrors,
    String? username,
    int? birthYear,
    String? gender,
  }) {
    return OnboardingContentState(
      currentPageIndex: currentPageIndex ?? this.currentPageIndex,
      selectedLifeSituation: selectedLifeSituation ?? this.selectedLifeSituation,
      selectedGoals: selectedGoals ?? List<String>.from(this.selectedGoals),
      validationErrors: validationErrors ?? Map<String, String?>.from(this.validationErrors),
      username: username ?? this.username,
      birthYear: birthYear ?? this.birthYear,
      gender: gender ?? this.gender,
    );
  }
}

/// State when the onboarding has been completed
class OnboardingCompletedState extends OnboardingState {
  const OnboardingCompletedState({
    required super.currentPageIndex,
    super.selectedLifeSituation,
    required super.selectedGoals,
    required super.validationErrors,
    super.username,
    super.birthYear,
    super.gender,
  });

  @override
  OnboardingState copyWith({
    int? currentPageIndex,
    String? selectedLifeSituation,
    List<String>? selectedGoals,
    Map<String, String?>? validationErrors,
    String? username,
    int? birthYear,
    String? gender,
  }) {
    return OnboardingCompletedState(
      currentPageIndex: currentPageIndex ?? this.currentPageIndex,
      selectedLifeSituation: selectedLifeSituation ?? this.selectedLifeSituation,
      selectedGoals: selectedGoals ?? List<String>.from(this.selectedGoals),
      validationErrors: validationErrors ?? Map<String, String?>.from(this.validationErrors),
      username: username ?? this.username,
      birthYear: birthYear ?? this.birthYear,
      gender: gender ?? this.gender,
    );
  }
}
