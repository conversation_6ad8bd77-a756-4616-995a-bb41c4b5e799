import 'package:bloc/bloc.dart';
import 'package:detoxme/core/constants/app_constants.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;
import 'package:detoxme/presentation/bloc/onboarding/onboarding_state.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class OnboardingCubit extends Cubit<OnboardingState> {
  final AuthCubit? _authCubit;
  final ProfileCubit? _profileCubit;

  OnboardingCubit({
    AuthCubit? authCubit,
    ProfileCubit? profileCubit,
  }) : _authCubit = authCubit,
      _profileCubit = profileCubit,
       super(const OnboardingLoadingState());

  /// Initialize the onboarding flow
  void initialize() {
    emit(const OnboardingContentState(
      currentPageIndex: 0,
      selectedGoals: [],
      validationErrors: {},
      username: null,
      birthYear: null,
      gender: null,
    ));
  }

  /// Navigate to the next page, validating the current page if needed
  void nextPage([AppLocalizations? l10n]) {
    final currentState = state;

    // Skip validation if we're not in the ContentState
    if (currentState is! OnboardingContentState) return;

    // Validate the current page before proceeding
    if (!_validateCurrentPage(l10n)) return;

    // Proceed to the next page
    emit(currentState.copyWith(
      currentPageIndex: currentState.currentPageIndex + 1,
    ));
  }

  /// Navigate to the previous page
  void previousPage() {
    final currentState = state;

    // Skip if we're not in ContentState or already on the first page
    if (currentState is! OnboardingContentState || currentState.currentPageIndex <= 0) return;

    emit(currentState.copyWith(
      currentPageIndex: currentState.currentPageIndex - 1,
    ));
  }

  /// Update the selected life situation
  void selectLifeSituation(String? lifeSituation) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;
    final updatedErrors = Map<String, String?>.from(currentState.validationErrors);
    updatedErrors['lifeSituation'] = null; // Clear error when a selection is made

    emit(currentState.copyWith(
      selectedLifeSituation: lifeSituation,
      validationErrors: updatedErrors,
    ));
  }

  /// Add a goal to the selected goals
  void addGoal(String goal) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;

    // Skip if already selected or at max selection
    if (currentState.selectedGoals.contains(goal) || currentState.selectedGoals.length >= 3) {
      return;
    }

    final updatedGoals = List<String>.from(currentState.selectedGoals)..add(goal);
    final updatedErrors = Map<String, String?>.from(currentState.validationErrors);
    updatedErrors['goals'] = null; // Clear error when goals are added

    emit(currentState.copyWith(
      selectedGoals: updatedGoals,
      validationErrors: updatedErrors,
    ));
  }

  /// Remove a goal from the selected goals
  void removeGoal(String goal) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;

    // Skip if not selected
    if (!currentState.selectedGoals.contains(goal)) return;

    final updatedGoals = List<String>.from(currentState.selectedGoals)..remove(goal);

    emit(currentState.copyWith(
      selectedGoals: updatedGoals,
    ));
  }

  /// Synchronize the page index without validation (for UI sync only)
  void syncPageIndex(int index) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;
    if (currentState.currentPageIndex == index) return; // No change needed

    emit(currentState.copyWith(currentPageIndex: index));
  }

  /// Update username during onboarding
  void updateUsername(String username) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;
    emit(currentState.copyWith(username: username));
  }

  /// Update birth year during onboarding
  void updateBirthYear(int birthYear) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;
    emit(currentState.copyWith(birthYear: birthYear));
  }

  /// Update gender during onboarding
  void updateGender(String gender) {
    if (state is! OnboardingContentState) return;

    final currentState = state as OnboardingContentState;
    emit(currentState.copyWith(gender: gender));
  }

  /// Complete the onboarding process and persist user choices
  Future<void> completeOnboarding() async {
    if (state is! OnboardingContentState) {
      if (kDebugMode) {
        print(
          'ERROR: Cannot complete onboarding: State is not OnboardingContentState',
        );
      }
      return;
    }

    final currentState = state as OnboardingContentState;

    // Validate required fields before proceeding
    if (currentState.username == null ||
        currentState.username!.trim().isEmpty) {
      if (kDebugMode) {
        print('ERROR: Cannot complete onboarding: Username is required');
      }
      return;
    }

    if (currentState.birthYear == null) {
      if (kDebugMode) {
        print('ERROR: Cannot complete onboarding: Birth year is required');
      }
      return;
    }

    if (currentState.gender == null) {
      if (kDebugMode) {
        print('ERROR: Cannot complete onboarding: Gender is required');
      }
      return;
    }

    if (kDebugMode) {
      print('INFO: Starting onboarding completion process');
    }

    final prefs = await SharedPreferences.getInstance();

    // Save collected data locally for backward compatibility
    if (currentState.selectedLifeSituation != null) {
      await prefs.setString(
        AppConstants.onboardingFamilyStatusKey,
        currentState.selectedLifeSituation!,
      );
    }

    if (currentState.selectedGoals.isNotEmpty) {
      await prefs.setStringList(
        AppConstants.onboardingGoalsKey,
        currentState.selectedGoals,
      );
    }

    // Check authentication status
    if (_authCubit == null) {
      if (kDebugMode) {
        print('ERROR: Cannot complete onboarding: AuthCubit is null');
      }
      return;
    }

    final authState = _authCubit.state;
    if (kDebugMode) {
      print(
        'DEBUG: Auth state during onboarding completion: ${authState.runtimeType}',
      );
    }

    if (authState is! auth_state.Authenticated) {
      if (kDebugMode) {
        print('ERROR: Cannot complete onboarding: User is not authenticated');
      }

      // Try to wait for authentication
      if (kDebugMode) {
        print(
          'INFO: Waiting for authentication before completing onboarding...',
        );
      }

      // Wait for up to 5 seconds for authentication
      for (int i = 0; i < 10; i++) {
        await Future.delayed(const Duration(milliseconds: 500));
        final newAuthState = _authCubit.state;
        if (newAuthState is auth_state.Authenticated) {
          if (kDebugMode) {
            print('SUCCESS: User authenticated after waiting');
          }
          break;
        }

        if (i == 9 && newAuthState is! auth_state.Authenticated) {
          if (kDebugMode) {
            print('ERROR: Timed out waiting for authentication');
          }
          // Continue anyway, but log the error
        }
      }
    }

    // Save to Supabase via UserProfileCubit if user is authenticated
    try {
      await _saveProfileToSupabase(currentState);
      if (kDebugMode) {
        print('SUCCESS: Profile saved to Supabase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ERROR: Failed to save profile to Supabase: $e');
      }
      // Continue with onboarding completion even if profile save fails
    }

    // Initialize detox points profile for the user
    try {
      await _initializeDetoxPointsProfile();
      if (kDebugMode) {
        print('SUCCESS: Detox points profile initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ERROR: Failed to initialize detox points profile: $e');
      }
      // Continue with onboarding completion even if detox points initialization fails
    }

    // Mark onboarding as complete
    await prefs.setBool(AppConstants.onboardingCompleteKey, true);
    if (kDebugMode) {
      print('SUCCESS: Onboarding marked as complete');
    }

    // Transition to completed state
    emit(OnboardingCompletedState(
      currentPageIndex: currentState.currentPageIndex,
      selectedLifeSituation: currentState.selectedLifeSituation,
      selectedGoals: currentState.selectedGoals,
      validationErrors: currentState.validationErrors,
    ));
  }

  /// Initialize a detox points profile for the new user
  Future<void> _initializeDetoxPointsProfile() async {
    // Skip if auth is not available
    if (_authCubit == null) return;

    try {
      // Check if user is authenticated
      final authState = _authCubit.state;
      if (authState is! auth_state.Authenticated) {
        if (kDebugMode) {
          print('Cannot initialize detox points: User is not authenticated');
        }
        return;
      }

      final userId = authState.user.id;
      final supabase = Supabase.instance.client;
      bool success = false;

      // Get username from user profile if available
      String? username = 'User';
      String? avatarUrl;

      try {
        final userProfile =
            await supabase
                .from('user_profiles')
                .select('username, avatar_url')
                .eq('id', userId)
                .maybeSingle();

        if (userProfile != null) {
          username = userProfile['username'] ?? 'User';
          avatarUrl = userProfile['avatar_url'];
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to fetch user profile data: $e');
          print('Using default username "User"');
        }
      }

      // ATTEMPT 1: Directly insert into detox_points table
      try {
        final detoxPointsData = {
          'user_id': userId,
          'user_name': username,
          'avatar_url': avatarUrl,
          'total_points': 0,
          'level': 1,
          'available_points': 0,
          'points_to_next_level': 100,
          'last_updated': DateTime.now().toIso8601String(),
          'daily_points': 0,
          'daily_goal_target': 50,
          'daily_date': DateTime.now().toIso8601String(),
        };

        // Check if record already exists
        final existingRecord =
            await supabase
                .from('detox_points')
                .select()
                .eq('user_id', userId)
                .maybeSingle();

        if (existingRecord == null) {
          // Insert new record
          await supabase.from('detox_points').insert(detoxPointsData);

          if (kDebugMode) {
            print('Successfully created detox points record for user: $userId');
          }
          success = true;
        } else {
          if (kDebugMode) {
            print('Detox points record already exists for user: $userId');
          }
          success = true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to create detox points record directly: $e');
          print('Will try RPC function as backup');
        }
      }

      // ATTEMPT 2: Try the RPC function if direct insertion failed
      if (!success) {
        try {
          await supabase.rpc(
            'initialize_user_detox_points',
            params: {'user_id_param': userId},
          );

          if (kDebugMode) {
            print(
              'Successfully initialized detox points via RPC for user: $userId',
            );
          }
          success = true;
        } catch (e) {
          if (kDebugMode) {
            print('RPC for detox points initialization failed: $e');
            print(
              'Detox points profile will be created on first access automatically',
            );
          }
        }
      }

      if (success) {
        if (kDebugMode) {
          print('SUCCESS: Detox points profile initialized');
        }
      } else {
        if (kDebugMode) {
          print('WARNING: All attempts to initialize detox points failed');
          print('The detox_points record will be created on first access');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in _initializeDetoxPointsProfile: $e');
      }
    }
  }

  /// Validate the current page and update errors if needed
  bool _validateCurrentPage([AppLocalizations? l10n]) {
    if (l10n == null) return true; // Skip validation if no l10n provided
    if (state is! OnboardingContentState) return false;

    final currentState = state as OnboardingContentState;
    bool isValid = true;
    final updatedErrors = Map<String, String?>.from(currentState.validationErrors);

    switch (currentState.currentPageIndex) {
      case 1: // Life Situation Page
        if (currentState.selectedLifeSituation == null) {
          updatedErrors['lifeSituation'] = l10n.requiredErrorText;
          isValid = false;
        }
        break;

      case 2: // Goals Page
        if (currentState.selectedGoals.isEmpty) {
          updatedErrors['goals'] = l10n.requiredErrorText;
          isValid = false;
        }
        break;

      case 3: // User Info Page
        // Validate username
        if (currentState.username == null ||
            currentState.username!.trim().isEmpty) {
          updatedErrors['username'] = l10n.requiredErrorText;
          isValid = false;
        }

        // Validate birth year
        if (currentState.birthYear == null) {
          updatedErrors['birthYear'] = l10n.requiredErrorText;
          isValid = false;
        }

        // Validate gender
        if (currentState.gender == null) {
          updatedErrors['gender'] = l10n.requiredErrorText;
          isValid = false;
        }
        break;
    }

    // Only emit a new state if there are errors to update
    if (!isValid) {
      emit(currentState.copyWith(validationErrors: updatedErrors));
    }

    return isValid;
  }

  /// Save user profile data to Supabase via UserProfileCubit
  Future<void> _saveProfileToSupabase(OnboardingContentState currentState) async {
    // Skip if auth or profile cubits are not available
    if (_authCubit == null || _profileCubit == null) {
      if (kDebugMode) {
        print('ERROR: Cannot save profile: Auth or UserProfile cubit is null');
      }
      return;
    }

    try {
      // Check if user is authenticated
      final authState = _authCubit.state;
      if (kDebugMode) {
        print(
          'DEBUG: Auth state during profile save: ${authState.runtimeType}',
        );
      }

      if (authState is! auth_state.Authenticated) {
        if (kDebugMode) {
          print('ERROR: Cannot save profile: User is not authenticated');
        }
        return;
      }

      final userId = authState.user.id;
      if (kDebugMode) {
        print('DEBUG: Attempting to save profile for user ID: $userId');
      }

      // Validate required fields before saving
      if (currentState.username == null ||
          currentState.username!.trim().isEmpty) {
        if (kDebugMode) {
          print('ERROR: Cannot save profile: Username is required');
        }
        return;
      }

      if (currentState.birthYear == null) {
        if (kDebugMode) {
          print('ERROR: Cannot save profile: Birth year is required');
        }
        return;
      }

      if (currentState.gender == null) {
        if (kDebugMode) {
          print('ERROR: Cannot save profile: Gender is required');
        }
        return;
      }

      // Create a new profile directly instead of trying to load an existing one
      if (kDebugMode) {
        print(
          'INFO: Creating user profile for user: $userId with username=${currentState.username}, birthYear=${currentState.birthYear}, gender=${currentState.gender}',
        );
      }

      // First try to directly create the profile in Supabase
      try {
        final supabase = Supabase.instance.client;

        // First check the table structure to determine which columns exist
        List<String> existingColumns = [];
        try {
          final response = await supabase.rpc(
            'get_table_columns',
            params: {'table_name_param': 'user_profiles'},
          );

          if (response != null) {
            existingColumns = List<String>.from(response);
            if (kDebugMode) {
              print(
                'DEBUG: Existing columns in user_profiles: $existingColumns',
              );
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('WARNING: Could not get columns for user_profiles: $e');
            print('INFO: Will attempt upsert with minimal fields');
          }
          // Default to minimal set of columns that should exist
          existingColumns = [
            'id',
            'username',
            'birth_year',
            'gender',
            'life_situation',
            'goals',
          ];
        }

        // Build profile data based on existing columns
        final Map<String, dynamic> profileData = {'id': userId};

        // Only add fields that exist in the table
        if (existingColumns.contains('username')) {
          profileData['username'] = currentState.username;
        }

        if (existingColumns.contains('birth_year')) {
          profileData['birth_year'] = currentState.birthYear;
        }

        if (existingColumns.contains('gender')) {
          profileData['gender'] = currentState.gender;
        }

        if (existingColumns.contains('life_situation')) {
          profileData['life_situation'] = currentState.selectedLifeSituation;
        }

        if (existingColumns.contains('goals')) {
          profileData['goals'] =
              currentState.selectedGoals.isNotEmpty
                  ? currentState.selectedGoals
                  : [];
        }

        if (existingColumns.contains('email') && authState.user.email != null) {
          profileData['email'] = authState.user.email;
        }

        if (kDebugMode) {
          print(
            'DEBUG: Attempting direct Supabase upsert with data: $profileData',
          );
        }

        await supabase.from('user_profiles').upsert(profileData);

        if (kDebugMode) {
          print('SUCCESS: Direct Supabase profile upsert successful');
        }
      } catch (e) {
        if (kDebugMode) {
          print('ERROR: Direct Supabase profile upsert failed: $e');
          print('INFO: Falling back to UserProfileCubit');
        }

        // Fall back to using the UserProfileCubit
        try {
          // Update profile with onboarding data - this will create it if doesn't exist
          await _profileCubit.updateProfile(
            email: authState.user.email,
            username: currentState.username,
            birthYear: currentState.birthYear,
            gender: currentState.gender,
            lifeSituation: currentState.selectedLifeSituation,
            goals:
                currentState.selectedGoals.isNotEmpty
                    ? currentState.selectedGoals
                    : [],
          );

          if (kDebugMode) {
            print('SUCCESS: UserProfileCubit profile update successful');
          }
        } catch (cubiteError) {
          if (kDebugMode) {
            print(
              'ERROR: UserProfileCubit profile update failed: $cubiteError',
            );
          }
          rethrow;
        }
      }

      // Also update in SharedPreferences for Dashboard compatibility
      if (currentState.username != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_name', currentState.username!);

        // Update other profile-related fields in SharedPreferences
        if (currentState.selectedLifeSituation != null) {
          final hasFamily =
              currentState.selectedLifeSituation!.contains('family') ||
              currentState.selectedLifeSituation!.contains('partner') ||
              currentState.selectedLifeSituation!.contains('married');
          await prefs.setBool('user_has_family', hasFamily);

          final hasChildren =
              currentState.selectedLifeSituation!.contains('children') ||
              currentState.selectedLifeSituation!.contains('kids');
          await prefs.setBool('user_has_children', hasChildren);
        }

        if (currentState.selectedGoals.isNotEmpty) {
          await prefs.setString('user_goal', currentState.selectedGoals.first);
        }
      }

      if (kDebugMode) {
        print('User profile successfully created/updated with onboarding data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving onboarding data to profile: $e');
      }
    }
  }

}
