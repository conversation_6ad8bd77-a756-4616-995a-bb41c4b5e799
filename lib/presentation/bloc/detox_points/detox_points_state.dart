import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import '../../../domain/entities/detox_points.dart';
import '../../../domain/entities/reward.dart';
import '../../../domain/entities/points_history.dart';

/// Status of detox points operations
enum DetoxPointsStatus {
  /// Initial state
  initial,

  /// Loading state for any operation
  loading,

  /// Successfully loaded points/rewards
  success,

  /// Error occurred
  error,

  /// Successfully redeemed a reward
  redeemed,
}

/// State class for Detox Points system
@immutable
class DetoxPointsState extends Equatable {
  /// Current status of the detox points operations
  final DetoxPointsStatus status;

  /// User's detox points
  final DetoxPoints? userPoints;

  /// List of users in the leaderboard
  final List<DetoxPoints>? leaderboard;

  /// Available rewards to redeem
  final List<Reward>? availableRewards;

  /// User's redemption history
  final List<Reward>? redemptionHistory;

  /// Error message if status is error
  final String? errorMessage;

  /// Selected reward category filter
  final RewardCategory? selectedCategory;

  /// Points history data for different time periods
  final PointsHistory? pointsHistory;

  /// Selected time period for points display
  final PointsTimePeriod selectedTimePeriod;

  /// Whether to show bar chart view instead of speedometer
  final bool showBarChart;

  /// Selected distance radius for rewards (in km)
  final double selectedRadius;

  /// Nearby rewards based on user location
  final List<Reward>? nearbyRewards;

  /// Constructor for DetoxPointsState
  const DetoxPointsState({
    this.status = DetoxPointsStatus.initial,
    this.userPoints,
    this.leaderboard,
    this.availableRewards,
    this.redemptionHistory,
    this.errorMessage,
    this.selectedCategory,
    this.pointsHistory,
    this.selectedTimePeriod = PointsTimePeriod.week,
    this.showBarChart = false,
    this.selectedRadius = 10.0, // Default 10km radius
    this.nearbyRewards,
  });

  @override
  List<Object?> get props => [
    status,
    userPoints,
    leaderboard,
    availableRewards,
    redemptionHistory,
    errorMessage,
    selectedCategory,
    pointsHistory,
    selectedTimePeriod,
    showBarChart,
    selectedRadius,
    nearbyRewards,
  ];

  /// Initial state
  factory DetoxPointsState.initial() => const DetoxPointsState();

  /// Create a copy of this state with the given fields replaced with new values
  DetoxPointsState copyWith({
    DetoxPointsStatus? status,
    DetoxPoints? userPoints,
    List<DetoxPoints>? leaderboard,
    List<Reward>? availableRewards,
    List<Reward>? redemptionHistory,
    String? errorMessage,
    RewardCategory? selectedCategory,
    PointsHistory? pointsHistory,
    PointsTimePeriod? selectedTimePeriod,
    bool? showBarChart,
    double? selectedRadius,
    List<Reward>? nearbyRewards,
    bool clearErrorMessage = false,
  }) {
    return DetoxPointsState(
      status: status ?? this.status,
      userPoints: userPoints ?? this.userPoints,
      leaderboard: leaderboard ?? this.leaderboard,
      availableRewards: availableRewards ?? this.availableRewards,
      redemptionHistory: redemptionHistory ?? this.redemptionHistory,
      errorMessage:
          clearErrorMessage ? null : errorMessage ?? this.errorMessage,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      pointsHistory: pointsHistory ?? this.pointsHistory,
      selectedTimePeriod: selectedTimePeriod ?? this.selectedTimePeriod,
      showBarChart: showBarChart ?? this.showBarChart,
      selectedRadius: selectedRadius ?? this.selectedRadius,
      nearbyRewards: nearbyRewards ?? this.nearbyRewards,
    );
  }
}

class DetoxPointsInitial extends DetoxPointsState {
  const DetoxPointsInitial();

  @override
  List<Object?> get props => [];
}

class DetoxPointsLoading extends DetoxPointsState {
  const DetoxPointsLoading();

  @override
  List<Object?> get props => [];
}

class DetoxPointsLoaded extends DetoxPointsState {
  final DetoxPoints points;
  final PointsHistory? history;

  const DetoxPointsLoaded(this.points, {this.history});

  @override
  List<Object?> get props => [points, history];
}

class DetoxPointsError extends DetoxPointsState {
  final String message;

  const DetoxPointsError(this.message);

  @override
  List<Object?> get props => [message];
}
