import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/user_notification.dart';
import 'package:detoxme/domain/repositories/notification_repository.dart';
import 'package:detoxme/presentation/bloc/notification/notification_state.dart';

/// Cubit for managing notification state and operations
class NotificationCubit extends Cubit<NotificationState> {
  final NotificationRepository _notificationRepository;
  StreamSubscription<List<UserNotification>>? _notificationSubscription;
  StreamSubscription<int>? _unreadCountSubscription;

  /// Creates a new [NotificationCubit]
  NotificationCubit({required NotificationRepository notificationRepository})
    : _notificationRepository = notificationRepository,
      super(const NotificationInitial());

  String? _currentUserId;

  /// Initialize notifications for a user
  void initializeNotifications(String userId) {
    if (_currentUserId == userId) return; // Already initialized for this user

    _currentUserId = userId;
    _startListening(userId);
  }

  /// Start listening to notification updates
  void _startListening(String userId) {
    _notificationSubscription?.cancel();
    _unreadCountSubscription?.cancel();

    // Listen to notifications stream
    _notificationSubscription = _notificationRepository
        .getUserNotificationsStream(userId)
        .listen(
          (notifications) {
            final currentState = state;
            if (currentState is NotificationLoaded) {
              emit(currentState.copyWith(notifications: notifications));
            } else {
              // Get initial unread count
              _getUnreadCount(userId, notifications);
            }
          },
          onError: (error) {
            emit(
              NotificationError(
                message: 'Failed to load notifications: $error',
              ),
            );
          },
        );

    // Listen to unread count stream
    _unreadCountSubscription = _notificationRepository
        .getUnreadNotificationCountStream(userId)
        .listen((unreadCount) {
          final currentState = state;
          if (currentState is NotificationLoaded) {
            emit(currentState.copyWith(unreadCount: unreadCount));
          }
        });
  }

  /// Get initial unread count
  void _getUnreadCount(
    String userId,
    List<UserNotification> notifications,
  ) async {
    final result = await _notificationRepository.getUnreadNotificationCount(
      userId,
    );
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (unreadCount) => emit(
        NotificationLoaded(
          notifications: notifications,
          unreadCount: unreadCount,
        ),
      ),
    );
  }

  /// Mark a notification as read
  Future<void> markAsRead(String notificationId, String userId) async {
    final result = await _notificationRepository.markNotificationAsRead(
      notificationId,
      userId,
    );
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) {
        // State will be updated automatically through stream
      },
    );
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead(String userId) async {
    final result = await _notificationRepository.markAllNotificationsAsRead(
      userId,
    );
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) {
        // State will be updated automatically through stream
      },
    );
  }

  /// Delete a notification
  Future<void> deleteNotification(String notificationId, String userId) async {
    final result = await _notificationRepository.deleteNotification(
      notificationId,
      userId,
    );
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) {
        // State will be updated automatically through stream
      },
    );
  }

  /// Create a comment like notification
  Future<void> createCommentLikeNotification({
    required String commentAuthorId,
    required String likerUserName,
    required String commentText,
    required String activityId,
    required String commentId,
  }) async {
    // Don't create notification if user likes their own comment
    if (commentAuthorId == _currentUserId) return;

    final result = await _notificationRepository.createCommentLikeNotification(
      commentAuthorId: commentAuthorId,
      likerUserName: likerUserName,
      commentText: commentText,
      activityId: activityId,
      commentId: commentId,
    );

    result.fold(
      (failure) => {
        // Silently fail for now, don't interrupt user experience
      },
      (_) {
        // Notification created successfully
      },
    );
  }

  /// Get current unread count (if state is loaded)
  int get unreadCount {
    final currentState = state;
    if (currentState is NotificationLoaded) {
      return currentState.unreadCount;
    }
    return 0;
  }

  /// Get current notifications (if state is loaded)
  List<UserNotification> get notifications {
    final currentState = state;
    if (currentState is NotificationLoaded) {
      return currentState.notifications;
    }
    return [];
  }

  @override
  Future<void> close() {
    _notificationSubscription?.cancel();
    _unreadCountSubscription?.cancel();
    return super.close();
  }
}
