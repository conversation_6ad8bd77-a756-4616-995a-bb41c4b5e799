import 'package:equatable/equatable.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';

/// Status of the wellbeing assistant
enum WellbeingAssistantStatus {
  /// Initial state, not yet initialized
  initial,

  /// Loading data
  loading,

  /// Successfully loaded data
  success,

  /// Error occurred
  failure,
}

/// State for the wellbeing assistant
class WellbeingAssistantState extends Equatable {
  /// Current status of the wellbeing assistant
  final WellbeingAssistantStatus status;

  /// Error message if status is failure
  final String? errorMessage;

  /// Current motivational content (legacy, for compatibility)
  final AiMotivation? motivation;

  /// Related activity (legacy, for compatibility)
  final Activity? activity;

  /// List of personalized motivations for the slider
  final List<AiMotivation> motivations;

  /// List of related activities for the slider
  final List<Activity> activities;

  /// Whether a notification has been scheduled
  final bool notificationScheduled;

  /// Creates a wellbeing assistant state
  const WellbeingAssistantState({
    this.status = WellbeingAssistantStatus.initial,
    this.errorMessage,
    this.motivation,
    this.activity,
    this.motivations = const [],
    this.activities = const [],
    this.notificationScheduled = false,
  });

  /// Creates the initial state
  factory WellbeingAssistantState.initial() => const WellbeingAssistantState();

  /// Creates a copy with the specified fields replaced
  WellbeingAssistantState copyWith({
    WellbeingAssistantStatus? status,
    String? errorMessage,
    AiMotivation? motivation,
    Activity? activity,
    List<AiMotivation>? motivations,
    List<Activity>? activities,
    bool? notificationScheduled,
  }) {
    return WellbeingAssistantState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      motivation: motivation ?? this.motivation,
      activity: activity ?? this.activity,
      motivations: motivations ?? this.motivations,
      activities: activities ?? this.activities,
      notificationScheduled: notificationScheduled ?? this.notificationScheduled,
    );
  }

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        motivation,
        activity,
        motivations,
        activities,
        notificationScheduled,
      ];
}
