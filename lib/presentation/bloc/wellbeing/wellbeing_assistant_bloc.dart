import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/core/services/wellbeing_assistant_service.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/usecases/get_current_user_usecase.dart';
import 'package:detoxme/presentation/bloc/activity/activity_timer_cubit.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_event.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_state.dart';
import 'package:flutter/foundation.dart';

/// Bloc for handling wellbeing assistant functionality
class WellbeingAssistantBloc
    extends Bloc<WellbeingAssistantEvent, WellbeingAssistantState> {
  /// Service for wellbeing assistance
  final WellbeingAssistantService _wellbeingService;

  /// Use case to get the current user
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  /// Cubit to manage activity timer
  final ActivityTimerCubit _activityTimerCubit;

  /// Timer for scheduling notifications
  Timer? _scheduleTimer;

  /// Creates a wellbeing assistant bloc
  WellbeingAssistantBloc({
    required WellbeingAssistantService wellbeingService,
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required ActivityTimerCubit activityTimerCubit,
  })  : _wellbeingService = wellbeingService,
        _getCurrentUserUseCase = getCurrentUserUseCase,
        _activityTimerCubit = activityTimerCubit,
        super(WellbeingAssistantState.initial()) {
    on<WellbeingAssistantStarted>(_onStarted);
    on<WellbeingNotificationTapped>(_onNotificationTapped);
    on<WellbeingRefreshRequested>(_onRefreshRequested);
    on<ScheduleWellbeingNotification>(_onScheduleNotification);
  }

  /// Handle the started event
  Future<void> _onStarted(
    WellbeingAssistantStarted event,
    Emitter<WellbeingAssistantState> emit,
  ) async {
    debugPrint('WellbeingAssistantBloc: Started event received');
    emit(state.copyWith(status: WellbeingAssistantStatus.loading));

    try {
      final userResult = await _getCurrentUserUseCase();

      await userResult.fold(
        (failure) {
          emit(
            state.copyWith(
              status: WellbeingAssistantStatus.failure,
              errorMessage: 'Failed to get user profile: ${failure.message}',
            ),
          );
        },
        (user) async {
          // Load personalized content
          await _loadPersonalizedContent(user, emit, language: event.language);

          // Schedule notifications
          add(const ScheduleWellbeingNotification());
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: WellbeingAssistantStatus.failure,
          errorMessage: 'An unexpected error occurred: $e',
        ),
      );
    }
  }

  /// Handle notification tapped event
  Future<void> _onNotificationTapped(
    WellbeingNotificationTapped event,
    Emitter<WellbeingAssistantState> emit,
  ) async {
    if (event.activityId.isEmpty) {
      return;
    }

    // We'll need to fetch the activity from its ID
    try {
      // Here we would fetch the activity and navigate to it
      // For example, routing to the activity details screen
      // and starting the timer if autoStartTimer is true

      if (event.autoStartTimer && state.activity != null) {
        _activityTimerCubit.startActivity(state.activity!);
      }
    } catch (e) {
      debugPrint('Error handling notification tap: $e');
    }
  }

  /// Handle refresh requested event
  Future<void> _onRefreshRequested(
    WellbeingRefreshRequested event,
    Emitter<WellbeingAssistantState> emit,
  ) async {
    emit(state.copyWith(status: WellbeingAssistantStatus.loading));

    try {
      final userResult = await _getCurrentUserUseCase();

      await userResult.fold(
        (failure) {
          emit(
            state.copyWith(
              status: WellbeingAssistantStatus.failure,
              errorMessage: 'Failed to get user profile: ${failure.message}',
            ),
          );
        },
        (user) async {
          await _loadPersonalizedContent(user, emit, language: null); // Use default language for refresh
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: WellbeingAssistantStatus.failure,
          errorMessage: 'An unexpected error occurred: $e',
        ),
      );
    }
  }

  /// Schedule wellbeing notifications
  Future<void> _onScheduleNotification(
    ScheduleWellbeingNotification event,
    Emitter<WellbeingAssistantState> emit,
  ) async {
    // Cancel any existing timer
    _scheduleTimer?.cancel();

    // Set up a periodic timer to check if we can send a notification
    _scheduleTimer = Timer.periodic(
      const Duration(hours: 1), // Check every hour
      (_) async {
        try {
          final userResult = await _getCurrentUserUseCase();

          await userResult.fold(
            (failure) {
              debugPrint('Failed to get user for notifications: ${failure.message}');
            },
            (user) async {
              final notificationSent =
                  await _wellbeingService.sendWellbeingNotification(user);

              if (notificationSent) {
                emit(state.copyWith(notificationScheduled: true));
              }
            },
          );
        } catch (e) {
          debugPrint('Error scheduling notification: $e');
        }
      },
    );

    // Also try to send a notification immediately if possible
    final userResult = await _getCurrentUserUseCase();

    await userResult.fold(
      (failure) {
        debugPrint('Failed to get user for notifications: ${failure.message}');
      },
      (user) async {
        final notificationSent =
            await _wellbeingService.sendWellbeingNotification(user);

        if (notificationSent) {
          emit(state.copyWith(notificationScheduled: true));
        }
      },
    );
  }

  /// Load personalized content for a user
  Future<void> _loadPersonalizedContent(
    UserProfile user,
    Emitter<WellbeingAssistantState> emit, {
    String? language,
  }) async {
    try {
      debugPrint('WellbeingAssistantBloc: Loading personalized content for user ${user.id}');
      debugPrint('WellbeingAssistantBloc: Calling wellbeing service');
      // Fetch the list for the slider with current language
      final contentList = await _wellbeingService.getPersonalizedMotivationAndActivities(user, limit: 5, language: language);
      if (contentList.isEmpty) {
        emit(
          state.copyWith(
            status: WellbeingAssistantStatus.failure,
            errorMessage: 'No suitable content found for your profile',
            motivations: [],
            activities: [],
          ),
        );
        return;
      }
      // For legacy compatibility, also set the first motivation/activity
      final first = contentList.first;
      emit(
        state.copyWith(
          status: WellbeingAssistantStatus.success,
          motivation: first.motivation,
          activity: first.activity,
          motivations: contentList.map((e) => e.motivation).toList(),
          activities: contentList.map((e) => e.activity).toList(),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: WellbeingAssistantStatus.failure,
          errorMessage: 'Failed to load personalized content: $e',
          motivations: [],
          activities: [],
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _scheduleTimer?.cancel();
    return super.close();
  }
}
