import 'package:equatable/equatable.dart';

/// Base event class for the wellbeing assistant
abstract class WellbeingAssistantEvent extends Equatable {
  const WellbeingAssistantEvent();

  @override
  List<Object?> get props => [];
}

/// Event to start the wellbeing assistant
class WellbeingAssistantStarted extends WellbeingAssistantEvent {
  /// The language code for content localization
  final String? language;
  
  const WellbeingAssistantStarted({this.language});

  @override
  List<Object?> get props => [language];
}

/// Event triggered when a notification is tapped
class WellbeingNotificationTapped extends WellbeingAssistantEvent {
  /// ID of the activity to navigate to
  final String activityId;
  
  /// Whether to automatically start the timer
  final bool autoStartTimer;

  /// Creates a notification tapped event
  const WellbeingNotificationTapped({
    required this.activityId,
    this.autoStartTimer = true,
  });

  @override
  List<Object?> get props => [activityId, autoStartTimer];
}

/// Event to request a manual check for personalized content
class WellbeingRefreshRequested extends WellbeingAssistantEvent {
  const WellbeingRefreshRequested();
}

/// Event to schedule the next wellbeing notification
class ScheduleWellbeingNotification extends WellbeingAssistantEvent {
  const ScheduleWellbeingNotification();
}
