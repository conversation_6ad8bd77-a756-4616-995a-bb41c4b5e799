import 'package:flutter/material.dart';
import 'package:detoxme/core/extensions/color_extensions.dart';
import 'package:detoxme/domain/entities/weather_data.dart';

/// Extension to add UI-specific methods to WeatherCondition
extension WeatherConditionUI on WeatherCondition {
  String get icon {
    switch (this) {
      case WeatherCondition.sunny:
        return '☀️';
      case WeatherCondition.partlyCloudy:
        return '🌤️';
      case WeatherCondition.cloudy:
        return '☁️';
      case WeatherCondition.rainy:
        return '🌧️';
      case WeatherCondition.stormy:
        return '⛈️';
      case WeatherCondition.snowy:
        return '❄️';
      case WeatherCondition.foggy:
        return '🌫️';
      case WeatherCondition.windy:
        return '💨';
      case WeatherCondition.unknown:
        return '❓';
    }
  }

  String get name {
    switch (this) {
      case WeatherCondition.sunny:
        return 'Sunny';
      case WeatherCondition.partlyCloudy:
        return 'Partly Cloudy';
      case WeatherCondition.cloudy:
        return 'Cloudy';
      case WeatherCondition.rainy:
        return 'Rainy';
      case WeatherCondition.stormy:
        return 'Stormy';
      case WeatherCondition.snowy:
        return 'Snowy';
      case WeatherCondition.foggy:
        return 'Foggy';
      case WeatherCondition.windy:
        return 'Windy';
      case WeatherCondition.unknown:
        return 'Unknown';
    }
  }

  Color getBackgroundColor(ColorScheme colorScheme) {
    switch (this) {
      case WeatherCondition.sunny:
        return Colors.orange.withValue(alpha: 26);
      case WeatherCondition.partlyCloudy:
        return Colors.amber.withValue(alpha: 26);
      case WeatherCondition.cloudy:
        return Colors.blueGrey.withValue(alpha: 26);
      case WeatherCondition.rainy:
        return Colors.blue.withValue(alpha: 26);
      case WeatherCondition.stormy:
        return Colors.deepPurple.withValue(alpha: 26);
      case WeatherCondition.snowy:
        return Colors.lightBlue.withValue(alpha: 26);
      case WeatherCondition.foggy:
        return Colors.grey.withValue(alpha: 26);
      case WeatherCondition.windy:
        return Colors.teal.withValue(alpha: 26);
      case WeatherCondition.unknown:
        return colorScheme.surface.withValue(alpha: 26);
    }
  }

  LinearGradient getGradient(ColorScheme colorScheme) {
    switch (this) {
      case WeatherCondition.sunny:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange.withValue(alpha: 179),
            Colors.amber.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.partlyCloudy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.lightBlue.withValue(alpha: 179),
            Colors.amber.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.cloudy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blueGrey.withValue(alpha: 179),
            Colors.grey.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.rainy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blueGrey.withValue(alpha: 179),
            Colors.blue.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.stormy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple.withValue(alpha: 179),
            Colors.indigo.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.snowy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.lightBlue.withValue(alpha: 179),
            Colors.white.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.foggy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey.withValue(alpha: 179),
            Colors.white.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.windy:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.teal.withValue(alpha: 179),
            Colors.cyan.withValue(alpha: 179),
          ],
        );
      case WeatherCondition.unknown:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.surface.withValue(alpha: 179),
            colorScheme.surfaceContainerHighest.withValue(alpha: 179),
          ],
        );
    }
  }

  /// Returns the appropriate icon for a weather condition
  IconData getIconData() {
    switch (this) {
      case WeatherCondition.sunny:
        return Icons.wb_sunny;
      case WeatherCondition.partlyCloudy:
        return Icons.cloud_queue;
      case WeatherCondition.cloudy:
        return Icons.cloud;
      case WeatherCondition.rainy:
        return Icons.grain;
      case WeatherCondition.stormy:
        return Icons.thunderstorm;
      case WeatherCondition.snowy:
        return Icons.ac_unit;
      case WeatherCondition.foggy:
        return Icons.cloud;
      case WeatherCondition.windy:
        return Icons.air;
      case WeatherCondition.unknown:
        return Icons.question_mark;
    }
  }

  /// Returns a color associated with the weather condition
  Color getColor(ThemeData theme) {
    switch (this) {
      case WeatherCondition.sunny:
        return Colors.orange;
      case WeatherCondition.partlyCloudy:
        return Colors.amber;
      case WeatherCondition.cloudy:
        return Colors.blueGrey;
      case WeatherCondition.rainy:
        return Colors.blue;
      case WeatherCondition.stormy:
        return Colors.deepPurple;
      case WeatherCondition.snowy:
        return Colors.lightBlue;
      case WeatherCondition.foggy:
        return Colors.grey;
      case WeatherCondition.windy:
        return Colors.teal;
      case WeatherCondition.unknown:
        return theme.colorScheme.onSurface;
    }
  }
}

/// A unified weather widget that can display weather information in different layouts
/// Replaces both WeatherCard and WeatherDisplay with a single, flexible widget
class WeatherCard extends StatelessWidget {
  /// Weather data to display (preferred - contains all weather info)
  final WeatherData? weatherData;

  /// Current weather condition (fallback if weatherData is null)
  final WeatherCondition? condition;

  /// Current temperature in Celsius (fallback if weatherData is null)
  final double? temperature;

  /// Location name (fallback if weatherData is null)
  final String? location;

  /// Whether to use compact layout
  final bool isCompact;

  /// Whether to show detailed information (humidity, wind, visibility)
  final bool showDetails;

  /// Size of the main weather icon
  final double iconSize;

  /// Creates a [WeatherCard] widget with WeatherData (preferred)
  const WeatherCard.fromWeatherData({
    super.key,
    required this.weatherData,
    this.isCompact = false,
    this.showDetails = true,
    this.iconSize = 48,
  }) : condition = null,
       temperature = null,
       location = null;

  /// Creates a [WeatherCard] widget with individual parameters (fallback)
  const WeatherCard({
    super.key,
    required this.condition,
    required this.temperature,
    required this.location,
    this.isCompact = false,
    this.showDetails = false,
    this.iconSize = 48,
  }) : weatherData = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Use weatherData if available, otherwise use individual parameters
    final effectiveCondition =
        weatherData?.condition ?? condition ?? WeatherCondition.unknown;
    final effectiveTemperature = weatherData?.temperature ?? temperature ?? 0.0;
    final effectiveLocation = weatherData?.location ?? location ?? 'Unknown';

    return isCompact
        ? _buildCompactView(theme, effectiveCondition, effectiveTemperature)
        : _buildFullView(
          theme,
          effectiveCondition,
          effectiveTemperature,
          effectiveLocation,
        );
  }

  /// Builds a compact weather display
  Widget _buildCompactView(
    ThemeData theme,
    WeatherCondition condition,
    double temperature,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: condition.getBackgroundColor(theme.colorScheme),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(condition.icon, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 8),
          Text(
            '${temperature.round()}°C',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a full weather card display
  Widget _buildFullView(
    ThemeData theme,
    WeatherCondition condition,
    double temperature,
    String location,
  ) {
    final textTheme = theme.textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: condition.getGradient(theme.colorScheme),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValue(alpha: 26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Location and weather icon row
          Row(
            children: [
              Icon(
                condition.getIconData(),
                size: iconSize,
                color: condition.getColor(theme),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      location,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      condition.name,
                      style: textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValue(
                          alpha: 179,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${temperature.round()}°',
                style: textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          // Show details if required and weatherData is available
          if (showDetails && weatherData != null) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildDetailItem(
                  theme,
                  Icons.water_drop_outlined,
                  '${weatherData!.humidity}%',
                  'Humidity',
                ),
                _buildDetailItem(
                  theme,
                  Icons.air,
                  '${weatherData!.windSpeed} km/h',
                  'Wind',
                ),
                _buildDetailItem(
                  theme,
                  Icons.visibility_outlined,
                  '${weatherData!.visibility} km',
                  'Visibility',
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Builds a detail item with icon, value and label
  Widget _buildDetailItem(
    ThemeData theme,
    IconData icon,
    String value,
    String label,
  ) {
    final textTheme = theme.textTheme;

    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary.withValue(alpha: 204),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        Text(
          label,
          style: textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValue(alpha: 153),
          ),
        ),
      ],
    );
  }
}
