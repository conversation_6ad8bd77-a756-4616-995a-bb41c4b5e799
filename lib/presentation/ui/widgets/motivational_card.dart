import 'package:flutter/material.dart';
import 'package:detoxme/core/extensions/color_extensions.dart';
import 'package:detoxme/domain/entities/weather_data.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'dart:ui';

/// A unified widget that displays motivational content in different formats
/// Can display either quote-based motivational content or AI-generated motivational text
class MotivationalCard extends StatelessWidget {
  /// AI motivation data (preferred for AI-generated content)
  final AiMotivation? aiMotivation;

  /// User profile for AI motivation display
  final UserProfile? userProfile;

  /// Weather info for AI motivation display
  final String? weatherInfo;

  /// Weather icon for AI motivation display
  final String? weatherIcon;

  /// Whether the user has children (for quote-based content)
  final bool? hasChildren;

  /// User's primary goal (for quote-based content)
  final String? userGoal;

  /// Current weather condition (for quote-based content)
  final WeatherCondition? weatherCondition;

  /// Current day progress (for quote-based content)
  final double? dayProgress;

  /// Current detox points (for quote-based content)
  final int? detoxPoints;

  /// Creates a [MotivationalCard] with AI motivation content (preferred)
  const MotivationalCard.fromAiMotivation({
    super.key,
    required this.aiMotivation,
    required this.userProfile,
    required this.weatherInfo,
    this.weatherIcon,
  }) : hasChildren = null,
       userGoal = null,
       weatherCondition = null,
       dayProgress = null,
       detoxPoints = null;

  /// Creates a [MotivationalCard] with quote-based content (fallback)
  const MotivationalCard({
    super.key,
    required this.hasChildren,
    required this.userGoal,
    required this.weatherCondition,
    required this.dayProgress,
    required this.detoxPoints,
  }) : aiMotivation = null,
       userProfile = null,
       weatherInfo = null,
       weatherIcon = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Use AI motivation if available, otherwise use quote-based content
    return aiMotivation != null
        ? _buildAiMotivationCard(theme)
        : _buildQuoteCard(theme);
  }

  /// Builds a modern AI motivation card
  Widget _buildAiMotivationCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.tertiary.withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: -20,
                right: -20,
                child: IgnorePointer(
                  child: Opacity(
                    opacity: 0.1,
                    child: Icon(
                      Icons.auto_awesome,
                      size: 150,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              Positioned(
                bottom: 10,
                left: 10,
                child: IgnorePointer(
                  child: Opacity(
                    opacity: 0.1,
                    child: Icon(
                      Icons.format_quote_rounded,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 20, 20),
                child: _buildAiMotivationalContent(theme),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the AI motivational content
  Widget _buildAiMotivationalContent(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(
          Icons.format_quote_rounded,
          color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
          size: 32,
        ),

        const SizedBox(height: 12),

        Text(
          aiMotivation!.motivationalText,
          textAlign: TextAlign.center,
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
            height: 1.5,
            fontWeight: FontWeight.w600,
            shadows: [
              Shadow(
                blurRadius: 6.0,
                color: Colors.black.withValues(alpha: 0.3),
                offset: const Offset(1.0, 1.0),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        Container(
          width: 60,
          height: 4,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    );
  }

  /// Builds a quote-based motivational card
  Widget _buildQuoteCard(ThemeData theme) {
    final quote = _getMotivationalQuote();

    return Container(
      padding: const EdgeInsets.all(20),
      margin: EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            Color.lerp(
              theme.colorScheme.primary,
              theme.colorScheme.tertiary,
              0.5,
            )!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValue(
              alpha: (0.15 * 255).toInt(),
            ),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Quote icon
          Icon(
            Icons.format_quote,
            color: Colors.white.withValue(alpha: (0.8 * 255).toInt()),
            size: 28,
          ),
          
          const SizedBox(height: 12),

          // Motivational quote
          Text(
            '"${quote['text']}"',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.white,
              height: 1.4,
              fontStyle: FontStyle.italic,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Author
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '— ${quote['author']}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withValue(alpha: (0.8 * 255).toInt()),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Returns a motivational quote with author based on user context
  Map<String, String> _getMotivationalQuote() {
    // Family specific quotes
    if (hasChildren == true) {
      final familyQuotes = [
        {'text': 'The days are long, but the years are short. Be present for the moments that matter.', 'author': 'Gretchen Rubin'},
        {'text': 'Children are great imitators, so give them something great to imitate.', 'author': 'Anonymous'},
        {'text': 'The best gift you can give your children is your presence, not your presents.', 'author': 'Jesse Jackson'},
      ];
      return familyQuotes[(detoxPoints ?? 0) % familyQuotes.length];
    }

    // Goal specific quotes
    if (userGoal == 'focus') {
      final focusQuotes = [
        {'text': 'The successful warrior is the average man with laser-like focus.', 'author': 'Bruce Lee'},
        {'text': 'Concentrate all your thoughts upon the work at hand. The sun\'s rays do not burn until brought to a focus.', 'author': 'Alexander Graham Bell'},
        {'text': 'Focus is a matter of deciding what things you\'re not going to do.', 'author': 'John Carmack'},
      ];
      return focusQuotes[(detoxPoints ?? 0) % focusQuotes.length];
    } else if (userGoal == 'sleep') {
      final sleepQuotes = [
        {'text': 'Sleep is the best meditation.', 'author': 'Dalai Lama'},
        {'text': 'A good laugh and a long sleep are the best cures in the doctor\'s book.', 'author': 'Irish Proverb'},
        {'text': 'Sleep is that golden chain that ties health and our bodies together.', 'author': 'Thomas Dekker'},
      ];
      return sleepQuotes[(detoxPoints ?? 0) % sleepQuotes.length];
    } else if (userGoal == 'social') {
      final socialQuotes = [
        {'text': 'The quality of your life is the quality of your relationships.', 'author': 'Tony Robbins'},
        {'text': 'We are all alone, born alone, die alone, and we shall all someday look back on our lives and see that, in spite of our company, we were alone the whole way.', 'author': 'Hunter S. Thompson'},
        {'text': 'The greatest thing in the world is to know how to belong to oneself.', 'author': 'Michel de Montaigne'},
      ];
      return socialQuotes[(detoxPoints ?? 0) % socialQuotes.length];
    }

    // Generic mindfulness and digital wellness quotes
    final List<Map<String, String>> genericQuotes = [
      {'text': 'The present moment is the only time over which we have dominion.', 'author': 'Thích Nhất Hạnh'},
      {'text': 'Technology is a useful servant but a dangerous master.', 'author': 'Christian Lous Lange'},
      {'text': 'Almost everything will work again if you unplug it for a few minutes, including you.', 'author': 'Anne Lamott'},
      {'text': 'The art of living is more like wrestling than dancing.', 'author': 'Marcus Aurelius'},
      {'text': 'Mindfulness is about being fully awake in our lives.', 'author': 'Jon Kabat-Zinn'},
    ];

    return genericQuotes[(detoxPoints ?? 0) % genericQuotes.length];
  }
}
