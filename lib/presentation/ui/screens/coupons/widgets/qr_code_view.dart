import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/redemption.dart';

/// A widget for displaying a QR code for coupon redemption
class QRCodeView extends StatelessWidget {
  /// The redemption containing the QR code
  final Redemption redemption;

  /// The coupon being redeemed
  final Coupon coupon;

  /// Function to call when the feedback button is pressed
  final VoidCallback? onFeedback;

  /// Constructor for QRCodeView
  const QRCodeView({
    required this.redemption,
    required this.coupon,
    this.onFeedback,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final partner = coupon.partner;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header with partner info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: theme.colorScheme.primaryContainer,
                radius: 24,
                child: partner.logoUrl != null
                    ? ClipOval(
                          child: CachedNetworkImage(
                            imageUrl: partner.logoUrl!,
                          width: 48,
                          height: 48,
                          fit: BoxFit.cover,
                            memCacheWidth: 96, // 2x for high DPI
                            memCacheHeight: 96,
                            useOldImageOnUrlChange: true,
                            placeholder:
                                (context, url) => Icon(
                                  Icons.storefront,
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                            errorWidget:
                                (_, __, ___) => Icon(
                            Icons.storefront,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.storefront,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      partner.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      coupon.discount,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // QR Code section
        Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Text(
                'Zeige diesen QR-Code beim Partner',
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFF008080), // Teal border
                    width: 4,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.all(16),
                child: Container(
                  width: 200,
                  height: 200,
                  color: Colors.white,
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.qr_code, size: 100),
                      const SizedBox(height: 8),
                      Text(
                        redemption.qrCode,
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                coupon.description,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              // Feedback button, only show if feedback hasn't been provided yet
              if (!redemption.hasFeedback && onFeedback != null)
                ElevatedButton.icon(
                  onPressed: onFeedback,
                  icon: const Icon(Icons.feedback),
                  label: const Text('War der Coupon hilfreich?'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primaryContainer,
                    foregroundColor: theme.colorScheme.onPrimaryContainer,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
