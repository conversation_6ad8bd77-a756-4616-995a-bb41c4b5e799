import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/redemption.dart';

/// Dialog for collecting feedback after coupon redemption
class CouponFeedbackDialog extends StatefulWidget {
  /// The redemption to provide feedback for
  final Redemption redemption;
  
  /// Callback function when feedback is submitted
  final void Function(int rating, String? comment) onSubmit;

  /// Constructor for CouponFeedbackDialog
  const CouponFeedbackDialog({
    required this.redemption,
    required this.onSubmit,
    super.key,
  });

  @override
  State<CouponFeedbackDialog> createState() => _CouponFeedbackDialogState();
}

class _CouponFeedbackDialogState extends State<CouponFeedbackDialog> {
  int _rating = 0;
  final TextEditingController _commentController = TextEditingController();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Wie war deine Erfahrung?',
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            // Rating stars
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                final starIndex = index + 1;
                return IconButton(
                  icon: Icon(
                    starIndex <= _rating
                        ? Icons.star_rounded
                        : Icons.star_outline_rounded,
                    size: 36,
                    color: starIndex <= _rating
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                  onPressed: () {
                    setState(() {
                      _rating = starIndex;
                    });
                  },
                );
              }),
            ),
            const SizedBox(height: 16),
            // Comment field
            TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Kommentar (optional)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 24),
            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Abbrechen',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _rating > 0
                      ? () {
                          widget.onSubmit(
                            _rating,
                            _commentController.text.isNotEmpty
                                ? _commentController.text
                                : null,
                          );
                          Navigator.of(context).pop();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: const Text('Senden'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
