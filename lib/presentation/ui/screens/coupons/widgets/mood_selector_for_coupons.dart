import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../domain/entities/mood.dart';
import '../../../../bloc/coupon/coupon_cubit.dart';
import '../../../../bloc/dashboard/dashboard_cubit.dart';

/// A widget for selecting a mood to filter coupons
class MoodSelectorForCoupons extends StatelessWidget {
  /// Creates a new MoodSelectorForCoupons widget
  const MoodSelectorForCoupons({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final allMoods = MoodType.values;

    // Calculate the number of moods for the first row
    final firstRowCount = (allMoods.length / 2).ceil();

    // Use LayoutBuilder to respond to available height
    return LayoutBuilder(
      builder: (context, constraints) {
        // If height is too small, don't render content to avoid overflow
        if (constraints.maxHeight < 50) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Use minimum required space
          children: [
            Text(
              'Find rewards for your mood',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Discover coupons that match how you feel',
              style: theme.textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 12),
            // First row of moods
            if (constraints.maxHeight >=
                120) // Only show mood rows if enough height
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  firstRowCount,
                  (index) => MoodItemForCoupons(mood: allMoods[index]),
                ),
              ),
            const SizedBox(height: 4),
            // Second row of moods
            if (allMoods.length > firstRowCount && constraints.maxHeight >= 170)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  allMoods.length - firstRowCount,
                  (index) => MoodItemForCoupons(
                    mood: allMoods[index + firstRowCount],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Widget representing a single mood item for coupon filtering
class MoodItemForCoupons extends StatelessWidget {
  /// The mood type to display
  final MoodType mood;

  /// Creates a new MoodItemForCoupons
  const MoodItemForCoupons({required this.mood, super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSelected = context.select<CouponCubit, bool>(
      (cubit) => cubit.state.selectedMood == mood,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              // Update both CouponCubit and DashboardCubit when mood is selected
              context.read<CouponCubit>().selectMood(mood);

              // Sync with dashboard cubit to ensure consistency across tabs
              context.read<DashboardCubit>().setCurrentMood(mood);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? mood.color.withValues(alpha: 0.2)
                        : theme.colorScheme.surface,
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected
                          ? mood.color
                          : theme.colorScheme.outline.withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  mood.emoji,
                  style: TextStyle(
                    fontSize: 28,
                    shadows:
                        isSelected
                            ? [
                              Shadow(
                                blurRadius: 10,
                                color: mood.color.withValues(alpha: 100),
                              ),
                            ]
                            : null,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 200),
            style:
                theme.textTheme.bodySmall?.copyWith(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color:
                      isSelected
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withValues(alpha: 180),
                  fontSize: isSelected ? 12 : 10,
                ) ??
                const TextStyle(),
            child: Text(
              mood.localizedDescription(context),
              textAlign: TextAlign.center,
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: isSelected ? 12 : 10,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
