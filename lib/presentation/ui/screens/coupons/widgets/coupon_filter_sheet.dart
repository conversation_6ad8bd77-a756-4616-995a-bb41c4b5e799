import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_cubit.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_state.dart';

/// A bottom sheet for filtering and sorting coupons
class CouponFilterSheet extends StatefulWidget {
  /// Creates a new CouponFilterSheet
  const CouponFilterSheet({super.key});

  @override
  State<CouponFilterSheet> createState() => _CouponFilterSheetState();
}

class _CouponFilterSheetState extends State<CouponFilterSheet> {
  late CouponSortOption _sortOption;
  late bool _showOnlyNearby;
  late bool _showOnlyRedeemed;
  late List<String> _selectedLanguages;

  @override
  void initState() {
    super.initState();

    // Initialize with current state values
    final state = context.read<CouponCubit>().state;
    _sortOption = state.sortOption;
    _showOnlyNearby = state.showOnlyNearby;
    _showOnlyRedeemed = state.showOnlyRedeemed;
    _selectedLanguages = List.from(state.selectedLanguages);
  }

  // Country/language options
  final Map<String, Map<String, String>> _languageOptions = {
    'en': {'name': 'English', 'flag': '🇬🇧'},
    'de': {'name': 'Deutsch', 'flag': '🇩🇪'},
    'ru': {'name': 'Russisch', 'flag': '🇷🇺'},
    'tr': {'name': 'Türkisch', 'flag': '🇹🇷'},
    'ar': {'name': 'Arabisch', 'flag': '🇸🇦'},
  };

  void _applyFilters() {
    final couponCubit = context.read<CouponCubit>();

    // Apply sort option
    couponCubit.applySortOption(_sortOption);

    // Apply nearby filter
    couponCubit.toggleNearbyFilter(_showOnlyNearby);

    // Apply redeemed filter
    couponCubit.toggleRedeemedFilter(_showOnlyRedeemed);

    // Apply language filters
    couponCubit.updateLanguageFilters(_selectedLanguages);

    // Close the bottom sheet
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.only(
        top: 16,
        left: 20,
        right: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 60),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 100),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                'Filter & Sortierung',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  foregroundColor: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          // Scrollable content
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  // Sorting section
                  Text(
                    'Sortierung',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Sort options in Wrap layout
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildSortOptionTile(
                        context,
                        'Neueste zuerst',
                        CouponSortOption.newest,
                        Icons.new_releases_outlined,
                      ),
                      _buildSortOptionTile(
                        context,
                        'Beliebteste',
                        CouponSortOption.mostPopular,
                        Icons.trending_up_outlined,
                      ),
                      _buildSortOptionTile(
                        context,
                        'Höchster Rabatt',
                        CouponSortOption.highestDiscount,
                        Icons.local_offer_outlined,
                      ),
                      _buildSortOptionTile(
                        context,
                        'Nächstgelegene',
                        CouponSortOption.nearest,
                        Icons.near_me_outlined,
                      ),
                      _buildSortOptionTile(
                        context,
                        'Stimmungsrelevanz',
                        CouponSortOption.moodRelevance,
                        Icons.mood_outlined,
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Filter options
                  Text(
                    'Filter',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Nearby filter
                  SwitchListTile(
                    title: const Row(
                      children: [
                        Icon(Icons.location_on_outlined, size: 20),
                        SizedBox(width: 12),
                        Text('Nur in der Nähe'),
                      ],
                    ),
                    value: _showOnlyNearby,
                    onChanged: (value) {
                      setState(() {
                        _showOnlyNearby = value;

                        // Don't allow both filters to be active at once
                        if (value && _showOnlyRedeemed) {
                          _showOnlyRedeemed = false;
                        }
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withValues(alpha: 30),
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    activeColor: theme.colorScheme.primary,
                  ),
                  const SizedBox(height: 8),
                  // Redeemed filter
                  SwitchListTile(
                    title: const Row(
                      children: [
                        Icon(Icons.history_outlined, size: 20),
                        SizedBox(width: 12),
                        Text('Nur eingelöste anzeigen'),
                      ],
                    ),
                    value: _showOnlyRedeemed,
                    onChanged: (value) {
                      setState(() {
                        _showOnlyRedeemed = value;

                        // Don't allow both filters to be active at once
                        if (value && _showOnlyNearby) {
                          _showOnlyNearby = false;
                        }
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withValues(alpha: 30),
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    activeColor: theme.colorScheme.primary,
                  ),
                  const SizedBox(height: 16),

                  // Languages
                  Text(
                    'Sprachen',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _languageOptions.entries.map((entry) {
                          final isSelected = _selectedLanguages.contains(
                            entry.key,
                          );
                          return FilterChip(
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(entry.value['flag'] ?? ''),
                                const SizedBox(width: 6),
                                Text(
                                  entry.value['name'] ?? '',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight:
                                        isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  _selectedLanguages.add(entry.key);
                                } else {
                                  // Ensure at least one language is selected
                                  if (_selectedLanguages.length > 1) {
                                    _selectedLanguages.remove(entry.key);
                                  }
                                }
                              });
                            },
                            backgroundColor:
                                theme.colorScheme.surfaceContainerHighest,
                            selectedColor: theme.colorScheme.primary,
                            showCheckmark: false,
                            labelStyle: TextStyle(
                              color:
                                  isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.onSurfaceVariant,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color:
                                    isSelected
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.outline.withValues(
                                          alpha: 50,
                                        ),
                                width: isSelected ? 1.5 : 0.2,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          // Apply filters button - Fixed at the bottom
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text('Filter anwenden'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptionTile(
    BuildContext context,
    String label,
    CouponSortOption option,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final isSelected = _sortOption == option;

    // Calculate width for 3 items per row with spacing
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth =
        (screenWidth - 40 - 16) /
        3; // 40 for padding, 16 for spacing between items

    return InkWell(
      onTap: () {
        setState(() {
          _sortOption = option;
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: itemWidth,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 30),
            width: isSelected ? 2 : 0.2,
          ),
        ),
        // Change back to Column to fix overflow
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color:
                  isSelected
                      ? Colors.white
                      : theme.colorScheme.onSurfaceVariant,
              size: 20,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.white : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
