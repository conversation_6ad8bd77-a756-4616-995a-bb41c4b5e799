import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/detox_points.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';

/// A widget that shows the top 3 users on the leaderboard
class LeaderboardPreview extends StatelessWidget {
  /// The list of detox points entries, ordered by rank
  final List<DetoxPoints> leaderboardEntries;

  /// Creates a [LeaderboardPreview]
  const LeaderboardPreview({super.key, required this.leaderboardEntries});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show placeholder if no data is available
    if (leaderboardEntries.isEmpty) {
      return _buildEmptyState(theme);
    }

    // Get top 3 users, or all if less than 3
    final topUsers = leaderboardEntries.take(3).toList();

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Leaderboard',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    context.push(AppRoutes.leaderboard);
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Display top 3 users
            ...List.generate(
              topUsers.length,
              (index) => FadeInUp(
                delay: Duration(milliseconds: 100 * index),
                duration: const Duration(milliseconds: 400),
                child: _buildLeaderboardItem(
                  theme,
                  topUsers[index],
                  index + 1, // 1-based rank
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeaderboardItem(ThemeData theme, DetoxPoints entry, int rank) {
    final isTopRank = rank <= 3;

    // Icons for top 3 positions
    IconData? medalIcon;
    Color? medalColor;

    if (rank == 1) {
      medalIcon = Icons.workspace_premium;
      medalColor = Colors.amber;
    } else if (rank == 2) {
      medalIcon = Icons.workspace_premium;
      medalColor = Colors.grey.shade400;
    } else if (rank == 3) {
      medalIcon = Icons.workspace_premium;
      medalColor = Colors.brown.shade300;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withAlpha(20)),
      ),
      child: Row(
        children: [
          // Rank badge
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color:
                  isTopRank
                      ? theme.colorScheme.primaryContainer
                      : theme.colorScheme.surfaceContainerHighest,
              shape: BoxShape.circle,
            ),
            child: Center(
              child:
                  isTopRank && medalIcon != null
                      ? Icon(medalIcon, color: medalColor, size: 20)
                      : Text(
                        rank.toString(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              isTopRank
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
            ),
          ),
          const SizedBox(width: 12),

          // User avatar
          ClipOval(
            child:
                entry.avatarUrl != null
                    ? CachedNetworkImage(
                      imageUrl: entry.avatarUrl!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      memCacheWidth: 80, // 2x for high DPI
                      memCacheHeight: 80,
                      useOldImageOnUrlChange: true,
                      placeholder:
                          (context, url) => _buildFallbackAvatar(theme),
                      errorWidget:
                          (context, error, stackTrace) =>
                              _buildFallbackAvatar(theme),
                    )
                    : _buildFallbackAvatar(theme),
          ),
          const SizedBox(width: 12),

          // Username and level
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.userName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'Level ${entry.level}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(180),
                  ),
                ),
              ],
            ),
          ),

          // Points
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${entry.totalPoints}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              Text(
                'Points',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(180),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFallbackAvatar(ThemeData theme) {
    return Container(
      width: 40,
      height: 40,
      color: theme.colorScheme.primary.withAlpha(30),
      child: Icon(Icons.person, color: theme.colorScheme.primary, size: 24),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Leaderboard',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Don't try to navigate from a static context
                    // Use a Builder or another approach if needed
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 48,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No leaderboard data yet',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Complete challenges to see your ranking',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(150),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
