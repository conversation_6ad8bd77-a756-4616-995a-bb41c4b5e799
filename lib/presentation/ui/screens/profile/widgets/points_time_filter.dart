import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/points_history.dart';

/// A widget that allows selecting different time periods for points display
class PointsTimeFilter extends StatelessWidget {
  /// Currently selected time period
  final PointsTimePeriod selectedPeriod;

  /// Callback when a time period is selected
  final Function(PointsTimePeriod) onPeriodSelected;

  /// Creates a [PointsTimeFilter] widget
  const PointsTimeFilter({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFilterOption(context, PointsTimePeriod.day, '24h', theme),
            const SizedBox(width: 8),
            _buildFilterOption(context, PointsTimePeriod.week, '7 Days', theme),
            const SizedBox(width: 8),
            _buildFilterOption(
              context,
              PointsTimePeriod.month,
              '1 Month',
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(
    BuildContext context,
    PointsTimePeriod period,
    String label,
    ThemeData theme,
  ) {
    final bool isSelected = selectedPeriod == period;

    return InkWell(
      onTap: () => onPeriodSelected(period),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color:
                isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
