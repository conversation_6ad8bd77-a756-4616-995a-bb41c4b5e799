import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:intl/intl.dart';

/// A widget that shows a preview of the user's reward history
class RewardHistoryPreview extends StatelessWidget {
  /// The list of rewards, ordered by date (newest first)
  final List<Reward> rewards;

  /// The number of rewards to show
  final int limit;

  /// Creates a [RewardHistoryPreview]
  const RewardHistoryPreview({
    super.key,
    required this.rewards,
    this.limit = 3,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show placeholder if no data is available
    if (rewards.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    // Get most recent rewards up to the limit
    final recentRewards = rewards.take(limit).toList();

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Rewards',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    context.push(AppRoutes.rewardHistory);
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Display recent rewards
            ...List.generate(
              recentRewards.length,
              (index) => FadeInUp(
                delay: Duration(milliseconds: 100 * index),
                duration: const Duration(milliseconds: 400),
                child: _buildRewardItem(context, theme, recentRewards[index]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardItem(
    BuildContext context,
    ThemeData theme,
    Reward reward,
  ) {
    final dateText = DateFormat.yMMMd().format(
      reward.createdAt,
    );
    final timeText = DateFormat.jm().format(
      reward.createdAt);

    // Get category from metadata if available
    RewardCategory category = RewardCategory.other;
    if (reward.metadata != null && reward.metadata!.containsKey('category')) {
      String? categoryStr = reward.metadata!['category'] as String?;
      if (categoryStr != null) {
        try {
          category = RewardCategory.values.firstWhere(
            (cat) => cat.name == categoryStr.toLowerCase(),
            orElse: () => RewardCategory.other,
          );
        } catch (_) {
          // Default to 'other' if we can't parse the category
          category = RewardCategory.other;
        }
      }
    } else if (reward.rewardType == RewardType.couponRedemption) {
      category = RewardCategory.shopping;
    } else if (reward.rewardType == RewardType.activityCompletion) {
      category = RewardCategory.wellness;
    } else if (reward.rewardType == RewardType.achievement) {
      category = RewardCategory.achievement;
    }
    
    // Assign icon and color based on reward type
    IconData icon;
    Color iconColor;

    switch (category) {
      case RewardCategory.digital:
        icon = Icons.smartphone;
        iconColor = theme.colorScheme.primary;
        break;
      case RewardCategory.physical:
        icon = Icons.card_giftcard;
        iconColor = theme.colorScheme.tertiary;
        break;
      case RewardCategory.experience:
        icon = Icons.star;
        iconColor = Colors.amber;
        break;
      case RewardCategory.donation:
        icon = Icons.volunteer_activism;
        iconColor = Colors.green;
        break;
      default:
        icon = Icons.card_giftcard;
        iconColor = theme.colorScheme.primary;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withAlpha(20)),
      ),
      child: Row(
        children: [
          // Reward icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Center(child: Icon(icon, color: iconColor, size: 24)),
          ),
          const SizedBox(width: 16),

          // Reward details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  reward.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (reward.description != null)
                  Text(
                    reward.description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(180),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),

          // Date and points
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${reward.pointsEarned} pts',
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                dateText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(150),
                  fontSize: 10,
                ),
              ),
              Text(
                timeText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(150),
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Rewards',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    context.push(AppRoutes.rewardHistory);
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.card_giftcard,
                    size: 48,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No rewards redeemed yet',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Redeem your points for rewards to see them here',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(150),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
