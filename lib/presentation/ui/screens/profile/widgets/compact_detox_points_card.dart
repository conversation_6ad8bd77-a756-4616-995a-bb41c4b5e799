import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_state.dart';

/// A compact card widget that displays the user's current detox points for the profile screen
class CompactDetoxPointsCard extends StatelessWidget {
  /// Creates a new CompactDetoxPointsCard
  const CompactDetoxPointsCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<DetoxPointsCubit, DetoxPointsState>(
      buildWhen: (previous, current) {
        // Only rebuild on meaningful state changes, not during loading
        // This prevents flickering during data refresh
        if (previous is DetoxPointsLoaded && current is DetoxPointsLoading) {
          return false;
        }
        return true;
      },
      builder: (context, state) {
        if (state is DetoxPointsLoaded) {
          final pointsData = state.points;
          // Use dailyPoints and dailyGoalTarget fields
          final int dailyGoal = pointsData.dailyGoalTarget;
          final int todaysPoints = pointsData.dailyPoints;
          final double progressPercentage =
              dailyGoal > 0 ? (todaysPoints / dailyGoal).clamp(0.0, 1.0) : 0.0;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.tertiary.withValues(alpha: 0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with level
                Row(
                  children: [
                    Icon(Icons.track_changes, color: Colors.white, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Daily Progress',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.star, color: Colors.white, size: 14),
                          const SizedBox(width: 4),
                          Text(
                            'Level ${pointsData.level}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Progress bar and points
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '$todaysPoints/$dailyGoal pts',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${(progressPercentage * 100).toInt()}% Complete',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.white.withAlpha(230),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Progress bar
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: progressPercentage,
                        backgroundColor: Colors.white.withAlpha(50),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withAlpha(200),
                        ),
                        minHeight: 8,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Total points and next level
                Row(
                  children: [
                    Text(
                      'Total: ${pointsData.totalPoints} pts',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withAlpha(230),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${pointsData.pointsToNextLevel} pts to Level ${pointsData.level + 1}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withAlpha(230),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                // Level progress bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value:
                        pointsData.totalPoints /
                        (pointsData.totalPoints + pointsData.pointsToNextLevel),
                    backgroundColor: Colors.white.withAlpha(50),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.white.withAlpha(200),
                    ),
                    minHeight: 4,
                  ),
                ),
              ],
            ),
          );
        }

        // Loading state
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.tertiary.withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withAlpha(51),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          height: 150,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.track_changes, color: Colors.white, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    'Daily Progress',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
