import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:detoxme/domain/entities/points_history.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;

enum PointsTimePeriod { weekly } // Add monthly/yearly later if needed

/// A bar chart widget that displays points data based on time period
class PointsBar<PERSON><PERSON> extends StatefulWidget {
  /// The points history data containing daily entries
  final PointsHistory history;

  /// Creates a [PointsBarChart]
  const PointsBarChart({super.key, required this.history});

  @override
  State<PointsBarChart> createState() => _PointsBarChartState();
}

class _PointsBarChartState extends State<PointsBarChart> {
  // TODO: Implement week navigation later
  final DateTime _currentWeekStart = _getStartOfWeek(DateTime.now());

  static DateTime _getStartOfWeek(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    // Filter data for the current week
    final currentWeekEnd = _currentWeekStart.add(const Duration(days: 7));
    final weekData =
        widget.history.dailyData.where((d) {
          return d.date.isAfter(
                _currentWeekStart.subtract(const Duration(days: 1)),
              ) &&
              d.date.isBefore(currentWeekEnd);
        }).toList();

    // Prepare chart data
    final List<BarChartGroupData> barGroups = [];
    double maxY = 0;
    for (int i = 0; i < 7; i++) {
      final dayDate = _currentWeekStart.add(Duration(days: i));
      final dataForDay = weekData.firstWhere(
        (d) => DateUtils.isSameDay(d.date, dayDate),
        orElse:
            () => DailyPointsData(
              date: dayDate,
              pointsEarned: 0,
              targetPoints:
                  50, // Default daily goal is 50 to match the rest of the app
            ), // Default if no data
      );

      // For all days, use points earned on that day
      // Cap the points value to the daily goal to prevent excessively high bars
      final double pointsValue = math.min(
        dataForDay.pointsEarned.toDouble(),
        dataForDay.targetPoints.toDouble() * 1.5,
      );
      final targetPoints = dataForDay.targetPoints.toDouble();

      if (pointsValue > maxY) maxY = pointsValue;
      if (targetPoints > maxY && targetPoints <= 300) maxY = targetPoints;

      // Create bar group with appropriate colors
      final List<BarChartRodData> barRods = [];

      // Check if this is a past day with no points
      final bool isPastDay = dayDate.isBefore(
        DateTime.now().subtract(const Duration(hours: 12)),
      );
      final bool hasNoPoints = pointsValue <= 0 && isPastDay;

      // Add the points bar if:
      // 1. There are points, OR
      // 2. It's the current day or a future day (even with no points yet)
      if (pointsValue > 0 || !isPastDay) {
        barRods.add(
          BarChartRodData(
            toY:
                pointsValue > 0
                    ? pointsValue
                    : 0, // Show 0 height bar for current day with no points
            color: Colors.green.shade400,
            width: 8,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
          ),
        );
      }

      // Only add the goal bar if the target is reasonable (not too high)
      // This prevents excessive bars that stretch beyond the chart boundaries
      if (targetPoints <= 300) {
        barRods.add(
          BarChartRodData(
            toY: targetPoints,
            color: Colors.blue.shade300,
            width: 8,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
          ),
        );
      }

      // If no points, add a small red indicator
      if (hasNoPoints) {
        barRods.add(
          BarChartRodData(
            toY: 5, // Small height just to be visible
            color: Colors.red.shade400,
            width: 8,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }

      barGroups.add(BarChartGroupData(x: i, barRods: barRods, barsSpace: 4));
    }
    // Cap the max Y at 300 and ensure it's at least the highest actual value
    maxY = (maxY * 1.2).ceilToDouble();

    // If maxY is too high, cap it at 300
    if (maxY > 300) {
      maxY = 300;
    }

    // Ensure maxY is at least 100 to avoid too small scales
    if (maxY < 100) {
      maxY = 100;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Week Navigation (TODO)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Weekly Progress', // Localize later
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${DateFormat.MMMd().format(_currentWeekStart)} - ${DateFormat.MMMd().format(currentWeekEnd.subtract(const Duration(days: 1)))}',
                    style: textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Legend for the chart
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.green.shade400,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text('Points Earned', style: textTheme.bodySmall),
                  const SizedBox(width: 16),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text('Daily Goal', style: textTheme.bodySmall),
                  const SizedBox(width: 16),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red.shade400,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text('No Points', style: textTheme.bodySmall),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),
          AspectRatio(
            aspectRatio: 1.7,
            child: BarChart(
              BarChartData(
                maxY: maxY,
                barTouchData: BarTouchData(
                  touchTooltipData: BarTouchTooltipData(
                    tooltipBgColor: colorScheme.primaryContainer,
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      final day = DateFormat.E().format(
                        _currentWeekStart.add(Duration(days: group.x.toInt())),
                      );

                      // Different tooltip based on which bar was touched
                      final String valueLabel;
                      final Color valueColor;

                      // Check if this is a past day with no points (red indicator)
                      if (rod.color == Colors.red.shade400) {
                        valueLabel = 'No points earned';
                        valueColor = Colors.red.shade400;
                      }
                      // Check if this is the points earned bar (green)
                      else if (rod.color == Colors.green.shade400) {
                        valueLabel = 'Earned: ${rod.toY.round()} pts';
                        valueColor = Colors.green.shade400;
                      }
                      // Otherwise it's the goal bar (blue)
                      else {
                        valueLabel = 'Goal: ${rod.toY.round()} pts';
                        valueColor = Colors.blue.shade300;
                      }

                      return BarTooltipItem(
                        '$day\n',
                        TextStyle(
                          color: colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text: valueLabel,
                            style: TextStyle(
                              color: valueColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final day = DateFormat.E().format(
                          _currentWeekStart.add(Duration(days: value.toInt())),
                        );
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          space: 4,
                          child: Text(
                            day.substring(0, 1),
                            style: textTheme.bodySmall,
                          ), // Show first letter of day
                        );
                      },
                      reservedSize: 28,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        if (value == 0 || value == meta.max) {
                          return const SizedBox.shrink();
                        }
                        return Text(
                          value.toInt().toString(),
                          style: textTheme.bodySmall,
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  // Use a fixed interval based on maxY for cleaner grid lines
                  horizontalInterval:
                      maxY <= 100 ? 25 : (maxY <= 200 ? 50 : 100),
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: theme.dividerColor.withValues(alpha: 0.1),
                      strokeWidth: 1,
                    );
                  },
                ),
                barGroups: barGroups,
              ),
              swapAnimationDuration: const Duration(milliseconds: 250),
              swapAnimationCurve: Curves.linear,
            ),
          ),
        ],
      ),
    );
  }
}
