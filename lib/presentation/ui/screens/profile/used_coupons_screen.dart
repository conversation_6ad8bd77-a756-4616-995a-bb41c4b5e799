import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/redemption.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_cubit.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/domain/repositories/coupon_repository.dart';

/// Screen for displaying user's used coupons
class UsedCouponsScreen extends StatelessWidget {
  /// Creates a new [UsedCouponsScreen]
  const UsedCouponsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get repository from service locator
    final couponRepository = serviceLocator<CouponRepository>();

    return BlocProvider<CouponCubit>(
      create: (_) => CouponCubit(couponRepository: couponRepository),
      child: const _UsedCouponsScreenContent(),
    );
  }
}

/// Content widget for the UsedCouponsScreen
class _UsedCouponsScreenContent extends StatefulWidget {
  const _UsedCouponsScreenContent();

  @override
  State<_UsedCouponsScreenContent> createState() =>
      _UsedCouponsScreenContentState();
}

class _UsedCouponsScreenContentState extends State<_UsedCouponsScreenContent> {
  @override
  void initState() {
    super.initState();
    _loadRedemptionHistory();
  }

  Future<void> _loadRedemptionHistory() async {
    final couponCubit = context.read<CouponCubit>();
    await couponCubit.loadRedemptionHistory();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold.withTitle(
      title: 'Used Coupons',
      titleIconData: Icons.confirmation_number_rounded,
      showBackButton: true,
      onBackPressed: () {
        // Navigate to profile screen explicitly
        context.go(AppRoutes.profile);
      },
      body: BlocBuilder<CouponCubit, CouponState>(
        builder: (context, state) {
          if (state.status == CouponStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (state.redemptions.isEmpty) {
            return _buildEmptyState(context);
          }
          
          return _buildRedemptionList(context, state);
        },
      ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.tertiary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.confirmation_number_rounded,
                size: 64,
                color: theme.colorScheme.tertiary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Used Coupons Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Redeem coupons to see your history here. Used coupons help track your rewards over time.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                context.go(AppRoutes.profile);
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Go Back'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRedemptionList(BuildContext context, CouponState state) {
    final theme = Theme.of(context);
    final redemptions = state.redemptions;
    
    // Group redemptions by date
    final groupedRedemptions = <String, List<Redemption>>{};
    
    for (final redemption in redemptions) {
      final dateKey = DateFormat('yyyy-MM-dd').format(redemption.redeemedAt);
      if (!groupedRedemptions.containsKey(dateKey)) {
        groupedRedemptions[dateKey] = [];
      }
      groupedRedemptions[dateKey]!.add(redemption);
    }
    
    // Sort dates in descending order (newest first)
    final sortedDates =
        groupedRedemptions.keys.toList()..sort((a, b) => b.compareTo(a));
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final redemptionsForDate = groupedRedemptions[dateKey]!;
        final date = DateTime.parse(dateKey);
        final formattedDate = DateFormat.yMMMMd().format(date);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.tertiary,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      formattedDate,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onTertiary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Divider(color: theme.colorScheme.outlineVariant),
                  ),
                ],
              ),
            ),
            ...redemptionsForDate.asMap().entries.map((entry) {
              final index = entry.key;
              final redemption = entry.value;
              
              // Find the corresponding coupon
              final coupon = state.coupons.firstWhere(
                (c) => c.id == redemption.couponId,
                orElse: () => _createEmptyCoupon(),
              );
              
              return FadeInRight(
                delay: Duration(milliseconds: 50 * index),
                duration: const Duration(milliseconds: 300),
                child: _buildRedemptionCard(context, redemption, coupon),
              );
            }),
          ],
        );
      },
    );
  }
  
  Widget _buildRedemptionCard(
    BuildContext context,
    Redemption redemption,
    Coupon coupon,
  ) {
    final theme = Theme.of(context);
    
    // Format time
    final timeFormatted = DateFormat.jm().format(redemption.redeemedAt);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Coupon icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: theme.colorScheme.tertiary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Icon(
                    Icons.confirmation_number_rounded,
                    color: theme.colorScheme.tertiary,
                    size: 28,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              
              // Coupon details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coupon.discount,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      coupon.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.store,
                          size: 14,
                          color: theme.colorScheme.tertiary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          coupon.partner.name,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: theme.colorScheme.tertiary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          timeFormatted,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    
                    // Show feedback if available
                    if (redemption.hasFeedback &&
                        redemption.feedbackRating != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 12),
                        child: Row(
                          children: [
                            ...List.generate(5, (index) {
                              return Icon(
                                index < (redemption.feedbackRating ?? 0)
                                    ? Icons.star
                                    : Icons.star_border,
                                color: Colors.amber,
                                size: 16,
                              );
                            }),
                            const SizedBox(width: 8),
                            if (redemption.feedbackComment != null &&
                                redemption.feedbackComment!.isNotEmpty)
                              Expanded(
                                child: Text(
                                  redemption.feedbackComment!,
                                  style: theme.textTheme.bodySmall,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper method to create an empty coupon
Coupon _createEmptyCoupon() {
  return Coupon(
    id: '',
    partner: Partner(
      id: '',
      name: 'Unknown Partner',
      category: PartnerCategory.retail,
      latitude: 0,
      longitude: 0,
      commissionRate: 0,
      address: '',
    ),
    description: 'Coupon no longer available',
    discount: 'Unknown Discount',
    pointsRequired: 0,
    expirationDate: DateTime.now(),
    recommendedForMoods: const [],
  );
}
