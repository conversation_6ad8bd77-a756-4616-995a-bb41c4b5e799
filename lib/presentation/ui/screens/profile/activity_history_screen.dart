import 'package:detoxme/core/themes/app_themes.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/completed_activity.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/activity/activity_state.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:intl/intl.dart';
import 'package:animate_do/animate_do.dart';

/// Screen for displaying user's activity history
class ActivityHistoryScreen extends StatefulWidget {
  const ActivityHistoryScreen({super.key});

  @override
  State<ActivityHistoryScreen> createState() => _ActivityHistoryScreenState();
}

class _ActivityHistoryScreenState extends State<ActivityHistoryScreen> {
  @override
  void initState() {
    super.initState();
    _loadActivityHistory();
  }

  Future<void> _loadActivityHistory() async {
    final authCubit = context.read<AuthCubit>();
    final userId = authCubit.getCurrentUserId();

    if (userId != null) {
      final activityCubit = context.read<ActivityCubit>();
      await activityCubit.loadCompletedActivities(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold.withTitle(
      title: 'Activity History',
      titleIconData: Icons.history_rounded,
      showBackButton: true,
      onBackPressed: () {
        // Navigate to profile screen explicitly
        context.go(AppRoutes.profile);
      },
      body: BlocBuilder<ActivityCubit, ActivityState>(
        builder: (context, state) {
          if (state.status == ActivityStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.completedActivities == null ||
              state.completedActivities!.isEmpty) {
            return _buildEmptyState(context);
          }

          return _buildActivityList(context, state.completedActivities!);
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.history_rounded,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Activity History Yet',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Complete activities to see them here. Your activity history helps track your progress over time.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                context.go(AppRoutes.profile);
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Go Back'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                context.go(AppRoutes.activities);
              },
              icon: const Icon(Icons.explore),
              label: const Text('Explore Activities'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityList(
    BuildContext context,
    List<CompletedActivity> activities,
  ) {
    // Group activities by date
    final groupedActivities = <String, List<CompletedActivity>>{};

    for (final activity in activities) {
      final dateKey = DateFormat('yyyy-MM-dd').format(activity.completedAt);
      if (!groupedActivities.containsKey(dateKey)) {
        groupedActivities[dateKey] = [];
      }
      groupedActivities[dateKey]!.add(activity);
    }

    // Sort dates in descending order (newest first)
    final sortedDates =
        groupedActivities.keys.toList()..sort((a, b) => b.compareTo(a));

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final activitiesForDate = groupedActivities[dateKey]!;
        final date = DateTime.parse(dateKey);
        final formattedDate = DateFormat.yMMMMd().format(date);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      formattedDate,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Divider(
                      color: Theme.of(context).colorScheme.outlineVariant,
                    ),
                  ),
                ],
              ),
            ),
            ...activitiesForDate.asMap().entries.map((entry) {
              final index = entry.key;
              final completedActivity = entry.value;

              return FadeInRight(
                delay: Duration(milliseconds: 50 * index),
                duration: const Duration(milliseconds: 300),
                child: _buildActivityCard(context, completedActivity),
              );
            }),
          ],
        );
      },
    );
  }

  Widget _buildActivityCard(
    BuildContext context,
    CompletedActivity completedActivity,
  ) {
    final activity = completedActivity.activity;

    // Format time
    final timeFormatted = DateFormat.jm().format(completedActivity.completedAt);
    final durationText = '${activity.duration} min';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: Theme.of(context).colorScheme.surface,
        child: InkWell(
          onTap: () {
            context.push(AppRoutes.activityDetail, extra: activity);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Activity image or category indicator
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(
                      activity.category,
                    ).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    image:
                        activity.imageUrl != null
                            ? DecorationImage(
                              image: NetworkImage(activity.imageUrl!),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      activity.imageUrl == null
                          ? Center(
                            child: Icon(
                              _getCategoryIcon(activity.category),
                              color: _getCategoryColor(activity.category),
                              size: 28,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 16),

                // Activity details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.title,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        activity.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            timeFormatted,
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color:
                                  Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Icon(
                            Icons.timer_outlined,
                            size: 14,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            durationText,
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color:
                                  Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods for activity categories
  Color _getCategoryColor(ActivityCategory category) {
    return AppColors.primary;
  }

  IconData _getCategoryIcon(ActivityCategory category) {
    return category.icon;
  }
}
