import 'package:cached_network_image/cached_network_image.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_state.dart';
import 'package:detoxme/presentation/ui/screens/activities/widgets/activity_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/data/local/hive_sync_queue_data_source.dart';
import 'package:detoxme/data/local/local_profile_data_source.dart';
import 'package:detoxme/data/repositories/profile_repository_impl.dart';
import 'package:detoxme/domain/entities/user_profile.dart';

import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_state.dart';
import 'package:detoxme/domain/repositories/detox_points_repository.dart';
import 'package:detoxme/presentation/ui/screens/profile/widgets/points_bar_chart.dart';
import 'package:detoxme/presentation/ui/screens/profile/widgets/compact_detox_points_card.dart';
import 'package:detoxme/presentation/ui/screens/profile/widgets/leaderboard_preview.dart';
import 'package:detoxme/core/services/service_locator.dart';

// Enums for the profile screen
enum ActivityCategory { physical, mental, social, creative }

enum DifficultyLevel { easy, medium, hard }

/// The main Profile screen showing user information and statistics
class ProfileScreen extends StatelessWidget {
  /// Creates a [ProfileScreen]
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final supabaseClient = Supabase.instance.client;
    final connectivity = Connectivity();
    final networkInfo = NetworkInfoImpl(connectivity);
    final LocalSyncQueueRepository localSyncQueueRepository =
        HiveSyncQueueDataSource();
    final localDataSource = LocalProfileDataSourceImpl();
    final repository = ProfileRepositoryImpl(
      localDataSource: localDataSource,
      supabaseClient: supabaseClient,
      networkInfo: networkInfo,
      localSyncQueueRepository: localSyncQueueRepository,
    );

    return MultiBlocProvider(
      providers: [
        BlocProvider<ProfileCubit>(
          create:
              (context) =>
                  ProfileCubit(profileRepository: repository)..loadProfile(),
        ),
        BlocProvider<DashboardCubit>(
          create: (context) => DashboardCubit()..loadDashboard(),
        ),
        BlocProvider<DetoxPointsCubit>(
          create:
              (context) => DetoxPointsCubit(
                detoxPointsRepository: serviceLocator<DetoxPointsRepository>(),
                authCubit: context.read<AuthCubit>(),
                    )
                    ..loadPointsAndHistory()
                    ..loadLeaderboard(
                      limit: 10,
                    ), // Load top 10 users for leaderboard
        ),
      ],
      child: const ProfileView(),
    );
  }
}

/// The profile view component
class ProfileView extends StatelessWidget {
  /// Creates a [ProfileView]
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseScaffold.withTitle(
      title: AppLocalizations.of(context)!.profileScreenTitle,
      titleIconData: Icons.person_rounded,
      showBottomNav: true,
      body: BlocConsumer<ProfileCubit, ProfileState>(
        listener: (context, state) {
          if (state is ProfileError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: theme.colorScheme.error,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is ProfileLoading) {
            return _buildLoadingView(context);
          } else if (state is ProfileLoaded) {
            return _buildProfileContent(context, state.profile);
          } else if (state is ProfileError) {
            return _buildErrorView(context, state.message);
          }

          return _buildLoadingView(context);
        },
      ),
    );
  }

  Widget _buildLoadingView(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.loadingYourProfile,
            style: theme.textTheme.titleMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String message) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: theme.colorScheme.error, size: 64),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.failedToLoadProfile,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<ProfileCubit>().loadProfile();
              },
              child: Text(AppLocalizations.of(context)!.tryAgain),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, UserProfile profile) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh profile data
        context.read<ProfileCubit>().loadProfile();

        // Refresh points and leaderboard data
        context.read<DetoxPointsCubit>().loadPointsAndHistory();
        context.read<DetoxPointsCubit>().loadLeaderboard(limit: 10);
      },
      child: ListView(
        padding: EdgeInsets.only(bottom: 42),
        children: [
          // Profile header with gradient
          _buildProfileHeader(context, profile),

          // Compact Detox Points Card
          CompactDetoxPointsCard(),
          const SizedBox(height: 16),

          // Leaderboard Preview
          BlocBuilder<DetoxPointsCubit, DetoxPointsState>(
            builder: (context, state) {
              if (state is DetoxPointsLoaded &&
                  state.leaderboard != null &&
                  state.leaderboard!.isNotEmpty) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: LeaderboardPreview(
                    leaderboardEntries: state.leaderboard!,
                  ),
                );
              }
              return const SizedBox.shrink(); // Don't show anything if no leaderboard data
            },
          ),
          const SizedBox(height: 16),

          // Points Bar Chart
          BlocBuilder<DetoxPointsCubit, DetoxPointsState>(
            buildWhen: (previous, current) {
              // Only rebuild on meaningful state changes, not during loading
              // This prevents flickering during data refresh
              if (previous is DetoxPointsLoaded &&
                  current is DetoxPointsLoading) {
                return false;
              }
              return true;
            },
            builder: (context, state) {
              // Keep showing the current chart during loading to prevent flickering
              if (state is DetoxPointsLoaded && state.history != null) {
                return PointsBarChart(history: state.history!);
              } else if (state is DetoxPointsError) {
                return SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('Error loading chart: ${state.message}'),
                  ),
                );
              }
              // Handle case where history is null even if points loaded
              else if (state is DetoxPointsLoaded && state.history == null) {
                return const SizedBox(
                  height: 200,
                  child: Center(child: Text('Points history not available.')),
                );
              }
              // Only show loading indicator on initial load, not during refresh
              else if (state is DetoxPointsInitial) {
                return const SizedBox(
                  height: 200,
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              // For loading state, return an empty container to maintain layout
              return const SizedBox(height: 200);
            },
          ),

          // About section
          if (profile.bio != null && profile.bio!.isNotEmpty)
            _buildAboutSection(context, profile),

          const SizedBox(height: 24),

          // Favorite activities section
          _buildFavoriteActivitiesSection(context),

          const SizedBox(height: 16),

          // Actions section
          _buildActionsSection(context),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, UserProfile profile) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // Top section with background
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.tertiary.withValues(alpha: 0.7),
                ],
              ),
              borderRadius: const BorderRadius.all(Radius.circular(24)),
            ),
            child: Stack(
              children: [
                // Decorative pattern
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(24),
                      bottomRight: Radius.circular(24),
                    ),
                    child: Opacity(
                      opacity: 0.1,
                      child: CustomPaint(
                        painter: ProfilePatternPainter(color: Colors.white),
                      ),
                    ),
                  ),
                ),

                // Centered avatar
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 40,
                          backgroundColor: theme.colorScheme.primaryContainer,
                          child:
                              profile.avatarUrl != null
                                  ? ClipOval(
                                    child: CachedNetworkImage(
                                      imageUrl: profile.avatarUrl!,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      memCacheWidth: 160, // 2x for high DPI
                                      memCacheHeight: 160,
                                      useOldImageOnUrlChange: true,
                                      placeholder:
                                          (context, url) => Icon(
                                            Icons.person,
                                            size: 40,
                                            color: Colors.white,
                                          ),
                                      errorWidget:
                                          (context, error, stackTrace) => Icon(
                                            Icons.person,
                                            size: 40,
                                            color: Colors.white,
                                          ),
                                    ),
                                  )
                                  : Icon(
                                    Icons.person,
                                    size: 40,
                                    color: Colors.white,
                                  ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '@${profile.username?.toLowerCase() ?? 'username'}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // User attributes/chips section
                      if (profile.gender != null ||
                          profile.birthYear != null ||
                          profile.lifeSituation != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 12),
                          child: SizedBox(
                            height: 36,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (profile.gender != null)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 4,
                                      ),
                                      child: _buildAttributeChip(
                                        profile.gender!,
                                        Icons.person_outline,
                                        theme,
                                      ),
                                    ),
                                  if (profile.birthYear != null)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 4,
                                      ),
                                      child: _buildAttributeChip(
                                        '${DateTime.now().year - (profile.birthYear ?? DateTime.now().year)} y.o.',
                                        Icons.cake_outlined,
                                        theme,
                                      ),
                                    ),
                                  if (profile.lifeSituation != null)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 4,
                                      ),
                                      child: _buildAttributeChip(
                                        profile.lifeSituation!,
                                        Icons.work_outline,
                                        theme,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Edit profile button (smaller)
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    height: 36,
                    width: 36,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.edit_outlined,
                        color: Colors.white,
                        size: 16,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        // Navigate to edit profile
                        context.go(AppRoutes.editProfile);
                      },
                      tooltip: 'Edit Profile',
                    ),
                  ),
                ),
              ],
            ),
          ),

          // User goals section
          if (profile.goals.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outlineVariant.withValues(
                      alpha: 0.5,
                    ),
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.flag_outlined,
                        size: 18,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.profileMyGoals,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        profile.goals.map((goal) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              goal,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onPrimaryContainer,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAttributeChip(String label, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context, UserProfile profile) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.person_outline,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'About Me',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(
                color: theme.colorScheme.outlineVariant.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Text(
              profile.bio ?? '',
              style: theme.textTheme.bodyLarge?.copyWith(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteActivitiesSection(BuildContext context) {
    final theme = Theme.of(context);
    final activityCubit = context.watch<ActivityCubit>();
    final favoriteActivities =
        activityCubit.getFavoriteActivities().take(10).toList();

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.favorite, color: Colors.red, size: 20),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context)!.profileFavoriteActivities,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              /* if (favoriteActivities.isNotEmpty)
                TextButton(
                  onPressed: () {
                    // Navigate to all favorites
                  },
                  child: Text(l10n.profileViewAll),
                ),*/
            ],
          ),
          if (favoriteActivities.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(28),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outlineVariant.withValues(
                    alpha: 0.5,
                  ),
                  width: 0.2,
                ),
              ),
              child: Center(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.favorite_border,
                        size: 40,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      AppLocalizations.of(context)!.profileNoFavoriteActivities,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      AppLocalizations.of(
                        context,
                      )!.profileAddFavoriteActivitiesHint,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to activities screen
                        context.go(AppRoutes.activities);
                      },
                      icon: const Icon(Icons.explore),
                      label: Text(
                        AppLocalizations.of(context)!.profileExploreActivities,
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Activity cards using ActivityCard component
                SizedBox(
                  height: 290, // Adjusted height to accommodate the cards
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: favoriteActivities.length,
                    itemBuilder: (context, index) {
                      final activity = favoriteActivities[index];
                      return Container(
                        width: 180,
                        margin: EdgeInsets.only(
                          right: index < favoriteActivities.length - 1 ? 16 : 0,
                        ),
                        child: ActivityCard(
                          activity: activity,
                          onTap: () {
                            context.push(
                              AppRoutes.activityDetail,
                              extra: activity,
                            );
                          },
                          // No onUpvote, onDownvote, or actionButton
                        ),
                      );
                    },
                  ),
                ),

                // View more button
                const SizedBox(height: 16),
                Center(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      context.go(AppRoutes.activities);
                    },
                    icon: const Icon(Icons.explore_outlined),
                    label: Text(
                      AppLocalizations.of(
                        context,
                      )!.profileExploreMoreActivities,
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.menu, color: theme.colorScheme.tertiary, size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                l10n.profileQuickActions,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.outlineVariant,
                width: 0.2,
              ),
            ),
            child: Column(
              children: [
                _buildActionTile(
                  context,
                  l10n.profileActionActivityHistory,
                  Icons.history_rounded,
                  theme.colorScheme.primary,
                  () {
                    // Navigate to activity history
                    context.go(AppRoutes.activityHistory);
                  },
                ),
                Divider(
                  height: 1,
                  indent: 16,
                  endIndent: 16,
                  color: theme.dividerColor.withValues(alpha: 0.5),
                ),
                _buildActionTile(
                  context,
                  l10n.profileActionUsedCoupons,
                  Icons.confirmation_number_rounded,
                  theme.colorScheme.secondary,
                  () {
                    // Navigate to used coupons
                    context.go(AppRoutes.usedCoupons);
                  },
                ),
                Divider(
                  height: 1,
                  indent: 16,
                  endIndent: 16,
                  color: theme.dividerColor.withValues(alpha: 0.5),
                ),
                _buildActionTile(
                  context,
                  l10n.profileActionRewardHistory,
                  Icons.card_giftcard_rounded,
                  Colors.amber,
                  () {
                    // Navigate to rewards
                    context.go(AppRoutes.rewardHistory);
                  },
                ),
                Divider(
                  height: 1,
                  indent: 16,
                  endIndent: 16,
                  color: theme.dividerColor.withValues(alpha: 0.5),
                ),
                _buildActionTile(
                  context,
                  l10n.profileActionSettings,
                  Icons.settings_rounded,
                  Colors.blueGrey,
                  () {
                    context.push(AppRoutes.settings);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 22),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_forward_ios_rounded,
                size: 14,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }


}

/// Custom painter for decorative pattern
class ProfilePatternPainter extends CustomPainter {
  final Color color;

  ProfilePatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

    // Draw a decorative pattern
    final double spacing = 30;

    // Draw curved lines
    for (double i = 0; i < size.width + size.height; i += spacing) {
      final path = Path();
      path.moveTo(0, i);
      path.quadraticBezierTo(size.width / 2, i - 40, size.width, i - 20);
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(ProfilePatternPainter oldDelegate) =>
      color != oldDelegate.color;
}
