import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A custom button widget for authentication screens with animation effects.
///
/// This button provides visual feedback when pressed and supports gradient backgrounds.
class LoginButton extends StatefulWidget {
  /// The background color of the button
  final Color backgroundColor;
  
  /// The border color of the button
  final Color borderColor;
  
  /// The asset path for the SVG icon to display on the left side of the button
  final String iconAssetPath;

  /// The color to tint the SVG icon (optional)
  final Color? iconColor;
  
  /// The text to display on the button
  final String text;
  
  /// The color of the button text
  final Color textColor;
  
  /// The callback to execute when the button is pressed
  final VoidCallback? onPressed;
  
  /// Whether to use a gradient background
  final bool isGradient;
  
  /// The gradient colors to use if isGradient is true
  final List<Color>? gradientColors;

  /// Creates a LoginButton.
  ///
  /// All parameters except [isGradient], [gradientColors], and [iconColor] are required.
  const LoginButton({
    super.key,
    required this.backgroundColor,
    required this.borderColor,
    required this.iconAssetPath,
    this.iconColor,
    required this.text,
    required this.textColor,
    required this.onPressed,
    this.isGradient = false,
    this.gradientColors,
  });

  @override
  State<LoginButton> createState() => _LoginButtonState();
}

class _LoginButtonState extends State<LoginButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        onTap:
            widget.onPressed == null
                ? null
                : () {
                  widget.onPressed!();
                },
        onHighlightChanged: (isHighlighted) {
          if (widget.onPressed != null) {
            if (isHighlighted) {
              _controller.forward();
            } else {
              _controller.reverse();
            }
          }
        },
        borderRadius: BorderRadius.circular(16),
        splashColor: widget.textColor.withValues(alpha: 0.1),
        highlightColor: widget.textColor.withValues(alpha: 0.05),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(scale: _scaleAnimation.value, child: child);
          },
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              gradient:
                  widget.isGradient && widget.gradientColors != null
                      ? LinearGradient(
                      colors: widget.gradientColors!,
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    )
                      : null,
              color: widget.isGradient ? null : widget.backgroundColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: widget.borderColor, width: 1.5),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 8.0),
                  child: SvgPicture.asset(
                    widget.iconAssetPath,
                    height: 24,
                    width: 24,
                    colorFilter:
                        widget.iconColor != null
                            ? ColorFilter.mode(
                              widget.iconColor!,
                              BlendMode.srcIn,
                            )
                            : null,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Text(
                      widget.text,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: widget.textColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
