import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart';
import 'package:detoxme/presentation/ui/screens/auth/widgets/login_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// Import localization (using the path that worked before)
import 'package:detoxme/localization/app_localizations.dart';
import 'package:go_router/go_router.dart'; // Import GoRouter
import 'package:detoxme/core/router/app_router.dart'; // Import AppRoutes
// Import for SVG support removed (unused)
// Import for Lottie animations
import 'package:animate_do/animate_do.dart';


class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLogin = true; // Toggle between Login and Sign Up
  String? _errorMessage;
  bool _isPasswordVisible = false;
  bool _isEmailSectionExpanded = false; // Track if email section is expanded
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    // Controller for UI animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Start animations
    _animationController.forward();
    
    // Ensure email section is collapsed by default
    _isEmailSectionExpanded = false;
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }



  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final email = _emailController.text.trim();
      final password = _passwordController.text.trim();
      final authCubit = context.read<AuthCubit>();

      setState(() {
        _errorMessage = null; // Clear previous error on new submission
      });

      if (_isLogin) {
        authCubit.signIn(email: email, password: password);
      } else {
        authCubit.signUp(email: email, password: password);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is Authenticated) {
          // Check if user needs to complete onboarding
          if (state.needsOnboarding) {
            // Navigate to onboarding
            context.go(AppRoutes.onboarding);
          } else {
            // User is already onboarded, go to dashboard
            context.go(AppRoutes.dashboard);
          }
        } else if (state is AuthError) {
          // Show error message
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final isLoading =
            state is AuthLoading ||
            state is AuthCodeSending ||
            state is AuthOtpVerificationInProgress;

        // Wrap the Stack with a Material widget
        return Material(
          color: theme.colorScheme.surface,
          child: Stack(
            children: [
              // Top wave gradient shape
              ClipPath(
                clipper: WaveClipper(),
                child: Container(
                  height: size.height * 0.32,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.tertiary,
                      ],
                    ),
                  ),
                ),
              ),

              // Add decorative background circles
              Positioned(
                top: size.height * 0.1,
                left: -size.width * 0.25,
                child: Container(
                  width: size.width * 0.7,
                  height: size.width * 0.7,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      radius: 0.8,
                      colors: [
                        theme.colorScheme.primary.withValues(alpha: 0.2),
                        theme.colorScheme.primary.withValues(alpha: 0.0),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: size.height * 0.15,
                right: -size.width * 0.3,
                child: Container(
                  width: size.width * 0.9,
                  height: size.width * 0.9,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      radius: 0.7,
                      colors: [
                        theme.colorScheme.tertiary.withValues(alpha: 0.15),
                        theme.colorScheme.tertiary.withValues(alpha: 0.0),
                      ],
                    ),
                  ),
                ),
              ),

              // Breathing animation
              Positioned(
                top: size.height * 0.1,
                left: 0,
                right: 0,
                child: FadeInDown(
                  duration: const Duration(milliseconds: 800),
                  child: Image.asset(
                    'assets/images/login_screen.png',
                    height: size.height * 0.3,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // Main content with login options
              SafeArea(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: size.height * 0.35),

                        // Title with gradient
                        FadeInUp(
                          delay: const Duration(milliseconds: 200),
                          child: ShaderMask(
                            shaderCallback:
                                (bounds) => LinearGradient(
                                  colors: [
                                    theme.colorScheme.primary,
                                    Color.lerp(
                                      theme.colorScheme.primary,
                                      theme.colorScheme.tertiary,
                                      0.5,
                                    )!,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ).createShader(bounds),
                            child: Text(
                              l10n.authTitle,
                              style: theme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 32,
                                letterSpacing: -0.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        FadeInUp(
                          delay: const Duration(milliseconds: 300),
                          child: Text(
                            l10n.authSubtitle,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              height: 1.6,
                              fontSize: 18,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Login buttons
                        FadeInUp(
                          delay: const Duration(milliseconds: 400),
                          child: Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                            elevation: 12,
                            color: theme.colorScheme.surface,
                            child: Padding(
                              padding: const EdgeInsets.all(24.0),
                              child: Column(
                                children: [
                                  // Phone login button
                                  LoginButton(
                                    onPressed:
                                        isLoading
                                            ? null
                                            : () => context.push(
                                              AppRoutes.phoneInput,
                                            ),
                                    text: l10n.loginWithPhone,
                                    iconAssetPath:
                                        'assets/images/svg/phone_icon.svg',
                                    backgroundColor: theme.colorScheme.primary,
                                    borderColor: Colors.transparent,
                                    iconColor: Colors.white,
                                    textColor: Colors.white,
                                    isGradient: true,
                                    gradientColors: [
                                      theme.colorScheme.primary,
                                      Color.lerp(
                                        theme.colorScheme.primary,
                                        theme.colorScheme.tertiary,
                                        0.3,
                                      )!,
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Google login button
                                  LoginButton(
                                    onPressed:
                                        isLoading
                                            ? null
                                            : () =>
                                                context
                                                    .read<AuthCubit>()
                                                    .signInWithGoogle(),
                                    text: l10n.continueWithGoogle,
                                    iconAssetPath:
                                        'assets/images/svg/google_logo.svg',
                                    // No iconColor needed for Google logo
                                    backgroundColor: const Color.fromARGB(
                                      255,
                                      40,
                                      136,
                                      214,
                                    ),
                                    borderColor: Colors.transparent,
                                    iconColor: Colors.white,
                                    textColor: theme.colorScheme.onSurface,
                                    isGradient: false,
                                  ),

                                  const SizedBox(height: 16),

                                  // Apple login button
                                  LoginButton(
                                    onPressed:
                                        isLoading
                                            ? null
                                            : () =>
                                                context
                                                    .read<AuthCubit>()
                                                    .signInWithApple(),
                                    text: l10n.continueWithApple,
                                    iconAssetPath:
                                        'assets/images/svg/apple_logo.svg',
                                    iconColor:
                                        Colors
                                            .white, // Tint Apple logo white for dark background
                                    backgroundColor: Colors.black,
                                    borderColor: Colors.transparent,
                                    textColor: Colors.white,
                                    isGradient: false,
                                  ),

                                  const SizedBox(height: 24),

                                  // Divider with email text
                                  GestureDetector(
                                    onTap:
                                        isLoading
                                            ? null
                                            : () {
                                              setState(() {
                                                _isEmailSectionExpanded =
                                                    !_isEmailSectionExpanded;
                                              });
                                            },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Divider(
                                              color: theme.colorScheme.outline
                                                  .withValues(alpha: 0.3),
                                              thickness: 1,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                            ),
                                            child: Row(
                                              children: [
                                                Text(
                                                  l10n.orWithEmail,
                                                  style: theme
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: theme
                                                            .colorScheme
                                                            .onSurface
                                                            .withValues(
                                                              alpha: 0.7,
                                                            ),
                                                      ),
                                                ),
                                                const SizedBox(width: 8),
                                                AnimatedRotation(
                                                  turns:
                                                      _isEmailSectionExpanded
                                                          ? 0.5
                                                          : 0,
                                                  duration: const Duration(
                                                    milliseconds: 300,
                                                  ),
                                                  child: Icon(
                                                    Icons.keyboard_arrow_down,
                                                    color: theme
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.7),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Divider(
                                              color: theme.colorScheme.outline
                                                  .withValues(alpha: 0.3),
                                              thickness: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  // Email login/signup section - now directly under the divider, not wrapped in another container
                                  AnimatedCrossFade(
                                    firstChild: const SizedBox(height: 0),
                                    secondChild: Padding(
                                      padding: const EdgeInsets.only(
                                        top: 24,
                                        bottom: 8,
                                      ),
                                      child: Form(
                                        key: _formKey,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          children: [
                                            // Email field
                                            TextFormField(
                                              controller: _emailController,
                                              decoration: InputDecoration(
                                                labelText: l10n.emailFieldLabel,
                                                prefixIcon: Icon(
                                                  Icons.email_outlined,
                                                  color:
                                                      theme.colorScheme.primary,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                      borderSide: BorderSide(
                                                        color: theme
                                                            .colorScheme
                                                            .outline
                                                            .withValues(
                                                              alpha: 0.3,
                                                            ),
                                                      ),
                                                    ),
                                              ),
                                              keyboardType:
                                                  TextInputType.emailAddress,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return l10n
                                                      .emailRequiredError;
                                                }
                                                if (!RegExp(
                                                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                                ).hasMatch(value)) {
                                                  return l10n.emailInvalidError;
                                                }
                                                return null;
                                              },
                                              enabled: !isLoading,
                                            ),

                                            const SizedBox(height: 16),

                                            // Password field
                                            TextFormField(
                                              controller: _passwordController,
                                              decoration: InputDecoration(
                                                labelText:
                                                    l10n.passwordFieldLabel,
                                                prefixIcon: Icon(
                                                  Icons.lock_outline,
                                                  color:
                                                      theme.colorScheme.primary,
                                                ),
                                                suffixIcon: IconButton(
                                                  icon: Icon(
                                                    _isPasswordVisible
                                                        ? Icons.visibility_off
                                                        : Icons.visibility,
                                                    color:
                                                        theme
                                                            .colorScheme
                                                            .primary,
                                                  ),
                                                  onPressed:
                                                      isLoading
                                                          ? null
                                                          : () {
                                                            setState(() {
                                                              _isPasswordVisible =
                                                                  !_isPasswordVisible;
                                                            });
                                                          },
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                      borderSide: BorderSide(
                                                        color: theme
                                                            .colorScheme
                                                            .outline
                                                            .withValues(
                                                              alpha: 0.3,
                                                            ),
                                                      ),
                                                    ),
                                              ),
                                              obscureText: !_isPasswordVisible,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return l10n
                                                      .passwordRequiredError;
                                                }
                                                if (value.length < 6) {
                                                  return l10n
                                                      .passwordLengthError;
                                                }
                                                return null;
                                              },
                                              enabled: !isLoading,
                                            ),

                                            const SizedBox(height: 8),

                                            // Toggle between login and signup
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Text(
                                                  _isLogin
                                                      ? l10n.noAccountQuestion
                                                      : l10n.hasAccountQuestion,
                                                  style:
                                                      theme.textTheme.bodySmall,
                                                ),
                                                TextButton(
                                                  onPressed:
                                                      isLoading
                                                          ? null
                                                          : () {
                                                            setState(() {
                                                              _isLogin =
                                                                  !_isLogin;
                                                            });
                                                          },
                                                  child: Text(
                                                    _isLogin
                                                        ? l10n.signupAction
                                                        : l10n.loginAction,
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          theme
                                                              .colorScheme
                                                              .primary,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),

                                            const SizedBox(height: 8),

                                            // Error message display
                                            if (_errorMessage != null)
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  bottom: 8.0,
                                                ),
                                                child: Text(
                                                  _errorMessage!,
                                                  style: TextStyle(
                                                    color:
                                                        theme.colorScheme.error,
                                                    fontSize: 14,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),

                                            // Submit button
                                            ElevatedButton(
                                              onPressed:
                                                  isLoading
                                                      ? null
                                                      : _submitForm,
                                              style: ElevatedButton.styleFrom(
                                                elevation: 6,
                                                shadowColor: theme
                                                    .colorScheme
                                                    .primary
                                                    .withValues(alpha: 0.5),
                                                backgroundColor:
                                                    theme.colorScheme.primary,
                                                foregroundColor: Colors.white,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 18,
                                                      horizontal: 28,
                                                    ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(30),
                                                ),
                                              ),
                                              child:
                                                  isLoading
                                                      ? SizedBox(
                                                        width: 24,
                                                        height: 24,
                                                        child:
                                                            CircularProgressIndicator(
                                                              strokeWidth: 3,
                                                              color:
                                                                  Colors.white,
                                                            ),
                                                      )
                                                      : Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Text(
                                                            _isLogin
                                                                ? l10n
                                                                    .loginButton
                                                                : l10n
                                                                    .signupButton,
                                                            style: theme
                                                                .textTheme
                                                                .titleMedium
                                                                ?.copyWith(
                                                                  color:
                                                                      Colors
                                                                          .white,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  letterSpacing:
                                                                      0.5,
                                                                ),
                                                          ),
                                                          const SizedBox(
                                                            width: 8,
                                                          ),
                                                          const Icon(
                                                            Icons.arrow_forward,
                                                            color: Colors.white,
                                                          ),
                                                        ],
                                                      ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    crossFadeState:
                                        _isEmailSectionExpanded
                                            ? CrossFadeState.showSecond
                                            : CrossFadeState.showFirst,
                                    duration: const Duration(milliseconds: 300),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Custom clipper for the top wave shape
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height - 40);
    final firstControlPoint = Offset(size.width * 0.25, size.height);
    final firstEndPoint = Offset(size.width * 0.5, size.height - 40);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );
    final secondControlPoint = Offset(size.width * 0.75, size.height - 80);
    final secondEndPoint = Offset(size.width, size.height - 40);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}
