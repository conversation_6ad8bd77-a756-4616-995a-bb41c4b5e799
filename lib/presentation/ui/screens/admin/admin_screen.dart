import 'package:detoxme/core/services/database_setup_service.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  bool _isLoading = false;
  String? _statusMessage;
  bool _isError = false;

  Future<void> _runDatabaseSetup() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Setting up database... Please wait.';
      _isError = false;
    });

    try {
      // Create the service (consider providing this via DI later)
      final supabaseClient = Supabase.instance.client;
      final setupService = DatabaseSetupService(supabaseClient);

      await setupService.setupDatabaseSchemaAndData();

      setState(() {
        _isLoading = false;
        _statusMessage = 'Database setup completed successfully!';
        _isError = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Database setup failed: ${e.toString()}';
        _isError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Panel'), // TODO: Add to l10n
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _runDatabaseSetup,
                icon:
                    _isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.data_object),
                label: const Text(
                  'Create Supabase Tables & Data',
                ), // TODO: Add to l10n
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              if (_statusMessage != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        _isError
                            ? Theme.of(context).colorScheme.errorContainer
                            : Theme.of(context).colorScheme.secondaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _statusMessage!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color:
                          _isError
                              ? Theme.of(context).colorScheme.onErrorContainer
                              : Theme.of(
                                context,
                              ).colorScheme.onSecondaryContainer,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
