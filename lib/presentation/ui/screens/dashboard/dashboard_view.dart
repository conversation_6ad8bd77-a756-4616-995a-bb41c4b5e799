import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_state.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/widgets/dashboard_content_view.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/widgets/reward_notification_dialog.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:uuid/uuid.dart';
import 'package:detoxme/presentation/bloc/notification/notification_cubit.dart';
import 'package:detoxme/presentation/bloc/notification/notification_state.dart';

/// Main view of the dashboard screen
class DashboardView extends StatelessWidget {
  /// Creates a [DashboardView]
  const DashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BlocListener<RewardCubit, RewardState>(
      listener: (context, state) {
        final l10n = AppLocalizations.of(context)!;
        if (state is RewardLoaded) {
          final bool hasRewards =
              state.xpGained != null ||
              state.levelReached != null ||
              state.badgeEarned != null ||
              state.cardEarned != null;

          if (hasRewards) {
            // Update dashboard points when rewards are earned
            if (state.xpGained != null && state.xpGained! > 0) {
              // Convert XP to points (simplified 1:1 conversion)
              int pointsEarned = state.xpGained!;
              context.read<DashboardCubit>().updatePoints(pointsEarned);
            }

            showRewardNotificationDialog(context, state);
          }
        } else if (state is RewardError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.rewardErrorSnackbar(state.failure.message)),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      },
      child: BlocBuilder<DashboardCubit, DashboardState>(
        builder: (context, dashboardState) {
          // Get time-based greeting
          final hour = DateTime.now().hour;
          String greeting;
          IconData weatherIcon;

          if (hour < 12) {
            greeting = l10n.goodMorning;
            weatherIcon = Icons.wb_sunny_rounded;
          } else if (hour < 17) {
            greeting = l10n.goodAfternoon;
            weatherIcon = Icons.light_mode_rounded;
          } else {
            greeting = l10n.goodEvening;
            weatherIcon = Icons.nights_stay_rounded;
          }

          return BaseScaffold.withTitle(
            title: greeting,
            titleIconData: weatherIcon,
            usePadding: false,
            actions: [
              // Notifications button with badge
              BlocBuilder<NotificationCubit, NotificationState>(
                builder: (context, notificationState) {
                  final unreadCount =
                      context.read<NotificationCubit>().unreadCount;

                  return Stack(
                    clipBehavior: Clip.none,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.notifications_rounded),
                        onPressed: () {
                          // Navigate to notifications screen
                          context.push(AppRoutes.notifications);
                        },
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      if (unreadCount > 0)
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                unreadCount > 99
                                    ? '99+'
                                    : unreadCount.toString(),
                                style: TextStyle(
                                  color: theme.colorScheme.onError,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
              const SizedBox(width: 4),
              // Points info button
              IconButton(
                icon: const Icon(Icons.info_outline_rounded),
                onPressed: () {
                  _showPointsInfoBottomSheet(context);
                },
                tooltip: 'Detox Points Info',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
            ],
            body: Stack(
          children: [
            BlocBuilder<DashboardCubit, DashboardState>(
              builder: (context, state) {
                // Skip loading state and show content directly
                if (state.status == DashboardStatus.initial ||
                    state.status == DashboardStatus.loading) {
                  // Instead of showing loading, load the dashboard if needed
                  if (state.status == DashboardStatus.initial) {
                    context.read<DashboardCubit>().loadDashboard();
                  }

                  // Return the content view anyway with the current state
                  return DashboardContentView(state: state);
                } else if (state.status == DashboardStatus.failure) {
                  return _buildErrorView(context, state.errorMessage);
                }

                return DashboardContentView(state: state);
              },
            ),

            // Debug mode activity button
            if (!kDebugMode)
              Positioned(
                bottom: 20,
                right: 20,
                child: FloatingActionButton.extended(
                  onPressed: () {
                    _startDebugActivity(context);
                  },
                  icon: const Icon(Icons.bug_report),
                  label: const Text('Debug Activity (20s)'),
                  backgroundColor: Colors.purple,
                ),
              ),
          ],
        ),
          );
        },
      ),
    );
  }

  /// Builds the error view
  Widget _buildErrorView(BuildContext context, String? errorMessage) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: theme.colorScheme.error, size: 64),
            const SizedBox(height: 16),
            Text(
              l10n.somethingWentWrong,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            if (errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                errorMessage,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<DashboardCubit>().loadDashboard();
              },
              child: Text(l10n.tryAgain),
            ),
          ],
        ),
      ),
    );
  }

  /// Shows a bottom sheet with information about the detox points system
  void _showPointsInfoBottomSheet(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(24),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 12, bottom: 8),
                    height: 4,
                    width: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onSurfaceVariant.withValues(
                        alpha: 0.4,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),

                  // Title
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.stars_rounded,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          'Detox Points System',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Expanded(
                    child: ListView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Introduction
                        _buildInfoSection(
                          theme,
                          'How Points Work',
                          'Detox points are earned by completing activities that help reduce screen time and promote healthier habits. These points track your progress and can be used to unlock rewards.',
                          Icons.lightbulb_outline,
                        ),

                        const SizedBox(height: 24),

                        // Points calculation
                        _buildInfoSection(
                          theme,
                          'Earning Points',
                          'Points are calculated based on the time you spend on detox activities:',
                          Icons.calculate_outlined,
                        ),

                        const SizedBox(height: 16),

                        // Points table
                        _buildPointsTable(theme),

                        const SizedBox(height: 24),

                        // Daily goals
                        _buildInfoSection(
                          theme,
                          'Daily Goals',
                          'Your daily goal is set to help you maintain consistent progress. Reaching your daily goal contributes to your overall detox journey and unlocks special achievements.',
                          Icons.flag_outlined,
                        ),

                        const SizedBox(height: 24),

                        // Milestones
                        _buildInfoSection(
                          theme,
                          'Milestones',
                          'Reaching certain point milestones (like 1,000 points) unlocks special rewards and recognition. Keep going to discover all milestones!',
                          Icons.emoji_events_outlined,
                        ),

                        const SizedBox(height: 24),

                        // Levels
                        _buildInfoSection(
                          theme,
                          'Levels',
                          'Every 100 points you earn increases your level. Higher levels unlock more advanced activities and rewards.',
                          Icons.trending_up_outlined,
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// Builds an information section with an icon, title, and description
  Widget _buildInfoSection(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: theme.colorScheme.primary),
            const SizedBox(width: 12),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          description,
          style: theme.textTheme.bodyLarge?.copyWith(height: 1.5),
        ),
      ],
    );
  }

  /// Builds a table showing how points are calculated for different activities
  Widget _buildPointsTable(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outlineVariant, width: 1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.5),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Activity Duration',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'Points',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Table rows
          _buildTableRow(theme, '5 minutes', '5 points', false),
          _buildTableRow(theme, '15 minutes', '15 points', true),
          _buildTableRow(theme, '30 minutes', '30 points', false),
          _buildTableRow(theme, '1 hour', '60 points', true),
          _buildTableRow(theme, '2 hours', '120 points', false),

          // Special case
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Aborted (< 15 min)',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    '0 points',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a single row in the points table
  Widget _buildTableRow(
    ThemeData theme,
    String duration,
    String points,
    bool isAlternate,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color:
            isAlternate
                ? theme.colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.3,
                )
                : null,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(duration, style: theme.textTheme.bodyMedium),
          ),
          Expanded(
            child: Text(
              points,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Starts a debug activity that lasts 20 seconds and gives 5 points
  void _startDebugActivity(BuildContext context) {
    final debugActivity = Activity(
      id: const Uuid().v4(),
      title: 'Debug Activity (20 seconds)',
      description:
          'This is a debug activity that lasts 20 seconds and gives 5 points when completed.',
      category: ActivityCategory.physical,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      duration:
          1, // Set to 1 minute for testing, but we'll make the timer show 20 seconds
      healthBenefits: ['Testing the app'],
      recommendedFor: [MoodType.neutral],
    );

    // Start the activity
    context.read<ActivityCubit>().startActivity(debugActivity);

    // Show snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Debug activity started! It will complete in 20 seconds.',
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
