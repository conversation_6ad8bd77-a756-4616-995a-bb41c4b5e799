import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';

/// Card widget for displaying a food recommendation in a calm, health-focused style
/// Matches the design language of dashboard and activity cards
class FoodRecommendationCard extends StatelessWidget {
  /// The food recommendation to display
  final FoodRecommendation recommendation;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Whether to show voting controls
  final bool showVoting;

  /// Creates a new [FoodRecommendationCard]
  const FoodRecommendationCard({
    required this.recommendation,
    required this.onTap,
    this.showVoting = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Soft, health-inspired colors that match dashboard design
    final Color accentColor = const Color(0xFF66BB6A); // Soft green for health
    final Color softBackground = colorScheme.surface;
    final isNew =
        DateTime.now().difference(recommendation.createdAt).inDays < 7;

    return Card(
      elevation: 1,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.08),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: softBackground,
            border: Border.all(
              color: accentColor.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with emoji and badges
                Row(
                  children: [
                    // Food emoji (larger, no container)
                    Text(_getFoodEmoji(), style: const TextStyle(fontSize: 32)),
                    const SizedBox(width: 12),
                    // Title and status badges
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title (trimmed)
                          Text(
                            recommendation.name.trimLeft(),
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                              height: 1.1,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          // Status badges (horizontal)
                          Wrap(
                            spacing: 6,
                            children: [
                              if (isNew)
                                _buildStatusBadge(
                                  'NEU',
                                  accentColor,
                                  Icons.auto_awesome,
                                ),
                              if (_isPopular())
                                _buildStatusBadge(
                                  'BELIEBT',
                                  const Color(0xFFFF9800),
                                  Icons.trending_up,
                                ),
                              if (_isHighlyRated())
                                _buildStatusBadge(
                                  'TOP',
                                  const Color(0xFFE91E63),
                                  Icons.star,
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                // Mood badges (prominent and colorful)
                if (recommendation.moodTypes != null &&
                    recommendation.moodTypes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildMoodBadges(theme, colorScheme),
                ],
                const SizedBox(height: 8),
                // Description (max 2 lines)
                Text(
                  recommendation.description.trimLeft(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    height: 1.2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // Additional info section
                _buildAdditionalInfo(theme, colorScheme),
                const SizedBox(height: 8),
                // Bottom section with votes and stats
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Vote count and stats
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Heart icon with count
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.favorite,
                                size: 14,
                                color: accentColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${_getTotalVotes()}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: accentColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Additional stats if available
                        if (recommendation.upvotes + recommendation.downvotes >
                            0) ...[
                          const SizedBox(width: 8),
                          Text(
                            '${recommendation.upvotes + recommendation.downvotes} Bewertungen',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ],
                    ),
                    // Vote buttons (minimal)
                    if (showVoting)
                      BlocBuilder<FoodMoodCubit, FoodMoodState>(
                        buildWhen:
                            (previous, current) =>
                                current.votingRecommendationId ==
                                    recommendation.id ||
                                previous.votingRecommendationId ==
                                    recommendation.id,
                        builder: (context, state) {
                          final isVoting =
                              state.isVoteInProgress &&
                              state.votingRecommendationId == recommendation.id;
                          if (isVoting) {
                            return SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: accentColor,
                              ),
                            );
                          }
                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Upvote button
                              InkWell(
                                onTap:
                                    () => _handleVote(context, VoteType.upvote),
                                borderRadius: BorderRadius.circular(6),
                                child: Padding(
                                  padding: const EdgeInsets.all(6),
                                  child: Icon(
                                    Icons.thumb_up_outlined,
                                    size: 16,
                                    color:
                                        recommendation.userVote ==
                                                VoteType.upvote
                                            ? accentColor
                                            : colorScheme.onSurface.withValues(
                                              alpha: 0.5,
                                            ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 4),
                              // Downvote button
                              InkWell(
                                onTap:
                                    () =>
                                        _handleVote(context, VoteType.downvote),
                                borderRadius: BorderRadius.circular(6),
                                child: Padding(
                                  padding: const EdgeInsets.all(6),
                                  child: Icon(
                                    Icons.thumb_down_outlined,
                                    size: 16,
                                    color:
                                        recommendation.userVote ==
                                                VoteType.downvote
                                            ? colorScheme.error
                                            : colorScheme.onSurface.withValues(
                                              alpha: 0.5,
                                            ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Get appropriate food emoji based on food name
  String _getFoodEmoji() {
    final name = recommendation.name.toLowerCase();

    // Chocolate and sweets
    if (name.contains('chocolate') || name.contains('schokolade')) return '🍫';
    if (name.contains('candy') || name.contains('süßigkeit')) return '🍬';
    if (name.contains('cookie') || name.contains('keks')) return '🍪';
    if (name.contains('cake') || name.contains('kuchen')) return '🍰';
    if (name.contains('ice cream') || name.contains('eis')) return '🍦';

    // Asian foods
    if (name.contains('soba') ||
        name.contains('noodle') ||
        name.contains('nudel')) {
      return '🍜';
    }
    if (name.contains('rice') || name.contains('reis')) return '🍚';
    if (name.contains('sushi')) return '🍣';
    if (name.contains('ramen')) return '🍜';

    // Snacks
    if (name.contains('trail mix') || name.contains('studentenfutter')) {
      return '🥜';
    }
    if (name.contains('almond') || name.contains('mandel')) return '🌰';
    if (name.contains('nuts') || name.contains('nüsse')) return '🥜';
    if (name.contains('chips')) return '🥨';

    // Fruits and vegetables
    if (name.contains('apple') || name.contains('apfel')) return '🍎';
    if (name.contains('banana') || name.contains('banane')) return '🍌';
    if (name.contains('orange') || name.contains('orange')) return '🍊';
    if (name.contains('salad') || name.contains('salat')) return '🥗';
    if (name.contains('avocado')) return '🥑';

    // Drinks
    if (name.contains('coffee') || name.contains('kaffee')) return '☕';
    if (name.contains('tea') || name.contains('tee')) return '🍵';
    if (name.contains('smoothie')) return '🥤';
    if (name.contains('juice') || name.contains('saft')) return '🧃';

    // Bread and grains
    if (name.contains('bread') || name.contains('brot')) return '🍞';
    if (name.contains('pasta') || name.contains('nudeln')) return '🍝';
    if (name.contains('pizza')) return '🍕';
    if (name.contains('sandwich')) return '🥪';

    // Meat and protein
    if (name.contains('chicken') || name.contains('hähnchen')) return '🍗';
    if (name.contains('fish') || name.contains('fisch')) return '🐟';
    if (name.contains('egg') || name.contains('ei')) return '🥚';
    if (name.contains('cheese') || name.contains('käse')) return '🧀';

    // Default food emoji
    return '🍽️';
  }

  /// Get total votes (upvotes - downvotes)
  int _getTotalVotes() {
    return recommendation.upvotes - recommendation.downvotes;
  }

  /// Handle vote action
  void _handleVote(BuildContext context, VoteType voteType) {
    final foodMoodCubit = context.read<FoodMoodCubit>();
    foodMoodCubit.submitVote(recommendation.id, voteType);
  }

  /// Build status badge (NEU, BELIEBT, etc.)
  Widget _buildStatusBadge(String text, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: Colors.white),
          const SizedBox(width: 3),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }

  /// Build mood badges with colors and emojis
  Widget _buildMoodBadges(ThemeData theme, ColorScheme colorScheme) {
    return Wrap(
      spacing: 8,
      runSpacing: 6,
      children:
          recommendation.moodTypes!.take(3).map((mood) {
            final moodColor = _getMoodColor(mood);
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: moodColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: moodColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(mood.emoji, style: const TextStyle(fontSize: 14)),
                  const SizedBox(width: 6),
                  Text(
                    mood.name,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: moodColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Build additional information section to fill empty space
  Widget _buildAdditionalInfo(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Creator and date info
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 16,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  recommendation.username.isNotEmpty
                      ? 'Von ${recommendation.username}'
                      : 'Anonymer Nutzer',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                _formatTimeAgo(recommendation.createdAt),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                  fontSize: 10,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Quick benefits/features
          Row(
            children: [
              _buildQuickFeature(
                icon: Icons.flash_on,
                text: 'Schnell',
                color: Colors.orange,
                theme: theme,
              ),
              const SizedBox(width: 12),
              _buildQuickFeature(
                icon: Icons.favorite_outline,
                text: 'Gesund',
                color: Colors.green,
                theme: theme,
              ),
              const SizedBox(width: 12),
              _buildQuickFeature(
                icon: Icons.store,
                text: 'Verfügbar',
                color: Colors.blue,
                theme: theme,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build a quick feature indicator
  Widget _buildQuickFeature({
    required IconData icon,
    required String text,
    required Color color,
    required ThemeData theme,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// Format time ago for display
  String _formatTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${difference.inDays ~/ 30}M';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}T';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'jetzt';
    }
  }

  /// Check if food is popular (high vote count)
  bool _isPopular() {
    return _getTotalVotes() >= 10; // Popular if 10+ net votes
  }

  /// Check if food is highly rated (high upvote ratio)
  bool _isHighlyRated() {
    final total = recommendation.upvotes + recommendation.downvotes;
    if (total < 5) return false; // Need at least 5 votes
    final ratio = recommendation.upvotes / total;
    return ratio >= 0.8; // 80%+ upvote ratio
  }

  /// Get mood-specific colors
  Color _getMoodColor(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return const Color(0xFFFFB74D); // Warm orange
      case MoodType.excited:
        return const Color(0xFFFF7043); // Vibrant orange-red
      case MoodType.calm:
        return const Color(0xFF81C784); // Soft green
      case MoodType.sad:
        return const Color(0xFF64B5F6); // Soft blue
      case MoodType.anxious:
        return const Color(0xFF4DB6AC); // Teal
      case MoodType.angry:
        return const Color(0xFFE57373); // Soft red
      case MoodType.tired:
        return const Color(0xFFBA68C8); // Soft purple
      case MoodType.neutral:
        return const Color(0xFF90A4AE); // Blue grey
      case MoodType.inspired:
        return const Color(0xFF4FC3F7); // Light blue
    }
  }
}
