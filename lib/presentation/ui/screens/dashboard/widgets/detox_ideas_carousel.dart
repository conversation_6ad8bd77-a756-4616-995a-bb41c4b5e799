import 'package:flutter/material.dart';
import 'package:detoxme/core/extensions/color_extensions.dart';
import 'package:detoxme/domain/entities/detox_idea.dart';

/// Auto-scrolling carousel for detox ideas
class DetoxIdeasCarousel extends StatefulWidget {
  /// List of detox ideas to display
  final List<DetoxIdea> ideas;
  
  /// Creates a [DetoxIdeasCarousel]
  const DetoxIdeasCarousel({
    super.key,
    required this.ideas,
  });
  
  @override
  State<DetoxIdeasCarousel> createState() => _DetoxIdeasCarouselState();
}

class _DetoxIdeasCarouselState extends State<DetoxIdeasCarousel> {
  late PageController _pageController;
  int _currentPage = 0;
  
  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      initialPage: 0,
      viewportFraction: 0.85,
    );
    
    // Auto-scroll timer
    Future.delayed(const Duration(seconds: 1), () {
      _autoScroll();
    });
  }
  
  void _autoScroll() {
    if (!mounted) return;
    
    // Calculate next page, wrapping around at the end
    final nextPage = (_currentPage + 1) % widget.ideas.length;
    
    // Animate to next page
    _pageController.animateToPage(
      nextPage,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
    );
    
    // Schedule next scroll
    Future.delayed(const Duration(seconds: 5), () {
      _autoScroll();
    });
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemCount: widget.ideas.length,
            itemBuilder: (context, index) {
              final idea = widget.ideas[index];
              
              // Apply scaling effect to cards
              final isCurrentPage = index == _currentPage;
              
              return AnimatedScale(
                scale: isCurrentPage ? 1.0 : 0.9,
                duration: const Duration(milliseconds: 300),
                child: _buildDetoxIdeaCard(idea, theme),
              );
            },
          ),
        ),
        
        // Page indicator dots
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.ideas.length,
            (index) => _buildIndicator(index == _currentPage, theme, widget.ideas[index].color),
          ),
        ),
      ],
    );
  }
  
  Widget _buildDetoxIdeaCard(DetoxIdea idea, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              idea.color.withValue(alpha: 51),
              idea.color.withValue(alpha: 26),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: idea.color.withValue(alpha: 51),
                shape: BoxShape.circle,
              ),
              child: Icon(
                idea.icon,
                color: idea.color,
                size: 36,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              idea.title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              idea.description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValue(alpha: 204),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildIndicator(bool isActive, ThemeData theme, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      height: 8,
      width: isActive ? 24 : 8,
      decoration: BoxDecoration(
        color: isActive ? color : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
