import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/collectible_card.dart';
import 'package:detoxme/domain/entities/badge.dart' as badge_entity;
import 'package:detoxme/presentation/bloc/reward/reward_state.dart';
import 'package:lottie/lottie.dart';

/// Shows a reward notification dialog to the user
Future<void> showRewardNotificationDialog(
  BuildContext context,
  RewardLoaded rewardState,
) async {
  final theme = Theme.of(context);

  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Celebration animation
              SizedBox(
                height: 150,
                child: Lottie.asset(
                  'assets/animations/confetti.json',
                  repeat: true,
                  animate: true,
                ),
              ),
              // Title
              Text(
                'Congratulations!',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 16),
              // XP gained
              if (rewardState.xpGained != null && rewardState.xpGained! > 0) ...[
                _buildXpGainedView(context, rewardState.xpGained!),
                const SizedBox(height: 16),
              ],
              // Level reached
              if (rewardState.levelReached != null) ...[
                Text(
                  'You reached level ${rewardState.levelReached!}!',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.tertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
              ],
              // Badge earned
              if (rewardState.badgeEarned != null) _buildBadgeSection(context, theme, rewardState.badgeEarned!),
              // Card earned  
              if (rewardState.cardEarned != null) _buildCardSection(context, theme, rewardState.cardEarned!),
              const SizedBox(height: 24),
              // Close button
              _buildContinueButton(context),
            ],
          ),
        ),
      );
    },
  );
}

/// Builds the badge section for the reward dialog
Widget _buildBadgeSection(BuildContext context, ThemeData theme, badge_entity.Badge badge) {
  return Column(
    children: [
      Text(
        'Badge Earned!',
        style: theme.textTheme.titleMedium,
        textAlign: TextAlign.center,
      ),
      const SizedBox(height: 12),
      _buildBadgeImage(context, badge),
      const SizedBox(height: 8),
      Text(
        badge.name,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
      const SizedBox(height: 4),
      Text(
        badge.description,
        style: theme.textTheme.bodyMedium,
        textAlign: TextAlign.center,
      ),
      const SizedBox(height: 16),
    ],
  );
}

/// Builds the card section for the reward dialog
Widget _buildCardSection(BuildContext context, ThemeData theme, CollectibleCard card) {
  return Column(
    children: [
      Text(
        'Card Earned!',
        style: theme.textTheme.titleMedium,
        textAlign: TextAlign.center,
      ),
      const SizedBox(height: 12),
      Container(
        width: 120,
        height: 80,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.secondary,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            card.name,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      const SizedBox(height: 8),
      Text(
        card.description,
        style: theme.textTheme.bodyMedium,
        textAlign: TextAlign.center,
      ),
      const SizedBox(height: 16),
    ],
  );
}

Widget _buildContinueButton(BuildContext context) {
  return ElevatedButton(
    onPressed: () {
      Navigator.of(context).pop();
    },
    style: ElevatedButton.styleFrom(
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
    ),
    child: const Text('Continue'),
  );
}

Widget _buildXpGainedView(BuildContext context, int xpGained) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Text(
        'Congratulations!',
        style: Theme.of(context).textTheme.headlineMedium!.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
      const SizedBox(height: 16),
      Text(
        'You gained $xpGained XP!',
        style: Theme.of(context).textTheme.titleLarge!.copyWith(
              fontSize: 20,
            ),
        textAlign: TextAlign.center,
      ),
    ],
  );
}

Widget _buildBadgeImage(BuildContext context, badge_entity.Badge badge) {
  // TODO: Implement badge image display
  return Container(
    width: 100,
    height: 100,
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.secondary,
      shape: BoxShape.circle,
    ),
    child: const Icon(
      Icons.star,
      color: Colors.white,
      size: 50,
    ),
  );
}
