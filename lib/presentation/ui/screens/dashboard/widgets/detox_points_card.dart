import 'package:detoxme/localization/arb/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_state.dart';
import 'animated_speedometer_chart.dart';
import 'dart:ui';

/// A card widget that displays the user's current detox points
class DetoxPointsCard extends StatefulWidget {
  /// Creates a new DetoxPointsCard
  const DetoxPointsCard({super.key});

  @override
  State<DetoxPointsCard> createState() => _DetoxPointsCardState();
}

class _DetoxPointsCardState extends State<DetoxPointsCard> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize data only once when the widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_hasInitialized) {
        context.read<DetoxPointsCubit>().loadPointsAndHistory();
        _hasInitialized = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<DetoxPointsCubit, DetoxPointsState>(
      listener: (context, state) {
        // This will be called whenever the state changes via stream subscriptions
        if (kDebugMode) {
          print('DetoxPointsCard: State changed to $state');
        }
      },
      builder: (context, state) {
        // Only load data if we're in initial state and haven't initialized yet
        if (state is DetoxPointsInitial && !_hasInitialized) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<DetoxPointsCubit>().loadPointsAndHistory();
            _hasInitialized = true;
          });
        }

        // Error state
        if (state is DetoxPointsError) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.7,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: Colors.white, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.failedToLoadProgressData,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context)!.connectionProblem,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withAlpha(230),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: () {
                        context.read<DetoxPointsCubit>().loadPointsAndHistory();
                      },
                      icon: const Icon(Icons.refresh),
                      label: Text(AppLocalizations.of(context)!.tryAgain),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: theme.colorScheme.error,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // Loaded state - data will update automatically via stream subscriptions
        if (state is DetoxPointsLoaded) {
          final pointsData = state.points;
          // Use dailyPoints and dailyGoalTarget fields
          final int dailyGoal = pointsData.dailyGoalTarget;
          final int todaysPoints = pointsData.dailyPoints;
          final double progressPercentage =
              dailyGoal > 0 ? (todaysPoints / dailyGoal).clamp(0.0, 1.0) : 0.0;

          return Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.2,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Total: ${pointsData.totalPoints} pts',
                              style: theme.textTheme.bodyMedium,
                            ),
                            const Spacer(),
                            Text(
                              '${pointsData.pointsToNextLevel} pts to Level ${pointsData.level + 1}',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        // Level progress bar
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: LinearProgressIndicator(
                            value:
                                pointsData.totalPoints /
                                (pointsData.totalPoints +
                                    pointsData.pointsToNextLevel),
                            backgroundColor: theme
                                .colorScheme
                                .surfaceContainerHighest
                                .withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.tertiary,
                            ),
                            minHeight: 4,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.star, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'Level ${pointsData.level}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Center(
                      child: AnimatedSpeedometerChart(
                        dimension: 240,
                        minValue: 0,
                        maxValue: dailyGoal.toDouble(),
                        value: todaysPoints.toDouble(),
                        graphColor: [
                          theme.colorScheme.tertiary,
                          theme.colorScheme.primary,
                          theme.colorScheme.secondary,
                        ],
                        pointerColor: theme.colorScheme.onSurface,
                        valueWidget: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '$todaysPoints/$dailyGoal',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              AppLocalizations.of(context)!.dailyPoints,
                              style: theme.textTheme.bodySmall,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(top: 8),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: theme
                                        .colorScheme
                                        .surfaceContainerHighest
                                        .withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${(progressPercentage * 100).toInt()}% Complete',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        minWidget: Text(
                          '0',
                          style: theme.textTheme.bodySmall?.copyWith(
                          ),
                        ),
                        maxWidget: Text(
                          dailyGoal.toString(),
                          style: theme.textTheme.bodySmall?.copyWith(),
                        ),
                        title: SizedBox(),
                        titleMargin: 12,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Notification prompt if not reached
                    if (todaysPoints < dailyGoal)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.emoji_events_outlined, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'Only ${dailyGoal - todaysPoints} pts to reach your daily goal!',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    if (todaysPoints >= dailyGoal)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle_outline, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'Daily goal reached! Keep going for more points!',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          );
        }

        // Loading state - only show this during initial load
        return ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 0),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.7,
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              height: 400,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with placeholder level
                  Row(
                    children: [
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.star, color: Colors.white, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Level ...',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Placeholder for level progress
                  Row(
                    children: [
                      Text(
                        'Loading points data...',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withAlpha(230),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  // Placeholder progress bar
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      backgroundColor: Colors.white.withAlpha(50),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.white.withAlpha(200),
                      ),
                      minHeight: 4,
                    ),
                  ),

                  // Centered loading indicator
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 50,
                            height: 50,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Loading progress...',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
