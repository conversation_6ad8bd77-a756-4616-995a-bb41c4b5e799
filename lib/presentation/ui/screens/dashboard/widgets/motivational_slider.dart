import 'dart:async';
import 'package:detoxme/localization/arb/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/weather_data.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_cubit.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_state.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import '../../../widgets/motivational_card.dart';

/// A horizontal slider that displays a list of motivational cards
/// with auto-sliding functionality every 6 seconds
class MotivationalSlider extends StatefulWidget {
  /// List of motivational content to display (fallback)
  final List<AiMotivation> motivations;

  /// User profile information
  final UserProfile userProfile;

  /// Weather information to display
  final String weatherInfo;

  /// Optional weather icon URL
  final String? weatherIcon;

  /// Current user language for quotes
  final String language;

  /// Creates a motivational slider
  const MotivationalSlider({
    super.key,
    required this.motivations,
    required this.userProfile,
    required this.weatherInfo,
    this.weatherIcon,
    this.language = 'en',
  });

  @override
  State<MotivationalSlider> createState() => _MotivationalSliderState();
}

class _MotivationalSliderState extends State<MotivationalSlider>
    with SingleTickerProviderStateMixin {
  int _currentPage = 0;
  final PageController _controller = PageController(viewportFraction: 1.0);
  Timer? _autoScrollTimer;
  late AnimationController _indicatorAnimController;

  @override
  void initState() {
    super.initState();
    // Initialize animation controller for indicator animations
    _indicatorAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    // Load mood-based quotes when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMoodBasedQuotes();
    });

    // Start auto-scrolling timer (every 6 seconds)
    _startAutoScroll();
  }

  /// Loads mood-based quotes based on current mood
  void _loadMoodBasedQuotes() {
    final dashboardState = context.read<DashboardCubit>().state;
    final currentMood = dashboardState.currentMood ?? MoodType.neutral;
    
    context.read<MoodQuoteCubit>().getQuotesForMood(
      moodType: currentMood,
      language: widget.language,
      limit: 5, // Get 5 quotes for rotation
    );
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _controller.dispose();
    _indicatorAnimController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 6), (timer) {
      // Get current content count (either mood quotes or fallback motivations)
      final moodQuoteState = context.read<MoodQuoteCubit>().state;
      final contentCount = moodQuoteState.status == MoodQuoteStatus.success && moodQuoteState.quotes.isNotEmpty
          ? moodQuoteState.quotes.length
          : widget.motivations.length;
      
      if (contentCount == 0) return;
      
      if (_currentPage < contentCount - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }

      _controller.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOutCubic,
      );

      _indicatorAnimController.reset();
      _indicatorAnimController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [

        SizedBox(
          height: 180, // Adjusted height for the new card design
          child: BlocBuilder<MoodQuoteCubit, MoodQuoteState>(
            builder: (context, moodQuoteState) {
              // Use mood quotes if available, otherwise fallback to AI motivations
              final shouldUseMoodQuotes = moodQuoteState.status == MoodQuoteStatus.success && 
                  moodQuoteState.quotes.isNotEmpty;
              
              final itemCount = shouldUseMoodQuotes 
                  ? moodQuoteState.quotes.length 
                  : widget.motivations.length;
              
              if (itemCount == 0) {
                return Center(
                  child: Text(AppLocalizations.of(context)!.noMotivationalContent),
                );
              }
              
              return PageView.builder(
                controller: _controller,
                itemCount: itemCount,
                padEnds: false,
                onPageChanged: (index) {
                  setState(() => _currentPage = index);
                  _indicatorAnimController.reset();
                  _indicatorAnimController.forward();
                },
                itemBuilder: (context, index) {
                  return shouldUseMoodQuotes
                      ? MotivationalCard(
                          key: ValueKey('mood_quote_$index'),
                          hasChildren: widget.userProfile.lifeSituation?.contains('children') ?? false,
                          userGoal: widget.userProfile.goals.isNotEmpty ? widget.userProfile.goals.first : 'focus',
                          weatherCondition: WeatherCondition.sunny, // Default weather
                          dayProgress: 0.5, // Default progress
                          detoxPoints: 0, // Default points since UserProfile doesn't have totalPoints
                        )
                      : MotivationalCard.fromAiMotivation(
                          key: ValueKey('motivation_${index}_$index'),
                        aiMotivation: widget.motivations[index],
                          userProfile: widget.userProfile,
                          weatherInfo: widget.weatherInfo,
                          weatherIcon: widget.weatherIcon,
                        );
                },
              );
            },
          ),
        ),

        // Page indicators
        const SizedBox(height: 16),
        BlocBuilder<MoodQuoteCubit, MoodQuoteState>(
          builder: (context, moodQuoteState) {
            final shouldUseMoodQuotes = moodQuoteState.status == MoodQuoteStatus.success && 
                moodQuoteState.quotes.isNotEmpty;
            
            final itemCount = shouldUseMoodQuotes 
                ? moodQuoteState.quotes.length 
                : widget.motivations.length;
            
            if (itemCount == 0) return const SizedBox.shrink();
            
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                itemCount,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentPage == index ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? theme.colorScheme.primary
                        : theme.colorScheme.primary.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
