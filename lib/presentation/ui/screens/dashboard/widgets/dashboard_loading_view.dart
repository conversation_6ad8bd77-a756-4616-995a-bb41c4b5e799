import 'package:flutter/material.dart';
import 'package:detoxme/localization/app_localizations.dart';

/// Loading state view for the dashboard
class DashboardLoadingView extends StatelessWidget {
  /// Creates a [DashboardLoadingView]
  const DashboardLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: 16),
          Text(l10n.loadingDashboard, style: theme.textTheme.titleMedium),
        ],
      ),
    );
  }
}
