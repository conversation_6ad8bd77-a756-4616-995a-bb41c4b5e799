import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/core/themes/app_themes.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/activity/activity_state.dart';
import 'package:detoxme/presentation/bloc/activity/activity_timer_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_state.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_bloc.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_state.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/widgets/detox_points_card.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/widgets/motivational_slider.dart';

import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';

/// Content view component for the Dashboard
class DashboardContentView extends StatefulWidget {
  /// The dashboard state
  final DashboardState state;

  /// Creates a [DashboardContentView]
  const DashboardContentView({super.key, required this.state});

  @override
  State<DashboardContentView> createState() => _DashboardContentViewState();
}

class _DashboardContentViewState extends State<DashboardContentView> {
  // Flag to track if we've already performed mood synchronization
  bool _hasSyncedMoods = false;

  @override
  void initState() {
    super.initState();

    // When dashboard loads, check mood from other cubits and ensure consistency
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _hasSyncedMoods) return;
      
      // Set the flag to prevent future syncs when tab is revisited
      _hasSyncedMoods = true;

      final dashboardCubit = context.read<DashboardCubit>();
      final activityCubit = context.read<ActivityCubit>();
      final foodMoodCubit = context.read<FoodMoodCubit>();

      // Get current mood from dashboard cubit
      final dashboardMood = dashboardCubit.getCurrentMood();

      // Check if activity cubit has a mood selected
      final activityMood = activityCubit.state.selectedMood;

      // Check if food mood cubit has a mood selected
      final foodMood = foodMoodCubit.state.selectedMood;

      // Only do full mood synchronization if dashboard has no mood or it's the first load
      if (dashboardMood == null) {
        if (activityMood != null) {
          // Sync from activity cubit
          dashboardCubit.setCurrentMood(activityMood);
          foodMoodCubit.loadFoodRecommendationsForMood(activityMood);
        } else if (foodMood != null) {
          // Sync from food mood cubit
          dashboardCubit.setCurrentMood(foodMood);
          activityCubit.selectMood(foodMood);
        }
      }

      // First make sure activities are loaded for display in dashboard
      if (activityCubit.state.activities == null ||
          activityCubit.state.activities!.isEmpty) {
        activityCubit.loadActivities();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<DashboardCubit, DashboardState>(
      listenWhen:
          (previous, current) => previous.currentMood != current.currentMood,
      listener: (context, dashboardState) {
        // When mood changes in dashboard, update activity filtering
        if (dashboardState.currentMood != null) {
          context.read<ActivityCubit>().selectMood(dashboardState.currentMood!);
        }
      },
      child: SingleChildScrollView(
        child: Padding(
        padding: const EdgeInsets.only(
          left: 8.0,
          right: 8.0,
          bottom: 110.0,
          top: 0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Motivational Slider with personalized content
            BlocBuilder<WellbeingAssistantBloc, WellbeingAssistantState>(
              builder: (context, wellbeingState) {
                if (wellbeingState.status == WellbeingAssistantStatus.loading) {
                  return const SizedBox(
                    height: 210,
                    child: Center(child: CircularProgressIndicator()),
                  );
                } else if (wellbeingState.status == WellbeingAssistantStatus.failure ||
                    wellbeingState.motivations.isEmpty) {
                  return const SizedBox.shrink();
                } else {
                  // Get user profile data
                    return BlocBuilder<ProfileCubit, ProfileState>(
                      builder: (context, userProfileState) {
                      // Default profile if not loaded
                      UserProfile userProfile = UserProfile.empty('default');
                      
                      // Get actual profile if available
                        if (userProfileState is ProfileLoaded) {
                          userProfile = userProfileState.profile;
                      }
                      
                      // Create sample weather data (could be dynamic in a real implementation)
                      final now = DateTime.now();
                      final hour = now.hour;
                      
                      // Simple weather info based on time of day
                      String weatherInfo;
                      String? weatherIcon;
                      
                      if (hour >= 6 && hour < 12) {
                        weatherInfo = '18°C Morning';
                      } else if (hour >= 12 && hour < 18) {
                        weatherInfo = '23°C Sunny';
                      } else if (hour >= 18 && hour < 22) {
                        weatherInfo = '20°C Evening';
                      } else {
                        weatherInfo = '15°C Night';
                      }

                      // Get current app language
                      final currentLanguage = Localizations.localeOf(context).languageCode;
                      
                      return FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        child: MotivationalSlider(
                          motivations: wellbeingState.motivations,
                          userProfile: userProfile,
                          weatherInfo: weatherInfo,
                          weatherIcon: weatherIcon,
                          language: currentLanguage,
                        ),
                      );
                    },
                  );
                }
              },
            ),

            const SizedBox(height: 24),

            // Detox Points Card with a proper header
            FadeInUp(
              duration: const Duration(milliseconds: 1100),
              delay: const Duration(milliseconds: 300),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Add title with icon for Detox Points
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              theme.colorScheme.primary.withValues(alpha: 0.2),
                              theme.colorScheme.primary.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                            width: 1.5,
                          ),
                        ),
                        child: Icon(
                          Icons.star_rounded,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        AppLocalizations.of(context)!.dailyProgress,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const DetoxPointsCard(),
                ],
              ),
            ),

            const SizedBox(height: 24),



            // Detox Activities Carousel with animation
            FadeInUp(
              duration: const Duration(milliseconds: 1600),
              delay: const Duration(milliseconds: 600),
              child: _buildDetoxActivitiesCarousel(context, theme),
            ),

            const SizedBox(height: 32),

            // Coming Soon - Coupon Section
            FadeInUp(
              duration: const Duration(milliseconds: 1800),
              delay: const Duration(milliseconds: 800),
              child: _buildComingSoonCouponSection(context, theme),
            ),
          ],
        ),
      ),
      ),
    );
  }

  /// Builds a "Coming Soon" card for coupons feature
  Widget _buildComingSoonCouponSection(BuildContext context, ThemeData theme) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    theme.colorScheme.primary.withValues(alpha: 0.2),
                    theme.colorScheme.primary.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  width: 1.5,
                ),
              ),
              child: Icon(
                Icons.verified_rounded,
                color: theme.colorScheme.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              l10n.comingSoon,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        InkWell(
          onTap: () => context.push(AppRoutes.categories),
          borderRadius: BorderRadius.circular(16),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Ink(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.2,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.tertiary.withValues(
                          alpha: 0.8,
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.card_giftcard_rounded,
                        size: 34,
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.8,
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  l10n.detoxRewards,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.tertiary.withValues(
                                    alpha: 0.4,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Soon',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            l10n.rewardsDescription,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.8,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the detox activities carousel section
  Widget _buildDetoxActivitiesCarousel(BuildContext context, ThemeData theme) {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, activityState) {
        if (activityState.status == ActivityStatus.loading) {
          return _buildLoadingActivities(context, theme);
        } else if (activityState.status == ActivityStatus.error) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                AppLocalizations.of(context)!.failedToLoadActivities,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ),
          );
        } else if (activityState.activities?.isEmpty ?? true) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                AppLocalizations.of(context)!.noActivitiesFound,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ),
          );
        } else {
          // Get all activities or filtered by mood if available
          final allActivities =
              activityState.filteredActivities ??
              activityState.activities ??
              [];

          // Group by category
          final List<Activity> mindfulnessActivities = allActivities
              .where((a) => a.category == ActivityCategory.mindfulness)
              .toList();
          final List<Activity> relaxationActivities = allActivities
              .where((a) => a.category == ActivityCategory.relaxation)
              .toList();
          final List<Activity> learningActivities = allActivities
              .where((a) => a.category == ActivityCategory.learning)
              .toList();

          // If no activities in any category after filtering, show a message
          if (mindfulnessActivities.isEmpty &&
              relaxationActivities.isEmpty &&
              learningActivities.isEmpty) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withValues(alpha: 0.2),
                            theme.colorScheme.primary.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          width: 1.5,
                        ),
                      ),
                      child: Icon(
                        Icons.info_outline_rounded,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Activities',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Text(
                      'No activities found for this mood. Try selecting a different mood.',
                      style: theme.textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (mindfulnessActivities.isNotEmpty) ...[                _buildActivitySection(
                  context,
                  theme,
                  "Mindfulness",
                  mindfulnessActivities,
                  AppColors.primary, // Use primary color
                ),
                const SizedBox(height: 24),
              ],
              if (relaxationActivities.isNotEmpty) ...[                _buildActivitySection(
                  context,
                  theme,
                  "Relaxation",
                  relaxationActivities,
                  AppColors.secondary, // Use secondary color
                ),
                const SizedBox(height: 24),
              ],
              if (learningActivities.isNotEmpty) ...[                _buildActivitySection(
                  context,
                  theme,
                  "Learning",
                  learningActivities,
                  AppColors.accent, // Use accent color
                ),
              ],
            ],
          );
        }
      },
    );
  }

  /// Builds a loading placeholder for activities
  Widget _buildLoadingActivities(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.detoxActivities,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 150,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: theme.colorScheme.surface,
                ),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Build a section of activities with a horizontal list
  Widget _buildActivitySection(
    BuildContext context,
    ThemeData theme,
    String title,
    List<Activity> activities,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withValues(alpha: 0.2),
                    color.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: color.withValues(alpha: 0.3),
                  width: 1.5,
                ),
              ),
              child: Icon(
                _getCategoryIcon(activities.first.category),
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    '${activities.length} activities available',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            TextButton.icon(
              onPressed: () {
                // Navigate to activities screen with this category filter
                context.push('/activities');
              },
              icon: Icon(Icons.arrow_forward_ios, size: 16, color: color),
              label: Text(
                'View All',
                style: TextStyle(color: color, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        SizedBox(
          height: 220,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            scrollDirection: Axis.horizontal,
            itemCount:
                activities.length > 6
                    ? 6
                    : activities.length, // Limit to 6 for performance
            itemBuilder: (context, index) {
              final activity = activities[index];
              return FadeInUp(
                duration: Duration(milliseconds: 600 + (index * 100)),
                delay: Duration(milliseconds: 200 + (index * 50)),
                child: GestureDetector(
                  onTap: () async {
                    final activityTimerCubit = context.read<ActivityTimerCubit>();
                    activityTimerCubit.startActivity(activity);
                    await context.push(AppRoutes.activityDetail, extra: activity);
                  },
                  child: Container(
                    width: 190,
                    margin: const EdgeInsets.only(right: 16, bottom: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          color.withValues(alpha: 0.25),
                          color.withValues(alpha: 0.15),
                          color.withValues(alpha: 0.08),
                          theme.colorScheme.surface.withValues(alpha: 0.95),
                        ],
                        stops: const [0.0, 0.3, 0.7, 1.0],
                      ),
                      border: Border.all(
                        color: color.withValues(alpha: 0.4),
                        width: 1.5,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(24),
                      child: Stack(
                        children: [
                          // Enhanced decorative background pattern
                          Positioned(
                            top: -30,
                            right: -30,
                            child: Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    color.withValues(alpha: 0.15),
                                    color.withValues(alpha: 0.05),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: -40,
                            left: -40,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    color.withValues(alpha: 0.12),
                                    color.withValues(alpha: 0.04),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Subtle geometric pattern
                          Positioned(
                            top: 20,
                            left: 20,
                            child: Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: color.withValues(alpha: 0.3),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 35,
                            left: 35,
                            child: Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: color.withValues(alpha: 0.2),
                              ),
                            ),
                          ),

                          // Main content
                          Padding(
                            padding: const EdgeInsets.all(18),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header with activity icon and duration
                                Row(
                                  children: [
                                    // Activity icon
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            color.withValues(alpha: 0.2),
                                            color.withValues(alpha: 0.1),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        border: Border.all(
                                          color: color.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Icon(
                                        _getActivityIcon(activity),
                                        size: 20,
                                        color: color,
                                      ),
                                    ),
                                    const Spacer(),
                                    // Duration badge
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.white.withValues(alpha: 0.9),
                                            Colors.white.withValues(alpha: 0.7),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: color.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: color.withValues(alpha: 0.1),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.schedule_rounded,
                                            size: 14,
                                            color: color,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${activity.duration} min',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: color,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                // Title with better typography
                                Text(
                                  activity.title,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    height: 1.2,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                
                                const SizedBox(height: 8),
                                
                                // Description with improved styling
                                Expanded(
                                  child: Text(
                                    activity.description,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                      height: 1.4,
                                      fontSize: 13,
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                
                                const SizedBox(height: 16),

                                // Enhanced start button
                                Container(
                                  width: double.infinity,
                                  height: 42,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        color,
                                        color.withValues(alpha: 0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: () async {
                                        final activityTimerCubit =
                                            context.read<ActivityTimerCubit>();
                                        activityTimerCubit.startActivity(
                                          activity,
                                        );
                                        await context.push(
                                          AppRoutes.activityDetail,
                                          extra: activity,
                                        );
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(16),
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Colors.white.withValues(alpha: 0.2),
                                              Colors.transparent,
                                            ],
                                          ),
                                        ),
                                        child: Center(
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Container(
                                                padding: const EdgeInsets.all(2),
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: Colors.white.withValues(alpha: 0.2),
                                                ),
                                                child: Icon(
                                                  Icons.play_arrow_rounded,
                                                  color: Colors.white,
                                                  size: 18,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                'Start Activity',
                                                style: theme.textTheme.labelLarge
                                                    ?.copyWith(
                                                      color: Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      letterSpacing: 0.5,
                                                    ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Get icon for activity category
  IconData _getCategoryIcon(ActivityCategory category) {
    switch (category) {
      case ActivityCategory.mindfulness:
        return Icons.spa_rounded;
      case ActivityCategory.relaxation:
        return Icons.favorite_rounded;
      case ActivityCategory.learning:
        return Icons.lightbulb_rounded;
      case ActivityCategory.physical:
        return Icons.fitness_center_rounded;
      case ActivityCategory.creative:
        return Icons.palette_rounded;
      case ActivityCategory.social:
        return Icons.people_rounded;
      default:
        return Icons.apps_rounded;
    }
  }

  /// Get specific icon for individual activity based on title and category
  IconData _getActivityIcon(Activity activity) {
    // First check activity title for specific icons
    final title = activity.title.toLowerCase();

    if (title.contains('meditat')) return Icons.self_improvement;
    if (title.contains('breathe') || title.contains('breathing')) {
      return Icons.air;
    }
    if (title.contains('walk')) return Icons.directions_walk;
    if (title.contains('read')) return Icons.menu_book;
    if (title.contains('write') || title.contains('journal')) {
      return Icons.edit_note;
    }
    if (title.contains('music')) return Icons.music_note;
    if (title.contains('exercise') || title.contains('workout')) {
      return Icons.fitness_center;
    }
    if (title.contains('nature')) return Icons.park;
    if (title.contains('call') || title.contains('friend')) return Icons.phone;
    if (title.contains('art') || title.contains('draw')) return Icons.brush;
    if (title.contains('cook')) return Icons.restaurant;
    if (title.contains('clean')) return Icons.cleaning_services;
    if (title.contains('stretch')) return Icons.accessibility_new;
    if (title.contains('dance')) return Icons.music_video;
    if (title.contains('game')) return Icons.sports_esports;
    if (title.contains('learn')) return Icons.school;

    // Fall back to category icon
    return _getCategoryIcon(activity.category);
  }






}
