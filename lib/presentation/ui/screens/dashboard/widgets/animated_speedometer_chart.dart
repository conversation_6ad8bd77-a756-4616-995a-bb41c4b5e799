
import 'package:flutter/material.dart';
import 'package:speedometer_chart/speedometer_chart.dart';

/// Animated Speedometer Chart that animates from 0 to max and back to current value
class AnimatedSpeedometerChart extends StatefulWidget {
  /// The current value to display
  final double value;

  /// The maximum value for the gauge
  final double maxValue;

  /// The minimum value for the gauge
  final double minValue;

  /// The colors for the gauge gradient
  final List<Color> graphColor;

  /// The color of the pointer
  final Color pointerColor;

  /// The widget to display in the center
  final Widget valueWidget;

  /// The widget to display at the minimum value
  final Widget minWidget;

  /// The widget to display at the maximum value
  final Widget maxWidget;

  /// The title widget
  final Widget title;

  /// The margin for the title
  final double titleMargin;

  /// The size of the gauge
  final double dimension;

  /// Creates an [AnimatedSpeedometerChart]
  const AnimatedSpeedometerChart({
    super.key,
    required this.value,
    required this.maxValue,
    required this.minValue,
    required this.graphColor,
    required this.pointerColor,
    required this.valueWidget,
    required this.minWidget,
    required this.maxWidget,
    required this.title,
    required this.titleMargin,
    required this.dimension,
  });

  @override
  State<AnimatedSpeedometerChart> createState() => _AnimatedSpeedometerChartState();
}

class _AnimatedSpeedometerChartState extends State<AnimatedSpeedometerChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _displayValue = 0;

  // Track if this is the initial animation
  bool _isInitialAnimation = true;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // Create a curved animation that goes to max and then back to the actual value
    _animation = TweenSequence<double>([
      // First go to max value (40% of the animation time)
      TweenSequenceItem(
        tween: Tween<double>(begin: widget.minValue, end: widget.maxValue)
          .chain(CurveTween(curve: Curves.easeOut)),
        weight: 40,
      ),
      // Then go back to the actual value (60% of the animation time)
      TweenSequenceItem(
        tween: Tween<double>(begin: widget.maxValue, end: widget.value)
          .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 60,
      ),
    ]).animate(_controller);

    _animation.addListener(() {
      setState(() {
        _displayValue = _animation.value;
      });
    });

    // Start the animation only on initial load
    _displayValue = widget.value;
    if (_isInitialAnimation) {
      _controller.forward();
      _isInitialAnimation = false;
    }
  }

  @override
  void didUpdateWidget(AnimatedSpeedometerChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Only animate if the value has actually increased (meaning an activity was completed)
    if (widget.value > oldWidget.value) {
      // Check if this is a significant change (more than 1%)
      final percentChange =
          (widget.value - oldWidget.value).abs() / widget.maxValue;

      // Only animate if the change is significant
      if (percentChange > 0.01) {
        // Activity completed, animate the change
        // Update the animation with the new values
        _controller.reset();
        _animation = TweenSequence<double>([
          // First go to max value
          TweenSequenceItem(
            tween: Tween<double>(
              begin: oldWidget.value,
              end: widget.maxValue,
            ).chain(CurveTween(curve: Curves.easeOut)),
            weight: 40,
          ),
          // Then go back to the actual value
          TweenSequenceItem(
            tween: Tween<double>(
              begin: widget.maxValue,
              end: widget.value,
            ).chain(CurveTween(curve: Curves.easeInOut)),
            weight: 60,
          ),
        ]).animate(_controller);

        // Start the animation
        _controller.forward();
      } else {
        // For small changes, just update the value directly without animation
        setState(() {
          _displayValue = widget.value;
        });
      }
    } else if (widget.value != oldWidget.value) {
      // For any other changes (like decreases or periodic refreshes), just update the value without animation
      setState(() {
        _displayValue = widget.value;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Create a Text widget for the title
    Text titleWidget = const Text("");
    if (widget.title is Text) {
      titleWidget = widget.title as Text;
    }

    // Use the enhanced speedometer with custom styling
    return Stack(
      alignment: Alignment.center,
      children: [
        // Base SpeedometerChart from the package
        SpeedometerChart(
          dimension: widget.dimension,
          minValue: widget.minValue,
          maxValue: widget.maxValue,
          value: _displayValue,
          // Use the same gradient as in _buildEnhancedMotivationalCard
          graphColor: [
            theme.colorScheme.primary,
            theme.colorScheme.tertiary.withAlpha(179), // 0.7 alpha
          ],
          pointerColor: widget.pointerColor,
          valueWidget: widget.valueWidget,
          minWidget: widget.minWidget,
          maxWidget: widget.maxWidget,
          title: titleWidget,
          titleMargin: widget.titleMargin,
        ),

        // Add a subtle glow effect when value is high
        if (_displayValue / widget.maxValue > 0.7)
          Positioned(
            top: widget.dimension * 0.25,
            child: Container(
              width: widget.dimension * 0.5,
              height: widget.dimension * 0.25,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withAlpha(40),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
