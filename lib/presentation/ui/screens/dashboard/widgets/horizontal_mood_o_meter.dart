import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_cubit.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';

/// A widget that displays a horizontal mood-o-meter
/// and allows the user to select a mood by swiping left/right.
class HorizontalMoodOMeter extends StatefulWidget {
  /// The currently selected mood
  final MoodType? currentMood;

  /// Callback when a mood is selected
  final Function(MoodType)? onMoodSelected;

  /// Creates a HorizontalMoodOMeter widget
  const HorizontalMoodOMeter({
    super.key,
    this.currentMood,
    this.onMoodSelected,
  });

  @override
  State<HorizontalMoodOMeter> createState() => _HorizontalMoodOMeterState();
}

class _HorizontalMoodOMeterState extends State<HorizontalMoodOMeter> {
  // Page controller for the mood selector
  late PageController _pageController;

  // Current mood index
  int _currentMoodIndex = 0;

  // Ordered list of moods with neutral in the center, good moods on left, bad moods on right
  final List<MoodType> _orderedMoods = [
    // Good moods on the left (calm to excited)
    MoodType.calm,
    MoodType.inspired,
    MoodType.happy,
    MoodType.excited,
    // Neutral in the center
    MoodType.neutral,
    // Bad moods on the right (mild to severe)
    MoodType.tired,
    MoodType.sad,
    MoodType.anxious,
    MoodType.angry,
  ];

  @override
  void initState() {
    super.initState();

    // Initialize with the provided mood if available
    if (widget.currentMood != null) {
      _currentMoodIndex = _orderedMoods.indexOf(widget.currentMood!);
      if (_currentMoodIndex == -1) {
        // Fallback to neutral if the mood is not in our ordered list
        _currentMoodIndex =
            4; // Neutral is at index 4 (center) in our ordered list
      }
    } else {
      // Preselect Neutral mood by default if no mood is selected
      _currentMoodIndex =
          4; // Neutral is at index 4 (center) in our ordered list

      // Update the dashboard cubit with the default mood if no callback
      if (widget.onMoodSelected == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final dashboardCubit = context.read<DashboardCubit>();
          dashboardCubit.setCurrentMood(_orderedMoods[4]); // Neutral
        });
      }
    }

    // Initialize the page controller
    _pageController = PageController(
      initialPage: _currentMoodIndex,
      viewportFraction: 0.2, // Show 5 items at once
    );
  }

  @override
  void didUpdateWidget(HorizontalMoodOMeter oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the mood if it changed externally
    if (widget.currentMood != null &&
        widget.currentMood != oldWidget.currentMood) {
      final newIndex = _orderedMoods.indexOf(widget.currentMood!);
      if (newIndex != -1) {
        _selectMood(newIndex);
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // Track if we're currently processing a selection to prevent infinite loops
  bool _isProcessingMoodSelection = false;

  // Handle mood selection 
  void _selectMood(int index, {bool fromExternalUpdate = false}) {
    // Prevent selecting the same mood or invalid indices
    if (index == _currentMoodIndex ||
        index < 0 ||
        index >= _orderedMoods.length) {
      return;
    }

    // Prevent reentrant calls that could cause infinite loops
    if (_isProcessingMoodSelection) {
      return;
    }
    
    _isProcessingMoodSelection = true;

    setState(() {
      _currentMoodIndex = index;
    });

    // Animate to the selected page if the page controller is initialized
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    // Get the selected mood
    final selectedMood = _orderedMoods[index];

    // Only update cubits if this is a user-initiated change, not from an external update
    if (!fromExternalUpdate) {
      // Update the dashboard cubit to ensure global mood state
      final dashboardCubit = context.read<DashboardCubit>();
      dashboardCubit.setCurrentMood(selectedMood);
      
      // Update activity cubit for activity tab sync
      final activityCubit = context.read<ActivityCubit>();
      activityCubit.selectMood(selectedMood);
      
      // Update food mood cubit for food tab sync
      try {
        final foodMoodCubit = context.read<FoodMoodCubit>();
        foodMoodCubit.loadFoodRecommendationsForMood(
          selectedMood,
          sortOption: foodMoodCubit.state.sortOption,
          excludeTooManyDownvotes: foodMoodCubit.state.excludeTooManyDownvotes,
        );
      } catch (e) {
        // FoodMoodCubit might not be available in this context
        if (kDebugMode) {
          print('Could not access FoodMoodCubit: $e');
        }
      }
      
      // Update mood quote cubit for motivational quotes sync
      try {
        final moodQuoteCubit = context.read<MoodQuoteCubit>();
        final localeProvider = context.read<AppLocaleProvider>();
        final currentLanguage = localeProvider.locale.languageCode;
        
        moodQuoteCubit.onMoodChanged(
          newMood: selectedMood,
          language: currentLanguage,
          limit: 5,
        );
      } catch (e) {
        // MoodQuoteCubit might not be available in this context
        if (kDebugMode) {
          print('Could not access MoodQuoteCubit: $e');
        }
      }
    }

    // Also notify callback if provided (for local handling)
    if (widget.onMoodSelected != null) {
      widget.onMoodSelected!(selectedMood);
    }
    
    _isProcessingMoodSelection = false;
  }

  // Handle swipe to change mood
  void _handleHorizontalDrag(DragEndDetails details) {
    if (details.primaryVelocity == 0) return;

    final int newIndex;
    if (details.primaryVelocity! < 0) {
      // Swipe left - increase mood index (move toward angry)
      newIndex = math.min(_currentMoodIndex + 1, _orderedMoods.length - 1);
    } else {
      // Swipe right - decrease mood index (move toward happy)
      newIndex = math.max(_currentMoodIndex - 1, 0);
    }

    _selectMood(newIndex);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // If using dashboard cubit (when onMoodSelected is not provided)
    if (widget.onMoodSelected == null) {
      return BlocListener<DashboardCubit, DashboardState>(
        listenWhen:
            (previous, current) => previous.currentMood != current.currentMood,
        listener: (context, state) {
          // When the mood changes in the dashboard cubit, update the mood-o-meter
          if (state.currentMood != null) {
            final index = _orderedMoods.indexOf(state.currentMood!);
            if (index != -1 && index != _currentMoodIndex) {
              // Pass fromExternalUpdate=true to avoid the infinite update loop
              _selectMood(index, fromExternalUpdate: true);
            }
          }
        },
        child: _buildMoodOMeterContent(theme, l10n),
      );
    }

    // If using callback (when onMoodSelected is provided)
    return _buildMoodOMeterContent(theme, l10n);
  }

  Widget _buildMoodOMeterContent(ThemeData theme, AppLocalizations l10n) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),

        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.2,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and current mood
            Row(
              children: [
                // Title and description
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        l10n.moodOMeterInstructions,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
          
              ],
            ),
      
            const SizedBox(height: 16),

            // Horizontal mood selector with fixed center
            GestureDetector(
              onHorizontalDragEnd: _handleHorizontalDrag,
              child: SizedBox(
                height: 80,
                child: Stack(
                  children: [
                    // Gradient line in the background
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          height: 4,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors:
                                  _orderedMoods
                                      .map((mood) => mood.color)
                                      .toList(),
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),

                    // Centered PageView for moods
                    Center(
                      child: SizedBox(
                        height: 80,
                        width: MediaQuery.of(context).size.width,
                        child: PageView.builder(
                          controller: _pageController,
                          onPageChanged: (index) {
                            // Only trigger mood selection if not already processing a selection
                            if (!_isProcessingMoodSelection) {
                              _selectMood(index);
                            }
                          },
                          itemCount: _orderedMoods.length,
                          itemBuilder: (context, index) {
                            final mood = _orderedMoods[index];
                            final isSelected = index == _currentMoodIndex;

                            return Center(
                              child: GestureDetector(
                                onTap: () => _selectMood(index),
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  width: isSelected ? 70 : 45,
                                  height: isSelected ? 70 : 45,
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected
                                            ? mood.color.withValues(alpha: 0.2)
                                            : theme.colorScheme.surface,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color:
                                          isSelected
                                              ? mood.color
                                              : mood.color.withValues(
                                                alpha: 0.3,
                                              ),
                                      width: isSelected ? 3 : 1,
                                    ),
                                    boxShadow:
                                        isSelected
                                            ? [
                                              BoxShadow(
                                                color: mood.color.withValues(
                                                  alpha: 0.3,
                                                ),
                                                blurRadius: 6,
                                                spreadRadius: 2,
                                              ),
                                            ]
                                            : null,
                                  ),
                                  child: Center(
                                    child: Text(
                                      mood.emoji,
                                      style: TextStyle(
                                        fontSize: isSelected ? 36 : 24,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
      
            // Mood spectrum labels
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _orderedMoods.first.localizedDescription(context),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _orderedMoods.first.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    _orderedMoods[_currentMoodIndex].localizedDescription(
                      context,
                    ),
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    _orderedMoods.last.localizedDescription(context),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _orderedMoods.last.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
