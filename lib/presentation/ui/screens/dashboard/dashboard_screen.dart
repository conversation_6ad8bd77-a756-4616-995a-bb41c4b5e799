import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/core/services/wellbeing_assistant_service.dart';
import 'package:detoxme/domain/usecases/get_current_user_usecase.dart';
import 'package:detoxme/presentation/bloc/activity/activity_timer_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_bloc.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_event.dart';
import 'package:detoxme/domain/repositories/notification_repository.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/dashboard_view.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/activity/activity_state.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';
import 'package:detoxme/presentation/bloc/notification/notification_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;

/// Dashboard screen showing detox progress, weather, and motivational messages
class DashboardScreen extends StatelessWidget {
  /// Creates a [DashboardScreen]
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get repositories from service locator
    final notificationRepository = serviceLocator<NotificationRepository>();

    // Provide BlocProviders
    return MultiBlocProvider(
      providers: [
        BlocProvider<RewardCubit>(
          create: (_) => serviceLocator<RewardCubit>()..loadPlayerStats(),
        ),
        BlocProvider<DashboardCubit>(
          create: (_) => serviceLocator<DashboardCubit>()..loadDashboard(),
        ),
        BlocProvider<ProfileCubit>(
          create: (_) => serviceLocator<ProfileCubit>(),
        ),
        // Add activity timer cubit for wellbeing activities
        BlocProvider<ActivityTimerCubit>(
          create: (_) => serviceLocator<ActivityTimerCubit>(),
        ),
        // Add wellbeing assistant bloc
        BlocProvider<WellbeingAssistantBloc>(
          create: (context) {
            return WellbeingAssistantBloc(
              wellbeingService: serviceLocator<WellbeingAssistantService>(),
              getCurrentUserUseCase: serviceLocator<GetCurrentUserUseCase>(),
              activityTimerCubit: context.read<ActivityTimerCubit>(),
            );
          },
        ),
        // Add notification cubit
        BlocProvider<NotificationCubit>(
          create:
              (_) => NotificationCubit(
                notificationRepository: notificationRepository,
              ),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          // Initialize notifications when user is authenticated
          BlocListener<AuthCubit, auth_state.AuthState>(
            listener: (context, state) {
              if (state is auth_state.Authenticated) {
                final notificationCubit = context.read<NotificationCubit>();
                notificationCubit.initializeNotifications(state.user.id);
                
                // Initialize wellbeing assistant with current language
                final currentLanguage = Localizations.localeOf(context).languageCode;
                final wellbeingBloc = context.read<WellbeingAssistantBloc>();
                wellbeingBloc.add(WellbeingAssistantStarted(language: currentLanguage));
              }
            },
          ),
          // Ensure dashboard cubit stays in sync with activity cubit
          BlocListener<ActivityCubit, ActivityState>(
            listenWhen:
                (previous, current) =>
                    previous.selectedMood != current.selectedMood,
            listener: (context, state) {
              if (state.selectedMood != null) {
                final dashboardCubit = context.read<DashboardCubit>();
                if (dashboardCubit.getCurrentMood() != state.selectedMood) {
                  dashboardCubit.setCurrentMood(state.selectedMood!);
                }
              }
            },
          ),
          // Ensure dashboard cubit stays in sync with food mood cubit
          BlocListener<FoodMoodCubit, FoodMoodState>(
            listenWhen:
                (previous, current) =>
                    previous.selectedMood != current.selectedMood,
            listener: (context, state) {
              if (state.selectedMood != null) {
                final dashboardCubit = context.read<DashboardCubit>();
                if (dashboardCubit.getCurrentMood() != state.selectedMood) {
                  dashboardCubit.setCurrentMood(state.selectedMood!);
                }
              }
            },
          ),
        ],
        child: const DashboardView(),
      ),
    );
  }
}
