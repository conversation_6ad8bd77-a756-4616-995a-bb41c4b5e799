import 'package:detoxme/core/themes/app_themes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/widgets/base_scaffold.dart';
import '../../../../domain/entities/activity.dart';
import '../../../../domain/entities/mood.dart';
import '../../../../localization/app_localizations.dart';
import '../../../bloc/activity/activity_cubit.dart';
import '../../../bloc/auth/auth_cubit.dart';

/// Screen for creating a new activity
class CreateActivityScreen extends StatefulWidget {
  /// Creates a new CreateActivityScreen
  const CreateActivityScreen({super.key});

  @override
  State<CreateActivityScreen> createState() => _CreateActivityScreenState();
}

class _CreateActivityScreenState extends State<CreateActivityScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _durationController = TextEditingController();
  final _healthBenefitsController = TextEditingController();

  int _durationValue = 15;
  ActivityCategory _selectedCategory = ActivityCategory.mindfulness;
  final Set<MoodType> _selectedMoods = {};
  bool _isPrivate = false;
  bool _showCreatorName = true;
  bool _isLoading = false;
  String? _errorMessage;
  String _selectedCountryCode = 'en';
  final List<String> _selectedHealthBenefits = [];

  // Map of country codes to display names and emoji flags
  final Map<String, Map<String, String>> _countryOptions = {
    'en': {'name': 'English', 'flag': '🇬🇧'},
    'de': {'name': 'German', 'flag': '🇩🇪'},
    'ru': {'name': 'Russian', 'flag': '🇷🇺'},
    'tr': {'name': 'Turkish', 'flag': '🇹🇷'},
    'ar': {'name': 'Arabic', 'flag': '🇸🇦'},
    // 'es': {'name': 'Spanish', 'flag': '🇪🇸'},
    // 'fr': {'name': 'French', 'flag': '🇫🇷'},
    // 'it': {'name': 'Italian', 'flag': '🇮🇹'},
    //  'zh': {'name': 'Chinese', 'flag': '🇨🇳'},
    //   'hi': {'name': 'Hindi', 'flag': '🇮🇳'},
  };

  // Predefined health benefits options - will be populated in initState
  List<String> _predefinedHealthBenefits = [];

  @override
  void initState() {
    super.initState();
    // Add listeners to controllers to update the form validation state
    _titleController.addListener(_updateFormState);
    _descriptionController.addListener(_updateFormState);
    _healthBenefitsController.addListener(_updateFormState);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initialize predefined health benefits with localized strings
    final l10n = AppLocalizations.of(context)!;
    _predefinedHealthBenefits = [
      l10n.healthBenefitStress,
      l10n.healthBenefitCardio,
      l10n.healthBenefitClarity,
      l10n.healthBenefitImmune,
      l10n.healthBenefitSleep,
      l10n.healthBenefitStrength,
      l10n.healthBenefitMindful,
      l10n.healthBenefitDigital,
      l10n.healthBenefitFocus,
      l10n.healthBenefitEmotional,
      l10n.healthBenefitEnergy,
      l10n.healthBenefitCreativity,
      l10n.healthBenefitPosture,
      l10n.healthBenefitWeight,
    ];
  }

  @override
  void dispose() {
    _titleController.removeListener(_updateFormState);
    _descriptionController.removeListener(_updateFormState);
    _healthBenefitsController.removeListener(_updateFormState);

    _titleController.dispose();
    _descriptionController.dispose();
    _durationController.dispose();
    _healthBenefitsController.dispose();
    super.dispose();
  }

  void _updateFormState() {
    // Just trigger a rebuild to update validation status
    setState(() {});
  }

  bool get _isFormValid {
    // Check if all required fields are valid
    final isTitleValid = _titleController.text.trim().isNotEmpty;
    final isDescriptionValid = _descriptionController.text.trim().isNotEmpty;
    final isHealthBenefitsValid =
        _selectedHealthBenefits.length >=
        2; // Changed to check for at least 2 health benefits
    final areMoodsValid =
        _selectedMoods.isNotEmpty; // Changed to require at least 1 mood

    return isTitleValid &&
        isDescriptionValid &&
        isHealthBenefitsValid &&
        areMoodsValid;
  }

  Future<void> _createActivity() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedMoods.isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.of(context)!.moodSelectionError;
      });
      return;
    }

    if (_selectedHealthBenefits.length < 2) {
      setState(() {
        _errorMessage =
            AppLocalizations.of(context)!.healthBenefitsSelectionError;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final activityCubit = context.read<ActivityCubit>();
    final authCubit = context.read<AuthCubit>();
    final userId = authCubit.getCurrentUserId();
    final displayName = "User"; // Simplified for now

    if (userId == null) {
      setState(() {
        _isLoading = false;
        _errorMessage = AppLocalizations.of(context)!.loginRequiredError;
      });
      return;
    }

    try {
      final result = await activityCubit.createActivity(
        userId: userId,
        title: _titleController.text,
        description: _descriptionController.text,
        category: _selectedCategory,
        duration: _durationValue,
        recommendedFor: _selectedMoods.toList(),
        healthBenefits: _selectedHealthBenefits.toList(),
        isPrivate: _isPrivate,
        showCreatorName: _showCreatorName,
        creatorName: _showCreatorName ? displayName : null,
        countryCode: _selectedCountryCode,
      );

      if (mounted) {
        result.fold(
          (failure) {
            setState(() {
              _isLoading = false;
              _errorMessage = failure.message;
            });
          },
          (activity) {
            // Show snackbar at the top with better design
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 12),
                    Text(AppLocalizations.of(context)!.activityCreatedSuccess),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.only(
                  bottom: MediaQuery.of(context).size.height - 100,
                  left: 16,
                  right: 16,
                ),
                duration: const Duration(seconds: 2),
              ),
            );

            // Navigate back to activities screen
            Navigator.of(context).pop();
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = AppLocalizations.of(
            context,
          )!.createActivityError(e.toString());
        });
      }
    }
  }

  void _showHealthBenefitsDialog() {
    final TextEditingController newBenefitController = TextEditingController();
    final Set<String> tempSelectedBenefits = Set.from(_selectedHealthBenefits);
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(l10n.healthBenefitsDialogTitle),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Add new benefit
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: newBenefitController,
                            decoration: InputDecoration(
                              hintText: l10n.addNewHealthBenefitHint,
                              isDense: true,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.add_circle, color: Colors.green),
                          onPressed: () {
                            final newBenefit = newBenefitController.text.trim();
                            if (newBenefit.isNotEmpty) {
                              setState(() {
                                tempSelectedBenefits.add(newBenefit);
                                newBenefitController.clear();
                              });
                            }
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Selected benefits
                    if (tempSelectedBenefits.isNotEmpty) ...[
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          l10n.selectedBenefitsLabel,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        constraints: BoxConstraints(maxHeight: 100),
                        child: SingleChildScrollView(
                          child: Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children:
                                tempSelectedBenefits.map((benefit) {
                                  return Chip(
                                    label: Text(
                                      benefit,
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    deleteIcon: Icon(Icons.clear, size: 16),
                                    onDeleted: () {
                                      setState(() {
                                        tempSelectedBenefits.remove(benefit);
                                      });
                                    },
                                  );
                                }).toList(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Predefined benefits
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        l10n.suggestedBenefitsLabel,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children:
                              _predefinedHealthBenefits.map((benefit) {
                                final isSelected = tempSelectedBenefits
                                    .contains(benefit);
                                return FilterChip(
                                  label: Text(
                                    benefit,
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() {
                                      if (selected) {
                                        tempSelectedBenefits.add(benefit);
                                      } else {
                                        tempSelectedBenefits.remove(benefit);
                                      }
                                    });
                                  },
                                );
                              }).toList(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(l10n.cancelButton),
                ),
                TextButton(
                  onPressed: () {
                    _selectedHealthBenefits.clear();
                    _selectedHealthBenefits.addAll(tempSelectedBenefits);
                    _healthBenefitsController.text = _selectedHealthBenefits
                        .join('\n• ');
                    if (_selectedHealthBenefits.isNotEmpty) {
                      _healthBenefitsController.text =
                          '• ${_healthBenefitsController.text}';
                    }
                    _updateFormState();
                    Navigator.of(context).pop();
                  },
                  child: Text(l10n.applyButton),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BaseScaffold(
      title: l10n.createActivityTitle,
      showBottomNav: false,
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Scaffold(
                body: Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.all(8),
                    children: [
                      // Title
                      TextFormField(
                        controller: _titleController,
                        decoration: InputDecoration(
                          labelText: l10n.titleLabel,
                          hintText: l10n.titleHint,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n.titleRequired;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: l10n.descriptionLabel,
                          hintText: l10n.descriptionHint,
                        ),
                        maxLines: 4,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n.descriptionRequired;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Duration Slider
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.durationLabel(_durationValue),
                            style: theme.textTheme.titleSmall,
                          ),
                          const SizedBox(height: 8),
                          Slider(
                            value: _durationValue.toDouble(),
                            min: 5,
                            max: 120,
                            divisions: 23,
                            label: l10n.durationLabel(_durationValue),
                            onChanged: (value) {
                              setState(() {
                                _durationValue = value.round();
                                _durationValue =
                                    ((_durationValue / 5).round() * 5);
                                _durationController.text =
                                    _durationValue.toString();
                              });
                            },
                          ),

                          // Show some duration presets
                          Wrap(
                            spacing: 8,
                            children:
                                [5, 15, 30, 60, 90, 120].map((minutes) {
                                  return ActionChip(
                                    label: Text(l10n.durationMinutes(minutes)),
                                    backgroundColor:
                                        _durationValue == minutes
                                            ? theme.colorScheme.primaryContainer
                                            : null,
                                    side: BorderSide(
                                      color: theme.colorScheme.outline,
                                      width: 0.2,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _durationValue = minutes;
                                        _durationController.text =
                                            minutes.toString();
                                      });
                                    },
                                  );
                                }).toList(),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Health Benefits
                      TextFormField(
                        controller: _healthBenefitsController,
                        decoration: InputDecoration(
                          labelText: l10n.healthBenefitsLabel,
                          hintText: l10n.healthBenefitsHint,
                          suffixIcon: IconButton(
                            icon: Icon(Icons.list_alt),
                            onPressed: _showHealthBenefitsDialog,
                            tooltip: l10n.healthBenefitsLabel,
                          ),
                        ),
                        maxLines: 3,
                        readOnly: true,
                        onTap: _showHealthBenefitsDialog,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n.healthBenefitsRequired;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Category
                      Text(
                        l10n.categoryLabel,
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<ActivityCategory>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                        items:
                            ActivityCategory.values.map((category) {
                              return DropdownMenuItem(
                                value: category,
                                child: Row(
                                  children: [
                                    Icon(category.icon, color: AppColors.primary),
                                    const SizedBox(width: 12),
                                    Text(category.displayName),
                                  ],
                                ),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 24),

                      // Language selection
                      Text(
                        l10n.languageLabel,
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _selectedCountryCode,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: l10n.selectLanguageHint,
                        ),
                        items:
                            _countryOptions.entries.map((entry) {
                              return DropdownMenuItem(
                                value: entry.key,
                                child: Row(
                                  children: [
                                    Text(
                                      entry.value['flag'] ?? '',
                                      style: const TextStyle(fontSize: 20),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(entry.value['name'] ?? ''),
                                  ],
                                ),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedCountryCode = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 24),

                      // Recommended for moods
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                l10n.recommendedForMoodsLabel,
                                style: theme.textTheme.titleMedium,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                l10n.selectAtLeastOneMood,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color:
                                      _selectedMoods.isEmpty
                                          ? theme.colorScheme.error
                                          : theme.colorScheme.onSurface
                                              .withAlpha(180),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children:
                                MoodType.values.map((mood) {
                                  final isSelected = _selectedMoods.contains(
                                    mood,
                                  );
                                  return FilterChip(
                                    label: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(mood.emoji),
                                        const SizedBox(width: 4),
                                        Text(
                                          mood.localizedDescription(context),
                                        ),
                                      ],
                                    ),
                                    selected: isSelected,
                                    side: BorderSide(
                                      color: theme.colorScheme.outline,
                                      width: 0.2,
                                    ),
                                    onSelected: (selected) {
                                      setState(() {
                                        if (selected) {
                                          _selectedMoods.add(mood);
                                        } else {
                                          _selectedMoods.remove(mood);
                                        }
                                        _errorMessage = null;
                                      });
                                    },
                                    backgroundColor:
                                        theme
                                            .colorScheme
                                            .surfaceContainerHighest,
                                    selectedColor:
                                        isSelected
                                            ? mood.color
                                            : mood.color.withAlpha(30),
                                    checkmarkColor: theme.colorScheme.onPrimary,
                                  );
                                }).toList(),
                          ),
                        ],
                      ),
                      if (_errorMessage != null && _selectedMoods.isEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: theme.colorScheme.error),
                          ),
                        ),
                      const SizedBox(height: 24),

                      // Privacy options
                      Text(
                        l10n.privacySettingsLabel,
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: Text(l10n.makeActivityPrivateLabel),
                        subtitle: Text(l10n.privateActivityDescription),
                        value: _isPrivate,
                        onChanged: (value) {
                          setState(() {
                            _isPrivate = value;
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text(l10n.showYourNameLabel),
                        subtitle: Text(l10n.showNameDescription),
                        value: _showCreatorName,
                        onChanged: (value) {
                          setState(() {
                            _showCreatorName = value;
                          });
                        },
                      ),
                      const SizedBox(height: 24),

                      // Error message
                      if (_errorMessage != null && _selectedMoods.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: theme.colorScheme.error),
                          ),
                        ),

                      // Remove regular submit button
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
                floatingActionButton: FloatingActionButton.extended(
                  onPressed: _isFormValid ? _createActivity : null,
                  backgroundColor:
                      _isFormValid
                          ? theme.colorScheme.primary
                          : theme.colorScheme.surfaceContainerHighest,
                  foregroundColor:
                      _isFormValid
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurfaceVariant.withValues(
                            alpha: 0.6,
                          ),
                  icon: Icon(Icons.check),
                  label: Text(l10n.createActivityButton),
                  elevation: 2,
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerFloat,
              ),
    );
  }
}
