import 'package:detoxme/core/themes/app_themes.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:animate_do/animate_do.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:intl/intl.dart';
import 'package:detoxme/localization/app_localizations.dart';

/// Screen for displaying the user's reward history
class RewardHistoryScreen extends StatelessWidget {
  /// Creates a [RewardHistoryScreen]
  const RewardHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text(l10n.rewardHistoryScreenTitle), elevation: 0),
      body: BlocBuilder<RewardCubit, RewardState>(
        builder: (context, state) {
          if (state is RewardLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is RewardError) {
            return Center(
              child: Text(
                'Error: ${state.failure.message}',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            );
          } else if (state is RewardLoaded) {
            final redeemedRewards = state.redeemedRewards;

            if (redeemedRewards.isEmpty) {
              return _buildEmptyState(context, theme, l10n);
            }

            return _buildRewardTimeline(context, theme, redeemedRewards);
          }

          // Initial state or fallback
          return Center(
            child: ElevatedButton(
              onPressed: () {
                context.read<RewardCubit>().loadRedemptionHistory();
              },
              child: Text(l10n.loadRedemptionsButton),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRewardTimeline(
    BuildContext context,
    ThemeData theme,
    List<Reward> rewards,
  ) {
    // Sort rewards by redemption date, newest first
    final sortedRewards = List<Reward>.from(rewards);
    // Sort logic would go here if we had redemption dates

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Timeline.tileBuilder(
        theme: TimelineThemeData(
          nodePosition: 0.1,
          indicatorTheme: IndicatorThemeData(
            color: theme.colorScheme.primary,
            size: 20.0,
          ),
          connectorTheme: ConnectorThemeData(
            color: theme.colorScheme.primary.withAlpha(100),
            thickness: 2.0,
          ),
        ),
        builder: TimelineTileBuilder.connected(
          connectionDirection: ConnectionDirection.before,
          itemCount: sortedRewards.length,
          contentsBuilder: (_, index) {
            final reward = sortedRewards[index];
            return FadeInRight(
              delay: Duration(milliseconds: index * 50),
              child: _buildTimelineItem(context, theme, reward, index),
            );
          },
          indicatorBuilder: (_, index) {
            return DotIndicator(
              color: theme.colorScheme.primary,
              child: Icon(
                Icons.check,
                color: theme.colorScheme.onPrimary,
                size: 12.0,
              ),
            );
          },
          connectorBuilder: (_, index, connectorType) {
            return SolidLineConnector(
              color: theme.colorScheme.primary.withAlpha(100),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTimelineItem(
    BuildContext context,
    ThemeData theme,
    Reward reward,
    int index,
  ) {
    // Format date - use reward's redeemedAt or a default date
    final date =
        reward.createdAt;
    final dateFormatted = DateFormat.yMMMd().format(date);

    // Get reward category
    RewardCategory category = RewardCategory.other;
    if (reward.metadata != null && reward.metadata!.containsKey('category')) {
      String? categoryStr = reward.metadata!['category'] as String?;
      if (categoryStr != null) {
        try {
          category = RewardCategory.values.firstWhere(
            (cat) => cat.name == categoryStr.toLowerCase(),
            orElse: () => RewardCategory.other,
          );
        } catch (_) {
          // Default to 'other' if we can't parse the category
          category = RewardCategory.other;
        }
      }
    }

    // Get points cost
    final pointsCost = reward.pointsEarned;

    // Get provider info
    final provider = reward.metadata?['provider'] as String? ?? 'DetoxMe';

    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 0, 24.0),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: theme.colorScheme.outline.withAlpha(40)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Category icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        category.icon,
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Reward title and date
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          reward.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          dateFormatted,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Points cost
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '$pointsCost pts',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
              if (reward.description != null &&
                  reward.description!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  reward.description!,
                  style: theme.textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 8),
              // Provider
              Row(
                children: [
                  Text(
                    'Provider:',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(150),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    provider,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.card_giftcard,
              size: 80,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 24),
            Text(
              l10n.rewardHistoryEmptyTitle,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.rewardHistoryEmptyMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(180),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop(); // Go back to previous screen
              },
              icon: const Icon(Icons.arrow_back),
              label: Text(l10n.goBackButton),
            ),
          ],
        ),
      ),
    );
  }
}

/// Wrapper that provides necessary BlocProviders for the RewardHistoryScreen
class RewardHistoryScreenWrapper extends StatelessWidget {
  /// Creates a [RewardHistoryScreenWrapper]
  const RewardHistoryScreenWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // RewardCubit is assumed to be already provided at a higher level
    return const RewardHistoryScreen();
  }
}
