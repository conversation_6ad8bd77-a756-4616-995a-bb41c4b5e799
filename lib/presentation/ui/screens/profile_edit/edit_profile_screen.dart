import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/data/local/hive_sync_queue_data_source.dart';
import 'package:detoxme/data/local/local_profile_data_source.dart';
import 'package:detoxme/data/repositories/profile_repository_impl.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_state.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// BlocProvider Wrapper for the Edit Profile Screen
class EditProfileScreenWrapper extends StatelessWidget {
  const EditProfileScreenWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    final supabaseClient = Supabase.instance.client;
    final connectivity = Connectivity();
    final networkInfo = NetworkInfoImpl(connectivity);
    final LocalSyncQueueRepository localSyncQueueRepository =
        HiveSyncQueueDataSource();
    final localDataSource = LocalProfileDataSourceImpl();
    final repository = ProfileRepositoryImpl(
      localDataSource: localDataSource,
      supabaseClient: supabaseClient,
      networkInfo: networkInfo,
      localSyncQueueRepository: localSyncQueueRepository,
    );

    return BlocProvider(
      create:
          (context) =>
              ProfileCubit(profileRepository: repository)..loadProfile(),
      child: const EditProfileScreen(),
    );
  }
}

/// Screen for editing user profile information
class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late final TextEditingController _usernameController;
  late final TextEditingController _bioController;

  // Selected values
  String? _selectedGender;
  int? _selectedBirthYear;

  // State variables for expandable sections
  bool _isLifeSituationExpanded = false;
  bool _isGoalsExpanded = false;

  // Selected values for life situation and goals
  String? _selectedLifeSituation;
  Set<String> _selectedGoals = <String>{};

  // Save state tracking
  bool _isSaving = false;

  final List<String> _lifeStatusOptions = [
    'Single',
    'In a relationship',
    'Have children',
    'Prefer not to say',
  ];
  final List<String> _goalOptions = [
    'Reduce screen time',
    'Improve focus & concentration',
    'Better family time',
    'Improve sleep',
    'Manage digital addiction',
  ];

  // Gender options for profile
  final List<String> _genderOptions = [
    'Male',
    'Female',
    'Non-binary',
    'Prefer not to say',
  ];

  // Birth year options - from 18 to 100 years old
  final List<int> _birthYearOptions = List.generate(
    83,
    (index) => DateTime.now().year - index - 18,
  );

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController();
    _bioController = TextEditingController();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  // Get user email or phone from auth state
  String? _getUserContact() {
    try {
      final authCubit = context.read<AuthCubit>();
      return authCubit.getCurrentUserEmail() ?? authCubit.getCurrentUserPhone();
    } catch (e) {
      return null;
    }
  }

  void _initializeFormWithProfile(UserProfile profile) {
    _usernameController.text = profile.username ?? '';
    _bioController.text = profile.bio ?? '';
    _selectedBirthYear = profile.birthYear;
    _selectedGender = profile.gender;
    _selectedLifeSituation = profile.lifeSituation;
    _selectedGoals = profile.goals.toSet();
  }

  Future<void> _saveProfile() async {
    if (_usernameController.text.isEmpty) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final cubit = context.read<ProfileCubit>();

      await cubit.updateProfile(
        username: _usernameController.text,
        bio: _bioController.text.isEmpty ? null : _bioController.text,
        birthYear: _selectedBirthYear,
        gender: _selectedGender,
        lifeSituation: _selectedLifeSituation,
        goals: _selectedGoals.toList(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaveSuccess),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save profile: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return BaseScaffold.withTitle(
      title: l10n.profileEditTitle,
      showBottomNav: false,
      showBackButton: true,
      body: SafeArea(
        child: BlocConsumer<ProfileCubit, ProfileState>(
          // Listener for side effects (Snackbars)
          listener: (context, state) {
            if (state is ProfileError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: theme.colorScheme.error,
                ),
              );
            }
          },
          // Builder for the main UI that depends on the state
          builder: (context, state) {
            UserProfile currentProfile;
            bool isLoading = state is ProfileLoading || state is ProfileInitial;

            // Display an error UI if we have a load failure
            if (state is ProfileError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: theme.colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load profile',
                      style: theme.textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: theme.textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              );
            }

            // Initialize currentProfile based on state
            if (state is ProfileLoaded) {
              currentProfile = state.profile;
              // Initialize form with profile data only if not already done
              if (_usernameController.text.isEmpty) {
                _initializeFormWithProfile(currentProfile);
              }
            } else {
              // If not loaded, create an empty profile. Need user ID.
              final authCubit = context.read<AuthCubit>();
              final userId = authCubit.getCurrentUserId() ?? '';
              currentProfile = UserProfile.empty(userId);
            }

            // Get user contact info
            final contactInfo = _getUserContact();

            return SingleChildScrollView(
              padding: const EdgeInsets.all(8.0),
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Username
                  FadeInUp(
                    delay: const Duration(milliseconds: 200),
                    duration: const Duration(milliseconds: 400),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Username', style: theme.textTheme.titleMedium),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _usernameController,
                          decoration: InputDecoration(
                            hintText: 'Enter username',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            prefixIcon: const Icon(Icons.person_outline),
                          ),
                          readOnly: _isSaving,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Birth Information
                  FadeInUp(
                    delay: const Duration(milliseconds: 250),
                    duration: const Duration(milliseconds: 400),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Birth Year', style: theme.textTheme.titleMedium),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<int>(
                          value: _selectedBirthYear,
                          decoration: InputDecoration(
                            hintText: 'Select birth year',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            prefixIcon: const Icon(Icons.cake_outlined),
                          ),
                          items:
                              _birthYearOptions.map((year) {
                                return DropdownMenuItem<int>(
                                  value: year,
                                  child: Text(year.toString()),
                                );
                              }).toList(),
                          onChanged:
                              _isSaving
                                  ? null
                                  : (value) {
                                    setState(() {
                                      _selectedBirthYear = value;
                                    });
                                  },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Gender
                  FadeInUp(
                    delay: const Duration(milliseconds: 300),
                    duration: const Duration(milliseconds: 400),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Gender', style: theme.textTheme.titleMedium),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children:
                              _genderOptions.map((gender) {
                                final isSelected = _selectedGender == gender;
                                return InkWell(
                                  onTap:
                                      _isSaving
                                          ? null
                                          : () {
                                            setState(() {
                                              _selectedGender = gender;
                                            });
                                          },
                                  borderRadius: BorderRadius.circular(20),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isSelected
                                              ? theme
                                                  .colorScheme
                                                  .primaryContainer
                                              : theme
                                                  .colorScheme
                                                  .surfaceContainerHighest,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color:
                                            isSelected
                                                ? theme.colorScheme.primary
                                                : theme.colorScheme.outline,
                                        width: 0.2,
                                      ),
                                    ),
                                    child: Text(
                                      gender,
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            color:
                                                isSelected
                                                    ? Colors.white
                                                    : theme
                                                        .colorScheme
                                                        .onSurfaceVariant,
                                            fontWeight:
                                                isSelected
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                          ),
                                    ),
                                  ),
                                );
                              }).toList(),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // User contact info (email/phone)
                  if (contactInfo != null)
                    FadeInUp(
                      delay: const Duration(milliseconds: 350),
                      duration: const Duration(milliseconds: 400),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 12,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.outline.withAlpha(51),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              contactInfo.contains('@')
                                  ? Icons.email_outlined
                                  : Icons.phone_outlined,
                              size: 20,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                contactInfo,
                                style: theme.textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  const SizedBox(height: 24),

                  // Bio
                  FadeInUp(
                    delay: const Duration(milliseconds: 400),
                    duration: const Duration(milliseconds: 400),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Bio', style: theme.textTheme.titleMedium),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _bioController,
                          decoration: InputDecoration(
                            hintText: 'Tell us about yourself...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            prefixIcon: const Icon(Icons.description_outlined),
                          ),
                          maxLines: 3,
                          readOnly: _isSaving,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Life Situation (collapsible)
                  FadeInUp(
                    delay: const Duration(milliseconds: 500),
                    duration: const Duration(milliseconds: 400),
                    child: _buildLifeSituationSelector(theme, _isSaving),
                  ),
                  const SizedBox(height: 24),

                  // Goals (collapsible)
                  FadeInUp(
                    delay: const Duration(milliseconds: 600),
                    duration: const Duration(milliseconds: 400),
                    child: _buildGoalsSelector(theme, _isSaving),
                  ),
                  const SizedBox(height: 24),

                  // Save button
                  Center(
                    child: FadeInUp(
                      delay: const Duration(milliseconds: 800),
                      duration: const Duration(milliseconds: 400),
                      child: ElevatedButton.icon(
                        onPressed:
                            (_isSaving ||
                                    isLoading ||
                                    (_usernameController.text.isEmpty))
                                ? null
                                : _saveProfile,
                        icon:
                            _isSaving
                                ? Container(
                                  width: 24,
                                  height: 24,
                                  padding: const EdgeInsets.all(2.0),
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 3,
                                  ),
                                )
                                : const Icon(Icons.save_outlined),
                        label: Text(l10n.profileSaveButton),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 28,
                            vertical: 18,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40), // Extra space at bottom
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Life situation selector (collapsible)
  Widget _buildLifeSituationSelector(
    ThemeData theme,
    bool isSaving,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap:
              isSaving
                  ? null
                  : () {
                    setState(() {
                      _isLifeSituationExpanded = !_isLifeSituationExpanded;
                    });
                  },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Life Situation', style: theme.textTheme.titleMedium),
                Icon(
                  _isLifeSituationExpanded
                      ? Icons.expand_less
                      : Icons.expand_more,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Selected item or expanded selection
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              _isLifeSituationExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
          firstChild: _buildCollapsedLifeSituation(theme),
          secondChild: _buildExpandedLifeSituation(theme, isSaving),
        ),
      ],
    );
  }

  Widget _buildCollapsedLifeSituation(ThemeData theme) {
    if (_selectedLifeSituation != null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 18,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              _selectedLifeSituation!,
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.outline.withAlpha(77)),
        ),
        child: Text(
          'Not selected',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(153),
          ),
        ),
      );
    }
  }

  Widget _buildExpandedLifeSituation(
    ThemeData theme,
    bool isSaving,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          _lifeStatusOptions.map((option) {
            final isSelected = _selectedLifeSituation == option;
            return ChoiceChip(
              label: Text(option),
              selected: isSelected,
              onSelected:
                  isSaving
                      ? null
                      : (_) {
                        setState(() {
                          _selectedLifeSituation = option;
                          _isLifeSituationExpanded = false; // Auto-collapse
                        });
                      },
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primary.withAlpha(51),
              labelStyle: TextStyle(
                color:
                    isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            );
          }).toList(),
    );
  }

  // Goals selector (collapsible)
  Widget _buildGoalsSelector(
    ThemeData theme,
    bool isSaving,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap:
              isSaving
                  ? null
                  : () {
                    setState(() {
                      _isGoalsExpanded = !_isGoalsExpanded;
                    });
                  },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Your Goals', style: theme.textTheme.titleMedium),
                Icon(
                  _isGoalsExpanded ? Icons.expand_less : Icons.expand_more,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Selected items or expanded selection
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              _isGoalsExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
          firstChild: _buildCollapsedGoals(theme),
          secondChild: _buildExpandedGoals(theme, isSaving),
        ),
      ],
    );
  }

  Widget _buildCollapsedGoals(ThemeData theme) {
    if (_selectedGoals.isNotEmpty) {
      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children:
            _selectedGoals.map((goal) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(26),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      goal,
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.outline.withAlpha(77)),
        ),
        child: Text(
          'No goals selected',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(153),
          ),
        ),
      );
    }
  }

  Widget _buildExpandedGoals(
    ThemeData theme,
    bool isSaving,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          _goalOptions.map((goal) {
            final isSelected = _selectedGoals.contains(goal);
            return FilterChip(
              label: Text(goal),
              selected: isSelected,
              onSelected:
                  isSaving
                      ? null
                      : (selected) {
                        setState(() {
                          if (selected) {
                            _selectedGoals.add(goal);
                          } else {
                            _selectedGoals.remove(goal);
                          }
                        });
                      },
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primary.withAlpha(51),
              checkmarkColor: theme.colorScheme.primary,
              labelStyle: TextStyle(
                color:
                    isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            );
          }).toList(),
    );
  }
}
