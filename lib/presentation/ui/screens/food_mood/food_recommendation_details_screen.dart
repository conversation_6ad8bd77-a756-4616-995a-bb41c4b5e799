import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';

/// IMPORTANT: For localization in this file, use the following property names:
/// - recommendation.name - For food name
/// - recommendation.description - For food description
/// - recommendation.benefits - For food benefits (NOT foodBenefits)
/// - recommendation.categories - For food categories
/// - recommendation.moods - For recommended moods
///
/// Refer to the FoodRecommendation class definition for the correct property names.

/// Screen for displaying details of a food recommendation
class FoodRecommendationDetailsScreen extends StatefulWidget {
  /// The food recommendation to display
  final FoodRecommendation recommendation;

  /// Creates a new [FoodRecommendationDetailsScreen]
  const FoodRecommendationDetailsScreen({
    required this.recommendation,
    super.key,
  });

  @override
  State<FoodRecommendationDetailsScreen> createState() =>
      _FoodRecommendationDetailsScreenState();
}

class _FoodRecommendationDetailsScreenState
    extends State<FoodRecommendationDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Animation-related properties
  bool _isScrolled = false;
  bool _showHeartBurst = false;
  AnimationController? _heartController;

  // Track the current recommendation to detect changes
  late FoodRecommendation _currentRecommendation;
  
  // Color based on recommendation mood
  late Color _recommendationColor;

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 2, vsync: this);

    // Add scroll listener for collapsing header effect
    _scrollController.addListener(_onScroll);

    // Initialize current recommendation
    _currentRecommendation = widget.recommendation;
    
    // Set color based on mood or use a default color from card
    _recommendationColor =
        widget.recommendation.moodTypes?.isNotEmpty == true
            ? _getMoodColor(widget.recommendation.moodTypes!.first)
            : _getRandomColor(); // Change from fixed Colors.teal to random color like card

    // Setup heart animation controller
    _heartController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _heartController!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() => _showHeartBurst = false);
        _heartController!.reset();
      }
    });
  }

  void _onScroll() {
    setState(() {
      _isScrolled = _scrollController.offset > 10;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _heartController?.dispose();
    super.dispose();
  }

  void triggerHeartBurst() {
    setState(() => _showHeartBurst = true);
    _heartController!.forward();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<FoodMoodCubit, FoodMoodState>(
      listener: (context, state) {
        // Handle vote submission result
        if (state.isVoteInProgress == false &&
            state.votingRecommendationId == widget.recommendation.id) {
          // Find the updated recommendation
          final updatedRecommendation = state.foodRecommendations.firstWhere(
            (rec) => rec.id == widget.recommendation.id,
            orElse: () => _currentRecommendation,
          );

          // Update the current recommendation
          if (updatedRecommendation != _currentRecommendation) {
            setState(() {
              _currentRecommendation = updatedRecommendation;
            });
          }

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Your vote has been recorded'),
              duration: const Duration(seconds: 2),
            ),
          );
        }

        // Handle vote submission error
        if (state.failure != null &&
            state.votingRecommendationId == widget.recommendation.id) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to submit vote: ${state.failure!.message}'),
              backgroundColor: theme.colorScheme.error,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      builder: (context, state) {
        // Get the current recommendation from state if available
        final recommendation = state.foodRecommendations.firstWhere(
          (rec) => rec.id == widget.recommendation.id,
          orElse: () => _currentRecommendation,
        );

        // Update current recommendation if it changed
        if (recommendation != _currentRecommendation) {
          _currentRecommendation = recommendation;
        }

        // Check if this recommendation is currently being voted on
        final isVoting =
            state.isVoteInProgress &&
            state.votingRecommendationId == recommendation.id;

        return Scaffold(
          backgroundColor: theme.scaffoldBackgroundColor,
          body: NestedScrollView(
            controller: _scrollController,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                // Flexible header with image
                SliverAppBar(
                  expandedHeight: 250.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: _recommendationColor,
                  elevation: 0,
                  flexibleSpace: FlexibleSpaceBar(
                    background: _buildFlexibleHeader(context, recommendation),
                  ),
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  title: AnimatedOpacity(
                    opacity: _isScrolled ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Text(
                      recommendation.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  centerTitle: true,
                ),

                // Tab bar
                SliverPersistentHeader(
                  delegate: _SliverTabBarDelegate(
                    TabBar(
                      controller: _tabController,
                      tabs: [Tab(text: 'Info'), Tab(text: 'Discussion')],
                      labelColor: _recommendationColor,
                      labelStyle: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
                      indicatorColor: _recommendationColor,
                    ),
                    backgroundColor: theme.scaffoldBackgroundColor,
                  ),
                  pinned: true,
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                // Info tab
                _buildInfoTab(context, recommendation, isVoting),

                // Discussion tab
                _buildDiscussionTab(context),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFlexibleHeader(
    BuildContext context,
    FoodRecommendation recommendation,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Stack(
      children: [
        // Background image with gradient overlay
        Positioned.fill(
          child:
              recommendation.imageUrl.isNotEmpty
                  ? Stack(
                    children: [
                      Positioned.fill(
                        child: CachedNetworkImage(
                          imageUrl: recommendation.imageUrl,
                          fit: BoxFit.cover,
                          maxWidthDiskCache: 800,
                          maxHeightDiskCache: 400,
                          memCacheWidth: 800,
                          memCacheHeight: 400,
                          useOldImageOnUrlChange: true,
                          placeholder:
                              (context, url) =>
                                  _buildImagePlaceholder(colorScheme),
                          errorWidget:
                              (context, url, error) =>
                                  _buildImagePlaceholder(colorScheme),
                        ),
                      ),
                      // Gradient overlay
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                _recommendationColor.withAlpha(100),
                                _recommendationColor.withAlpha(180),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  : _buildImagePlaceholder(colorScheme),
        ),

        // Food name and subtitle
        Positioned(
          left: 20,
          right: 20,
          bottom: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Text(
                recommendation.name,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      color: Colors.black45,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // Creation date/time badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(40),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.calendar_today, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(recommendation.createdAt),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Up/Downvote buttons at bottom right
        Positioned(
          bottom: 20,
          right: 20,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withAlpha(100),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Upvote Icon
                  GestureDetector(
                    onTap: () => _submitVote(recommendation, true),
                    child: Icon(
                      recommendation.userVote == VoteType.upvote
                          ? Icons.thumb_up
                          : Icons.thumb_up_outlined,
                      color:
                          recommendation.userVote == VoteType.upvote
                              ? Colors.white
                              : Colors.white.withAlpha(200),
                      size: 22,
                    ),
                  ),
                  // Vote Count
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      '${recommendation.upvotes - recommendation.downvotes}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // Downvote Icon
                  GestureDetector(
                    onTap: () => _submitVote(recommendation, false),
                    child: Icon(
                      recommendation.userVote == VoteType.downvote
                          ? Icons.thumb_down
                          : Icons.thumb_down_outlined,
                      color:
                          recommendation.userVote == VoteType.downvote
                              ? Colors.white
                              : Colors.white.withAlpha(200),
                      size: 22,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Heart burst animation overlay
        if (_showHeartBurst)
          Positioned.fill(
            child: Center(
              child: SizedBox(
                width: 160,
                height: 160,
                child: Icon(Icons.favorite, color: Colors.red, size: 100),
              ),
            ),
          ),
      ],
    );
  }

  void _submitVote(FoodRecommendation recommendation, bool isUpvote) {
    final foodMoodCubit = context.read<FoodMoodCubit>();

    if (isUpvote && recommendation.userVote != VoteType.upvote) {
      foodMoodCubit.submitVote(recommendation.id, VoteType.upvote);
      triggerHeartBurst();
    } else if (!isUpvote && recommendation.userVote != VoteType.downvote) {
      foodMoodCubit.submitVote(recommendation.id, VoteType.downvote);
    } else {
      // Remove vote if already voted the same way
      // Note: API support for removing votes would be needed
      foodMoodCubit.submitVote(
        recommendation.id,
        isUpvote ? VoteType.upvote : VoteType.downvote,
      );
    }
  }

  Widget _buildInfoTab(
    BuildContext context,
    FoodRecommendation recommendation,
    bool isVoting,
  ) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Creator info if available
          if (recommendation.username.isNotEmpty)
            Text(
              l10n.createdBy(recommendation.username),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          if (recommendation.username.isNotEmpty) const SizedBox(height: 16),
            
          // Description
          _buildSection(
            context,
            title: l10n.description,
            child: Text(
              recommendation.description,
              style: theme.textTheme.bodyLarge,
            ),
          ),
          const SizedBox(height: 24),
          
          // Eye-catching nutritional points section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _recommendationColor.withAlpha(30),
                  _recommendationColor.withAlpha(60),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _recommendationColor.withAlpha(100),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Nutritional Benefits',
                  style: theme.textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.health_and_safety_outlined,
                      color: _recommendationColor,
                      size: 32,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Highly Nutritious',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _recommendationColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Text(
                  'Adding this food to your diet can help improve your mood and overall wellbeing',
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Recommended for moods
          if (recommendation.moodTypes != null &&
              recommendation.moodTypes!.isNotEmpty)
            _buildSection(
              context,
              title: l10n.recommendedForMoods,
              child: _buildMoodsList(context, recommendation),
            ),
          if (recommendation.moodTypes != null &&
              recommendation.moodTypes!.isNotEmpty)
            const SizedBox(height: 24),
            
          // Preparation suggestions
          _buildSection(
            context,
            title: 'Preparation Suggestions',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  leading: Icon(
                    Icons.restaurant_menu,
                    color: _recommendationColor,
                  ),
                  title: Text('Simple preparation'),
                  subtitle: Text('Can be easily incorporated into your meals'),
                  contentPadding: EdgeInsets.zero,
                ),
                ListTile(
                  leading: Icon(Icons.access_time, color: _recommendationColor),
                  title: Text('Quick to prepare'),
                  subtitle: Text('Takes minimal time to get ready'),
                  contentPadding: EdgeInsets.zero,
                ),
                ListTile(
                  leading: Icon(Icons.store, color: _recommendationColor),
                  title: Text('Widely available'),
                  subtitle: Text('Can be found in most grocery stores'),
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildDiscussionTab(BuildContext context) {
    final theme = Theme.of(context);
    // For now, we'll use an empty list since we haven't implemented comments for food yet
    final comments = <dynamic>[];

    return Column(
      children: [
        // Comments list
        Expanded(
          child:
              comments.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.comment_outlined,
                          size: 64,
                          color: _recommendationColor.withAlpha(150),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No comments yet',
                          style: theme.textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Be the first to share your experience',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(180),
                          ),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: comments.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 16,
                                    backgroundColor: _recommendationColor
                                        .withAlpha(30),
                                    child: const Text(
                                      'U',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Username'),
                                  const Spacer(),
                                  Text(
                                    '2h ago',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withAlpha(150),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              const Text('Comment text'),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
        ),

        // Comment input
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: 'Add a comment...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: 3,
                  minLines: 1,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  if (_commentController.text.trim().isNotEmpty) {
                    // TODO: Implement comment functionality for food recommendations
                    _commentController.clear();
                  }
                },
                icon: const Icon(Icons.send, color: Colors.white),
                style: IconButton.styleFrom(
                  backgroundColor: _recommendationColor,
                  padding: const EdgeInsets.all(12),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMoodsList(
    BuildContext context,
    FoodRecommendation recommendation,
  ) {
    final theme = Theme.of(context);

    if (recommendation.moodTypes == null || recommendation.moodTypes!.isEmpty) {
      return const Text('No mood information available');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          recommendation.moodTypes!.map((moodType) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.tertiaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${moodType.emoji} ${moodType.name}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onTertiaryContainer,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: _recommendationColor,
          ),
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }

  Widget _buildImagePlaceholder(ColorScheme colorScheme) {
    // Match the style with food recommendation card
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: _recommendationColor.withAlpha(200),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            _recommendationColor.withAlpha(150),
            _recommendationColor.withAlpha(200),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bakery_dining, // Food icon
              size: 80,
              color: Colors.white.withAlpha(200),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(40),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'Food Recommendation',
                style: TextStyle(
                  color: Colors.white.withAlpha(230),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${date.month}/${date.day}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }

  // Helper method to get a color based on mood type - match exactly with food_recommendation_card.dart
  Color _getMoodColor(MoodType? mood) {
    if (mood == null) return Colors.blueGrey;

    switch (mood) {
      case MoodType.happy:
        return const Color(0xFFFF9800); // Warm orange - matches mood entity
      case MoodType.sad:
        return Colors.blue;
      case MoodType.anxious:
        return Colors.teal;
      case MoodType.angry:
        return Colors.deepOrange;
      case MoodType.tired:
        return Colors.purple;
      case MoodType.excited:
        return const Color(0xFF8E24AA); // Vibrant purple - matches mood entity
      case MoodType.calm:
        return Colors.green;
      case MoodType.neutral:
        return Colors.grey;
      case MoodType.inspired:
        return Colors.lightBlue;
    }
  }

  // Add matching method from food_recommendation_card
  Color _getRandomColor() {
    final colors = [
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.green,
    ];

    // Use recommendation ID hash to pick a color (consistent for same recommendation)
    final hash = _currentRecommendation.id.hashCode.abs();
    return colors[hash % colors.length];
  }
}

/// Custom delegate for the pinned TabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  final Color backgroundColor;

  _SliverTabBarDelegate(this.tabBar, {required this.backgroundColor});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: backgroundColor, child: tabBar);
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant _SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar ||
        backgroundColor != oldDelegate.backgroundColor;
  }
}
