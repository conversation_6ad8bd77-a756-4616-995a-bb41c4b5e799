import 'package:flutter/material.dart';

/// A custom shimmer effect for loading cards
class Shimmer<PERSON>ard extends StatefulWidget {
  /// Creates a shimmer card with the specified width, height, and colors
  const ShimmerCard({
    super.key,
    this.width,
    this.height,
    this.borderRadius = 20,
    required this.baseColor,
    required this.highlightColor,
  });

  /// Width of the shimmer
  final double? width;

  /// Height of the shimmer
  final double? height;

  /// Border radius of the shimmer
  final double borderRadius;

  /// Base color of the shimmer
  final Color baseColor;

  /// Highlight color of the shimmer
  final Color highlightColor;

  @override
  State<ShimmerCard> createState() => _ShimmerCardState();
}

class _ShimmerCardState extends State<ShimmerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();

    _animation = Tween<double>(begin: -2.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: const [0.0, 0.5, 1.0],
              transform: GradientRotation(_animation.value),
            ),
          ),
        );
      },
    );
  }
}

/// A card with shimmer effect for food recommendation loading state
class FoodRecommendationShimmerCard extends StatelessWidget {
  /// Creates a shimmer card for food recommendation loading state
  const FoodRecommendationShimmerCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseColor = theme.colorScheme.surfaceContainerLowest;
    final highlightColor = theme.colorScheme.surfaceContainerHigh;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      clipBehavior: Clip.antiAlias,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Image placeholder
            ShimmerCard(
              height: 110,
              width: double.infinity,
              baseColor: baseColor,
              highlightColor: highlightColor,
            ),

            // Content placeholder
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  ShimmerCard(
                    height: 20,
                    width: 150,
                    borderRadius: 4,
                    baseColor: baseColor,
                    highlightColor: highlightColor,
                  ),
                  const SizedBox(height: 12),

                  // Description lines
                  ShimmerCard(
                    height: 14,
                    width: double.infinity,
                    borderRadius: 4,
                    baseColor: baseColor,
                    highlightColor: highlightColor,
                  ),
                  const SizedBox(height: 8),
                  ShimmerCard(
                    height: 14,
                    width: 180,
                    borderRadius: 4,
                    baseColor: baseColor,
                    highlightColor: highlightColor,
                  ),

                  // Voting controls placeholder
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: baseColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.outlineVariant.withAlpha(
                          51,
                        ), // 0.2 opacity
                        width: 0.5,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Header row
                        Row(
                          mainAxisSize: MainAxisSize.min, // Prevent overflow
                          children: [
                            Flexible(
                              child: ShimmerCard(
                                height: 12,
                                width: 100,
                                borderRadius: 4,
                                baseColor: baseColor,
                                highlightColor: highlightColor,
                              ),
                            ),
                            const Spacer(),
                            Flexible(
                              child: ShimmerCard(
                                height: 12,
                                width: 60,
                                borderRadius: 4,
                                baseColor: baseColor,
                                highlightColor: highlightColor,
                              ),
                            ),
                          ],
                        ),

                        // Separator
                        Container(
                          height: 1,
                          width: double.infinity,
                          color: theme.colorScheme.outlineVariant.withAlpha(
                            26,
                          ), // 0.1 opacity
                          margin: const EdgeInsets.symmetric(vertical: 12),
                        ),

                        // Voting buttons placeholder
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  ShimmerCard(
                                    height: 20,
                                    width: 20,
                                    borderRadius: 4,
                                    baseColor: baseColor,
                                    highlightColor: highlightColor,
                                  ),
                                  const SizedBox(height: 8),
                                  ShimmerCard(
                                    height: 12,
                                    width: 20,
                                    borderRadius: 4,
                                    baseColor: baseColor,
                                    highlightColor: highlightColor,
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              height: 36,
                              width: 1,
                              color: theme.colorScheme.outlineVariant.withAlpha(
                                26,
                              ), // 0.1 opacity
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  ShimmerCard(
                                    height: 20,
                                    width: 20,
                                    borderRadius: 4,
                                    baseColor: baseColor,
                                    highlightColor: highlightColor,
                                  ),
                                  const SizedBox(height: 8),
                                  ShimmerCard(
                                    height: 12,
                                    width: 20,
                                    borderRadius: 4,
                                    baseColor: baseColor,
                                    highlightColor: highlightColor,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
