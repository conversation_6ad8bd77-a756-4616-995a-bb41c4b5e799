import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../bloc/dashboard/dashboard_cubit.dart';
import '../../../../bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// A widget for selecting a mood using emoji in food mood screen
class FoodMoodSelector extends StatelessWidget {
  /// Callback for when a mood is selected
  /// This callback is intended for external users of the widget if they want to handle selection differently.
  /// If null, the widget will update the DashboardCubit directly.
  final Function(MoodType)? onMoodSelected;

  /// Creates a new FoodMoodSelector widget
  const FoodMoodSelector({this.onMoodSelected, super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      buildWhen: (prev, curr) => prev.currentMood != curr.currentMood,
      builder: (context, dashboardState) {
        final MoodType currentMood =
            dashboardState.currentMood ?? MoodType.happy;
        final allMoods = MoodType.values;
        final commonMoods = [
          MoodType.happy,
          MoodType.sad,
          MoodType.calm,
          MoodType.excited,
          MoodType.tired,
        ];
        final otherMoods =
            allMoods.where((mood) => !commonMoods.contains(mood)).toList();
        return _FoodMoodSelectorContentView(
          currentMood: currentMood,
          commonMoods: commonMoods,
          otherMoods: otherMoods,
          onMoodSelected: (mood) {
            // Update DashboardCubit and call external callback if provided
            context.read<DashboardCubit>().setCurrentMood(mood);
            if (onMoodSelected != null) {
              onMoodSelected!(mood);
            }
          },
        );
      },
    );
  }
}

// New Stateful widget to handle the expanded state locally and selection logic
class _FoodMoodSelectorContentView extends StatefulWidget {
  final MoodType currentMood;
  final List<MoodType> commonMoods;
  final List<MoodType> otherMoods;
  final Function(MoodType) onMoodSelected;

  const _FoodMoodSelectorContentView({
    required this.currentMood,
    required this.commonMoods,
    required this.otherMoods,
    required this.onMoodSelected,
  });

  @override
  State<_FoodMoodSelectorContentView> createState() =>
      _FoodMoodSelectorContentViewState();
}

class _FoodMoodSelectorContentViewState
    extends State<_FoodMoodSelectorContentView> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.tertiary,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        '😊',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocalizations.of(context)!.selectYourMood,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _isExpanded
                      ? AppLocalizations.of(context)!.allMoodOptions
                      : AppLocalizations.of(context)!.commonMoods,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ...widget.commonMoods.map(
                  (mood) => FoodMoodItem(
                    mood: mood,
                    currentMood: widget.currentMood,
                    onMoodSelected: widget.onMoodSelected,
                  ),
                ),
                _buildExpandButton(),
              ],
            ),
          ),
          if (_isExpanded)
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _isExpanded ? null : 0,
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children:
                        widget.otherMoods
                            .map(
                              (mood) => FoodMoodItem(
                                mood: mood,
                                currentMood: widget.currentMood,
                                onMoodSelected: widget.onMoodSelected,
                              ),
                            )
                            .toList(),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExpandButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withAlpha(100),
                width: 1,
              ),
            ),
            child: Center(
              child: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _isExpanded
                ? AppLocalizations.of(context)!.lessMoodsButton
                : AppLocalizations.of(context)!.moreMoodsButton,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withAlpha(220),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// Represents a mood item in the [FoodMoodSelector]
class FoodMoodItem extends StatelessWidget {
  /// The mood type
  final MoodType mood;

  /// The currently selected mood
  final MoodType currentMood;

  /// Callback when mood is selected
  final Function(MoodType) onMoodSelected;

  /// Creates a new [FoodMoodItem]
  const FoodMoodItem({
    super.key,
    required this.mood,
    required this.currentMood,
    required this.onMoodSelected,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = mood == currentMood;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      child: GestureDetector(
        onTap: () => onMoodSelected(mood),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? mood.color.withAlpha(100)
                        : Theme.of(context).colorScheme.surface,
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected
                          ? mood.color
                          : Theme.of(context).colorScheme.outline.withAlpha(50),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: mood.color.withAlpha(70),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ]
                        : null,
              ),
              child: Center(
                child: Text(mood.emoji, style: const TextStyle(fontSize: 24)),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              mood.localizedDescription(context),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.onSurface
                        : Theme.of(
                          context,
                        ).colorScheme.onSurface.withAlpha(220),
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
