import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/ui/screens/food_mood/food_mood_view.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';

/// Screen for the Food Mood feature
class FoodMoodScreen extends StatefulWidget {
  /// Creates a new [FoodMoodScreen]
  const FoodMoodScreen({super.key});

  @override
  State<FoodMoodScreen> createState() => _FoodMoodScreenState();
}

class _FoodMoodScreenState extends State<FoodMoodScreen>
    with AutomaticKeepAliveClientMixin {
  // Keep the state alive when switching tabs
  @override
  bool get wantKeepAlive => true;

  // Store cubit reference to manage lifecycle
  late final FoodMoodCubit _foodMoodCubit;

  @override
  void initState() {
    super.initState();

    // Initialize the cubit
    _foodMoodCubit = serviceLocator<FoodMoodCubit>();

    // Don't load data here - we'll do it in the build method after we have access to the dashboardCubit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  void _initializeData() {
    if (!mounted) return;

    try {
      // Get the current app locale
      final localeProvider = Provider.of<AppLocaleProvider>(
        context,
        listen: false,
      );
      final currentLanguage = localeProvider.locale.languageCode;

      // Get current mood from dashboard if available
      final dashboardCubit = BlocProvider.of<DashboardCubit>(
        context,
        listen: false,
      );
      MoodType? currentMood = dashboardCubit.getCurrentMood();

      // Default to happy if no mood is selected
      currentMood ??= MoodType.happy;

      if (kDebugMode) {
        print(
          'Initializing FoodMoodScreen with mood: $currentMood, language: $currentLanguage',
        );
      }

      // Set the initial country code in the cubit state
      _foodMoodCubit.setCountryCode(currentLanguage);

      // Initialize with the current mood. The Cubit will use the country code from its state.
      _foodMoodCubit.loadFoodRecommendationsForMood(
        currentMood,
        // sortOption and excludeTooManyDownvotes are not needed here as they are default and managed by state
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing FoodMoodScreen: $e');
      }
    }
  }

  @override
  void dispose() {
    // We don't close the cubit here since it's provided by the service locator
    // and might be shared with other parts of the app
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final l10n = AppLocalizations.of(context)!;

    return BlocProvider<FoodMoodCubit>.value(
      value: _foodMoodCubit,
      child: BlocListener<DashboardCubit, DashboardState>(
        listenWhen:
            (previous, current) => previous.currentMood != current.currentMood,
        listener: (context, state) {
          if (state.currentMood != null && mounted) {
            // When dashboard mood changes, update food recommendations
            try {
              _foodMoodCubit.loadFoodRecommendationsForMood(
                state.currentMood!,
                // Pass current sort and exclude settings from FoodMoodCubit state
                sortOption: _foodMoodCubit.state.sortOption,
                excludeTooManyDownvotes:
                    _foodMoodCubit.state.excludeTooManyDownvotes,
              );
            } catch (e) {
              if (kDebugMode) {
                print('Error updating food mood from dashboard: $e');
              }
            }
          }
        },
        child: BaseScaffold.withTitle(
          title: l10n.foodMoodTitle,
          showBackButton: false,
          titleIconData: Icons.fastfood,
          padding: EdgeInsets.zero,
          actions: [
            BlocBuilder<FoodMoodCubit, FoodMoodState>(
              builder: (context, foodMoodState) {
                final cubit =
                    context
                        .read<
                          FoodMoodCubit
                        >(); // Get cubit within the builder context
                return IconButton(
                  icon: const Icon(Icons.filter_list, color: Colors.white),
                  tooltip: l10n.filterAndSort,
                  onPressed:
                      () => _showFilterAndSortSheet(
                        context,
                        foodMoodState,
                        cubit,
                      ), // Pass foodMoodState and cubit
                  color: Theme.of(context).colorScheme.onSurface,
                );
              }
            ),
          ],
          body: const FoodMoodView(),
        ),
      ),
    );
  }

  void _showFilterAndSortSheet(
    BuildContext context,
    FoodMoodState state,
    FoodMoodCubit cubit,
  ) {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      builder: (context) {
        final l10n = AppLocalizations.of(context)!;
        // Local state for filter sheet
        FoodSortOption selectedSort = state.sortOption;
        bool excludeDownvotes = state.excludeTooManyDownvotes;
        String? selectedLang = state.countryCode;
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                top: 16,
                left: 16,
                right: 16,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          l10n.filterAndSort,
                          style: theme.textTheme.titleLarge,
                        ),
                        const Spacer(),
                        TextButton(
                          child: const Text('Close'),
                          onPressed: () {
                            // Just close the sheet
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Sort options as single-select chips
                    Text(l10n.sortBy, style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildSortChip(
                          context,
                          l10n.defaultOption,
                          FoodSortOption.default_,
                          selectedSort,
                          (val) {
                            setState(() => selectedSort = val);
                            cubit.applySortOption(val);
                          },
                        ),
                        _buildSortChip(
                          context,
                          l10n.topRated,
                          FoodSortOption.highestVotes,
                          selectedSort,
                          (val) {
                            setState(() => selectedSort = val);
                            cubit.applySortOption(val);
                          },
                        ),
                        _buildSortChip(
                          context,
                          "Votes ↑",
                          FoodSortOption.oldest,
                          selectedSort,
                          (val) {
                            setState(() => selectedSort = val);
                            cubit.applySortOption(val);
                          },
                        ),
                        _buildSortChip(
                          context,
                          "Votes ↓",
                          FoodSortOption.newest,
                          selectedSort,
                          (val) {
                            setState(() => selectedSort = val);
                            cubit.applySortOption(val);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    // Language filters (single select)
                    Text(l10n.languages, style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _buildLanguageChips(
                        theme,
                        selectedLang,
                        (
                        lang,
                      ) {
                        setState(() => selectedLang = lang);
                        cubit.setCountryCode(lang);
                          
                        // Apply immediately
                          if (cubit.state.isFilteredByMood &&
                              cubit.state.selectedMood != null) {
                            cubit.loadFoodRecommendationsForMood(
                              cubit.state.selectedMood!,
                              sortOption: selectedSort,
                              excludeTooManyDownvotes: excludeDownvotes,
                            );
                          } else {
                            cubit.loadFoodRecommendations(
                              countryCode: lang,
                            );
                          }
                      },
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Downvote filter
                    SwitchListTile(
                      title: Text(l10n.hideDownvotedItems),
                      subtitle: Text(l10n.hideDownvotedDescription),
                      value: excludeDownvotes,
                      onChanged: (val) {
                        setState(() => excludeDownvotes = val);
                        cubit.toggleExcludeTooManyDownvotes(value: val);
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  List<Widget> _buildLanguageChips(
    ThemeData theme,
    String? selectedLang,
    Function(String) onSelected,
  ) {
    final Map<String, Map<String, String>> countryOptions = {
      'en': {'name': 'English', 'flag': '🇬🇧'},
      'de': {'name': 'German', 'flag': '🇩🇪'},
      'ru': {'name': 'Russian', 'flag': '🇷🇺'},
      'tr': {'name': 'Turkish', 'flag': '🇹🇷'},
      'ar': {'name': 'Arabic', 'flag': '🇸🇦'},
    };
    return countryOptions.entries.map((entry) {
      final isSelected = selectedLang == entry.key;
      return ChoiceChip(
        side: BorderSide(color: theme.colorScheme.outline, width: 0.2),
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(entry.value['flag'] ?? ''),
            const SizedBox(width: 8),
            Text(entry.value['name'] ?? ''),
          ],
        ),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onSelected(entry.key);
          }
        },
        backgroundColor: theme.colorScheme.surfaceContainerHighest,
        selectedColor: theme.colorScheme.primary,
        labelStyle: theme.textTheme.bodyMedium?.copyWith(
          color:
              isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface,
        ),
      );
    }).toList();
  }

  Widget _buildSortChip(
    BuildContext context,
    String label,
    FoodSortOption sortOption,
    FoodSortOption selectedSort,
    Function(FoodSortOption) onSelected,
  ) {
    final theme = Theme.of(context);
    final isSelected = selectedSort == sortOption;
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      side: BorderSide(color: theme.colorScheme.outline, width: 0.2),
      onSelected: (selected) {
        if (selected) {
          onSelected(sortOption);
        }
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primary,
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        color:
            isSelected
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurface,
      ),
    );
  }
}
