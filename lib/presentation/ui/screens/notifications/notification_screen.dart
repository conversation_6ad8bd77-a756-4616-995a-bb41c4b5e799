import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/domain/entities/user_notification.dart';
import 'package:detoxme/presentation/bloc/notification/notification_cubit.dart';
import 'package:detoxme/presentation/bloc/notification/notification_state.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:intl/intl.dart';

/// Sort order options for notifications
enum NotificationSortOrder {
  /// Sort by date (newest first)
  byDate,

  /// Unread notifications first, then by date
  unreadFirst,
}

/// Screen to display all user notifications
class NotificationScreen extends StatefulWidget {
  /// Creates a [NotificationScreen]
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  late NotificationSortOrder _currentSortOrder;

  @override
  void initState() {
    super.initState();
    _currentSortOrder = NotificationSortOrder.byDate;
    
    // Initialize notifications for current user
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authCubit = context.read<AuthCubit>();
      final userId = authCubit.getCurrentUserId();
      if (userId != null) {
        context.read<NotificationCubit>().initializeNotifications(userId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BlocBuilder<NotificationCubit, NotificationState>(
      builder: (context, state) {
        final unreadCount = context.read<NotificationCubit>().unreadCount;

        // Build the title with unread count as part of the actions array
        final unreadBadge =
            unreadCount > 0
            ? Container(
                  margin: const EdgeInsets.only(right: 16),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    l10n.unreadCount(unreadCount),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
            : const SizedBox.shrink();

        return BaseScaffold(
          title: l10n.notifications,
          titleIconData: Icons.notifications_rounded,
          iconBackgroundColor: theme.colorScheme.secondary,
          showBackButton: true,
          onBackPressed: () => context.pop(),
          showBottomNav: false,
          actions: [
            // Unread badge
            unreadBadge,
            
            // Mark all as read button
            if (unreadCount > 0)
              IconButton(
                icon: const Icon(Icons.done_all),
                tooltip: 'Mark all as read',
                onPressed: () {
                  final authCubit = context.read<AuthCubit>();
                  final userId = authCubit.getCurrentUserId();
                  if (userId != null) {
                    context.read<NotificationCubit>().markAllAsRead(userId);
                  }
                },
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  foregroundColor: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            
            // Sort button
            IconButton(
              icon: const Icon(Icons.sort),
              tooltip: l10n.sort,
              onPressed: () {
                _showSortOptions(context);
              },
              style: IconButton.styleFrom(
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                foregroundColor: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 8),
          ],
          body: _buildBody(context, state),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, NotificationState state) {
    if (state is NotificationLoading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state is NotificationError) {
      return _buildErrorState(context, state.message);
    } else if (state is NotificationLoaded) {
      if (state.notifications.isEmpty) {
        return _buildEmptyState(context);
      } else {
        return _buildNotificationsList(context, state.notifications);
      }
    } else {
      return _buildEmptyState(context);
    }
  }

  Widget _buildErrorState(BuildContext context, String error) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Failed to load notifications',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              error,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                final authCubit = context.read<AuthCubit>();
                final userId = authCubit.getCurrentUserId();
                if (userId != null) {
                  context.read<NotificationCubit>().initializeNotifications(
                    userId,
                  );
                }
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.notifications_outlined,
                size: 64,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              l10n.noNotifications,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noNotificationsDescription,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(
    BuildContext context,
    List<UserNotification> notifications,
  ) {
    // Apply sorting
    final sortedNotifications = List<UserNotification>.from(notifications);
    _sortNotifications(sortedNotifications);

    // Group notifications by date
    final groupedNotifications = <String, List<UserNotification>>{};

    for (final notification in sortedNotifications) {
      final dateKey = DateFormat('yyyy-MM-dd').format(notification.timestamp);
      if (!groupedNotifications.containsKey(dateKey)) {
        groupedNotifications[dateKey] = [];
      }
      groupedNotifications[dateKey]!.add(notification);
    }

    // Sort dates in descending order (newest first)
    final sortedDates =
        groupedNotifications.keys.toList()..sort((a, b) => b.compareTo(a));

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 16),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final notificationsForDate = groupedNotifications[dateKey]!;
        final date = DateTime.parse(dateKey);
        final formattedDate = _getFormattedDate(date);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateHeader(context, formattedDate),
            ...notificationsForDate.map(
              (notification) => _buildNotificationItem(context, notification),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(BuildContext context, String date) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              date,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Divider(color: theme.colorScheme.outlineVariant)),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    UserNotification notification,
  ) {
    final theme = Theme.of(context);
    final time = DateFormat.jm().format(notification.timestamp);

    // Choose icon based on notification type
    IconData icon;
    Color iconColor;

    switch (notification.type) {
      case NotificationType.activity:
        icon = Icons.directions_run_rounded;
        iconColor = theme.colorScheme.primary;
        break;
      case NotificationType.coupon:
        icon = Icons.confirmation_number_rounded;
        iconColor = theme.colorScheme.tertiary;
        break;
      case NotificationType.achievement:
        icon = Icons.emoji_events_rounded;
        iconColor = Colors.amber;
        break;
      case NotificationType.commentLike:
        icon = Icons.favorite_rounded;
        iconColor = Colors.red;
        break;
      case NotificationType.reminder:
        icon = Icons.alarm_rounded;
        iconColor = theme.colorScheme.secondary;
        break;
      case NotificationType.motivation:
        icon = Icons.psychology_rounded;
        iconColor = Colors.purple;
        break;
      default:
        icon = Icons.notifications_rounded;
        iconColor = theme.colorScheme.primary;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Dismissible(
        key: Key(notification.id),
        background: _buildSwipeActionBackground(
          context,
          Icons.delete_outline_rounded,
          theme.colorScheme.error,
          Alignment.centerLeft,
        ),
        secondaryBackground: _buildSwipeActionBackground(
          context,
          notification.isRead
              ? Icons.mark_email_unread_outlined
              : Icons.mark_email_read_outlined,
          theme.colorScheme.tertiary,
          Alignment.centerRight,
        ),
        confirmDismiss: (direction) async {
          final authCubit = context.read<AuthCubit>();
          final userId = authCubit.getCurrentUserId();
          if (userId == null) return false;

          if (direction == DismissDirection.startToEnd) {
            // Delete notification
            await context.read<NotificationCubit>().deleteNotification(
              notification.id,
              userId,
            );

            // Show snackbar feedback
            final l10n = AppLocalizations.of(context)!;
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(l10n.notificationDeleted),
                  backgroundColor: theme.colorScheme.error,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }

            return true;
          } else if (direction == DismissDirection.endToStart) {
            // Toggle read/unread status
            if (!notification.isRead) {
              await context.read<NotificationCubit>().markAsRead(
                notification.id,
                userId,
              );
            }

            // Show snackbar feedback
            final l10n = AppLocalizations.of(context)!;
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    notification.isRead
                        ? l10n.notificationMarkedUnread
                        : l10n.notificationMarkedRead,
                  ),
                  backgroundColor: theme.colorScheme.tertiary,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }

            return false; // Keep the item
          }
          return false;
        },
        child: Card(
          elevation: 0,
          color:
              notification.isRead
                  ? theme.colorScheme.surface.withValues(alpha: 0.2)
                  : theme.colorScheme.primaryContainer.withValues(alpha: 0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color:
                  notification.isRead
                      ? theme.colorScheme.outlineVariant.withValues(alpha: 0.3)
                      : theme.colorScheme.primary.withValues(alpha: 0.4),
              width: notification.isRead ? 0.5 : 1.5,
            ),
          ),
          child: InkWell(
            onTap: () => _onNotificationTap(notification),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: iconColor.withValues(
                        alpha: notification.isRead ? 0.05 : 0.15,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color:
                          notification.isRead
                              ? iconColor.withValues(alpha: 0.2)
                              : iconColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                notification.title,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight:
                                      notification.isRead
                                          ? FontWeight.normal
                                          : FontWeight.bold,
                                  color:
                                      notification.isRead
                                          ? theme.colorScheme.onSurface
                                              .withValues(alpha: 0.2)
                                          : theme.colorScheme.onSurface,
                                ),
                              ),
                            ),
                            if (!notification.isRead)
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          notification.message,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color:
                                notification.isRead
                                    ? theme.colorScheme.onSurfaceVariant
                                        .withValues(alpha: 0.2)
                                    : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          time,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                notification.isRead
                                    ? theme.colorScheme.onSurfaceVariant
                                        .withValues(alpha: 0.2)
                                    : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the background widget for swipe actions
  Widget _buildSwipeActionBackground(
    BuildContext context,
    IconData icon,
    Color color,
    Alignment alignment,
  ) {
    final l10n = AppLocalizations.of(context)!;
    final isLeftSwipe = alignment == Alignment.centerLeft;

    String actionText = '';
    if (isLeftSwipe) {
      actionText = l10n.delete;
    } else {
      actionText =
          icon == Icons.mark_email_read_outlined
              ? l10n.markRead
              : l10n.markUnread;
    }

    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.20),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.50), width: 1),
      ),
      child: Align(
        alignment: alignment,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.8),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 8),
              Text(
                actionText,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onNotificationTap(UserNotification notification) {
    final authCubit = context.read<AuthCubit>();
    final userId = authCubit.getCurrentUserId();
    if (userId == null) return;

    // Mark as read
    if (!notification.isRead) {
      context.read<NotificationCubit>().markAsRead(notification.id, userId);
    }

    // Handle navigation based on type and action data
    if (notification.actionData != null) {
      switch (notification.type) {
        case NotificationType.activity:
          final activityId = notification.actionData!['activity_id'];
          if (activityId != null) {
            context.push('${AppRoutes.activities}/$activityId');
          }
          break;
        case NotificationType.coupon:
          context.push(AppRoutes.categories);
          break;
        case NotificationType.commentLike:
          final activityId = notification.actionData!['activity_id'];
          if (activityId != null) {
            context.push('${AppRoutes.activities}/$activityId');
          }
          break;
        default:
          break;
      }
    }
  }

  String _getFormattedDate(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(date.year, date.month, date.day);
    final l10n = AppLocalizations.of(context)!;

    if (dateToCheck == DateTime(now.year, now.month, now.day)) {
      return l10n.today;
    } else if (dateToCheck == yesterday) {
      return l10n.yesterday;
    } else {
      return DateFormat.yMMMMd().format(date);
    }
  }

  // Sort notifications based on current sort order
  void _sortNotifications(List<UserNotification> notifications) {
    switch (_currentSortOrder) {
      case NotificationSortOrder.byDate:
        // Sort by date (newest first)
        notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        break;
      case NotificationSortOrder.unreadFirst:
        // Sort unread first, then by date
        notifications.sort((a, b) {
          if (a.isRead == b.isRead) {
            // If both have same read status, sort by date
            return b.timestamp.compareTo(a.timestamp);
          }
          // Unread first
          return a.isRead ? 1 : -1;
        });
        break;
    }
  }

  // Show a bottom sheet with sort options
  void _showSortOptions(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      l10n.sortNotifications,
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.calendar_today),
                    title: Text(l10n.byDateNewestFirst),
                    trailing:
                        _currentSortOrder == NotificationSortOrder.byDate
                            ? Icon(
                              Icons.check,
                              color: theme.colorScheme.primary,
                            )
                            : null,
                    onTap: () {
                      setState(() {
                        _currentSortOrder = NotificationSortOrder.byDate;
                      });
                      Navigator.pop(context);
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.mark_email_unread),
                    title: Text(l10n.unreadFirst),
                    trailing:
                        _currentSortOrder == NotificationSortOrder.unreadFirst
                            ? Icon(
                              Icons.check,
                              color: theme.colorScheme.primary,
                            )
                            : null,
                    onTap: () {
                      setState(() {
                        _currentSortOrder = NotificationSortOrder.unreadFirst;
                      });
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
