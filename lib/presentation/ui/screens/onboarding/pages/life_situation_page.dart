import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_cubit.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_state.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/widgets/input_pages/life_situation_selector_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// A page in the onboarding flow that allows the user to select their life situation.
///
/// This widget uses BlocBuilder to listen to onboarding state changes and displays
/// a selector for different life situations. Handles localization and validation errors.
class LifeSituationPage extends StatelessWidget {
  const LifeSituationPage({super.key});

  static const String lifeSituationKey = 'lifeSituation';

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    assert(l10n != null, 'AppLocalizations not found in context. Make sure localization is properly configured.');
    if (l10n == null) {
      // Fallback UI if localization is not available
      return const Center(child: Text('Localization error'));
    }
    return _LifeSituationBlocBuilder(l10n: l10n);
  }
}

class _LifeSituationBlocBuilder extends StatelessWidget {
  final AppLocalizations l10n;
  const _LifeSituationBlocBuilder({required this.l10n});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      buildWhen: (previous, current) {
        if (previous is OnboardingContentState &&
            current is OnboardingContentState) {
          return previous.selectedLifeSituation !=
                  current.selectedLifeSituation ||
              previous.validationErrors != current.validationErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! OnboardingContentState) {
          return const Center(child: CircularProgressIndicator());
        }

        // Options for life situations
        final List<String> options = [
          l10n.lifeSituationIndividual,
          l10n.lifeSituationRelationship,
          l10n.lifeSituationFamily,
          l10n.lifeSituationCustom,
        ];

        return LifeSituationSelectorWidget(
          title: l10n.lifeSituationTitle,
          body: l10n.lifeSituationDescription,
          options: options,
          selectedOption: state.selectedLifeSituation,
          onSelectionChanged: (selected) {
            if (selected != null) {
              context.read<OnboardingCubit>().selectLifeSituation(selected);
            }
          },
          errorText: state.validationErrors[LifeSituationPage.lifeSituationKey],
        );
      },
    );
  }
}
