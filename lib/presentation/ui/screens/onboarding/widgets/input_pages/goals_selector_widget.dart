import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';

class GoalsSelectorWidget extends StatelessWidget {
  final String title;
  final String body;
  final List<String> options;
  final List<IconData> icons;
  final List<String> selectedOptions;
  final int maxSelection;
  final Function(String) onOptionSelected;
  final Function(String) onOptionDeselected;
  final String? errorText;

  const GoalsSelectorWidget({
    super.key,
    required this.title,
    required this.body,
    required this.options,
    required this.icons,
    required this.selectedOptions,
    required this.maxSelection,
    required this.onOptionSelected,
    required this.onOptionDeselected,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 100),
            child: ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    colors: [
                      colorScheme.primary,
                      Color.lerp(
                        colorScheme.primary,
                        colorScheme.tertiary,
                        0.5,
                      )!,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
              child: Text(
                title,
                style: textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 28,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Body with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 200),
            child: Text(
              body,
              style: textTheme.bodyLarge?.copyWith(
                height: 1.5,
                fontSize: 16,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Goals grid wrapped in Stack for FAB positioning
          Expanded(
            child: Stack(
              children: [
                // Goals grid with icons
                FadeInUp(
                  delay: const Duration(milliseconds: 300),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: GridView.builder(
                      physics: const BouncingScrollPhysics(),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1.2,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                      itemCount: options.length,
                      itemBuilder: (context, index) {
                        final option = options[index];
                        final icon = icons[index];
                        final isSelected = selectedOptions.contains(option);

                        // Unique delay for cascade effect
                        final cascadeDelay = Duration(
                          milliseconds: 350 + (index * 80),
                        );

                        return FadeInUp(
                          delay: cascadeDelay,
                          child: _buildGoalCard(
                            option,
                            icon,
                            isSelected,
                            theme,
                            colorScheme,
                            textTheme,
                            index,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Floating counter button
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: FadeInUp(
                    delay: const Duration(
                      milliseconds: 400,
                    ), // Delay after grid fades in
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color:
                            colorScheme
                                .primary, // Use primary color for FAB look
                        borderRadius: BorderRadius.circular(
                          30,
                        ), // More rounded like FAB
                        boxShadow: [
                          BoxShadow(
                            color: colorScheme.primary.withValues(alpha: 0.5),
                            blurRadius: 8,
                            spreadRadius: 1,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            size: 20,
                            color:
                                Colors
                                    .white, // White icon on primary background
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${selectedOptions.length}/$maxSelection',
                            style: textTheme.titleSmall?.copyWith(
                              color:
                                  Colors
                                      .white, // White text on primary background
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Error text with animation
          if (errorText != null && errorText!.isNotEmpty)
            FadeIn(
              duration: const Duration(milliseconds: 300),
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        errorText!,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.error,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Enhanced goal card with icon
  Widget _buildGoalCard(
    String option,
    IconData icon,
    bool isSelected,
    ThemeData theme,
    ColorScheme colorScheme,
    TextTheme textTheme,
    int index,
  ) {
    // Consistent blue-purple gradient for all cards
    final baseColor = colorScheme.primary;
    final accentColor = Color.lerp(baseColor, colorScheme.tertiary, 0.7)!;

    return InkWell(
      onTap: () {
        if (isSelected) {
          onOptionDeselected(option);
        } else if (selectedOptions.length < maxSelection) {
          onOptionSelected(option);
        }
      },
      borderRadius: BorderRadius.circular(20),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [baseColor, accentColor],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.4),
                      blurRadius: 10,
                      spreadRadius: 1,
                      offset: const Offset(0, 4),
                    ),
                  ]
                  : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 6,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
          border: Border.all(
            color:
                isSelected
                    ? Colors.transparent
                    : colorScheme.primary.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    size: 32,
                    color: isSelected ? Colors.white : colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    option,
                    style: textTheme.titleSmall?.copyWith(
                      color: isSelected ? Colors.white : colorScheme.onSurface,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Add/Remove indicator
            Positioned(
              top: 10,
              right: 10,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: 1.0,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors.white
                            : colorScheme.primary.withValues(alpha: 0.15),
                    shape: BoxShape.circle,
                    boxShadow:
                        isSelected
                            ? [
                              BoxShadow(
                                color: colorScheme.primary.withValues(
                                  alpha: 0.5,
                                ),
                                blurRadius: 5,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null,
                  ),
                  child: Icon(
                    isSelected ? Icons.remove : Icons.add,
                    size: 16,
                    color: isSelected ? colorScheme.primary : Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
