import 'package:flutter/material.dart';

class LifeSituationSelectorWidget extends StatelessWidget {
  final String title;
  final String body;
  final List<String> options;
  final String? selectedOption;
  final ValueChanged<String?> onSelectionChanged;
  final String? errorText;

  const LifeSituationSelectorWidget({
    super.key,
    required this.title,
    required this.body,
    required this.options,
    required this.selectedOption,
    required this.onSelectionChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Icons for life situations
    final icons = [
      Icons.person,
      Icons.favorite,
      Icons.family_restroom,
      Icons.help_outline,
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title with enhanced styling
          Text(
            title,
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
              fontSize: 28,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Body with enhanced styling
          Text(
            body,
            style: textTheme.bodyLarge?.copyWith(
              height: 1.5,
              fontSize: 16,
              letterSpacing: 0.3,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Life situation options
          Expanded(
            child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options[index];
                final isSelected = selectedOption == option;
                final icon = icons[index % icons.length];

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: _buildLifeSituationCard(
                    option,
                    icon,
                    isSelected,
                    theme,
                    colorScheme,
                    textTheme,
                    index,
                  ),
                );
              },
            ),
          ),

          // Error text
          if (errorText != null && errorText!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  color: colorScheme.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: colorScheme.error,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      errorText!,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Life situation card styled identically to goal cards
  Widget _buildLifeSituationCard(
    String option,
    IconData icon,
    bool isSelected,
    ThemeData theme,
    ColorScheme colorScheme,
    TextTheme textTheme,
    int index,
  ) {
    // Consistent blue-purple gradient for all cards (from Goals Card)
    final baseColor = colorScheme.primary;
    final accentColor = Color.lerp(baseColor, colorScheme.tertiary, 0.7)!;

    return InkWell(
      // Use the existing single-selection logic
      onTap: () => onSelectionChanged(option),
      borderRadius: BorderRadius.circular(20), // Match Goals Card
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [baseColor, accentColor],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : theme.cardColor,
          borderRadius: BorderRadius.circular(20), // Match Goals Card
          boxShadow:
              isSelected
                  ? [
                    // Selected shadow from Goals Card
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.4),
                      blurRadius: 10,
                      spreadRadius: 1,
                      offset: const Offset(0, 4),
                    ),
                  ]
                  : [
                    // Unselected shadow from Goals Card
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 6,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
          border: Border.all(
            // Border from Goals Card
            color:
                isSelected
                    ? Colors.transparent
                    : colorScheme.primary.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            // Use Column layout like Goals Card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    icon,
                    size: 32, // Match Goals Card icon size
                    color: isSelected ? Colors.white : colorScheme.primary,
                  ),
                  const SizedBox(height: 12), // Match Goals Card spacing
                  Text(
                    option,
                    // Use titleSmall like Goals Card, adjust styling
                    style: textTheme.titleSmall?.copyWith(
                      color: isSelected ? Colors.white : colorScheme.onSurface,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Add/Remove indicator styled like Goals Card, but using Check for single select
            Positioned(
              top: 10,
              right: 10,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: 1.0, // Keep it simple for now
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors
                                .white // Selected indicator background
                            : colorScheme.primary.withValues(
                              alpha: 0.15,
                            ), // Unselected
                    shape: BoxShape.circle,
                    boxShadow:
                        isSelected
                            ? [
                              // Selected indicator shadow
                              BoxShadow(
                                color: colorScheme.primary.withValues(
                                  alpha: 0.5,
                                ),
                                blurRadius: 5,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null, // No shadow when unselected
                  ),
                  child: Icon(
                    // Use Check icon when selected, Add icon when not (like Goals)
                    isSelected ? Icons.check : Icons.add,
                    size: 16,
                    // Adjust icon color based on selection
                    color: isSelected ? colorScheme.primary : Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
