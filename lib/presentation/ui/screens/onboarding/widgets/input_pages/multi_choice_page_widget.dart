import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';

class MultiChoicePageWidget extends StatelessWidget {
  final String title;
  final String body;
  final List<String> options;
  final List<String> selectedOptions;
  final int maxSelection;
  final Function(String) onOptionSelected;
  final Function(String) onOptionDeselected;
  final String? errorText;

  const MultiChoicePageWidget({
    super.key,
    required this.title,
    required this.body,
    required this.options,
    required this.selectedOptions,
    required this.maxSelection,
    required this.onOptionSelected,
    required this.onOptionDeselected,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 100),
            child: ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    colors: [
                      colorScheme.primary,
                      Color.lerp(
                        colorScheme.primary,
                        colorScheme.tertiary,
                        0.5,
                      )!,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
              child: Text(
                title,
                style: textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 28,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Body with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 200),
            child: Text(
              body,
              style: textTheme.bodyLarge?.copyWith(
                height: 1.5,
                fontSize: 16,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // Selection counter with animation
          FadeInUp(
            delay: const Duration(milliseconds: 250),
            child: Padding(
              padding: const EdgeInsets.only(top: 24, bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withValues(alpha: 0.15),
                          blurRadius: 8,
                          spreadRadius: 1,
                          offset: const Offset(0, 3),
                        ),
                      ],
                      border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.4),
                        width: 1.0,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          size: 20,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${selectedOptions.length}/$maxSelection Selected',
                          style: textTheme.titleSmall?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Options with enhanced styling
          Expanded(
            child: FadeInUp(
              delay: const Duration(milliseconds: 300),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children:
                        options.asMap().entries.map((entry) {
                          final index = entry.key;
                          final option = entry.value;
                          final isSelected = selectedOptions.contains(option);
                      
                          // Unique delay for cascade effect
                          final cascadeDelay = Duration(
                            milliseconds: 350 + (index * 80),
                          );

                          return FadeInRight(
                            delay: cascadeDelay,
                            child: _buildEnhancedOptionChip(
                              option,
                              isSelected,
                              theme,
                              colorScheme,
                              textTheme,
                              index,
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
            ),
          ),

          // Error text with animation
          if (errorText != null && errorText!.isNotEmpty)
            FadeIn(
              duration: const Duration(milliseconds: 300),
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        errorText!,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.error,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Enhanced option chip with gradient and animation
  Widget _buildEnhancedOptionChip(
    String option,
    bool isSelected,
    ThemeData theme,
    ColorScheme colorScheme,
    TextTheme textTheme,
    int index,
  ) {
    // Generate color variation based on the option index
    final baseColor =
        isSelected ? colorScheme.primary : theme.scaffoldBackgroundColor;
    final accentColor = Color.lerp(
      baseColor,
      isSelected ? colorScheme.tertiary : colorScheme.primary,
      (index % 4) * 0.2,
    );

    return InkWell(
      onTap: () {
        if (isSelected) {
          onOptionDeselected(option);
        } else if (selectedOptions.length < maxSelection) {
          onOptionSelected(option);
        }
      },
      borderRadius: BorderRadius.circular(20),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        constraints: const BoxConstraints(minWidth: 140, minHeight: 55),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [colorScheme.primary, accentColor!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.4),
                      blurRadius: 10,
                      spreadRadius: 1,
                      offset: const Offset(0, 4),
                    ),
                  ]
                  : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 6,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
          border: Border.all(
            color:
                isSelected
                    ? Colors.transparent
                    : colorScheme.primary.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Main text
            Padding(
              padding:
                  isSelected
                      ? const EdgeInsets.only(right: 12)
                      : EdgeInsets.zero,
              child: Text(
                option,
                style: textTheme.titleMedium?.copyWith(
                  color: isSelected ? Colors.white : colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            // Add/Remove indicator
            Positioned(
              top: 0,
              right: 0,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: 1.0,
                child: Container(
                  width: 22,
                  height: 22,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors.white
                            : colorScheme.primary.withValues(alpha: 0.15),
                    shape: BoxShape.circle,
                    boxShadow:
                        isSelected
                            ? [
                              BoxShadow(
                                color: colorScheme.primary.withValues(
                                  alpha: 0.5,
                                ),
                                blurRadius: 5,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null,
                  ),
                  child: Icon(
                    isSelected ? Icons.remove : Icons.add,
                    size: 14,
                    color: isSelected ? colorScheme.primary : Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
