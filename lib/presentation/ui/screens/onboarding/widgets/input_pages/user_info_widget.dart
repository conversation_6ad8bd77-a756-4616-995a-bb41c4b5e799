import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_cubit.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:animate_do/animate_do.dart';

/// Widget for collecting user information during onboarding
class UserInfoWidget extends StatefulWidget {
  /// FormKey passed from parent to control validation
  final GlobalKey<FormState> formKey;
  const UserInfoWidget({super.key, required this.formKey});

  @override
  State<UserInfoWidget> createState() => _UserInfoWidgetState();
}

class _UserInfoWidgetState extends State<UserInfoWidget> {
  final TextEditingController _usernameController = TextEditingController();
  int? _selectedYear;
  String? _selectedGender;

  final List<String> _genderOptions = ['Male', 'Female', 'Prefer not to say'];
  final List<IconData> _genderIcons = [Icons.male, Icons.female, Icons.person_outline];
  final List<int> _yearOptions = List.generate(
    80,
    (index) => DateTime.now().year - index - 13, // Start from 13 years ago
  );

  @override
  void initState() {
    super.initState();

    // Initialize from cubit state if available
    final state = context.read<OnboardingCubit>().state;
    _usernameController.text = state.username ?? '';
    _selectedYear = state.birthYear;
    _selectedGender = state.gender;
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        return Form(
          key: widget.formKey,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FadeInUp(
                  duration: const Duration(milliseconds: 600),
                  child: Text(
                    l10n.onboardingTellUsAboutYourself,
                    style: textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                FadeInUp(
                  delay: const Duration(milliseconds: 100),
                  duration: const Duration(milliseconds: 600),
                  child: Text(
                    l10n.onboardingPersonalizeExperience,
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Username field
                FadeInUp(
                  delay: const Duration(milliseconds: 200),
                  duration: const Duration(milliseconds: 600),
                  child: TextFormField(
                    controller: _usernameController,
                    decoration: InputDecoration(
                      labelText: l10n.username,
                      hintText: l10n.enterYourUsername,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.person_outline),
                        errorText: state.validationErrors['username'],
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return l10n.usernameRequired;
                      }
                      return null;
                    },
                    onChanged: (value) {
                      context.read<OnboardingCubit>().updateUsername(value);
                    },
                  ),
                ),
                const SizedBox(height: 24),

                // Birth year dropdown
                FadeInUp(
                  delay: const Duration(milliseconds: 300),
                  duration: const Duration(milliseconds: 600),
                  child: DropdownButtonFormField<int>(
                    value: _selectedYear,
                    decoration: InputDecoration(
                      labelText: l10n.birthYear,
                      hintText: l10n.selectYourBirthYear,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.cake_outlined),
                        errorText: state.validationErrors['birthYear'],
                    ),
                    items: _yearOptions.map((year) {
                      return DropdownMenuItem<int>(
                        value: year,
                        child: Text(year.toString()),
                      );
                    }).toList(),
                    validator: (value) {
                      if (value == null) {
                        return l10n.birthYearRequired;
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _selectedYear = value;
                      });
                      if (value != null) {
                        context.read<OnboardingCubit>().updateBirthYear(value);
                      }
                    },
                  ),
                ),
                const SizedBox(height: 24),

                // Gender selection
                FadeInUp(
                  delay: const Duration(milliseconds: 400),
                  duration: const Duration(milliseconds: 600),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.gender,
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                        if (state.validationErrors['gender'] != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              state.validationErrors['gender']!,
                              style: TextStyle(
                                color: theme.colorScheme.error,
                                fontSize: 12.0,
                              ),
                            ),
                          ),
                      const SizedBox(height: 12),
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 1.0,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                        itemCount: _genderOptions.length,
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          final gender = _genderOptions[index];
                          final icon = _genderIcons[index];
                          final isSelected = _selectedGender == gender;

                          // Base and accent colors for gradient
                          final baseColor = colorScheme.primary;
                          final accentColor = Color.lerp(baseColor, colorScheme.tertiary, 0.7)!;

                          return InkWell(
                            onTap: () {
                              setState(() {
                                _selectedGender = gender;
                              });
                              context.read<OnboardingCubit>().updateGender(gender);
                            },
                            borderRadius: BorderRadius.circular(20),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              decoration: BoxDecoration(
                                gradient: isSelected
                                  ? LinearGradient(
                                      colors: [baseColor, accentColor],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                  : null,
                                color: isSelected ? null : theme.cardColor,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: isSelected
                                  ? [
                                      BoxShadow(
                                              color: colorScheme.primary
                                                  .withValues(alpha: 0.4),
                                        blurRadius: 10,
                                        spreadRadius: 1,
                                        offset: const Offset(0, 4),
                                      ),
                                    ]
                                  : [
                                      BoxShadow(
                                              color: Colors.black.withValues(
                                                alpha: 0.05,
                                              ),
                                        blurRadius: 6,
                                        spreadRadius: 1,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                border: Border.all(
                                  color: isSelected
                                      ? Colors.transparent
                                            : colorScheme.primary.withValues(
                                              alpha: 0.3,
                                            ),
                                  width: 1.5,
                                ),
                              ),
                              child: Stack(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          icon,
                                          size: 24,
                                          color: isSelected ? Colors.white : colorScheme.primary,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          gender,
                                          style: textTheme.bodyMedium?.copyWith(
                                            color: isSelected ? Colors.white : colorScheme.onSurface,
                                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                            letterSpacing: 0.3,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Selected indicator
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: AnimatedOpacity(
                                      duration: const Duration(milliseconds: 300),
                                      opacity: 1.0,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Colors.white
                                                    : colorScheme.primary
                                                        .withValues(
                                                          alpha: 0.15,
                                                        ),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          child: Icon(
                                            isSelected ? Icons.check : Icons.add,
                                            size: 14,
                                            color: isSelected
                                                ? colorScheme.primary
                                                      : Colors.white.withValues(
                                                        alpha: 0.8,
                                                      ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                // Add extra padding at the bottom to avoid overflow
                const SizedBox(height: 24),
              ],
            ),
            ),
          ),
        );
      },
    );
  }
}
