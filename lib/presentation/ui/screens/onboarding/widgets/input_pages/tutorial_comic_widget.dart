import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';

class TutorialComicWidget extends StatelessWidget {
  final String? lifeSituation;
  final List<String> selectedGoals;

  const TutorialComicWidget({
    super.key,
    this.lifeSituation,
    this.selectedGoals = const [],
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Personalized greeting based on user selections
    final String personalGreeting = _getPersonalizedGreeting(
      lifeSituation,
      selectedGoals,
    );

    // Define key differentiators with icons - focused on just 2 key AI features
    final differentiators = [
      {
        'icon': Icons.psychology_alt_outlined,
        'title': 'AI Personalization',
        'subtitle': 'Adapts to your unique needs',
        'color': colorScheme.primary,
      },
      {
        'icon': Icons.auto_awesome,
        'title': 'Smart Recommendations',
        'subtitle': 'Powered by your goals',
        'color': colorScheme.secondary,
      },
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Stack(
        children: [
          // Subtle background pattern
          Positioned.fill(
            child: CustomPaint(
              painter: PatternPainter(
                colorScheme.primary.withValues(alpha: 0.02),
              ),
            ),
          ),

          // Main content
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // AI-Powered icon and motto
              Center(
                child: FadeInDown(
                  duration: const Duration(milliseconds: 500),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.psychology,
                          size: 60,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ShaderMask(
                        shaderCallback: (bounds) => LinearGradient(
                          colors: [
                            colorScheme.primary,
                            Color.lerp(
                              colorScheme.primary,
                              colorScheme.tertiary,
                              0.5,
                            )!,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          personalGreeting,
                          style: textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 28,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'AI makes it possible',
                        style: textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w900,
                          color: colorScheme.primary,
                          fontSize: 28,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 60),

              // Differentiators - simplified to just 2 key items
              Expanded(
                child: FadeInUp(
                  duration: const Duration(milliseconds: 700),
                  child: Column(
                    children:
                        differentiators.map((item) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 32.0),
                            child: _buildSimplifiedDifferentiator(
                              context,
                              item['icon'] as IconData,
                              item['title'] as String,
                              item['subtitle'] as String,
                              item['color'] as Color,
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Simplified differentiator with more whitespace
  Widget _buildSimplifiedDifferentiator(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    Color accentColor,
  ) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Colored icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 32, color: accentColor),
          ),
          const SizedBox(width: 20),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  subtitle,
                  style: textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to create personalized greeting based on life situation and goals
  String _getPersonalizedGreeting(String? lifeSituation, List<String> goals) {
    if (lifeSituation == null && goals.isEmpty) {
      return "Your journey begins here";
    }

    if (goals.isNotEmpty && goals.length == 1) {
      return "Your path to ${goals[0]}";
    }

    if (goals.length > 1) {
      return "Your path to mindfulness";
    }

    if (lifeSituation != null && lifeSituation.isNotEmpty) {
      return "Personalized for your life";
    }

    return "A journey just for you";
  }
}

// Custom painter for subtle background pattern
class PatternPainter extends CustomPainter {
  final Color color;

  PatternPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 0.8
          ..style = PaintingStyle.stroke;

    // Draw fewer diagonal lines for a cleaner look
    for (var i = 0.0; i < size.width + size.height; i += 80.0) {
      canvas.drawLine(Offset(i, 0), Offset(0, i), paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
