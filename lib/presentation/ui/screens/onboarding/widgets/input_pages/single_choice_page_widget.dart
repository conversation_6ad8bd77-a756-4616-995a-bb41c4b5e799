import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';

class SingleChoicePageWidget extends StatelessWidget {
  final String title;
  final String body;
  final List<String> options;
  final String? selectedOption;
  final ValueChanged<String?> onSelectionChanged;
  final String? errorText; // Optional error text

  const SingleChoicePageWidget({
    super.key,
    required this.title,
    required this.body,
    required this.options,
    required this.selectedOption,
    required this.onSelectionChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 100),
            child: ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    colors: [
                      colorScheme.primary,
                      Color.lerp(
                        colorScheme.primary,
                        colorScheme.secondary,
                        0.5,
                      )!,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
            child: Text(
              title,
              style: textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 28,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Subtitle with enhanced styling
          FadeInUp(
            delay: const Duration(milliseconds: 200),
            child: Text(
              body,
              style: textTheme.bodyLarge?.copyWith(
                height: 1.5,
                fontSize: 16,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),

          // Options (Using Wrap for responsiveness)
          Expanded(
            child: FadeInUp(
              delay: const Duration(milliseconds: 300),
              child: SingleChildScrollView(
                // Ensure options are scrollable if many
                physics: const BouncingScrollPhysics(),
                child: Center(
                  child: Wrap(
                    spacing: 16.0, // Horizontal space between chips
                    runSpacing: 16.0, // Vertical space between lines of chips
                    alignment: WrapAlignment.center,
                    children:
                        options.asMap().entries.map((entry) {
                          final index = entry.key;
                          final option = entry.value;
                          final isSelected = selectedOption == option;
                      
                          // Calculate a unique delay for each option to create a cascade effect
                          final cascadeDelay = Duration(
                            milliseconds: 300 + (index * 100),
                          );

                          return FadeInLeft(
                            delay: cascadeDelay,
                            child: _buildEnhancedChoiceChip(
                              option,
                              isSelected,
                              theme,
                              colorScheme,
                              textTheme,
                              index,
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
            ),
          ),

          // Error Text with animation
          if (errorText != null && errorText!.isNotEmpty)
            FadeIn(
              duration: const Duration(milliseconds: 300),
              child: Padding(
                padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        errorText!,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.error,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Enhanced choice chip with gradient and animation
  Widget _buildEnhancedChoiceChip(
    String option,
    bool isSelected,
    ThemeData theme,
    ColorScheme colorScheme,
    TextTheme textTheme,
    int index,
  ) {
    // Generate a subtle color variation based on the option index
    final baseColor =
        isSelected ? colorScheme.primary : theme.scaffoldBackgroundColor;
    final accentColor = Color.lerp(
      baseColor,
      isSelected ? colorScheme.tertiary : colorScheme.primary,
      (index % 3) * 0.2,
    );

    return InkWell(
      onTap: () => onSelectionChanged(option),
      borderRadius: BorderRadius.circular(20),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        constraints: const BoxConstraints(minWidth: 140, minHeight: 60),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [colorScheme.primary, accentColor!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.4),
                      blurRadius: 10,
                      spreadRadius: 1,
                      offset: const Offset(0, 4),
                    ),
                  ]
                  : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 6,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
          border: Border.all(
            color: colorScheme.primary.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Main text
            Text(
              option,
              style: textTheme.titleMedium?.copyWith(
                color: isSelected ? Colors.white : colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
            
            // Selected indicator
            if (isSelected)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 22,
                  height: 22,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.5),
                        blurRadius: 5,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.check,
                    size: 14,
                    color: colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
