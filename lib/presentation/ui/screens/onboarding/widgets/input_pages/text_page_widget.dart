import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';

class TextPageWidget extends StatelessWidget {
  final String title;
  final String body;
  final IconData? icon; // Optional icon

  const TextPageWidget({
    super.key,
    required this.title,
    required this.body,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      color: colorScheme.surface,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Optional Icon
          if (icon != null)
            FadeInDown(
              delay: const Duration(milliseconds: 100),
              child: Icon(icon, size: 80, color: colorScheme.primary),
            ),
          if (icon != null) const SizedBox(height: 40),

          // Text Content Area
          Expanded(
            child: Column(
              mainAxisAlignment:
                  MainAxisAlignment.center, // Center text vertically
              children: [
                FadeInUp(
                  delay: const Duration(milliseconds: 200),
                  child: Text(
                    title,
                    style: textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
                FadeInUp(
                  delay: const Duration(milliseconds: 300),
                  child: Text(
                    body,
                    style: textTheme.bodyLarge?.copyWith(
                      height: 1.5,
                    ), // Improved readability
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Spacer to push content up if needed, adjust flex as necessary
          const Spacer(flex: 1),
        ],
      ),
    );
  }
}
