import 'package:flutter/material.dart';

class OnboardingProgressIndicator extends StatelessWidget {
  final int itemCount;
  final int currentIndex;

  const OnboardingProgressIndicator({
    super.key,
    required this.itemCount,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Row(
        children: List.generate(itemCount, (index) {
          final isCompleted = index <= currentIndex;

          // Create segments with spaces between them
          return Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: index == 0 ? 0 : 4,
                right: index == itemCount - 1 ? 0 : 4,
              ),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                height: 4,
                decoration: BoxDecoration(
                  // Use gradient for completed segments, light color for incomplete
                  color:
                      isCompleted ? null : Colors.black.withValues(alpha: 0.08),
                  gradient:
                      isCompleted
                          ? LinearGradient(
                            colors: [
                              colorScheme.primary,
                              colorScheme.primary.withValues(alpha: 0.7),
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          )
                          : null,
                  borderRadius: BorderRadius.circular(4),
                  boxShadow:
                      isCompleted
                          ? [
                            BoxShadow(
                              color: colorScheme.primary.withValues(
                                alpha: 0.25,
                              ),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ]
                          : null,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
