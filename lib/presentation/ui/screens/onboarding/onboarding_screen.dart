import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_cubit.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_state.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';

import 'package:detoxme/presentation/ui/screens/onboarding/widgets/input_pages/goals_selector_widget.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/widgets/input_pages/life_situation_selector_widget.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/widgets/input_pages/user_info_widget.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/widgets/progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import 'dart:math';
import 'dart:ui' as ui;

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OnboardingCubit(
        authCubit: context.read<AuthCubit>(),
            profileCubit: context.read<ProfileCubit>(),
      )..initialize(),
      child: const _OnboardingScreenContent(),
    );
  }
}

class _OnboardingScreenContent extends StatefulWidget {
  const _OnboardingScreenContent();

  @override
  State<_OnboardingScreenContent> createState() =>
      _OnboardingScreenContentState();
}

class _OnboardingScreenContentState extends State<_OnboardingScreenContent>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  // Form key for validating the user info page
  final GlobalKey<FormState> _userInfoFormKey = GlobalKey<FormState>();
  late AnimationController _backgroundAnimationController;
  late Animation<double> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    // Set immersive mode
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive, overlays: []);

    _backgroundAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat(reverse: true);

    _backgroundAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _backgroundAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Add listener to cubit to handle page navigation
    context.read<OnboardingCubit>().stream.listen((state) {
      // Only handle page changes if the current page index is different than the pageController's
      if (_pageController.hasClients &&
          state.currentPageIndex != _pageController.page?.round()) {
        _pageController.animateToPage(
          state.currentPageIndex,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  // Sync page controller changes with the cubit
  void _onPageChanged(int index) {
    // Use a method that's properly exposed by the cubit instead of emit
    // We'll create a custom method in OnboardingCubit for this
    final currentState = context.read<OnboardingCubit>().state;
    if (currentState.currentPageIndex != index) {
      // This manually syncs the page index without validation
      context.read<OnboardingCubit>().syncPageIndex(index);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _backgroundAnimationController.dispose();

    // Restore system UI when disposing
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final size = MediaQuery.of(context).size;

    // Use BlocBuilder to rebuild UI when state changes
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        // Define the 5 main onboarding screens (removed the tutorial comic strip)
        final List<Widget> pages = [
          // 1. Welcome screen with calming nature background
          _buildWelcomeScreen(l10n, theme),

          // 2. Life situation selector (single/parent/professional)
          _buildLifeSituationScreen(l10n, theme, state),

          // 3. Goal setting with icon grid
          _buildGoalsScreen(l10n, theme, state),

          // 4. User information (username, birth year, gender)
          UserInfoWidget(formKey: _userInfoFormKey),

          // 5. Completion screen with progress bar
          _buildCompletionScreen(l10n, theme),
        ];

        final bool isFirstPage = state.currentPageIndex == 0;
        final bool isLastPage = state.currentPageIndex == pages.length - 1;

        return Scaffold(
          body: AnimatedBuilder(
            animation: _backgroundAnimation,
            builder: (context, child) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.lerp(
                        colorScheme.surface,
                        colorScheme.primary,
                        0.05 + 0.05 * _backgroundAnimation.value,
                      )!,
                      Color.lerp(
                        colorScheme.surface,
                        colorScheme.secondary,
                        0.05 + 0.07 * _backgroundAnimation.value,
                      )!,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Decorative elements
                    _buildDecorativeElements(size, colorScheme),

                    // Main content
                    Column(
                      children: [
                        // Progress indicator
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: SafeArea(
                            child: OnboardingProgressIndicator(
                              itemCount: pages.length,
                              currentIndex: state.currentPageIndex,
                            ),
                          ),
                        ),

                        // Main content
                        Expanded(
                          child: PageView(
                            controller: _pageController,
                            onPageChanged: _onPageChanged,
                            physics: const NeverScrollableScrollPhysics(),
                            children: pages,
                          ),
                        ),

                        // Navigation Row
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 20.0,
                            right: 20.0,
                            bottom: 24,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Back button (hide on first page)
                              if (!isFirstPage)
                                IconButton(
                                  onPressed: () {
                                    // Use cubit to navigate back
                                    context
                                        .read<OnboardingCubit>()
                                        .previousPage();
                                  },
                                  icon: const Icon(Icons.arrow_back),
                                  style: IconButton.styleFrom(
                                    foregroundColor: colorScheme.primary,
                                    backgroundColor: Colors.white.withValues(
                                      alpha: 0.8,
                                    ),
                                    padding: const EdgeInsets.all(12),
                                    shape: const CircleBorder(),
                                  ),
                                )
                              else
                                const SizedBox(
                                  width: 48,
                                ), // Smaller spacer for alignment
                              // Next/Finish button - made more visible
                              ElevatedButton(
                                onPressed: () {
                                  if (isLastPage) {
                                    // Complete onboarding
                                    _onCompletionRequested(context);
                                  } else {
                                    // If on the user info page, validate the form first
                                    if (state.currentPageIndex == 3) {
                                      // User info is now the 4th page (index 3)
                                      final isValid =
                                          _userInfoFormKey.currentState
                                              ?.validate() ??
                                          false;
                                      if (!isValid) {
                                        // Show a snackbar with error message
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              l10n.requiredErrorText,
                                              style: TextStyle(
                                                color: Colors.white,
                                              ),
                                            ),
                                            backgroundColor:
                                                theme.colorScheme.error,
                                            duration: const Duration(
                                              seconds: 3,
                                            ),
                                          ),
                                        );
                                        return;
                                      }

                                      // Additional validation for birth year and gender
                                      if (state.birthYear == null ||
                                          state.gender == null ||
                                          state.username == null ||
                                          state.username!.trim().isEmpty) {
                                        // Show a snackbar with error message
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              l10n.requiredErrorText,
                                              style: TextStyle(
                                                color: Colors.white,
                                              ),
                                            ),
                                            backgroundColor:
                                                theme.colorScheme.error,
                                            duration: const Duration(
                                              seconds: 3,
                                            ),
                                          ),
                                        );
                                        return;
                                      }
                                    }
                                    // Proceed to next page
                                    context.read<OnboardingCubit>().nextPage(
                                      l10n,
                                    );
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  elevation: 6,
                                  shadowColor: colorScheme.primary.withValues(
                                    alpha: 0.5,
                                  ),
                                  backgroundColor: colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: 28,
                                  ),

                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      isLastPage
                                          ? _getPersonalizedButtonText(state)
                                          : l10n.onboardingButtonNext,
                                      style: theme.textTheme.titleMedium
                                          ?.copyWith(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            letterSpacing: 0.5,
                                          ),
                                    ),
                                    const SizedBox(width: 8),
                                    Icon(
                                      isLastPage
                                          ? Icons.check_circle
                                          : Icons.arrow_forward,
                                      color: Colors.white,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Handler for completion button
  void _onCompletionRequested(BuildContext context) async {
    // Show loading indicator
    final scaffold = ScaffoldMessenger.of(context);
    scaffold.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text('Saving profile...'),
          ],
        ),
        duration: const Duration(seconds: 10),
      ),
    );

    // Store the cubit reference before the async gap
    final onboardingCubit = context.read<OnboardingCubit>();

    try {
      // Use cubit to complete onboarding
      await onboardingCubit.completeOnboarding();

      // Check if the widget is still mounted after the async operation
      if (!context.mounted) return;

      // Dismiss the loading indicator
      scaffold.hideCurrentSnackBar();

      // Listen for completion state, then navigate
      if (onboardingCubit.state is OnboardingCompletedState) {
        // Show success message
        scaffold.showSnackBar(
          SnackBar(
            content: Text('Profile saved successfully!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );

        // Restore system UI before navigating
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

        // Wait a moment to show the success message
        await Future.delayed(const Duration(seconds: 2));

        // Check if the widget is still mounted after the delay
        if (!context.mounted) return;

        // Navigate to the authentication screen after onboarding
        context.go(AppRoutes.auth);
      } else {
        // Show error message
        scaffold.showSnackBar(
          SnackBar(
            content: Text('Error saving profile'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Check if the widget is still mounted after the error
      if (!context.mounted) return;

      // Dismiss the loading indicator
      scaffold.hideCurrentSnackBar();

      // Show error message
      scaffold.showSnackBar(
        SnackBar(
          content: Text('Error saving profile: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Decorative background elements
  Widget _buildDecorativeElements(Size size, ColorScheme colorScheme) {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // Top right circle - blurred
            Positioned(
              top: -size.height * 0.1 + (30 * _backgroundAnimation.value),
              right: -size.width * 0.1 - (20 * _backgroundAnimation.value),
              child: Container(
                width: size.width * 0.4,
                height: size.width * 0.4,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      colorScheme.primary.withValues(
                        alpha: 0.12,
                      ), // Increased opacity
                      colorScheme.primary.withValues(
                        alpha: 0.02,
                      ), // Increased opacity
                    ],
                    stops: const [0.2, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.primary.withValues(
                        alpha: 0.08,
                      ), // Increased opacity
                      blurRadius: 25, // Slightly reduced blur
                      spreadRadius: 10,
                    ),
                  ],
                ),
              ),
            ),

            // Bottom left circle - blurred
            Positioned(
              bottom: -size.height * 0.05 - (25 * _backgroundAnimation.value),
              left: -size.width * 0.15 + (15 * _backgroundAnimation.value),
              child: Container(
                width: size.width * 0.5,
                height: size.width * 0.5,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      colorScheme.secondary.withValues(
                        alpha: 0.12,
                      ), // Increased opacity
                      colorScheme.secondary.withValues(
                        alpha: 0.02,
                      ), // Increased opacity
                    ],
                    stops: const [0.2, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.secondary.withValues(
                        alpha: 0.08,
                      ), // Increased opacity
                      blurRadius: 25, // Slightly reduced blur
                      spreadRadius: 10,
                    ),
                  ],
                ),
              ),
            ),

            // Small floating bubbles - blurred and slightly more visible
            for (int i = 0; i < 4; i++)
              Positioned(
                top:
                    size.height *
                    ((0.2 + 0.15 * i) +
                        0.05 * sin((_backgroundAnimation.value * pi) + i)),
                right:
                    size.width *
                    ((0.1 + 0.15 * i % 3) +
                        0.05 * cos((_backgroundAnimation.value * pi) + i)),
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(
                    sigmaX: 6, // Slightly reduced blur
                    sigmaY: 6, // Slightly reduced blur
                  ),
                  child: Container(
                    width: 8 + (i % 3) * 4,
                    height: 8 + (i % 3) * 4,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colorScheme.primary.withValues(
                        alpha: 0.15 - (i % 5) * 0.01,
                      ), // Increased opacity
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withValues(
                            alpha: 0.08,
                          ), // Increased opacity
                          blurRadius: 6, // Slightly reduced blur
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Small accent bubbles on left side - blurred and slightly more visible
            for (int i = 0; i < 3; i++)
              Positioned(
                top:
                    size.height *
                    ((0.3 + 0.2 * i) -
                        0.04 * cos((_backgroundAnimation.value * pi) + i + 1)),
                left:
                    size.width *
                    ((0.08 + 0.07 * i % 3) -
                        0.03 * sin((_backgroundAnimation.value * pi) + i + 2)),
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(
                    sigmaX: 6, // Slightly reduced blur
                    sigmaY: 6, // Slightly reduced blur
                  ),
                  child: Container(
                    width: 6 + (i % 4) * 4,
                    height: 6 + (i % 4) * 4,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colorScheme.tertiary.withValues(
                        alpha: 0.15 - (i % 4) * 0.01,
                      ), // Increased opacity
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.tertiary.withValues(
                            alpha: 0.08,
                          ), // Increased opacity
                          blurRadius: 6, // Slightly reduced blur
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  // Custom welcome screen with enhanced visuals
  Widget _buildWelcomeScreen(AppLocalizations l10n, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Spacer(),
          // Logo-like icon animation
          FadeInDown(
            delay: const Duration(milliseconds: 100),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.spa_outlined,
                size: 80,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(height: 40),

          // Welcome text with enhanced typography
          FadeInUp(
            delay: const Duration(milliseconds: 200),
            child: Text(
              l10n.adultOnboardingWelcomeTitle,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
                fontSize: 32,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),

          FadeInUp(
            delay: const Duration(milliseconds: 300),
            child: Text(
              l10n.adultOnboardingWelcomeBody,
              style: theme.textTheme.bodyLarge?.copyWith(
                height: 1.6,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(flex: 2),
        ],
      ),
    );
  }

  // Custom life situation screen with enhanced visuals and grid layout
  Widget _buildLifeSituationScreen(
    AppLocalizations l10n,
    ThemeData theme,
    OnboardingState state,
  ) {
    // Extract values from state
    final selectedLifeSituation = state.selectedLifeSituation;
    final lifeSituationErrorText = state.validationErrors['lifeSituation'];

    return LifeSituationSelectorWidget(
        title: l10n.adultOnboardingFamilyTitle,
        body: l10n.adultOnboardingFamilyBody,
        options: [
          l10n.adultOnboardingFamilyOptionSingle,
          l10n.adultOnboardingFamilyOptionInRelationship,
          l10n.adultOnboardingFamilyOptionHaveChildren,
          l10n.adultOnboardingFamilyOptionPreferNotToSay,
        ],
      selectedOption: selectedLifeSituation,
        onSelectionChanged:
          (value) => context.read<OnboardingCubit>().selectLifeSituation(value),
      errorText: lifeSituationErrorText,
    );
  }



  // Custom goals screen with enhanced visuals
  Widget _buildGoalsScreen(
    AppLocalizations l10n,
    ThemeData theme,
    OnboardingState state,
  ) {
    // Extract values from state
    final selectedGoals = state.selectedGoals;
    final goalsErrorText = state.validationErrors['goals'];
    final goalOptions = [
          l10n.adultOnboardingGoalReduceScreenTime,
          l10n.adultOnboardingGoalImproveFocus,
          l10n.adultOnboardingGoalBeMorePresent,
          l10n.adultOnboardingGoalSpendMoreTimeFamily,
          l10n.adultOnboardingGoalDigitalDetox,
          l10n.adultOnboardingGoalImproveSleep,
          l10n.adultOnboardingGoalOther,
    ];
    final goalIcons = [
      Icons.timelapse_rounded, // Reduce screen time
      Icons.bolt_rounded, // Improve focus
      Icons.self_improvement_rounded, // Be more present
      Icons.family_restroom_rounded, // Spend more time with family
      Icons.spa_rounded, // Digital detox
      Icons.nightlight_round, // Improve sleep
      Icons.lightbulb_outline_rounded, // Other
    ];
    return GoalsSelectorWidget(
      title: l10n.adultOnboardingGoalsTitle,
      body: l10n.adultOnboardingGoalsBody,
      options: goalOptions,
      icons: goalIcons,
      selectedOptions: selectedGoals,
      maxSelection: 3,
      onOptionSelected: (option) {
        if (selectedGoals.length < 3 && !selectedGoals.contains(option)) {
          context.read<OnboardingCubit>().addGoal(option);
        }
      },
      onOptionDeselected: (option) {
        context.read<OnboardingCubit>().removeGoal(option);
      },
      errorText: goalsErrorText,
    );
  }

  // Custom completion screen with enhanced visuals
  Widget _buildCompletionScreen(AppLocalizations l10n, ThemeData theme) {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        // Create personalized completion message based on selections
        final String personalizedTitle = _getPersonalizedCompletionTitle(
          state.selectedLifeSituation,
          state.selectedGoals,
        );

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Spacer(),
              // AI-Powered animated effect
              FadeInDown(
                delay: const Duration(milliseconds: 100),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Outer pulse effect
                    _buildAnimatedPulse(
                      size: 120,
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      duration: const Duration(seconds: 3),
                    ),
                    // Middle layer
                    _buildAnimatedPulse(
                      size: 110,
                      color: theme.colorScheme.primary.withValues(alpha: 0.15),
                      duration: const Duration(seconds: 2),
                    ),
                    // Inner layer
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    // AI icon
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.psychology,
                          size: 50,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: 5),
                        Text(
                          "AI",
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    // Neural connections
                    Positioned.fill(
                      child: CustomPaint(
                        painter: NeuralNetworkPainter(
                          theme.colorScheme.primary.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Personalized success text with enhanced typography
              FadeInUp(
                delay: const Duration(milliseconds: 200),
                child: ShaderMask(
                  shaderCallback:
                      (bounds) => LinearGradient(
                        colors: [
                          theme.colorScheme.primary,
                          Color.lerp(
                            theme.colorScheme.primary,
                            theme.colorScheme.tertiary,
                            0.5,
                          )!,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                  child: Text(
                    personalizedTitle,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 32,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 24),

              FadeInUp(
                delay: const Duration(milliseconds: 300),
                child: Text(
                  _getPersonalizedCompletionBody(state.selectedGoals),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    height: 1.6,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 40),

              // AI-Powered features section with Coming Soon button
              FadeInUp(
                delay: const Duration(milliseconds: 400),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.auto_awesome,
                            color: theme.colorScheme.secondary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "AI-POWERED FEATURES",
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.secondary,
                              letterSpacing: 1,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              "COMING SOON",
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildAIFeatureItem(
                        theme,
                        Icons.analytics_outlined,
                        "Smart Analytics",
                        "AI monitors your progress and adapts in real-time",
                      ),
                      const SizedBox(height: 12),
                      _buildAIFeatureItem(
                        theme,
                        Icons.assistant,
                        "Personal AI Assistant",
                        "Get intelligent guidance throughout your journey",
                      ),
                      const SizedBox(height: 20),
                      // Coming Soon button
                      ElevatedButton(
                        onPressed: () {
                          // Show a snackbar to indicate this feature is coming soon
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                "AI-based learning and personalized suggestions coming soon!",
                                style: TextStyle(color: Colors.white),
                              ),
                              backgroundColor: theme.colorScheme.secondary,
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.secondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 32,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.rocket_launch, size: 18),
                            const SizedBox(width: 8),
                            Text(
                              "AI Learning & Suggestions",
                              style: theme.textTheme.labelLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const Spacer(flex: 2),
            ],
          ),
        );
      },
    );
  }

  // Helper method to build animated pulse effect
  Widget _buildAnimatedPulse({
    required double size,
    required Color color,
    required Duration duration,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.85, end: 1.0),
      duration: duration,
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(shape: BoxShape.circle, color: color),
          ),
        );
      },
    );
  }

  // Helper method to build AI feature item
  Widget _buildAIFeatureItem(
    ThemeData theme,
    IconData icon,
    String title,
    String subtitle,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: theme.colorScheme.primary, size: 18),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
      ],
    );
  }

  // Helper method to create personalized completion title
  String _getPersonalizedCompletionTitle(
    String? lifeSituation,
    List<String> goals,
  ) {
    if (goals.isNotEmpty) {
      if (goals.length == 1) {
        return "AI Ready for ${goals[0]}!";
      } else {
        return "Your AI Journey is Ready!";
      }
    }

    if (lifeSituation != null && lifeSituation.isNotEmpty) {
      return "AI Tailored for Your Life!";
    }

    return "Your AI Experience is Ready!";
  }

  // Helper method to create personalized completion body text
  String _getPersonalizedCompletionBody(List<String> goals) {
    if (goals.isEmpty) {
      return "Our AI has prepared everything to help you achieve digital balance.";
    }

    if (goals.length == 1) {
      return "Our AI has tailored your experience to help you ${goals[0].toLowerCase()}.";
    }

    return "Our AI has created a personalized experience based on your unique goals.";
  }

  // Helper method to create personalized button text
  String _getPersonalizedButtonText(OnboardingState state) {
    final l10n = AppLocalizations.of(context)!;

    // Let's personalize the completion button based on user's choices
    if (state.selectedGoals.contains(
      l10n.adultOnboardingGoalReduceScreenTime,
    )) {
      return l10n.startMyDetoxJourney;
    } else if (state.selectedGoals.contains(
      l10n.adultOnboardingGoalBeMorePresent,
    )) {
      return l10n.startBeingPresent;
    } else if (state.selectedGoals.contains(
      l10n.adultOnboardingGoalDigitalDetox,
    )) {
      return l10n.startMyDetox;
    } else {
      return l10n.getStarted;
    }
  }


}

// Neural network painter for visual effect
class NeuralNetworkPainter extends CustomPainter {
  final Color color;

  NeuralNetworkPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

    final nodePaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size.width / 2;
    final center = Offset(size.width / 2, size.height / 2);

    // Draw outer nodes
    const nodeCount = 8;
    final nodes = <Offset>[];

    for (var i = 0; i < nodeCount; i++) {
      final angle = 2 * 3.14159 * i / nodeCount;
      final x = center.dx + radius * 0.8 * cos(angle);
      final y = center.dy + radius * 0.8 * sin(angle);
      final nodePosition = Offset(x, y);

      nodes.add(nodePosition);
      canvas.drawCircle(nodePosition, 2, nodePaint);
    }

    // Connect nodes to center
    for (var node in nodes) {
      canvas.drawLine(center, node, paint);
    }

    // Connect adjacent nodes
    for (var i = 0; i < nodes.length; i++) {
      final next = (i + 1) % nodes.length;
      if (i % 2 == 0) {
        canvas.drawLine(nodes[i], nodes[next], paint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
