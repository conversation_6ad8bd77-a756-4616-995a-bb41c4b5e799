import 'package:detoxme/core/services/notification_service.dart';
import 'package:detoxme/core/services/logger_service.dart';
import 'package:detoxme/core/services/analytics_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import 'package:detoxme/core/services/service_locator.dart';

/// Handles initialization of core external services.
/// Dependency registration is handled by [initializeServiceLocator].
class ServiceInitializer {
  /// Initialize core external services like Supabase, Firebase, Logger, Analytics.
  static Future<void> initialize() async {
    // Initialize logger first as other services might use it.
    final loggerService = serviceLocator<LoggerService>();

    try {
      // Initialize Supabase
      await _initializeSupabase(loggerService);

      // Initialize Firebase
      await _initializeFirebase(loggerService);

      // Initialize analytics after Supa<PERSON> is ready
      await _initializeAnalytics(loggerService);

      // Initialize NotificationService (requires registration first)
      await _initializeNotificationService(loggerService);

      // Log successful initialization
      loggerService.info(
        'ServiceInitializer',
        'External services initialized successfully',
      );
    } catch (e, stackTrace) {
      // Log initialization error
      loggerService.error(
        'ServiceInitializer',
        'Error initializing external services',
        e,
        stackTrace,
      );
      rethrow; // Rethrow to allow caller to handle the error
    }
  }

  /// Initialize Supabase
  static Future<void> _initializeSupabase(LoggerService loggerService) async {
    // Check if Supabase is already initialized (idempotency)
    try {
      Supabase.instance.client; // Access client
      loggerService.info(
        'ServiceInitializer',
        'Supabase already initialized, skipping',
      );
      return;
    } catch (e) {
      loggerService.info(
        'ServiceInitializer',
        'Initializing Supabase for the first time',
      );
    }

    final supabaseUrl = dotenv.env['SUPABASE_URL'];
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];

    if (supabaseUrl == null || supabaseAnonKey == null) {
      throw Exception(
        'Supabase URL or Anon Key not found in environment variables',
      );
    }

    loggerService.info('ServiceInitializer', 'Initializing Supabase');
    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
    loggerService.info('ServiceInitializer', 'Supabase initialized');
  }

  /// Initialize Firebase
  static Future<void> _initializeFirebase(LoggerService loggerService) async {
    loggerService.info('ServiceInitializer', 'Initializing Firebase');
    // Ensure Firebase is initialized only once
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
      loggerService.info('ServiceInitializer', 'Firebase initialized');
    } else {
      loggerService.info(
        'ServiceInitializer',
        'Firebase already initialized, skipping',
      );
    }
  }

  /// Initialize Analytics
  static Future<void> _initializeAnalytics(LoggerService loggerService) async {
    loggerService.info('ServiceInitializer', 'Initializing Analytics');
    final analyticsService = serviceLocator<AnalyticsService>();
    await analyticsService.initialize(
      supabaseClient: serviceLocator<SupabaseClient>(), // Get client from SL
      enabled: true,
    );
    loggerService.info('ServiceInitializer', 'Analytics initialized');
  }

  /// Initialize NotificationService
  static Future<void> _initializeNotificationService(
    LoggerService loggerService,
  ) async {
    loggerService.info(
      'ServiceInitializer',
      'Initializing Notification Service',
    );
    final notificationService = serviceLocator<NotificationService>();
    await notificationService.initialize();
    loggerService.info(
      'ServiceInitializer',
      'Notification Service initialized',
    );
  }
}
