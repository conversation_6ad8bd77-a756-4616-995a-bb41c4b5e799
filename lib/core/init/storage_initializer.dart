import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:detoxme/core/constants/hive_boxes.dart';
import 'package:detoxme/core/constants/hive_constants.dart';
import 'package:detoxme/data/local/models/task_hive_model.dart';
import 'package:detoxme/data/local/models/player_stats_hive_model.dart';
import 'package:detoxme/data/local/models/queued_update_hive_model.dart';
import 'package:detoxme/data/local/models/failed_update_hive_model.dart';
import 'package:detoxme/data/local/models/detox_points_model.dart';
import 'package:detoxme/data/local/models/reward_model.dart';
import 'package:detoxme/data/local/models/user_profile_hive_model.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

/// Handles initialization of local storage (Hive)
class StorageInitializer {
  // Keep track of initialized boxes to prevent duplicate initialization
  static final Set<String> _initializedBoxes = {};
  
  /// Initialize Hive and open required boxes
  static Future<void> initialize() async {
    try {
      // Initialize Hive
      final appDocumentDir = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDir.path);

      // Register adapters
      _registerAdapters();

      // Fix corrupted boxes due to model changes
      await _resetIncompatibleBoxes(appDocumentDir.path);

      // Open required boxes
      await _openBoxes();
      
      if (kDebugMode) {
        print('Hive storage initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Hive storage: $e');
      }
      rethrow;
    }
  }

  /// Reset boxes that might have incompatible data due to model changes
  static Future<void> _resetIncompatibleBoxes(String basePath) async {
    try {
      // List of boxes that might need to be reset due to model changes
      final boxesToReset = [
        'rewards_box',
        'redeemed_rewards',
        'player_stats',
        HiveConstants.foodRecommendationsBox,
        HiveConstants.userVotesBox,
      ];

      for (final boxName in boxesToReset) {
        final boxPath = '$basePath/$boxName.hive';
        final boxFile = File(boxPath);
        if (await boxFile.exists()) {
          try {
            await boxFile.delete();
            if (kDebugMode) {
              print('Deleted incompatible box: $boxName');
            }

            // Also delete the lock file if it exists
            final lockFile = File('$boxPath.lock');
            if (await lockFile.exists()) {
              await lockFile.delete();
            }
          } catch (e) {
            if (kDebugMode) {
              print('Error deleting box $boxName: $e');
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error resetting incompatible boxes: $e');
      }
    }
  }

  /// Register all Hive adapters
  static void _registerAdapters() {
    try {
      Hive.registerAdapter(TaskHiveModelAdapter()); // TypeId: 1
      Hive.registerAdapter(PlayerStatsHiveModelAdapter()); // TypeId: 2
      Hive.registerAdapter(QueuedUpdateHiveModelAdapter()); // TypeId: 3
      Hive.registerAdapter(FailedUpdateHiveModelAdapter()); // TypeId: 4
      Hive.registerAdapter(DetoxPointsModelAdapter()); // TypeId: 5
      Hive.registerAdapter(RewardModelAdapter()); // TypeId: 6
      Hive.registerAdapter(UserProfileHiveModelAdapter()); // TypeId: 7
      
      // Food Mood adapters
      Hive.registerAdapter(FoodRecommendationAdapter());
      Hive.registerAdapter(VoteTypeAdapter());
      Hive.registerAdapter(FoodSortOptionAdapter());
    } catch (e) {
      // Adapters might already be registered, which is fine
      if (kDebugMode) {
        print('Note: Some Hive adapters might already be registered: $e');
      }
    }
  }

  /// Open required Hive boxes
  static Future<void> _openBoxes() async {
    try {
      await Future.wait([
        _safeOpenBox<DetoxPointsModel>('detox_points_box'),
        _safeOpenBox<RewardModel>('rewards_box'),
        // Add sync queue boxes
        _safeOpenLazyBox<QueuedUpdateHiveModel>(HiveBoxes.syncQueueBox),
        _safeOpenLazyBox<FailedUpdateHiveModel>(HiveBoxes.failedQueueBox),
        // Food mood boxes
        _safeOpenBox<FoodRecommendation>(HiveConstants.foodRecommendationsBox),
        _safeOpenBox<String>(HiveConstants.userVotesBox),
        // Add other boxes as needed
      ]);
    } catch (e) {
      if (kDebugMode) {
        print('Error opening Hive boxes: $e');
      }
      rethrow;
    }
  }

  /// Safely open a box, handling if it's already open
  static Future<Box<T>> _safeOpenBox<T>(String boxName) async {
    if (_initializedBoxes.contains(boxName)) {
      return Hive.box<T>(boxName);
    }

    try {
      if (Hive.isBoxOpen(boxName)) {
        _initializedBoxes.add(boxName);
        return Hive.box<T>(boxName);
      } else {
        final box = await Hive.openBox<T>(boxName);
        _initializedBoxes.add(boxName);
        return box;
      }
    } catch (e) {
      if (e.toString().contains('already been initialized')) {
        _initializedBoxes.add(boxName);
        return Hive.box<T>(boxName);
      }
      rethrow;
    }
  }

  /// Safely open a lazy box, handling if it's already open
  static Future<LazyBox<T>> _safeOpenLazyBox<T>(String boxName) async {
    if (_initializedBoxes.contains(boxName)) {
      return Hive.lazyBox<T>(boxName);
    }

    try {
      if (Hive.isBoxOpen(boxName)) {
        _initializedBoxes.add(boxName);
        return Hive.lazyBox<T>(boxName);
      } else {
        final box = await Hive.openLazyBox<T>(boxName);
        _initializedBoxes.add(boxName);
        return box;
      }
    } catch (e) {
      if (e.toString().contains('already been initialized')) {
        _initializedBoxes.add(boxName);
        return Hive.lazyBox<T>(boxName);
      }
      rethrow;
    }
  }

  /// Get a reference to an opened box
  static Box<T> getBox<T>(String boxName) {
    try {
      return Hive.box<T>(boxName);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting box $boxName: $e. Trying to open it...');
      }

      // Try to open the box if it's not already open
      if (!Hive.isBoxOpen(boxName)) {
        try {
          // Open the box synchronously - this is not ideal but helps in emergency situations
          Hive.openBox<T>(boxName);
          return Hive.box<T>(boxName);
        } catch (innerE) {
          if (kDebugMode) {
            print('Failed to open box $boxName: $innerE');
          }
          rethrow;
        }
      }
      rethrow;
    }
  }

  /// Get a reference to an opened lazy box
  static LazyBox<T> getLazyBox<T>(String boxName) {
    try {
      return Hive.lazyBox<T>(boxName);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting lazy box $boxName: $e. Trying to open it...');
      }

      // Try to open the box if it's not already open
      if (!Hive.isBoxOpen(boxName)) {
        try {
          // Open the box synchronously - this is not ideal but helps in emergency situations
          Hive.openLazyBox<T>(boxName);
          return Hive.lazyBox<T>(boxName);
        } catch (innerE) {
          if (kDebugMode) {
            print('Failed to open lazy box $boxName: $innerE');
          }
          rethrow;
        }
      }
      rethrow;
    }
  }
}
