import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Handles initialization of environment variables
class EnvInitializer {
  /// Initialize environment variables
  static Future<void> initialize() async {
    // Get environment from dart-define, default to development
    const env = String.fromEnvironment('ENV', defaultValue: 'development');

    // Construct filename based on environment
    final envFileName = 'env/.env.${env == 'production' ? 'prod' : 'dev'}';

    try {
      await dotenv.load(fileName: envFileName);
      if (kDebugMode) {
        print('Loaded environment: $env ($envFileName)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading $envFileName file: $e');
      }
      // Fail if environment variables are critical
      throw Exception('Failed to load environment variables: $e');
    }
  }
}
