import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:detoxme/core/init/env_initializer.dart';
import 'package:detoxme/core/init/storage_initializer.dart';
import 'package:detoxme/core/init/service_initializer.dart';
import 'package:detoxme/core/constants/app_constants.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;
import 'package:detoxme/core/utils/app_locale_provider.dart';

/// Main app initializer that coordinates all initialization steps
class AppInitializer {
  /// Initialize all app dependencies and return the initial route
  static Future<String> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize environment variables
    await EnvInitializer.initialize();

    // Initialize storage (Hive)
    await StorageInitializer.initialize();

    // Register all dependencies using GetIt
    await initializeServiceLocator();

    // Initialize core external services (Supabase, Firebase, Notifications etc.)
    await ServiceInitializer.initialize();

    // Register locale provider in service locator
    final localeProvider = AppLocaleProvider();
    await localeProvider.initialize();
    if (!serviceLocator.isRegistered<AppLocaleProvider>()) {
    serviceLocator.registerSingleton<AppLocaleProvider>(localeProvider);
    }

    // Check initial authentication state *after* services and DI are ready
    await serviceLocator<AuthCubit>().checkAuthState();

    // Determine initial route based on auth state
    final initialRoute = await _determineInitialRoute();

    // Initialize the router
    _initializeRouter(initialRoute);

    return initialRoute;
  }

  /// Initialize the router with the specified initial route
  static void _initializeRouter(String initialRoute) {
    // Initialize the router, fetching AuthCubit from GetIt
    router = createRouter(
      initialLocation: initialRoute,
      authCubit: serviceLocator<AuthCubit>(), // Use GetIt
    );
  }

  /// Determine the initial route based on authentication and onboarding state
  static Future<String> _determineInitialRoute() async {
    final prefs = serviceLocator<SharedPreferences>(); // Use GetIt
    final bool introCompleted =
        prefs.getBool(AppConstants.introCompletedKey) ?? false;
    final bool onboardingComplete =
        prefs.getBool(AppConstants.onboardingCompleteKey) ?? false;

    // Get current auth state from GetIt
    final authState = serviceLocator<AuthCubit>().state;

    if (!introCompleted) {
      return AppRoutes.intro;
    }

    if (authState is! auth_state.Authenticated) {
      return AppRoutes.auth;
    }

    if (!onboardingComplete) {
      return AppRoutes.onboarding;
    }

    return AppRoutes.dashboard;
  }
}
