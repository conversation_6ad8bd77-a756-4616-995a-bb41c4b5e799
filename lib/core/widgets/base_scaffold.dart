import 'package:detoxme/core/themes/app_themes.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/core/widgets/detox_bottom_nav_bar.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/activity/activity_state.dart';
import 'package:detoxme/domain/entities/activity.dart';


/// A base scaffold that provides common UI elements for all screens
class BaseScaffold extends StatefulWidget {
  /// The body of the scaffold
  final Widget body;

  /// Title text for the app bar
  final String? title;

  /// Icon data for the app bar title icon
  final IconData? titleIconData;

  /// Actions for the app bar
  final List<Widget>? actions;

  /// Background color for the title icon container
  final Color? iconBackgroundColor;

  /// Whether to show the bottom navigation bar
  final bool showBottomNav;

  /// Optional floating action button
  final Widget? floatingActionButton;

  /// Whether the scaffold should be padded
  final bool usePadding;

  /// Whether to use safe area
  final bool useSafeArea;

  /// Background color for the scaffold
  final Color? backgroundColor;

  /// Extra padding to add
  final EdgeInsets? padding;

  /// Resizeability
  final bool resizeToAvoidBottomInset;

  /// Custom app bar if needed
  final PreferredSizeWidget? customAppBar;

  /// Custom back button handler
  final VoidCallback? onBackPressed;

  /// Background color for the app bar
  final Color? appBarBackgroundColor;

  /// Whether to show the back button
  final bool showBackButton;

  /// Creates a [BaseScaffold]
  const BaseScaffold({
    required this.body,
    this.title,
    this.titleIconData,
    this.actions,
    this.iconBackgroundColor,
    this.customAppBar,
    this.showBottomNav = true,
    this.floatingActionButton,
    this.usePadding = true,
    this.useSafeArea = true,
    this.backgroundColor,
    this.padding,
    this.resizeToAvoidBottomInset = true,
    this.onBackPressed,
    this.appBarBackgroundColor,
    this.showBackButton = false,
    super.key,
  });

  /// Creates a [BaseScaffold] with a title in appbar (deprecated, use main constructor instead)
  factory BaseScaffold.withTitle({
    required Widget body,
    required String title,
    IconData? titleIconData,
    List<Widget>? actions,
    bool showBottomNav = true,
    Widget? floatingActionButton,
    bool usePadding = true,
    bool useSafeArea = true,
    Color? backgroundColor,
    EdgeInsets? padding,
    bool resizeToAvoidBottomInset = true,
    VoidCallback? onBackPressed,
    Color? appBarBackgroundColor,
    bool showBackButton = false,
    Key? key,
  }) {
    return BaseScaffold(
      body: body,
      title: title,
      titleIconData: titleIconData,
      actions: actions,
      showBottomNav: showBottomNav,
      floatingActionButton: floatingActionButton,
      usePadding: usePadding,
      useSafeArea: useSafeArea,
      backgroundColor: backgroundColor,
      padding: padding,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      onBackPressed: onBackPressed,
      appBarBackgroundColor: appBarBackgroundColor,
      showBackButton: showBackButton,
      key: key,
    );
  }

  @override
  State<BaseScaffold> createState() => _BaseScaffoldState();
}

class _BaseScaffoldState extends State<BaseScaffold>
    with TickerProviderStateMixin {
  final List<AnimationController> _animationControllers = [];
  final List<Animation<double>> _animations = [];

  @override
  void initState() {
    super.initState();
    // Initialize animation controllers for bottom nav items
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Create controllers for each tab
    _animationControllers.clear();
    _animations.clear();

    for (int i = 0; i < 4; i++) {
      final controller = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 300),
      );

      _animationControllers.add(controller);

      _animations.add(
        CurvedAnimation(parent: controller, curve: Curves.easeOutBack),
      );
    }
  }

  @override
  void dispose() {
    // Dispose all animation controllers
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedIndex = _calculateSelectedIndex(context);

    // Update animations based on selected index
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAnimations(selectedIndex);
    });

    // Create base content
    Widget content = widget.body;

    // Add padding if needed
    if (widget.usePadding) {
      content = Padding(
        padding: widget.padding ?? const EdgeInsets.all(16.0),
        child: content,
      );
    }

    // Add safe area if needed
    if (widget.useSafeArea) {
      content = content;
    }

    // Build app bar if title is provided or custom app bar exists
    final appBar = _buildAppBar(context);

    // Create the scaffold without bottom navigation
    final scaffold = Scaffold(
      appBar: appBar,
      body: content,
      backgroundColor: widget.backgroundColor ?? theme.scaffoldBackgroundColor,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      floatingActionButton: widget.floatingActionButton,
    );

    // Return scaffold with DetoxBottomNavBar if needed
    return widget.showBottomNav ? DetoxBottomNavBar(child: scaffold) : scaffold;
  }

  /// Builds the app bar
  PreferredSizeWidget? _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);

    // Use custom app bar if provided
    if (widget.customAppBar != null) {
      return widget.customAppBar;
    }

    // If no title or custom app bar, return null
    if (widget.title == null) {
      return null;
    }

    // Build custom app bar with pronounced bottom curve and white overlay
    return PreferredSize(
      preferredSize: const Size.fromHeight(100),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Colored AppBar background with only bottom corners rounded
          Container(
            height: 150,
            decoration: BoxDecoration(
              color: widget.appBarBackgroundColor ?? AppColors.primary,

            ),
            child: AppBar(
              title: Row(
                children: [
                  if (widget.titleIconData != null) ...[
                    if (!widget.showBackButton)
                      Icon(widget.titleIconData, color: Colors.white, size: 24),
                    if (!widget.showBackButton) const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Text(
                      widget.title!,
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              forceMaterialTransparency: false,
              actions: [
                BlocBuilder<ActivityCubit, ActivityState>(
                  buildWhen:
                      (previous, current) =>
                          previous.activeActivityId !=
                              current.activeActivityId ||
                          previous.remainingDuration !=
                              current.remainingDuration,
                  builder: (context, state) {
                    // Only show if there's an active activity and it's not completed
                    if (state.activeActivityId != null &&
                        state.remainingDuration != null &&
                        state.remainingDuration! > Duration.zero) {
                      return _buildActiveActivityIndicator(context, theme);
                    }
                    return const SizedBox.shrink();
                  },
                ),
                // Add activity creation button only in activities tab
                if (_isActivitiesTab(context))
                  IconButton(
                    icon: const Icon(Icons.add, color: Colors.white),
                    onPressed: () => context.push('/activities/create'),
                    tooltip: 'Create Activity',
                  ),
                if (widget.actions != null) ...widget.actions!,
              ],
              leading:
                  (widget.showBackButton || Navigator.of(context).canPop()) &&
                          !_isBottomSheetShown(context)
                      ? Container(
                          margin: const EdgeInsets.only(left: 16),
                          decoration: BoxDecoration(
                            color:
                                widget.iconBackgroundColor ?? AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed:
                                widget.onBackPressed ??
                                () {
                                  // Use GoRouter's context.pop() instead of Navigator.pop()
                                  // This handles the case when we're at a route without a parent route
                                  try {
                                    context.pop();
                                  } catch (e) {
                                    // If we can't pop with GoRouter, try to use Navigator
                                    if (Navigator.of(context).canPop()) {
                                      Navigator.of(context).pop();
                                    } else {
                                      // If we can't pop at all, navigate to the dashboard
                                      context.go(AppRoutes.dashboard);
                                    }
                                  }
                                },
                          ),
                        )
                      : null,
            ),
          ),
          // White overlay container with top corners rounded, overlapping the AppBar
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                  topRight: Radius.circular(32),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Check if a modal bottom sheet is currently shown
  bool _isBottomSheetShown(BuildContext context) {
    return ModalRoute.of(context)?.isCurrent != true;
  }

  Widget _buildActiveActivityIndicator(BuildContext context, ThemeData theme) {
    final activityCubit = context.read<ActivityCubit>();
    final activity = activityCubit.activeActivity;

    if (activity == null) return const SizedBox.shrink();

    return BlocBuilder<ActivityCubit, ActivityState>(
      buildWhen:
          (previous, current) =>
              previous.remainingDuration != current.remainingDuration,
      builder: (context, state) {
        // Calculate progress percentage
        final totalSeconds = activity.duration * 60;
        final elapsedSeconds =
            totalSeconds - (state.remainingDuration?.inSeconds ?? 0);
        final progress = totalSeconds > 0 ? elapsedSeconds / totalSeconds : 0.0;

        return Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: IconButton(
            onPressed: () {
              _showFullScreenTimer(context, activity);
            },
            iconSize: 40,
            icon: Stack(
              alignment: Alignment.center,
              children: [
                // Simple progress ring
                SizedBox(
                  width: 36,
                  height: 36,
                  child: CircularProgressIndicator(
                    value: progress,
                    strokeWidth: 3,
                    backgroundColor: theme.colorScheme.surfaceContainerHighest
                        .withAlpha(76),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                // Center icon
                Icon(
                  Icons.directions_run,
                  color: theme.colorScheme.primary,
                  size: 18,
                ),
              ],
            ),
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.surface.withAlpha(204),
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
            ),
          ),
        );
      },
    );
  }

  void _showFullScreenTimer(BuildContext context, Activity activity) {
    final colorScheme = Theme.of(context).colorScheme;

    // Use showGeneralDialog instead of showDialog to get full screen coverage
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (dialogContext, animation, secondaryAnimation) {
        return BlocListener<ActivityCubit, ActivityState>(
          listener: (listenerContext, state) {
            // Close dialog if activity is completed or canceled
            if (state.activeActivityId == null &&
                state.completedActivityId != null) {
              Navigator.of(dialogContext).pop();

              // Use a post-frame callback to ensure the dialog is fully dismissed
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted) {
                  // Show celebration screen
                  _showCompletionCelebration(context, activity);
                }
              });
            } else if (state.activeActivityId == null) {
              // Activity was canceled
              Navigator.of(dialogContext).pop();
            }
          },
          child: BlocBuilder<ActivityCubit, ActivityState>(
            buildWhen:
                (previous, current) =>
                    previous.remainingDuration != current.remainingDuration,
            builder: (blocContext, state) {
              // Calculate progress percentage
              final totalSeconds = activity.duration * 60;
              final elapsedSeconds =
                  totalSeconds - (state.remainingDuration?.inSeconds ?? 0);
              final progress =
                  totalSeconds > 0 ? elapsedSeconds / totalSeconds : 0.0;

              // Format time
              final minutes = state.remainingDuration?.inMinutes ?? 0;
              final seconds = (state.remainingDuration?.inSeconds ?? 0) % 60;
              final timeString =
                  '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

              // Use a dark navy background color
              final backgroundColor = const Color(0xFF2C3E50);

              return Material(
                color: backgroundColor,
                child: SizedBox(
                  width: MediaQuery.of(dialogContext).size.width,
                  height: MediaQuery.of(dialogContext).size.height,
                  child: Stack(
                    children: [
                      // Close button at top right
                      Positioned(
                        top: 54,
                        right: 16,
                        child: IconButton(
                          icon: Icon(Icons.close, color: colorScheme.onSurface),
                          onPressed: () => Navigator.of(dialogContext).pop(),
                        ),
                      ),

                      // Main content
                      Column(
                        children: [
                          // Header with title
                          Padding(
                            padding: EdgeInsets.only(
                              left: 24,
                              right: 24,
                              top: MediaQuery.of(context).padding.top + 24,
                              bottom: 0,
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Activity in Progress',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: colorScheme.onSurface,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  activity.title,
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: colorScheme.onSurface,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      activity.category.icon,
                                      color: AppColors.primary,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      activity.category.displayName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: colorScheme.onSurface.withAlpha(
                                          180,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // Timer with circular progress (expanded to take available space)
                          Expanded(
                            child: Center(
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  // Progress circle
                                  SizedBox(
                                    width: 300,
                                    height: 300,
                                    child: CircularProgressIndicator(
                                      value: progress,
                                      strokeWidth: 15,
                                      backgroundColor: colorScheme.onSurface
                                          .withAlpha(30),
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.blue.shade300,
                                      ),
                                    ),
                                  ),

                                  // Timer text
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        timeString,
                                        style: TextStyle(
                                          fontSize: 72,
                                          fontWeight: FontWeight.bold,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                      Text(
                                        'REMAINING',
                                        style: TextStyle(
                                          fontSize: 18,
                                          color: colorScheme.onSurface
                                              .withAlpha(200),
                                          letterSpacing: 2,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Motivation text
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32.0,
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: const Color(0xFF5D8B6E),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.emoji_events,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    "You're doing great! Stay focused and complete this activity to earn valuable Detox Points.",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Bottom button
                          Padding(
                            padding: EdgeInsets.only(
                              left: 24,
                              right: 24,
                              bottom:
                                  MediaQuery.of(dialogContext).padding.bottom +
                                  24,
                              top: 24,
                            ),
                            child: Center(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  _showStopConfirmationDialog(
                                    dialogContext,
                                    activity,
                                  );
                                },
                                icon: const Icon(Icons.stop),
                                label: const Text('Stop Activity'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red.shade700,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 32,
                                    vertical: 16,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Shows a celebration UI when an activity is completed
  void _showCompletionCelebration(BuildContext context, Activity activity) {
    // Check if context is still valid
    if (!context.mounted) return;
    
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Show dialog with animation and points reward
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: SizedBox(
            width: screenWidth * 0.8,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Celebration icon
                Icon(
                  Icons.celebration,
                  color: theme.colorScheme.primary,
                  size: 64,
                ),
                const SizedBox(height: 16),
                // Completion title
                Text(
                  'Activity Completed!',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // Activity title
                Text(
                  activity.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                // Points earned
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.star,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '+${activity.duration} Points',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                // Button
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text('Great!'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showStopConfirmationDialog(BuildContext context, Activity activity) {
    final theme = Theme.of(context);
    final activityCubit = context.read<ActivityCubit>();

    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            backgroundColor: theme.colorScheme.surface,
            title: Row(
              children: [
                Icon(
                  Icons.sentiment_dissatisfied,
                  color: theme.colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    "Hold on!",
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Come on, don't give up now! You're doing great - bring it to the end and earn those valuable Detox Points!",
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.5,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '+${activity.duration} Points waiting for you!',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  "Keep Going! 💪",
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  activityCubit.cancelActivity();
              
                  // Close dialog
                  Navigator.of(dialogContext).pop();
              
                  // Close full screen timer if it's open
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.errorContainer,
                  foregroundColor: theme.colorScheme.onErrorContainer,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text("Yes, I'm sure - Stop"),
              ),
            ],
            actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          ),
    );
  }

  /// Calculate the selected index based on current route
  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;

    if (location.startsWith(AppRoutes.dashboard)) return 0;
    if (location.startsWith(AppRoutes.activities)) return 1;
    if (location.startsWith(AppRoutes.categories)) return 2;
    if (location.startsWith(AppRoutes.profile)) return 3;
    return 0;
  }

  /// Check if current route is activities tab
  bool _isActivitiesTab(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    return location.startsWith(AppRoutes.activities);
  }

  /// Update animations based on selected index
  void _updateAnimations(int selectedIndex) {
    for (int i = 0; i < _animationControllers.length; i++) {
      if (i == selectedIndex) {
        _animationControllers[i].forward();
      } else {
        _animationControllers[i].reverse();
      }
    }
  }
}


