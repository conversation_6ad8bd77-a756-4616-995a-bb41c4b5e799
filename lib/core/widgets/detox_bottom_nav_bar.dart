import 'package:detoxme/core/themes/app_themes.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:go_router/go_router.dart';
import 'package:floaty_nav_bar/floaty_nav_bar.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_state.dart';
// Removed mood-related imports
import 'package:detoxme/presentation/ui/screens/dashboard/widgets/horizontal_mood_o_meter.dart';

/// A floating navigation bar for the DetoxMe app with action buttons
class DetoxBottomNavBar extends StatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Constructor for DetoxBottomNavBar
  const DetoxBottomNavBar({required this.child, super.key});

  @override
  State<DetoxBottomNavBar> createState() => _DetoxBottomNavBarState();
}

class _DetoxBottomNavBarState extends State<DetoxBottomNavBar> {
  bool _isScrollingDown = false;
  bool _isAtTop = true;
  final ScrollController _scrollController = ScrollController();
  // Removed mood color - using fixed AppColors.primary

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    
    // Removed mood color initialization
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_handleScroll)
      ..dispose();
    super.dispose();
  }

  void _handleScroll() {
    if (!_scrollController.hasClients) return;

    // Check if we're at the top of the scroll view
    final bool isAtTop =
        _scrollController.position.pixels <= 10; // 10px tolerance

    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      if (!_isScrollingDown && !isAtTop) {
        setState(() {
          _isScrollingDown = true;
          _isAtTop = isAtTop;
        });
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      if (_isScrollingDown) {
        setState(() {
          _isScrollingDown = false;
          _isAtTop = isAtTop;
        });
      }
    }

    // Always update top status if it changed
    if (_isAtTop != isAtTop) {
      setState(() {
        _isAtTop = isAtTop;
      });
    }
  }

  void _handleNavigation(BuildContext context, int index) {
    final currentLocation = GoRouterState.of(context).uri.path;
    String targetRoute;

    switch (index) {
      case 0:
        targetRoute = AppRoutes.dashboard;
        break;
      case 1:
        targetRoute = AppRoutes.activities;
        break;
      case 2:
        targetRoute = AppRoutes.foodMood;
        break;
      case 3:
        targetRoute = AppRoutes.profile;
        break;
      default:
        return;
    }

    if (currentLocation != targetRoute) {
      context.go(targetRoute);
    }
  }

  // Open mood selector bottom sheet
  void _toggleMoodSelector() {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            margin: const EdgeInsets.all(0),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Title
                  Row(
                    children: [
                      Icon(Icons.mood, color: AppColors.primary, size: 24),
                      const SizedBox(width: 12),
                      Text(
                        'How are you feeling?',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Mood selector
                  HorizontalMoodOMeter(
                    currentMood:
                        context.read<DashboardCubit>().getCurrentMood(),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
    );
  }

  // Build mood-aware floating action button
  FloatyActionButton _buildMoodActionButton(BuildContext context) {
    final theme = Theme.of(context);
    return FloatyActionButton(
      icon: Text(_getCurrentMoodEmoji(), style: const TextStyle(fontSize: 20)),
      backgroundColor: AppColors.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      onTap: () => _toggleMoodSelector(),
    );
  }

  String _getCurrentMoodEmoji() {
    if (context.mounted) {
      final currentMood = context.read<DashboardCubit>().state.currentMood;
      return currentMood?.emoji ?? '😊';
    }
    return '😊';
  }

  // Build avatar or placeholder for profile tab
  Widget _buildProfileAvatar(
    BuildContext context,
    ThemeData theme,
    bool isSelected,
  ) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        if (state is ProfileLoaded && state.profile.avatarUrl != null) {
          return Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    isSelected ? AppColors.primary : Colors.transparent,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: Image.network(
                state.profile.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder:
                    (context, error, stackTrace) => Icon(
                      Icons.person_outline,
                      color: isSelected ? AppColors.primary : null,
                    ),
              ),
            ),
          );
        } else {
          return Icon(
            Icons.person_outline,
            color: isSelected ? AppColors.primary : null,
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedIndex = _calculateSelectedIndex(context);

    return NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollUpdateNotification) {
            if (notification.scrollDelta == null) return false;

            // Check if at top
            final bool isAtTop = notification.metrics.pixels <= 10;

            if (notification.scrollDelta! > 0 && !isAtTop) {
              if (!_isScrollingDown) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {
                      _isScrollingDown = true;
                      _isAtTop = isAtTop;
                    });
                  }
                });
              }
            } else if (notification.scrollDelta! < 0) {
              if (_isScrollingDown) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {
                      _isScrollingDown = false;
                      _isAtTop = isAtTop;
                    });
                  }
                });
              }
            }

            // Always update top status
            if (_isAtTop != isAtTop) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    _isAtTop = isAtTop;
                  });
                }
              });
            }
          }
          return false;
        },
        child: Stack(
          children: [
            // Main content
            widget.child,
            
            // Bottom navigation bar
            Positioned(
              bottom: -8,
              left: 0,
              right: 0,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                transform: Matrix4.translationValues(
                  0,
                  // Show navbar if at top or not scrolling down
                  (_isScrollingDown && !_isAtTop) ? 90 : 0,
                  0,
                ),
                child: FloatyNavBar(
                  selectedTab: selectedIndex,
                  tabs: [
                    FloatyTab(
                      isSelected: selectedIndex == 0,
                      title: AppLocalizations.of(context)!.dashboardTitle,
                      icon: Icon(Icons.home_outlined),
                      onTap: () => _handleNavigation(context, 0),
                      selectedColor: AppColors.primary,
                      titleStyle: theme.textTheme.labelMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    floatyActionButton: _buildMoodActionButton(context),
                    ),
                    FloatyTab(
                      isSelected: selectedIndex == 1,
                      title: AppLocalizations.of(context)!.activitiesTitle,
                      icon: Icon(Icons.directions_run_rounded),
                      onTap: () => _handleNavigation(context, 1),
                      selectedColor: AppColors.primary,
                      titleStyle: theme.textTheme.labelMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    floatyActionButton: _buildMoodActionButton(context),
                    ),
                    FloatyTab(
                      isSelected: selectedIndex == 2,
                      title: AppLocalizations.of(context)!.foodMoodTitle,
                      icon: Icon(Icons.restaurant_menu_rounded),
                      onTap: () => _handleNavigation(context, 2),
                      selectedColor: AppColors.primary,
                      titleStyle: theme.textTheme.labelMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    floatyActionButton: _buildMoodActionButton(context),
                    ),
                    FloatyTab(
                      isSelected: selectedIndex == 3,
                      title: AppLocalizations.of(context)!.profileScreenTitle,
                      icon: _buildProfileAvatar(
                        context,
                        theme,
                        selectedIndex == 3,
                      ),
                      onTap: () => _handleNavigation(context, 3),
                      selectedColor: AppColors.primary,
                      titleStyle: theme.textTheme.labelMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    floatyActionButton: _buildMoodActionButton(context),
                    ),
                  ],
                ),
              ),
            ),
          ],
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;

    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/activities')) return 1;
    if (location.startsWith('/food-mood')) return 2;
    if (location.startsWith('/profile')) return 3;
    return 0;
  }
}
