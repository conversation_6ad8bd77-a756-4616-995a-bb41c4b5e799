import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';

/// A widget that provides different layouts based on screen size
class ResponsiveWidget extends StatelessWidget {
  /// Widget to show on mobile screens
  final Widget mobile;

  /// Widget to show on tablet screens (optional, falls back to mobile)
  final Widget? tablet;

  /// Widget to show on desktop screens (optional, falls back to tablet or mobile)
  final Widget? desktop;

  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    if (ResponsiveHelper.isDesktop(context) && desktop != null) {
      return desktop!;
    } else if (ResponsiveHelper.isTablet(context) && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

/// A responsive container that adjusts its properties based on screen size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? borderRadius;
  final double? elevation;
  final Color? backgroundColor;
  final double? maxWidth;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.backgroundColor,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? ResponsiveHelper.getMaxContentWidth(context),
      ),
      padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
      margin: margin ?? ResponsiveHelper.getResponsiveMargin(context),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(
          borderRadius ?? ResponsiveHelper.getBorderRadius(context),
        ),
        boxShadow:
            elevation != null
                ? [
                  BoxShadow(
                    color: theme.shadowColor.withValues(alpha: 0.1),
                    blurRadius: elevation! * 2,
                    offset: Offset(0, elevation!),
                  ),
                ]
                : null,
      ),
      child: child,
    );
  }
}

/// A responsive grid view that adjusts column count based on screen size
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final EdgeInsets? padding;
  final int? maxColumns;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.padding,
    this.maxColumns,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
      physics: physics,
      shrinkWrap: shrinkWrap,
      gridDelegate: ResponsiveHelper.getResponsiveGridDelegate(
        context,
        childAspectRatio: childAspectRatio,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        maxColumns: maxColumns,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// A responsive text widget that adjusts font size based on screen size
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? fontSizeMultiplier;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontSizeMultiplier,
  });

  @override
  Widget build(BuildContext context) {
    final multiplier =
        fontSizeMultiplier ?? ResponsiveHelper.getFontSizeMultiplier(context);
    final adjustedStyle =
        style?.copyWith(fontSize: (style?.fontSize ?? 14) * multiplier) ??
        TextStyle(fontSize: 14 * multiplier);

    return Text(
      text,
      style: adjustedStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      textScaler: ResponsiveHelper.getTextScaler(context),
    );
  }
}

/// A responsive card widget that adjusts its properties based on screen size
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? elevation;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    Widget card = Card(
      elevation: elevation ?? ResponsiveHelper.getElevation(context),
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getBorderRadius(context, baseRadius: 12),
        ),
      ),
      child: Container(
        width: width ?? ResponsiveHelper.getCardWidth(context),
        height: height,
        padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
        child: child,
      ),
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getBorderRadius(context, baseRadius: 12),
        ),
        child: card,
      );
    }

    return Container(
      margin: margin ?? ResponsiveHelper.getResponsiveMargin(context),
      child: card,
    );
  }
}

/// A responsive button that adjusts its size based on screen size
class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final double? width;
  final double? height;
  final EdgeInsets? padding;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.width,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final buttonHeight = height ?? ResponsiveHelper.getButtonHeight(context);
    final buttonPadding =
        padding ?? ResponsiveHelper.getResponsivePadding(context);

    return SizedBox(
      width: width,
      height: buttonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style:
            style?.copyWith(
              padding: WidgetStateProperty.all(buttonPadding),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getBorderRadius(context, baseRadius: 8),
                  ),
                ),
              ),
            ) ??
            ElevatedButton.styleFrom(
              padding: buttonPadding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getBorderRadius(context, baseRadius: 8),
                ),
              ),
            ),
        child: child,
      ),
    );
  }
}

/// A responsive dialog that adjusts its size based on screen size
class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsets? contentPadding;
  final double? maxWidth;
  final double? maxHeight;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.contentPadding,
    this.maxWidth,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    final dialogWidth = maxWidth ?? ResponsiveHelper.getDialogWidth(context);
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogMaxHeight = maxHeight ?? screenHeight * 0.8;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getBorderRadius(context, baseRadius: 16),
        ),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          maxHeight: dialogMaxHeight,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null)
              Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: ResponsiveText(
                  title!,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
            Flexible(
              child: Padding(
                padding:
                    contentPadding ??
                    ResponsiveHelper.getResponsivePadding(context),
                child: child,
              ),
            ),
            if (actions != null)
              Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
