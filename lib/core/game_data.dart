import 'package:detoxme/domain/entities/level.dart';
import 'package:detoxme/domain/entities/badge.dart';
import 'package:detoxme/domain/entities/collectible_card.dart';

// Static game configuration data

// --- Level Progression ---
// Defines the XP required to reach each level and the level name.
// XP required is the total XP needed *from the start* to reach this level.
// (Alternatively, could store XP needed from previous level)

const List<Level> gameLevels = [
  Level(levelNumber: 1, xpRequired: 0, name: 'Sprout'), // Base level
  Level(levelNumber: 2, xpRequired: 100, name: 'Seedling'),
  Level(levelNumber: 3, xpRequired: 250, name: 'Blossom'),
  Level(levelNumber: 4, xpRequired: 500, name: 'Shining Star'),
  Level(levelNumber: 5, xpRequired: 1000, name: 'Super Helper'),
  Level(levelNumber: 6, xpRequired: 1500, name: 'Digital Detox Novice'),
  Level(levelNumber: 7, xpRequired: 2000, name: 'Digital Detox Apprentice'),
  Level(levelNumber: 8, xpRequired: 3000, name: 'Digital Detox Expert'),
  Level(levelNumber: 9, xpRequired: 4000, name: 'Digital Detox Master'),
  Level(levelNumber: 10, xpRequired: 5000, name: 'Digital Detox Champion'),
];

// Helper function to get level info based on XP
Level getLevelForXp(int xp) {
  Level currentLevel = gameLevels.first;
  for (final level in gameLevels) {
    if (xp >= level.xpRequired) {
      currentLevel = level;
    } else {
      break; // Levels are ordered by xpRequired
    }
  }
  return currentLevel;
}

// Helper function to get XP needed for the *next* level
int getXpForNextLevel(int currentLevelNumber) {
  final nextLevel = gameLevels.firstWhere(
    (level) => level.levelNumber == currentLevelNumber + 1,
    orElse: () => gameLevels.last, // Return last level if already max
  );
  // If already max level, return current level's requirement (or infinity, or handle differently)
  if (nextLevel.levelNumber <= currentLevelNumber) {
    // Find the current level's requirement to show progress within the max level
    final currentLevelData = gameLevels.firstWhere(
      (l) => l.levelNumber == currentLevelNumber,
      orElse: () => gameLevels.last,
    );
    return currentLevelData
        .xpRequired; // Effectively shows XP / XP for current level
  }
  return nextLevel.xpRequired;
}

// --- Badges ---
// List of all achievable badges

const List<Badge> gameBadges = [
  // Task-based badges
  Badge(
    id: 'badge_first_task',
    name: 'First Step!',
    description: 'Completed your first task.',
    imageAssetPath: 'assets/images/badges/first_step.png', // Placeholder path
  ),
  Badge(
    id: 'badge_five_tasks',
    name: 'Task Enthusiast',
    description: 'Completed 5 tasks.',
    imageAssetPath: 'assets/images/badges/enthusiast.png', // Placeholder path
  ),
  Badge(
    id: 'badge_hugger',
    name: 'Warm Hugs',
    description: 'Completed the Family Hug Time task.',
    imageAssetPath: 'assets/images/badges/hugger.png', // Placeholder path
  ),
  Badge(
    id: 'badge_artist',
    name: 'Creative Soul',
    description: 'Completed the Drawing Challenge task.',
    imageAssetPath: 'assets/images/badges/artist.png', // Placeholder path
  ),

  // Level achievement badges
  Badge(
    id: 'badge_level_3',
    name: 'Blossoming',
    description: 'Reached Level 3: Blossom',
    imageAssetPath: 'assets/images/badges/level3.png', // Placeholder path
  ),
  Badge(
    id: 'badge_level_5',
    name: 'Super Helper',
    description: 'Reached Level 5: Super Helper',
    imageAssetPath: 'assets/images/badges/level5.png', // Placeholder path
  ),
  Badge(
    id: 'badge_level_7',
    name: 'Digital Detox Apprentice',
    description: 'Reached Level 7: Digital Detox Apprentice',
    imageAssetPath: 'assets/images/badges/level7.png', // Placeholder path
  ),
  Badge(
    id: 'badge_level_10',
    name: 'Digital Detox Champion',
    description: 'Reached Level 10: Digital Detox Champion',
    imageAssetPath: 'assets/images/badges/level10.png', // Placeholder path
  ),

  // Activity-based badges
  Badge(
    id: 'badge_mindfulness_master',
    name: 'Mindfulness Master',
    description: 'Completed 10 mindfulness activities',
    imageAssetPath: 'assets/images/badges/mindfulness.png', // Placeholder path
  ),
  Badge(
    id: 'badge_outdoor_explorer',
    name: 'Outdoor Explorer',
    description: 'Completed 10 outdoor activities',
    imageAssetPath: 'assets/images/badges/outdoor.png', // Placeholder path
  ),
  Badge(
    id: 'badge_social_butterfly',
    name: 'Social Butterfly',
    description: 'Completed 10 social activities',
    imageAssetPath: 'assets/images/badges/social.png', // Placeholder path
  ),
];

// --- Collectible Cards ---
// List of all collectible cards

const List<CollectibleCard> gameCards = [
  CollectibleCard(
    id: 'card_cat_common',
    name: 'Playful Kitten',
    description: 'Loves chasing yarn!',
    imageAssetPath: 'assets/images/cards/cat_common.png', // Placeholder
    rarity: 'Common',
  ),
  CollectibleCard(
    id: 'card_dog_common',
    name: 'Loyal Pup',
    description: 'Always happy to see you.',
    imageAssetPath: 'assets/images/cards/dog_common.png', // Placeholder
    rarity: 'Common',
  ),
  CollectibleCard(
    id: 'card_unicorn_rare',
    name: 'Sparkle Unicorn',
    description: 'Gallops on rainbows.',
    imageAssetPath: 'assets/images/cards/unicorn_rare.png', // Placeholder
    rarity: 'Rare',
  ),
  CollectibleCard(
    id: 'card_dragon_epic',
    name: 'Mighty Dragon',
    description: 'Guards ancient treasures.',
    imageAssetPath: 'assets/images/cards/dragon_epic.png', // Placeholder
    rarity: 'Epic',
  ),
  // Add more cards
];
