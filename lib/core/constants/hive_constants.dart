/// Constants for Hive storage
class HiveConstants {
  /// Box name for tasks
  static const String taskBox = 'tasks';

  /// Box name for player stats
  static const String playerStatsBox = 'player_stats';

  /// Box name for queued updates
  static const String queuedUpdatesBox = 'queued_updates';

  /// Box name for failed updates
  static const String failedUpdatesBox = 'failed_updates';

  /// Box name for detox points
  static const String detoxPointsBox = 'detox_points';

  /// Box name for rewards
  static const String rewardsBox = 'rewards';

  /// Box name for user profile
  static const String userProfileBox = 'user_profile';

  /// Box name for food recommendations
  static const String foodRecommendationsBox = 'food_recommendations';

  /// Box name for user votes
  static const String userVotesBox = 'user_votes';

  /// Box name for food votes
  static const String foodVotesBox = 'food_votes';

  /// Box for activities
  static const String activitiesBox = 'activities_box';

  /// Box for mood entries
  static const String moodEntriesBox = 'mood_entries_box';

  /// Box for sync queue
  static const String syncQueueBox = 'sync_queue_box';

  /// Type ID for FoodRecommendation adapter
  static const int foodRecommendationTypeId = 10;

  /// Type ID for VoteType adapter
  static const int voteTypeTypeId = 12;

  /// Type ID for FoodSortOption adapter
  static const int foodSortOptionTypeId = 13;
}
