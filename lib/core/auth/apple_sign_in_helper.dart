import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

/// Helper class for Apple Sign-In
class AppleSignInHelper {
  /// The Supabase client instance
  final supabase.SupabaseClient supabaseClient;

  /// Constructor
  AppleSignInHelper({required this.supabaseClient});

  /// Check if Apple Sign-In is available on this device
  Future<bool> isAvailable() async {
    if (!Platform.isIOS && !Platform.isMacOS) {
      return false;
    }
    
    try {
      return await SignInWithApple.isAvailable();
    } catch (e) {
      _logError('Error checking Apple Sign-In availability', e);
      return false;
    }
  }

  /// Sign in with Apple using the simplest possible approach
  Future<supabase.AuthResponse?> signIn() async {
    try {
      // Get Apple credentials
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Get the identity token
      final idToken = credential.identityToken;
      if (idToken == null) {
        _logError('Missing identity token from Apple Sign In', null);
        return null;
      }

      // Sign in to Supabase with the Apple ID token
      return await supabaseClient.auth.signInWithIdToken(
        provider: supabase.OAuthProvider.apple,
        idToken: idToken,
      );
    } catch (e) {
      _logError('Error during Apple Sign-In', e);
      return null;
    }
  }

  /// Log errors in debug mode
  void _logError(String message, Object? e) {
    if (kDebugMode) {
      print('[AppleSignInHelper] $message: $e');
    }
  }
}
