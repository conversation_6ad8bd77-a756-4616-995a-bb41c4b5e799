import 'package:flutter/material.dart';

/// Extension on Color that adds withValue method to replace withOpacity
extension ColorExtension on Color {
  /// Creates a copy of this Color but with the given alpha value.
  /// The alpha value must be between 0 and 255, inclusive.
  ///
  /// This is an alternative to withOpacity that takes an integer alpha value
  /// rather than a double opacity value.
  Color withValue({int? red, int? green, int? blue, int? alpha}) {
    return Color.fromARGB(
      alpha ?? (a * 255.0).round() & 0xff,
      red ?? (r * 255.0).round() & 0xff,
      green ?? (g * 255.0).round() & 0xff,
      blue ?? (b * 255.0).round() & 0xff,
    );
  }
  
  /// Alias for withValue method for consistency with UI code
  /// Uses the same parameters but with a different method name
  Color withValues({int? red, int? green, int? blue, int? alpha}) {
    return withValue(red: red, green: green, blue: blue, alpha: alpha);
  }
}
