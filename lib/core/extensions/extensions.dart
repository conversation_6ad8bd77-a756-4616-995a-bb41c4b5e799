// Placeholder for extensions 

extension DurationFormatting on Duration {
  /// Formats duration into a simple string like "5 min" or "1 hr 30 min".
  /// TODO: Add localization support later using intl package if needed.
  String toFormattedString() {
    if (inHours >= 1) {
      final hours = inHours;
      final minutes = inMinutes.remainder(60);
      if (minutes == 0) {
        return '$hours hr'; // TODO: Localize hr
      } else {
        return '$hours hr $minutes min'; // TODO: Localize hr/min
      }
    } else if (inMinutes >= 1) {
      return '$inMinutes min'; // TODO: Localize min
    } else {
      return '$inSeconds sec'; // TODO: Localize sec
    }
  }
}
