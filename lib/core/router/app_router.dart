import 'dart:async'; // Import async
import 'dart:convert';

import 'package:detoxme/core/constants/app_constants.dart'; // Import for redirect check
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart'; // Import AuthState
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/ui/screens/activities/widgets/activity_detail_screen.dart';
import 'package:detoxme/presentation/ui/screens/auth/auth_screen.dart';
import 'package:detoxme/presentation/ui/screens/auth/pin_screen.dart';
import 'package:detoxme/presentation/ui/screens/auth/phone_input_screen.dart';
import 'package:detoxme/presentation/ui/screens/auth/otp_verification_screen.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/dashboard_screen.dart';
import 'package:detoxme/presentation/ui/screens/activities/activities_screen.dart'; // Import for new activities screen
import 'package:detoxme/presentation/ui/screens/activities/create_activity_screen.dart'; // Import for activity creation
import 'package:detoxme/presentation/ui/screens/onboarding/onboarding_screen.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/intro_screen.dart';
import 'package:detoxme/presentation/ui/screens/profile/profile_screen.dart'
    as profile;
import 'package:detoxme/presentation/ui/screens/profile_edit/edit_profile_screen.dart';
import 'package:detoxme/presentation/ui/screens/profile/activity_history_screen.dart';
import 'package:detoxme/presentation/ui/screens/profile/used_coupons_screen.dart';
import 'package:detoxme/presentation/ui/screens/coupons/coupons_screen.dart'; // Import for coupon screen
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart'; // Import for redirect check
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/presentation/ui/screens/coupons/coupon_details_screen.dart';

import 'package:detoxme/presentation/ui/screens/profile/reward_history_screen.dart'; // Import for reward history screen
import 'package:detoxme/presentation/ui/screens/settings/settings_screen.dart'; // Import for settings screen
import 'package:detoxme/presentation/ui/screens/notifications/notification_screen.dart'; // Import for notification screen
import 'package:detoxme/presentation/ui/screens/food_mood/food_mood_screen.dart';
import 'package:detoxme/presentation/ui/screens/food_mood/food_recommendation_details_screen.dart';
import 'package:detoxme/presentation/ui/screens/celebration/daily_goal_reached_screen.dart';
import 'package:detoxme/presentation/ui/screens/celebration/milestone_reached_screen.dart';

// Define route paths as constants
class AppRoutes {
  static const String intro = '/intro';
  static const String auth = '/auth';
  static const String phoneInput = '/phone-input';
  static const String otpVerification = '/otp-verification';
  static const String pin = '/pin';
  static const String onboarding = '/onboarding';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String activityHistory = '/profile/activity-history';
  static const String usedCoupons = '/profile/used-coupons';
  static const String leaderboard = '/leaderboard';
  static const String settings = '/settings';
  static const String notifications =
      '/notifications'; // Add notifications route
  static const String taskDetail = '/task-detail';
  static const String activityDetail = '/activity-detail';
  static const String themeShowcase = '/theme-showcase';
  static const String activities = '/activities';
  static const String createActivity =
      '/activities/create'; // Add route for creating activities
  static const String categories = '/categories'; // Renamed from tasks
  static const String admin = '/admin'; // Add admin route path
  static const String rewardHistory =
      '/reward-history'; // Add reward history route
  static const String couponDetails =
      '/coupon-details/:id'; // Add coupon details route
  static const String foodMood = '/food-mood';
  static const String foodRecommendationDetails =
      '/food-recommendation-details'; // Add food recommendation details route
  static const String dailyGoalReached =
      '/celebration/daily-goal'; // Add daily goal celebration route
  static const String milestoneReached =
      '/celebration/milestone'; // Add milestone celebration route
}

// Composite codec that combines multiple codecs
class CompositeCodec extends Codec<Object?, Object?> {
  final Codec<Object?, Object?> _first;
  final Codec<Object?, Object?>? _second;

  const CompositeCodec(this._first, [this._second]);

  @override
  Converter<Object?, Object?> get decoder => _CompositeDecoder(_first, _second);

  @override
  Converter<Object?, Object?> get encoder => _CompositeEncoder(_first, _second);
}

class _CompositeDecoder extends Converter<Object?, Object?> {
  final Codec<Object?, Object?> _first;
  final Codec<Object?, Object?>? _second;

  const _CompositeDecoder(this._first, this._second);

  @override
  Object? convert(Object? input) {
    // Try first codec
    final result1 = _first.decoder.convert(input);
    if (result1 != input) {
      return result1;
    }

    // Try second codec if available
    if (_second != null) {
      final result2 = _second.decoder.convert(input);
      if (result2 != input) {
        return result2;
      }
    }

    // No codec could decode the input
    return input;
  }
}

class _CompositeEncoder extends Converter<Object?, Object?> {
  final Codec<Object?, Object?> _first;
  final Codec<Object?, Object?>? _second;

  const _CompositeEncoder(this._first, this._second);

  @override
  Object? convert(Object? input) {
    // Try first codec
    final result1 = _first.encoder.convert(input);
    if (result1 != input) {
      return result1;
    }

    // Try second codec if available
    if (_second != null) {
      final result2 = _second.encoder.convert(input);
      if (result2 != input) {
        return result2;
      }
    }

    // No codec could encode the input
    return input;
  }
}

// Custom codec for handling Activity objects in routes
class ActivityCodec extends Codec<Object?, Object?> {
  const ActivityCodec();

  @override
  Converter<Object?, Object?> get decoder => const _ActivityDecoder();

  @override
  Converter<Object?, Object?> get encoder => const _ActivityEncoder();
}

// Custom codec for handling FoodRecommendation objects in routes
class FoodRecommendationCodec extends Codec<Object?, Object?> {
  const FoodRecommendationCodec();

  @override
  Converter<Object?, Object?> get decoder => const _FoodRecommendationDecoder();

  @override
  Converter<Object?, Object?> get encoder => const _FoodRecommendationEncoder();
}

class _ActivityDecoder extends Converter<Object?, Object?> {
  const _ActivityDecoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    // We're getting a Map<String, dynamic> here when deserializing
    if (input is Map<String, dynamic> && input['_type'] == 'Activity') {
      return Activity(
        id: input['id'] as String,
        title: input['title'] as String,
        description: input['description'] as String,
        category: ActivityCategory.values.firstWhere(
          (c) => c.toString() == input['category'],
        ),
        duration: input['duration'] as int,
        recommendedFor:
            (input['recommendedFor'] as List)
                .map(
                  (e) => MoodType.values.firstWhere((m) => m.toString() == e),
                )
                .toList(),
        imageAsset: input['imageAsset'] as String?,
        imageUrl: input['imageUrl'] as String?,
        isSystemActivity: input['isSystemActivity'] as bool,
        createdBy: input['createdBy'] as String?,
        creatorName: input['creatorName'] as String?,
        showCreatorName: input['showCreatorName'] as bool,
        isPrivate: input['isPrivate'] as bool,
        countryCode: input['countryCode'] as String,
        upvotes: input['upvotes'] as int,
        downvotes: input['downvotes'] as int,
        createdAt: DateTime.parse(input['createdAt'] as String),
        updatedAt: DateTime.parse(input['updatedAt'] as String),
      );
    }

    return input;
  }
}

class _ActivityEncoder extends Converter<Object?, Object?> {
  const _ActivityEncoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    if (input is Activity) {
      return {
        '_type': 'Activity',
        'id': input.id,
        'title': input.title,
        'description': input.description,
        'category': input.category.toString(),
        'duration': input.duration,
        'recommendedFor':
            input.recommendedFor.map((m) => m.toString()).toList(),
        'imageAsset': input.imageAsset,
        'imageUrl': input.imageUrl,
        'isSystemActivity': input.isSystemActivity,
        'createdBy': input.createdBy,
        'creatorName': input.creatorName,
        'showCreatorName': input.showCreatorName,
        'isPrivate': input.isPrivate,
        'countryCode': input.countryCode,
        'upvotes': input.upvotes,
        'downvotes': input.downvotes,
        'createdAt': input.createdAt.toIso8601String(),
        'updatedAt': input.updatedAt.toIso8601String(),
      };
    }

    return input;
  }
}

// Custom codec for handling Coupon objects in routes
class CouponCodec extends Codec<Object?, Object?> {
  const CouponCodec();

  @override
  Converter<Object?, Object?> get decoder => const _CouponDecoder();

  @override
  Converter<Object?, Object?> get encoder => const _CouponEncoder();
}

class _CouponDecoder extends Converter<Object?, Object?> {
  const _CouponDecoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    // We're getting a Map<String, dynamic> here when deserializing
    if (input is Map<String, dynamic> && input['_type'] == 'Coupon') {
      final partner = Partner(
        id: input['partner']['id'] as String,
        name: input['partner']['name'] as String,
        category: PartnerCategory.values.firstWhere(
          (c) => c.toString() == input['partner']['category'],
        ),
        latitude: input['partner']['latitude'] as double,
        longitude: input['partner']['longitude'] as double,
        commissionRate: input['partner']['commissionRate'] as double,
        address: input['partner']['address'] as String,
      );

      return Coupon(
        id: input['id'] as String,
        partner: partner,
        description: input['description'] as String,
        discount: input['discount'] as String,
        pointsRequired: input['pointsRequired'] as int,
        expirationDate: DateTime.parse(input['expirationDate'] as String),
        recommendedForMoods:
            (input['recommendedForMoods'] as List)
                .map(
                  (e) => MoodType.values.firstWhere((m) => m.toString() == e),
                )
                .toList(),
      );
    }

    return input;
  }
}

class _CouponEncoder extends Converter<Object?, Object?> {
  const _CouponEncoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    if (input is Coupon) {
      return {
        '_type': 'Coupon',
        'id': input.id,
        'partner': {
          'id': input.partner.id,
          'name': input.partner.name,
          'category': input.partner.category.toString(),
          'latitude': input.partner.latitude,
          'longitude': input.partner.longitude,
          'commissionRate': input.partner.commissionRate,
          'address': input.partner.address,
        },
        'description': input.description,
        'discount': input.discount,
        'pointsRequired': input.pointsRequired,
        'expirationDate': input.expirationDate.toIso8601String(),
        'recommendedForMoods':
            input.recommendedForMoods.map((m) => m.toString()).toList(),
      };
    }

    return input;
  }
}

// Decoder for FoodRecommendation objects
class _FoodRecommendationDecoder extends Converter<Object?, Object?> {
  const _FoodRecommendationDecoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    // We're getting a Map<String, dynamic> here when deserializing
    if (input is Map<String, dynamic> &&
        input['_type'] == 'FoodRecommendation') {
      return FoodRecommendation(
        id: input['id'] as String,
        name: input['name'] as String,
        description: input['description'] as String,
        imageUrl: input['imageUrl'] as String,
        createdAt: DateTime.parse(input['createdAt'] as String),
        updatedAt: DateTime.parse(input['updatedAt'] as String),
        upvotes: input['upvotes'] as int,
        downvotes: input['downvotes'] as int,
        userVote:
            input['userVote'] != null
                ? VoteType.values.firstWhere(
                  (v) => v.toString() == input['userVote'],
                )
                : null,
        userId: input['userId'] as String,
        username: input['username'] as String,
        userAvatarUrl: input['userAvatarUrl'] as String?,
        moodTypes:
            input['moodTypes'] != null
                ? (input['moodTypes'] as List)
                    .map(
                      (e) =>
                          MoodType.values.firstWhere((m) => m.toString() == e),
                    )
                    .toList()
                : null,
        countryCode: input['countryCode'] as String,
      );
    }

    return input;
  }
}

// Encoder for FoodRecommendation objects
class _FoodRecommendationEncoder extends Converter<Object?, Object?> {
  const _FoodRecommendationEncoder();

  @override
  Object? convert(Object? input) {
    if (input == null) return null;

    if (input is FoodRecommendation) {
      return {
        '_type': 'FoodRecommendation',
        'id': input.id,
        'name': input.name,
        'description': input.description,
        'imageUrl': input.imageUrl,
        'createdAt': input.createdAt.toIso8601String(),
        'updatedAt': input.updatedAt.toIso8601String(),
        'upvotes': input.upvotes,
        'downvotes': input.downvotes,
        'userVote': input.userVote?.toString(),
        'userId': input.userId,
        'username': input.username,
        'userAvatarUrl': input.userAvatarUrl,
        'moodTypes': input.moodTypes?.map((m) => m.toString()).toList(),
        'countryCode': input.countryCode,
      };
    }

    return input;
  }
}

// Helper for fade transition
CustomTransitionPage<T> buildPageWithFadeTransition<T>({
  required BuildContext context,
  required GoRouterState state,
  required Widget child,
}) {
  return CustomTransitionPage<T>(
    key: state.pageKey,
    child: child,
    transitionsBuilder:
        (context, animation, secondaryAnimation, child) =>
            FadeTransition(opacity: animation, child: child),
    transitionDuration: const Duration(milliseconds: 300), // Adjust duration
  );
}

// GoRouter configuration
GoRouter createRouter({
  required String initialLocation,
  required AuthCubit authCubit,
  List<NavigatorObserver> observers = const [],
}) {
  return GoRouter(
    initialLocation: initialLocation,
    debugLogDiagnostics: kDebugMode,
    observers: observers,
    extraCodec: const CompositeCodec(
      ActivityCodec(),
      FoodRecommendationCodec(),
    ),
    routes: [
      // Introduction Screens - the very first screens showing app features/benefits
      GoRoute(
        path: AppRoutes.intro,
        builder: (context, state) => const IntroScreen(),
      ),

      // Authentication Screens
      GoRoute(
        path: AppRoutes.auth,
        builder: (context, state) => const AuthScreen(),
      ),
      GoRoute(
        path: AppRoutes.phoneInput,
        builder: (context, state) => const PhoneInputScreen(),
      ),
      GoRoute(
        path: AppRoutes.otpVerification,
        builder: (context, state) {
          final phoneNumber = state.uri.queryParameters['phoneNumber'] ?? '';
          return OtpVerificationScreen(phoneNumber: phoneNumber);
        },
      ),
      GoRoute(
        path: AppRoutes.pin,
        builder: (context, state) => const PinScreen(),
      ),

      // Onboarding for user profile data collection (only after authentication)
      GoRoute(
        path: AppRoutes.onboarding,
        builder: (context, state) => const OnboardingScreen(),
      ),

      // Main app screens wrapped in a ShellRoute with bottom navigation
      ShellRoute(
        builder: (context, state, child) {
          return BaseScaffold(
            showBottomNav: false,
            body: child,
            usePadding: false,
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.dashboard,
            builder: (context, state) => const DashboardScreen(),
          ),
          GoRoute(
            path: AppRoutes.profile,
            builder: (context, state) => const profile.ProfileScreen(),
          ),
          GoRoute(
            path: AppRoutes.editProfile,
            builder: (context, state) => const EditProfileScreenWrapper(),
          ),
          GoRoute(
            path: AppRoutes.activityHistory,
            builder: (context, state) => const ActivityHistoryScreen(),
          ),
          GoRoute(
            path: AppRoutes.usedCoupons,
            pageBuilder: (context, state) {
              return const NoTransitionPage(child: UsedCouponsScreen());
            },
          ),
          GoRoute(
            path: AppRoutes.rewardHistory,
            pageBuilder: (context, state) {
              return const NoTransitionPage(child: RewardHistoryScreen());
            },
          ),
          // Add other main app routes here
          GoRoute(
            path: AppRoutes.activities,
            builder: (context, state) => const ActivitiesScreen(),
            routes: [
              GoRoute(
                path: 'create',
                builder: (context, state) => const CreateActivityScreen(),
              ),
            ],
          ),
          // Add activity detail route
          GoRoute(
            path: AppRoutes.activityDetail,
            builder: (context, state) {
              final activity = state.extra as Activity;
              return ActivityDetailScreen(activity: activity);
            },
          ),
          GoRoute(
            path: AppRoutes.categories,
            builder: (context, state) => const CouponsScreen(),
          ),
          // Coupon details route
          GoRoute(
            path: AppRoutes.couponDetails,
            builder: (context, state) {
              // The coupon is passed as extra data
              final coupon = state.extra as Coupon;
              return CouponDetailsScreen(coupon: coupon);
            },
          ),
          // Settings route
          GoRoute(
            path: AppRoutes.settings,
            builder: (context, state) => const SettingsScreen(),
          ),
          // Notifications route
          GoRoute(
            path: AppRoutes.notifications,
            builder: (context, state) => const NotificationScreen(),
          ),
          GoRoute(
            path: AppRoutes.foodMood,
            builder: (context, state) => const FoodMoodScreen(),
          ),
          // Food recommendation details route
          GoRoute(
            path: AppRoutes.foodRecommendationDetails,
            builder: (context, state) {
              final recommendation = state.extra as FoodRecommendation;
              return FoodRecommendationDetailsScreen(
                recommendation: recommendation,
              );
            },
          ),
          // Celebration screens
          GoRoute(
            path: AppRoutes.dailyGoalReached,
            pageBuilder:
                (context, state) => buildPageWithFadeTransition(
                  context: context,
                  state: state,
                  child: const DailyGoalReachedScreen(),
                ),
          ),
          GoRoute(
            path: AppRoutes.milestoneReached,
            pageBuilder:
                (context, state) => buildPageWithFadeTransition(
                  context: context,
                  state: state,
                  child: const MilestoneReachedScreen(),
                ),
          ),
        ],
      ),
    ],
    redirect: (context, state) async {
      final authState = authCubit.state;
      final isIntroPath = state.uri.toString() == AppRoutes.intro;
      final isAuthPath =
          state.uri.toString() == AppRoutes.auth ||
          state.uri.toString() == AppRoutes.phoneInput ||
          state.uri.toString() == AppRoutes.otpVerification ||
          state.uri.toString() == AppRoutes.pin;
      final isOnboardingPath = state.uri.toString() == AppRoutes.onboarding;

      // Check if intro screens have been seen
      final prefs = await SharedPreferences.getInstance();
      final bool introCompleted =
          prefs.getBool(AppConstants.introCompletedKey) ?? false;

      // Check if onboarding is completed
      final bool onboardingCompleted =
          prefs.getBool(AppConstants.onboardingCompleteKey) ?? false;

      // New user flow (not seen intro yet) -> show intro
      if (!introCompleted && !isIntroPath) {
        return AppRoutes.intro;
      }

      // User has seen intro but not authenticated -> go to auth
      if (introCompleted && authState is! Authenticated && !isAuthPath) {
        return AppRoutes.auth;
      }

      // User is authenticated but hasn't completed onboarding -> go to onboarding
      if (authState is Authenticated &&
          !onboardingCompleted &&
          !isOnboardingPath) {
        return AppRoutes.onboarding;
      }

      // User is authenticated and has completed onboarding but is on an auth or onboarding path -> go to dashboard
      if (authState is Authenticated &&
          onboardingCompleted &&
          (isAuthPath || isOnboardingPath || isIntroPath)) {
        return AppRoutes.dashboard;
      }

      // No redirection needed
      return null;
    },
    errorPageBuilder:
        (context, state) => buildPageWithFadeTransition(
          context: context,
          state: state,
          child: ErrorScreen(error: state.error),
        ),
  );
}

// Helper class to make Bloc stream usable with GoRouter refreshListenable
class GoRouterRefreshStream extends ChangeNotifier {
  late final StreamSubscription<dynamic> _subscription;
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen((_) => notifyListeners());
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

// Variable to hold the created router instance
// We will initialize this in main() after checking SharedPreferences
late final GoRouter router;

// Error screen when a route isn't found
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(child: Text(error?.toString() ?? 'Page not found.')),
    );
  }
}
