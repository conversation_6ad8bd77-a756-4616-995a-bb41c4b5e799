import 'package:equatable/equatable.dart';

abstract class NetworkStatusState extends Equatable {
  const NetworkStatusState();

  @override
  List<Object?> get props => [];
}

// Represents the initial state before the first check
class NetworkInitial extends NetworkStatusState {}

// Represents that the device has network connectivity
class NetworkOnline extends NetworkStatusState {}

// Represents that the device does not have network connectivity
class NetworkOffline extends NetworkStatusState {}
