import 'package:connectivity_plus/connectivity_plus.dart';

/// Interface for checking network connectivity
abstract class NetworkInfo {
  /// Returns true if device is connected to internet
  Future<bool> get isConnected;
}

/// Implementation of [NetworkInfo] using the connectivity_plus package
class NetworkInfoImpl implements NetworkInfo {
  /// Connectivity instance for checking network status
  final Connectivity connectivity;

  /// Creates a new [NetworkInfoImpl]
  NetworkInfoImpl(this.connectivity);

  @override
  Future<bool> get isConnected async {
    final connectivityResults = await connectivity.checkConnectivity();
    // Handle both single result and list of results
    return !connectivityResults.contains(ConnectivityResult.none) &&
        connectivityResults.isNotEmpty;
  }
}
