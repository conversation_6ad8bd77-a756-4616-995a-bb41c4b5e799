import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:detoxme/core/network/network_status_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NetworkStatusCubit extends Cubit<NetworkStatusState> {
  final Connectivity _connectivity;
  StreamSubscription? _connectivitySubscription;

  NetworkStatusCubit({required Connectivity connectivity})
    : _connectivity = connectivity,
      super(NetworkInitial()) {
    _checkInitialStatus();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  Future<void> _checkInitialStatus() async {
    final results = await _connectivity.checkConnectivity();
    _updateConnectionStatus(results);
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    // Consider connected if *any* result is not 'none'
    final isConnected = !results.contains(ConnectivityResult.none);

    if (isConnected && state is! NetworkOnline) {
      emit(NetworkOnline());
    } else if (!isConnected && state is! NetworkOffline) {
      emit(NetworkOffline());
    }
  }

  @override
  Future<void> close() {
    _connectivitySubscription?.cancel();
    return super.close();
  }
}
