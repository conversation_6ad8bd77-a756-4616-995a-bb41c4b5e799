import 'package:flutter/foundation.dart';

/// Configuration for Supabase authentication
class SupabaseAuthConfig {
  /// The URL scheme for deep linking
  static const String urlScheme = 'com.innovatio.detoxme';

  /// The callback path for authentication
  static const String callbackPath = 'login-callback';

  /// The full redirect URL for OAuth providers
  static String get redirectUrl => '$urlScheme://$callbackPath/';

  /// The Supabase project URL
  static const String projectUrl = 'https://vbsdxnhkpoipturpmshl.supabase.co';

  /// The Supabase API endpoint for authentication
  static const String authEndpoint = '$projectUrl/auth/v1';

  /// The Supabase API endpoint for authorization
  static const String authorizeEndpoint = '$authEndpoint/authorize';

  /// The Supabase API endpoint for token exchange
  static const String tokenEndpoint = '$authEndpoint/token';

  /// The Supabase API endpoint for user information
  static const String userInfoEndpoint = '$authEndpoint/user';

  /// Log authentication events in debug mode
  static void logAuthEvent(String message) {
    if (kDebugMode) {
      print('[SupabaseAuth] $message');
    }
  }

  /// Get the Apple Sign In configuration
  static Map<String, String> get appleSignInConfig => {
    'client_id': 'host.exp.Exponent', // This is the default client ID for Expo
    'redirect_uri': redirectUrl,
    'response_type': 'code',
    'scope': 'email name',
    'response_mode': 'form_post',
  };
}
