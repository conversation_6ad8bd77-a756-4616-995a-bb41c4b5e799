import 'package:flutter/material.dart';

/// Helper class for responsive design
class ResponsiveHelper {
  /// Breakpoints for different screen sizes
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Check if the screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if the screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  /// Check if the screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24.0);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(8.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(12.0);
    } else {
      return const EdgeInsets.all(16.0);
    }
  }

  /// Get responsive font size multiplier
  static double getFontSizeMultiplier(BuildContext context) {
    if (isMobile(context)) {
      return 1.0;
    } else if (isTablet(context)) {
      return 1.1;
    } else {
      return 1.2;
    }
  }

  /// Get responsive grid column count
  static int getGridColumnCount(BuildContext context, {int? maxColumns}) {
    final width = MediaQuery.of(context).size.width;
    int columns;

    if (width < mobileBreakpoint) {
      columns = 2;
    } else if (width < tabletBreakpoint) {
      columns = 3;
    } else if (width < desktopBreakpoint) {
      columns = 4;
    } else {
      columns = 5;
    }

    if (maxColumns != null && columns > maxColumns) {
      columns = maxColumns;
    }

    return columns;
  }

  /// Get responsive card width
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (isMobile(context)) {
      return screenWidth - 32; // Full width minus padding
    } else if (isTablet(context)) {
      return (screenWidth - 48) / 2; // Two cards per row
    } else {
      return (screenWidth - 64) / 3; // Three cards per row
    }
  }

  /// Get responsive dialog width
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (isMobile(context)) {
      return screenWidth * 0.9;
    } else if (isTablet(context)) {
      return screenWidth * 0.7;
    } else {
      return screenWidth * 0.5;
    }
  }

  /// Get responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    if (isMobile(context)) {
      return kToolbarHeight;
    } else {
      return kToolbarHeight + 8;
    }
  }

  /// Get responsive bottom navigation height
  static double getBottomNavHeight(BuildContext context) {
    if (isMobile(context)) {
      return 60.0;
    } else {
      return 70.0;
    }
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {double baseSize = 24.0}) {
    final multiplier = getFontSizeMultiplier(context);
    return baseSize * multiplier;
  }

  /// Get responsive border radius
  static double getBorderRadius(
    BuildContext context, {
    double baseRadius = 8.0,
  }) {
    if (isMobile(context)) {
      return baseRadius;
    } else if (isTablet(context)) {
      return baseRadius * 1.2;
    } else {
      return baseRadius * 1.5;
    }
  }

  /// Get responsive elevation
  static double getElevation(
    BuildContext context, {
    double baseElevation = 2.0,
  }) {
    if (isMobile(context)) {
      return baseElevation;
    } else {
      return baseElevation * 1.5;
    }
  }

  /// Get responsive spacing
  static double getSpacing(BuildContext context, {double baseSpacing = 8.0}) {
    if (isMobile(context)) {
      return baseSpacing;
    } else if (isTablet(context)) {
      return baseSpacing * 1.2;
    } else {
      return baseSpacing * 1.5;
    }
  }

  /// Get responsive text scaler
  static TextScaler getTextScaler(BuildContext context) {
    // Ensure text doesn't get too large on bigger screens
    final mediaQueryData = MediaQuery.of(context);
    final textScaler = mediaQueryData.textScaler;

    // Clamp text scale factor to reasonable bounds
    final scaleFactor = textScaler.scale(1.0).clamp(0.8, 1.3);
    return TextScaler.linear(scaleFactor);
  }

  /// Get responsive safe area padding
  static EdgeInsets getResponsiveSafeArea(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final safePadding = mediaQuery.padding;

    // Add extra padding on larger screens
    if (isDesktop(context)) {
      return EdgeInsets.only(
        top: safePadding.top + 16,
        bottom: safePadding.bottom + 16,
        left: safePadding.left + 32,
        right: safePadding.right + 32,
      );
    } else if (isTablet(context)) {
      return EdgeInsets.only(
        top: safePadding.top + 8,
        bottom: safePadding.bottom + 8,
        left: safePadding.left + 16,
        right: safePadding.right + 16,
      );
    } else {
      return safePadding;
    }
  }

  /// Get responsive maximum width for content
  static double getMaxContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (isDesktop(context)) {
      return 1200.0; // Maximum width for desktop
    } else if (isTablet(context)) {
      return screenWidth * 0.9;
    } else {
      return screenWidth;
    }
  }

  /// Get responsive layout orientation
  static bool shouldUseHorizontalLayout(BuildContext context) {
    return !isMobile(context) &&
        MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get responsive aspect ratio for cards
  static double getCardAspectRatio(BuildContext context) {
    if (isMobile(context)) {
      return 1.2; // Slightly taller on mobile
    } else {
      return 1.0; // Square on larger screens
    }
  }

  /// Get responsive list tile height
  static double getListTileHeight(BuildContext context) {
    if (isMobile(context)) {
      return 72.0;
    } else {
      return 80.0;
    }
  }

  /// Get responsive button height
  static double getButtonHeight(BuildContext context) {
    if (isMobile(context)) {
      return 48.0;
    } else {
      return 56.0;
    }
  }

  /// Get responsive input field height
  static double getInputHeight(BuildContext context) {
    if (isMobile(context)) {
      return 56.0;
    } else {
      return 64.0;
    }
  }

  /// Wrap content with responsive constraints
  static Widget wrapWithResponsiveConstraints(
    BuildContext context,
    Widget child, {
    double? maxWidth,
  }) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: maxWidth ?? getMaxContentWidth(context),
        ),
        child: child,
      ),
    );
  }

  /// Get responsive grid delegate
  static SliverGridDelegate getResponsiveGridDelegate(
    BuildContext context, {
    double? childAspectRatio,
    double? mainAxisSpacing,
    double? crossAxisSpacing,
    int? maxColumns,
  }) {
    final columnCount = getGridColumnCount(context, maxColumns: maxColumns);

    return SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: columnCount,
      childAspectRatio: childAspectRatio ?? getCardAspectRatio(context),
      mainAxisSpacing: mainAxisSpacing ?? getSpacing(context),
      crossAxisSpacing: crossAxisSpacing ?? getSpacing(context),
    );
  }
}
