import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for app locale settings
class AppLocaleProvider extends ChangeNotifier {
  /// Shared preferences key for storing locale
  static const String localePrefsKey = 'app_locale';

  /// List of supported locales
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('de'), // German
    Locale('ru'), // Russian
    Locale('tr'), // Turkish
    Locale('ar'), // Arabic
  ];

  /// Current locale of the app
  Locale _locale = const Locale('en');

  /// Get current locale
  Locale get locale => _locale;

  /// SharedPreferences instance
  late SharedPreferences _prefs;

  /// Initializes the locale provider
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    final storedLocale = _prefs.getString(localePrefsKey);

    if (storedLocale != null) {
      _locale = Locale(storedLocale);
    } else {
      // Default to system locale if available and supported
      final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
      if (isLocaleSupported(deviceLocale)) {
        _locale = deviceLocale;
      }
    }

    notifyListeners();
  }

  /// Check if a locale is supported
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any(
      (supported) => supported.languageCode == locale.languageCode,
    );
  }

  /// Set app locale
  Future<void> setLocale(Locale newLocale) async {
    if (!isLocaleSupported(newLocale)) return;

    // Only update if actually changing
    if (_locale.languageCode != newLocale.languageCode) {
      _locale = newLocale;
      
      // Save the locale setting
      await _prefs.setString(localePrefsKey, newLocale.languageCode);
      
      // Ensure UI updates by notifying listeners
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  /// Get localized name for a language code
  static String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'de':
        return 'Deutsch';
      case 'ru':
        return 'Русский';
      case 'tr':
        return 'Türkçe';
      case 'ar':
        return 'العربية';
      default:
        return 'Unknown';
    }
  }

  /// Returns the list of available languages
  static List<Map<String, String>> getAvailableLanguages() {
    return supportedLocales
        .map(
          (locale) => {
            'code': locale.languageCode,
            'name': getLanguageName(locale.languageCode),
          },
        )
        .toList();
  }
}
