import 'package:detoxme/core/services/logger_service.dart';
import 'package:detoxme/core/services/analytics_service.dart';

/// Utility function to easily replace print statements with structured logging
///
/// Example:
/// ```dart
/// // Replace
/// print('Loading user data');
///
/// // With
/// logDebug('Loading user data');
/// ```
void logDebug(
  String message, [
  String tag = 'App',
  Map<String, dynamic>? data,
]) {
  LoggerService().debug(tag, message, data);
}

/// Utility function for info level logging
void logInfo(String message, [String tag = 'App', Map<String, dynamic>? data]) {
  LoggerService().info(tag, message, data);
}

/// Utility function for warning level logging
void logWarning(
  String message, [
  String tag = 'App',
  Map<String, dynamic>? data,
]) {
  LoggerService().warning(tag, message, data);
}

/// Utility function for error level logging
void logError(
  String message, [
  dynamic error,
  StackTrace? stackTrace,
  String tag = 'App',
  Map<String, dynamic>? data,
]) {
  LoggerService().error(tag, message, error, stackTrace, data);
}

/// Utility function for tracking analytics events
void trackEvent(String eventName, [Map<String, dynamic>? properties]) {
  AnalyticsService().trackEvent(eventName, properties);
}

/// Utility function for tracking screen views
void trackScreenView(String screenName, [Map<String, dynamic>? properties]) {
  AnalyticsService().trackScreenView(screenName, properties: properties);
}

/// Utility function for tracking user actions
void trackUserAction(String action, [Map<String, dynamic>? properties]) {
  AnalyticsService().trackUserAction(action, properties: properties);
}

/// Utility function for tracking errors in analytics
void trackError(
  String errorType,
  String message, [
  Map<String, dynamic>? properties,
]) {
  AnalyticsService().trackError(errorType, message, properties: properties);
  // Also log the error locally
  LoggerService().error('Analytics', message, errorType, null, properties);
}

/// Legacy print function - use for debugging only during development
/// This will print in development but use proper logging in production
void debugPrint(String message) {
  LoggerService().debug('Debug', message);
}

/// Extension to add logging methods to any object
extension LoggingExtension on Object {
  /// Log debug information about this object
  void logDebug(
    String message, [
    String tag = 'App',
    Map<String, dynamic>? additionalData,
  ]) {
    final data = {'objectType': runtimeType.toString(), ...?additionalData};
    LoggerService().debug(tag, message, data);
  }

  /// Log error information about this object
  void logError(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
    String tag = 'App',
    Map<String, dynamic>? additionalData,
  ]) {
    final data = {'objectType': runtimeType.toString(), ...?additionalData};
    LoggerService().error(tag, message, error, stackTrace, data);
  }

  /// Track an analytics event related to this object
  void trackEvent(
    String eventName, [
    Map<String, dynamic>? additionalProperties,
  ]) {
    final properties = {
      'objectType': runtimeType.toString(),
      ...?additionalProperties,
    };
    AnalyticsService().trackEvent(eventName, properties);
  }
}
