/// Custom exception for errors originating from the server/API.
class ServerException implements Exception {
  final String message;

  ServerException(this.message);

  @override
  String toString() => 'ServerException: $message';
}

/// Custom exception for errors originating from local cache/storage.
class CacheException implements Exception {
  final String message;

  CacheException(this.message);

  @override
  String toString() => 'CacheException: $message';
}

// Add other specific exceptions as needed (e.g., NetworkException)
