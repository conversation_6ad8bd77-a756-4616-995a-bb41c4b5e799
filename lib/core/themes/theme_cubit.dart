import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// States for theme management
enum ThemeState { light, dark, system }

/// Cubit for managing app theme with persistence
class ThemeCubit extends Cubit<ThemeState> {
  /// Key for storing theme preference
  static const String _themeKey = 'theme_mode';

  ThemeCubit() : super(ThemeState.system) {
    _loadTheme();
  }

  /// Load saved theme preference
  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeString = prefs.getString(_themeKey) ?? 'system';

      final themeState = _stringToThemeState(themeString);
      emit(themeState);
    } catch (e) {
      // If loading fails, fallback to system theme
      emit(ThemeState.system);
    }
  }

  /// Set theme and save to preferences
  Future<void> setTheme(ThemeState themeState) async {
    if (state == themeState) return;

    emit(themeState);

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, _themeStateToString(themeState));
    } catch (e) {
      // Handle error silently - the theme is still changed in memory
      debugPrint('Failed to save theme preference: $e');
    }
  }

  /// Toggle between light and dark themes (ignores system)
  Future<void> toggleTheme() async {
    final newTheme =
        state == ThemeState.dark ? ThemeState.light : ThemeState.dark;
    await setTheme(newTheme);
  }

  /// Set to light theme
  Future<void> setLightTheme() async {
    await setTheme(ThemeState.light);
  }

  /// Set to dark theme
  Future<void> setDarkTheme() async {
    await setTheme(ThemeState.dark);
  }

  /// Set to system theme
  Future<void> setSystemTheme() async {
    await setTheme(ThemeState.system);
  }

  /// Get Flutter ThemeMode from current state
  ThemeMode get themeMode {
    switch (state) {
      case ThemeState.light:
        return ThemeMode.light;
      case ThemeState.dark:
        return ThemeMode.dark;
      case ThemeState.system:
        return ThemeMode.system;
    }
  }

  /// Check if current theme is dark
  bool get isDarkMode => state == ThemeState.dark;

  /// Check if current theme is light
  bool get isLightMode => state == ThemeState.light;

  /// Check if current theme is system
  bool get isSystemMode => state == ThemeState.system;

  /// Convert string to ThemeState
  ThemeState _stringToThemeState(String value) {
    switch (value) {
      case 'light':
        return ThemeState.light;
      case 'dark':
        return ThemeState.dark;
      default:
        return ThemeState.system;
    }
  }

  /// Convert ThemeState to string
  String _themeStateToString(ThemeState themeState) {
    switch (themeState) {
      case ThemeState.light:
        return 'light';
      case ThemeState.dark:
        return 'dark';
      case ThemeState.system:
        return 'system';
    }
  }
} 