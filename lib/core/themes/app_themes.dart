import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// Define App Colors - Relaxing, calming palette for adult digital detox
class AppColors {
  // Primary - Soft Lavender Blue (promotes relaxation and tranquility)
  static const Color primary = Color(0xFF7986CB); // Soft lavender blue
  
  // Secondary - Muted Sage Green (natural, balanced, calming)
  static const Color secondary = Color(0xFF9CCC65); // Muted sage green
  
  // Accent - Soft Peach (warm, gentle, used for rewards and highlights)
  static const Color accent = Color(0xFFFFCC80); // Soft peach
  
  // Background Light - Warm off-white (gentle on eyes, creates comfort)
  static const Color backgroundLight = Color(0xFFF8F5F2); // Warm off-white
  
  // Background Dark - Deep blue-gray (soothing night mode)
  static const Color backgroundDark = Color(0xFF303F5F); // Deep blue-gray
  
  // Text Light - Soft charcoal (gentle on eyes, easy to read)
  static const Color textLight = Color(0xFF424242); // Soft charcoal
  
  // Text Dark - Warm white (reduced eye strain)
  static const Color textDark = Color(0xFFF5F5F5); // Warm white
  
  // Additional colors for indicators and status
  static const Color success = Color(0xFF81C784); // Softer green
  static const Color warning = Color(0xFFFFD54F); // Softer amber
  static const Color error = Color(0xFFE57373); // Soft red
  static const Color info = Color(0xFF64B5F6); // Soft blue

  // Surface colors with opacity
  static const Color surfaceLight = Color(0xFFF0EDE9); // Slightly darker than background
  static const Color surfaceDark = Color(0xFF3A4A6B); // Slightly lighter than background
  
  // Border colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF455A64);
  
  // Input field colors
  static const Color inputBgLight = Color(0xFFF5F2EF);
  static const Color inputBgDark = Color(0xFF384863);
}

// App Themes
class AppThemes {
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: AppColors.primary,
    colorScheme: ColorScheme.light(
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      tertiary: AppColors.accent,
      surface: AppColors.surfaceLight,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: AppColors.textLight,
      onSurface: AppColors.textLight,
      error: AppColors.error,
      onError: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.backgroundLight,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: GoogleFonts.poppins(
        fontSize: 20, 
        fontWeight: FontWeight.w500,
        color: Colors.white,
        letterSpacing: 0.5,
      ),
    ),
    textTheme: GoogleFonts.montserratTextTheme(
      ThemeData.light().textTheme.copyWith(
        headlineLarge: const TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: AppColors.textLight,
          letterSpacing: -0.5,
          height: 1.3,
        ),
        headlineMedium: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w500,
          color: AppColors.textLight,
          letterSpacing: 0,
          height: 1.3,
        ),
        titleLarge: const TextStyle(
          fontSize: 20, 
          fontWeight: FontWeight.w500,
          color: AppColors.textLight,
          letterSpacing: 0.15,
        ),
        bodyLarge: GoogleFonts.lora(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: AppColors.textLight,
          height: 1.5,
          letterSpacing: 0.15,
        ),
        bodyMedium: GoogleFonts.lora(
          fontSize: 14,
          color: AppColors.textLight,
          height: 1.5,
          letterSpacing: 0.25,
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        side: const BorderSide(color: AppColors.primary, width: 1.5),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.inputBgLight,
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.borderLight, width: 1.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.error, width: 1.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.error, width: 1.5),
      ),
      labelStyle: const TextStyle(
        color: Color(0xFF757575),
        fontWeight: FontWeight.w400,
        fontSize: 15,
      ),
      hintStyle: const TextStyle(
        color: Color(0xFF9E9E9E),
        fontWeight: FontWeight.w300,
        fontSize: 14,
      ),
    ),
    cardTheme: const CardThemeData(
      color: Colors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    dividerTheme: const DividerThemeData(
      color: Color(0xFFE0E0E0),
      thickness: 1,
      space: 24,
    ),
    iconTheme: const IconThemeData(
      color: AppColors.primary,
      size: 24,
    ),
  );

  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: AppColors.primary,
    colorScheme: ColorScheme.dark(
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      tertiary: AppColors.accent,
      surface: AppColors.surfaceDark,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: AppColors.textDark,
      onSurface: AppColors.textDark,
      error: AppColors.error,
      onError: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.backgroundDark,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.backgroundDark,
      foregroundColor: AppColors.textDark,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: GoogleFonts.poppins(
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: AppColors.textDark,
        letterSpacing: 0.5,
      ),
    ),
    textTheme: GoogleFonts.montserratTextTheme(
      ThemeData.dark().textTheme.copyWith(
        headlineLarge: const TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
          letterSpacing: -0.5,
          height: 1.3,
        ),
        headlineMedium: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w500,
          color: AppColors.textDark,
          letterSpacing: 0,
          height: 1.3,
        ),
        titleLarge: const TextStyle(
          fontSize: 20, 
          fontWeight: FontWeight.w500,
          color: AppColors.textDark,
          letterSpacing: 0.15,
        ),
        bodyLarge: GoogleFonts.lora(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: AppColors.textDark,
          height: 1.5,
          letterSpacing: 0.15,
        ),
        bodyMedium: GoogleFonts.lora(
          fontSize: 14,
          color: AppColors.textDark,
          height: 1.5,
          letterSpacing: 0.25,
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.textDark,
        side: const BorderSide(color: AppColors.primary, width: 1.5),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          letterSpacing: 0.5,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.inputBgDark,
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.borderDark, width: 1.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.error, width: 1.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16.0),
        borderSide: const BorderSide(color: AppColors.error, width: 1.5),
      ),
      labelStyle: const TextStyle(
        color: Color(0xFFBDBDBD),
        fontWeight: FontWeight.w400,
        fontSize: 15,
      ),
      hintStyle: const TextStyle(
        color: Color(0xFF9E9E9E),
        fontWeight: FontWeight.w300,
        fontSize: 14,
      ),
    ),
    cardTheme: const CardThemeData(
      color: AppColors.surfaceDark,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    dividerTheme: const DividerThemeData(
      color: Color(0xFF455A64),
      thickness: 1,
      space: 24,
    ),
    iconTheme: const IconThemeData(
      color: AppColors.primary,
      size: 24,
    ),
  );
}
