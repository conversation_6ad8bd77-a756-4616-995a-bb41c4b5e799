import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

/// Service for playing short sound effects.
class SoundService {
  // Use multiple players if sounds might overlap, or a single player
  // if only one sound should play at a time.
  // For simple UI feedback, a single player is often sufficient.
  final AudioPlayer _audioPlayer = AudioPlayer();

  // Pre-load sounds for lower latency? Might not be necessary for short effects.

  /// Plays the sound for task completion.
  Future<void> playTaskCompleteSound() async {
    await _playSound('audio/task_complete.mp3');
  }

  /// Plays the generic reward sound (e.g., XP gain, badge, card).
  Future<void> playRewardSound() async {
    await _playSound('audio/reward.mp3');
  }

  /// Plays the sound for leveling up.
  Future<void> playLevelUpSound() async {
    await _playSound('audio/level_up.mp3');
  }

  /// Plays the sound for earning a badge.
  Future<void> playBadgeEarnedSound() async {
    // Use a specific sound or the generic reward sound?
    await _playSound('audio/badge_earned.mp3'); // Placeholder
    // await playRewardSound(); // Alternative
  }

  /// Plays the sound for earning a collectible card.
  Future<void> playCardEarnedSound() async {
    // Use a specific sound or the generic reward sound?
    await _playSound('audio/card_earned.mp3'); // Placeholder
    // await playRewardSound(); // Alternative
  }

  /// Helper method to play a sound asset.
  Future<void> _playSound(String assetPath) async {
    try {
      // Use AssetSource for local assets
      await _audioPlayer.play(AssetSource(assetPath));
      if (kDebugMode) {
        print('Playing sound: $assetPath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound $assetPath: $e');
      }
      // Optionally handle errors, e.g., log them
    }
  }

  /// Dispose the audio player when no longer needed.
  void dispose() {
    _audioPlayer.dispose();
  }
}
