import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:detoxme/core/services/logger_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

/// Service for tracking analytics events and user behavior
class AnalyticsService {
  /// Singleton instance
  static final AnalyticsService _instance = AnalyticsService._internal();

  /// Logger service for local logging
  final LoggerService _logger = LoggerService();

  /// Supabase client for sending analytics
  late final SupabaseClient _supabaseClient;

  /// Current user ID
  String? _userId;

  /// Session ID for grouping events
  late final String _sessionId;

  /// App version from package info
  String? _appVersion;

  /// Platform info
  String? _platformInfo;

  /// Device info
  Map<String, dynamic>? _deviceInfo;

  /// Event queue for batch sending
  final List<Map<String, dynamic>> _eventQueue = [];

  /// Timer for batch sending
  Timer? _batchTimer;

  /// Whether analytics is enabled
  bool _analyticsEnabled = true;

  /// Whether to send analytics in real-time or batch
  final bool _batchSend;

  /// Batch sending interval in seconds
  final int _batchIntervalSeconds;

  /// Factory constructor for the singleton
  factory AnalyticsService() => _instance;

  /// Private constructor for singleton
  AnalyticsService._internal()
    : _sessionId = const Uuid().v4(),
      _batchSend = true,
      _batchIntervalSeconds = 30;

  /// Initialize the analytics service
  Future<void> initialize({
    required SupabaseClient supabaseClient,
    bool enabled = true,
  }) async {
    _supabaseClient = supabaseClient;
    _analyticsEnabled = enabled;

    // Initialize logger
    _logger.initialize(
      supabaseClient: supabaseClient,
      analyticsEnabled: enabled,
    );

    // Generate persistent session ID
    await _loadDeviceInfo();
    await _loadAppInfo();

    // Start batch timer if using batch sending
    if (_batchSend) {
      _startBatchTimer();
    }

    // Log initialization
    _logger.info('Analytics', 'Initialized with sessionId: $_sessionId', {
      'sessionId': _sessionId,
      'enabled': enabled,
      'batchMode': _batchSend,
    });

    // Track app start event
    trackEvent('app_start', {
      'cold_start': true,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Load device info for better analytics
  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      _deviceInfo = <String, dynamic>{};

      if (Platform.isAndroid) {
        final info = await deviceInfo.androidInfo;
        _deviceInfo = {
          'model': info.model,
          'brand': info.brand,
          'androidVersion': info.version.release,
          'sdkInt': info.version.sdkInt,
          'manufacturer': info.manufacturer,
        };
        _platformInfo = 'Android ${info.version.release}';
      } else if (Platform.isIOS) {
        final info = await deviceInfo.iosInfo;
        _deviceInfo = {
          'model': info.model,
          'name': info.name,
          'systemName': info.systemName,
          'systemVersion': info.systemVersion,
          'isPhysicalDevice': info.isPhysicalDevice,
        };
        _platformInfo = '${info.systemName} ${info.systemVersion}';
      }
    } catch (e) {
      _logger.error('Analytics', 'Failed to load device info', e);
      _deviceInfo = {'error': 'Failed to load device info'};
      _platformInfo = 'Unknown';
    }
  }

  /// Load app info for versioning
  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
    } catch (e) {
      _logger.error('Analytics', 'Failed to load app info', e);
      _appVersion = 'Unknown';
    }
  }

  /// Set the current user ID for analytics
  void setUserId(String? userId) {
    if (_userId != userId) {
      final previousId = _userId;
      _userId = userId;

      // Log user change
      _logger.info(
        'Analytics',
        'User ID changed${previousId != null ? " from $previousId" : ""} to ${userId ?? "anonymous"}',
      );

      // Track user change event if we have a valid ID
      if (userId != null) {
        trackEvent('user_identified', {
          'method': 'manual',
          'previous_id': previousId,
        });
      }
    }
  }

  /// Clear the current user ID (for logout)
  void clearUserId() {
    if (_userId != null) {
      final previousId = _userId;
      _userId = null;

      // Log user cleared
      _logger.info('Analytics', 'User ID cleared from $previousId');

      // Track logout event
      trackEvent('user_logged_out', {'previous_id': previousId});
    }
  }

  /// Track an analytics event
  void trackEvent(String eventName, [Map<String, dynamic>? properties]) {
    // Skip if analytics is disabled
    if (!_analyticsEnabled) return;

    // Build event data
    final eventData = {
      'event': eventName,
      'properties': properties ?? <String, dynamic>{},
      'userId': _userId,
      'sessionId': _sessionId,
      'deviceInfo': _deviceInfo,
      'appVersion': _appVersion,
      'platform': _platformInfo,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Log locally
    _logger.logAnalytics(eventName, eventData);

    // Add to queue if batch sending, otherwise send immediately
    if (_batchSend) {
      _eventQueue.add(eventData);
      _checkQueueSize();
    } else {
      _sendToSupabase(eventData);
    }
  }

  /// Track a screen view
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    trackEvent('screen_view', {'screen': screenName, ...?properties});
  }

  /// Track a user action
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    trackEvent('user_action', {'action': action, ...?properties});
  }

  /// Track an error
  void trackError(
    String errorType,
    String message, {
    Map<String, dynamic>? properties,
  }) {
    trackEvent('error', {
      'type': errorType,
      'message': message,
      ...?properties,
    });
  }

  /// Start batch timer for sending events periodically
  void _startBatchTimer() {
    _batchTimer?.cancel();
    _batchTimer = Timer.periodic(
      Duration(seconds: _batchIntervalSeconds),
      (_) => _sendBatchEvents(),
    );
  }

  /// Check queue size and send if above threshold
  void _checkQueueSize() {
    if (_eventQueue.length >= 10) {
      _sendBatchEvents();
    }
  }

  /// Send batched events to Supabase
  Future<void> _sendBatchEvents() async {
    if (_eventQueue.isEmpty) return;

    try {
      // Make a copy of the queue and clear it
      final eventsCopy = List<Map<String, dynamic>>.from(_eventQueue);
      _eventQueue.clear();

      // Send events to Supabase
      for (final event in eventsCopy) {
        await _sendToSupabase(event);
      }

      _logger.debug('Analytics', 'Sent ${eventsCopy.length} events in batch');
    } catch (e) {
      _logger.error('Analytics', 'Failed to send batch events', e);

      // Attempt to retry with exponential backoff
      _retryFailedEvents();
    }
  }

  /// Send a single event to Supabase
  Future<void> _sendToSupabase(Map<String, dynamic> eventData) async {
    try {
      // Check if user is authenticated
      final currentUser = _supabaseClient.auth.currentUser;

      // Use current user ID if available and no explicit ID is set
      final effectiveUserId = _userId ?? currentUser?.id;

      // Call the RPC function directly for better error handling
      await _supabaseClient.rpc(
        'log_analytics_event',
        params: {
          'event_name': eventData['event'],
          'event_properties': eventData,
          if (effectiveUserId != null) 'user_id': effectiveUserId,
        },
      );
    } catch (e) {
      // Log the error but don't prevent the app from functioning
      _logger.error('Analytics', 'Failed to send event to Supabase', e);

      // Store failed event for retry
      _storeFailedEvent(eventData);
    }
  }

  /// Store failed event for later retry
  Future<void> _storeFailedEvent(Map<String, dynamic> eventData) async {
    try {
      // Get stored failed events
      final prefs = await SharedPreferences.getInstance();
      final failedEventsString =
          prefs.getStringList('failed_analytics_events') ?? [];

      // Add new failed event
      failedEventsString.add(eventData.toString());

      // Limit size to prevent memory issues
      if (failedEventsString.length > 100) {
        failedEventsString.removeAt(0); // Remove oldest
      }

      // Save updated list
      await prefs.setStringList('failed_analytics_events', failedEventsString);
    } catch (e) {
      _logger.error('Analytics', 'Failed to store failed event', e);
    }
  }

  /// Retry sending failed events
  Future<void> _retryFailedEvents() async {
    try {
      // Get stored failed events
      final prefs = await SharedPreferences.getInstance();
      final failedEventsString =
          prefs.getStringList('failed_analytics_events') ?? [];

      if (failedEventsString.isEmpty) return;

      // Clear stored events (we'll add back any that still fail)
      await prefs.setStringList('failed_analytics_events', []);

      // Retry each event with exponential backoff
      for (int i = 0; i < min(10, failedEventsString.length); i++) {
        try {
          final eventData = Map<String, dynamic>.from(
            // This is a simplistic conversion - in production you'd use json.decode
            // but this requires proper formatting of the stored string
            failedEventsString[i] as Map<String, dynamic>,
          );
          await _sendToSupabase(eventData);
        } catch (e) {
          // If still failing, store again
          _storeFailedEvent(failedEventsString[i] as Map<String, dynamic>);
        }
      }
    } catch (e) {
      _logger.error('Analytics', 'Failed to retry events', e);
    }
  }

  /// Enable or disable analytics
  void setAnalyticsEnabled(bool enabled) {
    _analyticsEnabled = enabled;
    _logger.setAnalyticsEnabled(enabled);
    _logger.info('Analytics', 'Analytics ${enabled ? 'enabled' : 'disabled'}');

    // Track setting change if enabled
    if (enabled) {
      trackEvent('analytics_setting_changed', {'enabled': enabled});
    }
  }

  /// Dispose the analytics service
  void dispose() {
    _batchTimer?.cancel();

    // Send any queued events before disposing
    if (_eventQueue.isNotEmpty) {
      _sendBatchEvents();
    }
  }
}
