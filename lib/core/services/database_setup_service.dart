import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DatabaseSetupService {
  final SupabaseClient _supabaseClient;

  DatabaseSetupService(this._supabaseClient);

  /// Creates all necessary tables and inserts sample data.
  /// Throws an exception if any step fails.
  Future<void> setupDatabaseSchemaAndData() async {
    if (kDebugMode) {
      print('Starting database setup...');
    }
    // Use a list of functions returning SQL strings to avoid complex escaping in one list
    final List<String Function()> sqlGetters = [
      () => _createProfilesTableSql,
      () => _enableRLSProfiles,
      () => _policySelectOwnProfile,
      () => _policyInsertOwnProfile,
      () => _policyUpdateOwnProfile,
      () => _createPartnersTableSql,
      () => _enableRLSPartners,
      () => _policySelectPublicPartners,
      () => _createRewardsTableSql,
      () => _addRewardsUniqueConstraintSql,
      () => _enableRLSRewards,
      () => _policySelectPublicRewards,
      () => _createDetoxPointsTableSql,
      () => _enableRLSDetoxPoints,
      () => _policySelectOwnDetoxPoints,
      () => _policySelectLeaderboardDetoxPoints,
      () => _createRedemptionsTableSql,
      () => _enableRLSRedemptions,
      () => _policySelectOwnRedemptions,
      () => _policyInsertOwnRedemption,
      () => _createPointsHistoryTableSql,
      () => _enableRLSPointsHistory,
      () => _policySelectOwnPointsHistory,
      () => _createLeaderboardViewSql,
      () => _insertSamplePartnersSql,
      () => _insertSampleRewardsSql,
      () => _insertSampleUsersSql,
      () => _insertSampleUserPointsSql,
      () => _insertSamplePointsHistorySql,
    ];

    for (final getSql in sqlGetters) {
      final sql = getSql(); // Get the SQL string from the getter
      if (sql.trim().isEmpty) continue;
      if (kDebugMode) {
        print('Executing: ${sql.split(' ').take(4).join(' ')}...');
      }
      try {
        await _supabaseClient.rpc('execute_sql', params: {'query': sql});
        if (kDebugMode) {
          print('  -> Success');
        }
      } catch (e) {
        // Provide more context in the error message
        final shortSql = sql.length > 100 ? '${sql.substring(0, 97)}...' : sql;
        if (kDebugMode) {
          print('Failed to execute SQL: \n$shortSql\nError: $e');
        }
        throw Exception(
          'Failed during database setup step. Check console logs. Error: ${e.toString()}',
        );
      }
    }
    if (kDebugMode) {
      print('Database setup finished.');
    }
  }

  // --- SQL Definitions ---

  String get _createProfilesTableSql => '''
  CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid NOT NULL PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username text UNIQUE NOT NULL,
    avatar_id text NOT NULL DEFAULT 'avatar_pro_0',
    bio text,
    age integer,
    family_status text,
    goals text[],
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL
  );

  -- Permissions
  ALTER TABLE public.profiles OWNER TO postgres;
  GRANT ALL ON TABLE public.profiles TO postgres;
  GRANT SELECT, INSERT, UPDATE ON TABLE public.profiles TO authenticated;
  GRANT SELECT ON TABLE public.profiles TO anon;
  COMMENT ON TABLE public.profiles IS 'Stores public profile information for users.';
  ''';

  String get _enableRLSProfiles =>
      'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;';

  String get _policySelectOwnProfile => '''
  DROP POLICY IF EXISTS "Allow authenticated users to select own profile" ON public.profiles;
  CREATE POLICY "Allow authenticated users to select own profile" ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);
  ''';

  String get _policyInsertOwnProfile => '''
  DROP POLICY IF EXISTS "Allow authenticated users to insert own profile" ON public.profiles;
  CREATE POLICY "Allow authenticated users to insert own profile" ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);
  ''';

  String get _policyUpdateOwnProfile => '''
  DROP POLICY IF EXISTS "Allow authenticated users to update own profile" ON public.profiles;
  CREATE POLICY "Allow authenticated users to update own profile" ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);
  ''';

  String get _createPartnersTableSql => '''
  CREATE TABLE IF NOT EXISTS public.partners (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    image_url text,
    description text,
    created_at timestamptz DEFAULT now() NOT NULL
  );

  -- Permissions
  ALTER TABLE public.partners OWNER TO postgres;
  GRANT ALL ON TABLE public.partners TO postgres;
  GRANT SELECT ON TABLE public.partners TO authenticated, anon;
  COMMENT ON TABLE public.partners IS 'Stores information about reward partners.';
  ''';

  String get _enableRLSPartners =>
      'ALTER TABLE public.partners ENABLE ROW LEVEL SECURITY;';

  String get _policySelectPublicPartners => '''
  DROP POLICY IF EXISTS "Allow public read access to partners" ON public.partners;
  CREATE POLICY "Allow public read access to partners" ON public.partners
  FOR SELECT
  USING (true);
  ''';

  String get _createRewardsTableSql => '''
  CREATE TABLE IF NOT EXISTS public.rewards (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    description text NOT NULL,
    points_cost integer NOT NULL CHECK (points_cost >= 0),
    partner_id uuid NOT NULL REFERENCES public.partners(id) ON DELETE CASCADE,
    category text NOT NULL,
    location text,
    expiry_date timestamptz,
    is_available boolean NOT NULL DEFAULT true,
    redemption_count integer NOT NULL DEFAULT 0 CHECK (redemption_count >= 0),
    max_redemptions integer CHECK (max_redemptions IS NULL OR max_redemptions >= 0),
    created_at timestamptz DEFAULT now() NOT NULL
  );

  -- Permissions
  ALTER TABLE public.rewards OWNER TO postgres;
  GRANT ALL ON TABLE public.rewards TO postgres;
  GRANT SELECT ON TABLE public.rewards TO authenticated, anon;
  COMMENT ON TABLE public.rewards IS 'Stores available rewards users can redeem.';
  ''';

  String get _addRewardsUniqueConstraintSql => '''
  -- First check if constraint already exists
  ALTER TABLE public.rewards 
  DROP CONSTRAINT IF EXISTS rewards_name_partner_id_key;
  
  -- Add the unique constraint for name + partner_id combination
  ALTER TABLE public.rewards ADD CONSTRAINT rewards_name_partner_id_key UNIQUE (name, partner_id);
  ''';

  String get _enableRLSRewards =>
      'ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;';

  String get _policySelectPublicRewards => '''
  DROP POLICY IF EXISTS "Allow public read access to rewards" ON public.rewards;
  CREATE POLICY "Allow public read access to rewards" ON public.rewards
  FOR SELECT
  USING (true);
  ''';

  String get _createDetoxPointsTableSql => '''
  CREATE TABLE IF NOT EXISTS public.detox_points (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    user_name text NOT NULL, -- Denormalized username
    avatar_url text, -- Denormalized avatar URL
    total_points integer NOT NULL DEFAULT 0 CHECK (total_points >= 0),
    level integer NOT NULL DEFAULT 1 CHECK (level >= 1),
    available_points integer NOT NULL DEFAULT 0 CHECK (available_points >= 0),
    points_to_next_level integer NOT NULL DEFAULT 100 CHECK (points_to_next_level >= 0),
    last_updated timestamptz DEFAULT now() NOT NULL,
    UNIQUE (user_id) -- Ensure only one points record per user
  );

  -- Permissions
  ALTER TABLE public.detox_points OWNER TO postgres;
  GRANT ALL ON TABLE public.detox_points TO postgres;
  GRANT SELECT, UPDATE ON TABLE public.detox_points TO authenticated;
  GRANT SELECT ON TABLE public.detox_points TO anon;
  COMMENT ON TABLE public.detox_points IS 'Stores user points, level, and related stats.';
  ''';

  String get _enableRLSDetoxPoints =>
      'ALTER TABLE public.detox_points ENABLE ROW LEVEL SECURITY;';

  String get _policySelectOwnDetoxPoints => '''
  DROP POLICY IF EXISTS "Allow user to select own points" ON public.detox_points;
  CREATE POLICY "Allow user to select own points" ON public.detox_points
  FOR SELECT
  USING (auth.uid() = user_id);
  ''';

  String get _policySelectLeaderboardDetoxPoints => '''
  DROP POLICY IF EXISTS "Allow public select for leaderboard" ON public.detox_points;
  CREATE POLICY "Allow public select for leaderboard" ON public.detox_points
  FOR SELECT
  USING (true);
  ''';

  String get _createRedemptionsTableSql => '''
  CREATE TABLE IF NOT EXISTS public.redemptions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    reward_id uuid NOT NULL REFERENCES public.rewards(id) ON DELETE RESTRICT,
    points_spent integer NOT NULL CHECK (points_spent >= 0),
    redeemed_at timestamptz DEFAULT now() NOT NULL
  );

  -- Permissions
  ALTER TABLE public.redemptions OWNER TO postgres;
  GRANT ALL ON TABLE public.redemptions TO postgres;
  GRANT SELECT, INSERT ON TABLE public.redemptions TO authenticated;
  COMMENT ON TABLE public.redemptions IS 'Logs when a user redeems a reward.';
  ''';

  String get _enableRLSRedemptions =>
      'ALTER TABLE public.redemptions ENABLE ROW LEVEL SECURITY;';

  String get _policySelectOwnRedemptions => '''
  DROP POLICY IF EXISTS "Allow user to select own redemptions" ON public.redemptions;
  CREATE POLICY "Allow user to select own redemptions" ON public.redemptions
  FOR SELECT
  USING (auth.uid() = user_id);
  ''';

  String get _policyInsertOwnRedemption => '''
  DROP POLICY IF EXISTS "Allow user to insert own redemptions" ON public.redemptions;
  CREATE POLICY "Allow user to insert own redemptions" ON public.redemptions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);
  ''';

  String get _createPointsHistoryTableSql => '''
  CREATE TABLE IF NOT EXISTS public.points_history (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    points integer NOT NULL,
    reason text NOT NULL,
    created_at timestamptz DEFAULT now() NOT NULL
  );

  -- Permissions
  ALTER TABLE public.points_history OWNER TO postgres;
  GRANT ALL ON TABLE public.points_history TO postgres;
  GRANT SELECT ON TABLE public.points_history TO authenticated;
  COMMENT ON TABLE public.points_history IS 'Logs changes to user points.';
  ''';

  String get _enableRLSPointsHistory =>
      'ALTER TABLE public.points_history ENABLE ROW LEVEL SECURITY;';

  String get _policySelectOwnPointsHistory => '''
  DROP POLICY IF EXISTS "Allow user to select own points history" ON public.points_history;
  CREATE POLICY "Allow user to select own points history" ON public.points_history
  FOR SELECT
  USING (auth.uid() = user_id);
  ''';

  String get _createLeaderboardViewSql => '''
  -- Create a view for the leaderboard sorted by total points
  DROP VIEW IF EXISTS public.leaderboard;
  CREATE VIEW public.leaderboard AS
    SELECT 
      dp.user_id,
      dp.user_name,
      dp.avatar_url,
      dp.total_points,
      dp.level,
      ROW_NUMBER() OVER (ORDER BY dp.total_points DESC) as rank
    FROM 
      public.detox_points dp
    ORDER BY 
      dp.total_points DESC;
      
  -- Grant permission to use the view
  GRANT SELECT ON TABLE public.leaderboard TO authenticated, anon;
  COMMENT ON VIEW public.leaderboard IS 'Provides a ranked view of users by total points';
  ''';

  String get _insertSamplePartnersSql => '''
  INSERT INTO public.partners (name, image_url, description)
  VALUES
    ('Local Cafe', 'https://placekitten.com/g/200/200', 'Cozy spot for coffee and pastries.'),
    ('Yoga Studio', 'https://placekitten.com/g/201/201', 'Relax and rejuvenate with our classes.'),
    ('Bookstore', 'https://placekitten.com/g/202/202', 'Find your next great read.'),
    ('Tech Store', 'https://placekitten.com/g/203/203', 'The latest gadgets and accessories.'),
    ('Movie Theater', 'https://placekitten.com/g/204/204', 'Catch the latest blockbusters.'),
    ('Sports Center', 'https://placekitten.com/g/205/205', 'Stay active with various sports facilities.'),
    ('Art Gallery', 'https://placekitten.com/g/206/206', 'Explore contemporary art exhibitions.'),
    ('Music Store', 'https://placekitten.com/g/207/207', 'Instruments and vinyl records for music lovers.')
  ON CONFLICT (name) DO NOTHING;
  ''';

  String get _insertSampleRewardsSql => '''
  WITH 
    cafe AS (SELECT id FROM public.partners WHERE name = 'Local Cafe'),
    yoga AS (SELECT id FROM public.partners WHERE name = 'Yoga Studio'),
    book AS (SELECT id FROM public.partners WHERE name = 'Bookstore'),
    tech AS (SELECT id FROM public.partners WHERE name = 'Tech Store'),
    movie AS (SELECT id FROM public.partners WHERE name = 'Movie Theater'),
    sports AS (SELECT id FROM public.partners WHERE name = 'Sports Center'),
    art AS (SELECT id FROM public.partners WHERE name = 'Art Gallery'),
    music AS (SELECT id FROM public.partners WHERE name = 'Music Store')
  INSERT INTO public.rewards (name, description, points_cost, partner_id, category, is_available)
  VALUES
    -- Dining Category
    ('Free Coffee', 'Get a free coffee of your choice.', 50, (SELECT id FROM cafe), 'dining', true),
    ('10% Off Pastry', 'Enjoy 10% off any pastry item.', 25, (SELECT id FROM cafe), 'dining', true),
    ('Free Dessert', 'Complimentary dessert with any meal purchase.', 75, (SELECT id FROM cafe), 'dining', true),
    ('Lunch Special', 'Buy one lunch item, get second 50% off.', 100, (SELECT id FROM cafe), 'dining', true),
    
    -- Wellness Category
    ('Drop-in Yoga Class', 'Attend one drop-in yoga session.', 150, (SELECT id FROM yoga), 'wellness', true),
    ('Meditation Session', 'A 30-minute guided meditation.', 75, (SELECT id FROM yoga), 'wellness', true),
    ('Personal Trainer Session', 'One hour with a certified trainer.', 200, (SELECT id FROM sports), 'wellness', true),
    ('Spa Day Pass', 'Full day access to spa facilities.', 250, (SELECT id FROM yoga), 'wellness', true),
    
    -- Shopping Category
    ('5 Dollar Off Any Book', 'Get 5 dollars off any book purchase over 20 dollars.', 100, (SELECT id FROM book), 'shopping', true),
    ('Free Bookmark', 'Stylish bookmark with any purchase.', 30, (SELECT id FROM book), 'shopping', true),
    ('Tech Accessory Discount', '15% off any accessory purchase.', 120, (SELECT id FROM tech), 'shopping', true),
    ('Phone Case', 'Free phone case with any device purchase.', 150, (SELECT id FROM tech), 'shopping', true),
    
    -- Entertainment Category
    ('Movie Ticket', 'One free movie ticket of your choice.', 175, (SELECT id FROM movie), 'entertainment', true),
    ('Popcorn Upgrade', 'Free size upgrade on any popcorn purchase.', 50, (SELECT id FROM movie), 'entertainment', true),
    ('Art Exhibition Pass', 'Free entry to special exhibitions.', 125, (SELECT id FROM art), 'entertainment', true),
    ('Museum Tour', 'Guided tour of featured exhibitions.', 100, (SELECT id FROM art), 'entertainment', true),
    
    -- Music Category
    ('Vinyl Discount', '20% off any vinyl record.', 110, (SELECT id FROM music), 'music', true),
    ('Free Guitar Pick', 'Custom guitar pick with any purchase.', 25, (SELECT id FROM music), 'music', true),
    ('Instrument Tuning', 'Professional tuning service for any instrument.', 80, (SELECT id FROM music), 'music', true),
    ('Music Lesson', '30-minute introductory lesson on instrument of choice.', 200, (SELECT id FROM music), 'music', true),
    
    -- Sports Category
    ('Equipment Rental', 'Free day rental of sports equipment.', 90, (SELECT id FROM sports), 'sports', true),
    ('Court Time', 'One hour of court time for tennis, basketball, etc.', 120, (SELECT id FROM sports), 'sports', true),
    ('Swimming Pass', 'Day pass to swimming facilities.', 80, (SELECT id FROM sports), 'sports', true),
    ('Sports Lesson', 'Introductory lesson in sport of choice.', 150, (SELECT id FROM sports), 'sports', true)
  ON CONFLICT (name, partner_id) DO NOTHING;
  ''';

  String get _insertSampleUsersSql => '''
  -- Note: In real usage, users are created through Supabase Auth
  -- This SQL is for documentation only - won't actually insert sample users
  -- due to the foreign key constraint requiring IDs to exist in auth.users
  
  -- To actually create users, use the Supabase Auth API first,
  -- then create profiles with the matching IDs
  
  -- SKIP EXECUTING THIS STATEMENT
  SELECT 1 WHERE false;
  ''';

  String get _insertSampleUserPointsSql => '''
  -- Since we can't insert sample users due to the foreign key constraint,
  -- We also can't insert sample points
  -- This is kept for documentation only
  
  -- SKIP EXECUTING THIS STATEMENT
  SELECT 1 WHERE false;
  ''';

  String get _insertSamplePointsHistorySql => '''
  -- Since we can't insert sample users due to the foreign key constraint,
  -- We also can't insert sample points history
  -- This is kept for documentation only
  
  -- SKIP EXECUTING THIS STATEMENT
  SELECT 1 WHERE false;
  ''';
}
