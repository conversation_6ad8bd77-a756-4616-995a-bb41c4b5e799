import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:detoxme/core/constants/hive_constants.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/core/network/network_status_cubit.dart';
import 'package:detoxme/core/services/analytics_service.dart';
import 'package:detoxme/core/services/logger_service.dart';
import 'package:detoxme/core/services/notification_service.dart';
import 'package:detoxme/core/services/sound_service.dart';
import 'package:detoxme/core/services/sync_service.dart';
import 'package:detoxme/core/services/weather_service.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/data/local/activity_local_cache.dart';
import 'package:detoxme/data/local/hive_sync_queue_data_source.dart';
import 'package:detoxme/data/local/local_auth_data_source.dart';
import 'package:detoxme/data/local/local_food_mood_data_source.dart';
import 'package:detoxme/data/local/local_food_mood_data_source_impl.dart';
import 'package:detoxme/data/local/local_profile_data_source.dart';
import 'package:detoxme/data/local/local_reward_data_source.dart';
import 'package:detoxme/data/local/models/detox_points_model.dart';
import 'package:detoxme/data/local/models/reward_model.dart';
import 'package:detoxme/data/remote/activity_remote_data_source.dart';
import 'package:detoxme/data/remote/activity_remote_data_source_impl.dart';
import 'package:detoxme/data/remote/remote_food_mood_data_source.dart';
import 'package:detoxme/data/remote/remote_food_mood_data_source_impl.dart';
import 'package:detoxme/data/repositories/activity_repository_impl.dart';
import 'package:detoxme/data/repositories/auth_repository_impl.dart';
import 'package:detoxme/data/repositories/dashboard_repository.dart';
import 'package:detoxme/data/repositories/detox_points_repository_impl.dart';
import 'package:detoxme/data/repositories/device_token_repository_impl.dart';
import 'package:detoxme/data/repositories/food_mood_repository_impl.dart';
import 'package:detoxme/data/repositories/profile_repository_impl.dart';
import 'package:detoxme/data/repositories/reward_repository_impl.dart';
import 'package:detoxme/data/repositories/mood_quote_repository_impl.dart';
import 'package:detoxme/data/repositories/coupon_repository_impl.dart';
import 'package:detoxme/data/repositories/notification_repository_impl.dart';
import 'package:detoxme/data/repositories/leaderboard_repository_impl.dart';
import 'package:detoxme/data/repositories/reward_history_repository_impl.dart';

import 'package:detoxme/data/remote/notification_remote_data_source.dart';
import 'package:detoxme/data/remote/notification_remote_data_source_impl.dart';
import 'package:detoxme/data/remote/leaderboard_remote_data_source.dart';
import 'package:detoxme/data/remote/leaderboard_remote_data_source_impl.dart';
import 'package:detoxme/data/remote/coupon_remote_data_source.dart';
import 'package:detoxme/data/remote/reward_remote_data_source.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/repositories/activity_repository.dart';
import 'package:detoxme/domain/repositories/auth_repository.dart';
import 'package:detoxme/domain/repositories/detox_points_repository.dart';
import 'package:detoxme/domain/repositories/device_token_repository.dart';
import 'package:detoxme/domain/repositories/food_mood_repository.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';
import 'package:detoxme/domain/repositories/reward_repository.dart';
import 'package:detoxme/domain/repositories/mood_quote_repository.dart';
import 'package:detoxme/domain/repositories/coupon_repository.dart';
import 'package:detoxme/domain/repositories/notification_repository.dart';
import 'package:detoxme/domain/repositories/leaderboard_repository.dart';
import 'package:detoxme/domain/repositories/reward_history_repository.dart';

import 'package:detoxme/presentation/bloc/activity/activity_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/detox_points/detox_points_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/profile/profile_cubit.dart';
import 'package:detoxme/presentation/bloc/mood_quote/mood_quote_cubit.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:detoxme/domain/usecases/complete_activity_usecase.dart';
import 'package:detoxme/domain/usecases/get_current_user_usecase.dart';
import 'package:detoxme/domain/usecases/get_best_motivation_match_usecase.dart';
import 'package:detoxme/presentation/bloc/activity/activity_timer_cubit.dart';
import 'package:detoxme/data/repositories/ai_motivation_repository_impl.dart';
import 'package:detoxme/domain/repositories/ai_motivation_repository.dart';
import 'package:detoxme/core/services/wellbeing_assistant_service.dart';

/// Global service locator
final serviceLocator = GetIt.instance;

/// Initialize service locator
Future<void> initializeServiceLocator() async {
  // External Dependencies
  // Check if SupabaseClient is already registered to prevent duplicate registration
  if (!serviceLocator.isRegistered<SupabaseClient>()) {
    serviceLocator.registerLazySingleton<SupabaseClient>(
      () => Supabase.instance.client,
    );
  }

  serviceLocator.registerLazySingleton<Connectivity>(() => Connectivity());
  final prefs = await SharedPreferences.getInstance();
  serviceLocator.registerSingleton<SharedPreferences>(prefs);

  // Hive Boxes (Assuming they are opened in StorageInitializer)
  serviceLocator.registerLazySingleton<Box<DetoxPointsModel>>(
    () => Hive.box<DetoxPointsModel>('detox_points_box'),
  );
  serviceLocator.registerLazySingleton<Box<RewardModel>>(
    () => Hive.box<RewardModel>('rewards_box'),
  );
  serviceLocator.registerLazySingleton<Box<FoodRecommendation>>(
    () => Hive.box<FoodRecommendation>(HiveConstants.foodRecommendationsBox),
  );
  serviceLocator.registerLazySingleton<Box<String>>(
    () => Hive.box<String>(HiveConstants.userVotesBox),
  );
  // Add other Hive boxes if needed (e.g., sync queue, profile)
  serviceLocator.registerLazySingleton<HiveSyncQueueDataSource>(
    () => HiveSyncQueueDataSource(), // Assumes default constructor
  );

  // Core Services
  serviceLocator.registerLazySingleton<LoggerService>(() => LoggerService());
  serviceLocator.registerLazySingleton<AnalyticsService>(
    () => AnalyticsService(), // Initialization happens separately
  );
  serviceLocator.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(serviceLocator<Connectivity>()),
  );
  serviceLocator.registerLazySingleton<SoundService>(() => SoundService());
  serviceLocator.registerLazySingleton<WeatherService>(() => WeatherService());

  // Register AppLocaleProvider
  serviceLocator.registerLazySingleton<AppLocaleProvider>(
    () => AppLocaleProvider()..initialize(),
  );

  // Data Sources
  serviceLocator.registerLazySingleton<LocalAuthDataSource>(
    () => LocalAuthDataSourceImpl(),
  );
  serviceLocator.registerLazySingleton<LocalProfileDataSource>(
    () => LocalProfileDataSourceImpl(),
  );
  serviceLocator.registerLazySingleton<LocalRewardDataSource>(
    () => LocalRewardDataSourceImpl(),
  );
  serviceLocator.registerLazySingleton<ActivityLocalCache>(
    () => ActivityLocalCache(prefs: serviceLocator<SharedPreferences>()),
  );
  serviceLocator.registerLazySingleton<ActivityRemoteDataSource>(
    () => ActivityRemoteDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  serviceLocator.registerLazySingleton<LocalFoodMoodDataSource>(
    () => LocalFoodMoodDataSourceImpl(
      foodRecommendationsBox: serviceLocator<Box<FoodRecommendation>>(),
      userVotesBox: serviceLocator<Box<String>>(),
    ),
  );
  serviceLocator.registerLazySingleton<RemoteFoodMoodDataSource>(
    () => RemoteFoodMoodDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  
  // Register missing data sources
  serviceLocator.registerLazySingleton<NotificationRemoteDataSource>(
    () => NotificationRemoteDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  serviceLocator.registerLazySingleton<LeaderboardRemoteDataSource>(
    () => LeaderboardRemoteDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  serviceLocator.registerLazySingleton<CouponRemoteDataSource>(
    () => CouponRemoteDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  serviceLocator.registerLazySingleton<RewardRemoteDataSource>(
    () => RewardRemoteDataSourceImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );

  // Repositories
  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      localDataSource: serviceLocator<LocalAuthDataSource>(),
    ),
  );
  serviceLocator.registerLazySingleton<DeviceTokenRepository>(
    () => DeviceTokenRepositoryImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );
  // Register NotificationService after DeviceTokenRepository
  serviceLocator.registerLazySingleton<NotificationService>(
    () => NotificationService(
      deviceTokenRepository: serviceLocator<DeviceTokenRepository>(),
      // TODO: Define notification handlers if needed here or pass callbacks
      onNotificationTapped: (payload) {
        serviceLocator<LoggerService>().info(
          'NotificationService',
          'Notification tapped with payload: $payload (handled by SL)',
        );
      },
      onNotificationAction: (actionId, payload) {
        serviceLocator<LoggerService>().info(
          'NotificationService',
          'Notification action: $actionId for payload: $payload (handled by SL)',
        );
        // Potentially call serviceLocator<ActivityCubit>().handleNotificationAction(...)
      },
    ),
  );
  serviceLocator.registerLazySingleton<ProfileRepositoryImpl>(
    () => ProfileRepositoryImpl(
      localDataSource: serviceLocator<LocalProfileDataSource>(),
      supabaseClient: serviceLocator<SupabaseClient>(),
      networkInfo: serviceLocator<NetworkInfo>(),
      localSyncQueueRepository: serviceLocator<HiveSyncQueueDataSource>(),
    ),
  );
  serviceLocator.registerLazySingleton<UserRepository>(
    () => serviceLocator<ProfileRepositoryImpl>(),
  );
  serviceLocator.registerLazySingleton<DetoxPointsRepository>(
    () => DetoxPointsRepositoryImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
      pointsBox: serviceLocator<Box<DetoxPointsModel>>(),
      rewardsBox: serviceLocator<Box<RewardModel>>(),
    ),
  );
  serviceLocator.registerLazySingleton<ActivityRepository>(
    () => ActivityRepositoryImpl(
      supabaseClient: serviceLocator<SupabaseClient>(),
      remoteDataSource: serviceLocator<ActivityRemoteDataSource>(),
      localCache: serviceLocator<ActivityLocalCache>(),
    ),
  );
  serviceLocator.registerLazySingleton<RewardRepository>(
    () => RewardRepositoryImpl(
      localDataSource: serviceLocator<LocalRewardDataSource>(),
      localProfileDataSource: serviceLocator<LocalProfileDataSource>(),
      localSyncQueueRepository: serviceLocator<HiveSyncQueueDataSource>(),
      supabaseClient: serviceLocator<SupabaseClient>(),
      networkInfo: serviceLocator<NetworkInfo>(),
    ),
  );
  serviceLocator.registerLazySingleton<FoodMoodRepository>(
    () => FoodMoodRepositoryImpl(
      remoteDataSource: serviceLocator<RemoteFoodMoodDataSource>(),
      localDataSource: serviceLocator<LocalFoodMoodDataSource>(),
      networkInfo: serviceLocator<NetworkInfo>(),
      supabaseClient: serviceLocator<SupabaseClient>(),
      localeProvider: serviceLocator<AppLocaleProvider>(),
    ),
  );
  serviceLocator.registerLazySingleton<DashboardRepository>(
    () => DashboardRepository(weatherService: serviceLocator<WeatherService>()),
  );
  serviceLocator.registerLazySingleton<SyncService>(
    () => SyncService(
      localSyncQueueRepository: serviceLocator<HiveSyncQueueDataSource>(),
      supabaseClient: serviceLocator<SupabaseClient>(),
      networkInfo: serviceLocator<NetworkInfo>(),
    ),
  );

  // Register AI Motivation Repository
  serviceLocator.registerLazySingleton<AiMotivationRepository>(
    () => AiMotivationRepositoryImpl(supabaseClient: serviceLocator<SupabaseClient>()),
  );

  // Register Mood Quote Repository
  serviceLocator.registerLazySingleton<MoodQuoteRepository>(
    () => MoodQuoteRepositoryImpl(supabaseClient: serviceLocator<SupabaseClient>()),
  );

  // Register missing repositories
  serviceLocator.registerLazySingleton<CouponRepository>(
    () => CouponRepositoryImpl(
      remoteDataSource: serviceLocator<CouponRemoteDataSource>(),
      networkInfo: serviceLocator<NetworkInfo>(),
    ),
  );

  serviceLocator.registerLazySingleton<NotificationRepository>(
    () => NotificationRepositoryImpl(
      remoteDataSource: serviceLocator<NotificationRemoteDataSource>(),
    ),
  );

  serviceLocator.registerLazySingleton<LeaderboardRepository>(
    () => LeaderboardRepositoryImpl(
      remoteDataSource: serviceLocator<LeaderboardRemoteDataSource>(),
      networkInfo: serviceLocator<NetworkInfo>(),
    ),
  );

  serviceLocator.registerLazySingleton<RewardHistoryRepository>(
    () => RewardHistoryRepositoryImpl(
      remoteDataSource: serviceLocator<RewardRemoteDataSource>(),
      networkInfo: serviceLocator<NetworkInfo>(),
    ),
  );



  // Register GetBestMotivationMatchUseCase
  serviceLocator.registerLazySingleton<GetBestMotivationMatchUseCase>(
    () => GetBestMotivationMatchUseCase(repository: serviceLocator<AiMotivationRepository>()),
  );

  // Register WellbeingAssistantService
  serviceLocator.registerLazySingleton<WellbeingAssistantService>(
    () => WellbeingAssistantService(
      notificationService: serviceLocator<NotificationService>(),
      supabaseClient: serviceLocator<SupabaseClient>(),
    ),
  );

  // Use Cases
  serviceLocator.registerFactory<CompleteActivityUseCase>(
    () => CompleteActivityUseCase(
      detoxPointsRepository: serviceLocator<DetoxPointsRepository>(),
      activityRepository: serviceLocator<ActivityRepository>(),
    ),
  );
  serviceLocator.registerFactory<GetCurrentUserUseCase>(
    () =>
        GetCurrentUserUseCase(userRepository: serviceLocator<UserRepository>()),
  );

  // Blocs/Cubits
  serviceLocator.registerLazySingleton<NetworkStatusCubit>(
    () => NetworkStatusCubit(connectivity: serviceLocator<Connectivity>()),
  );
  serviceLocator.registerLazySingleton<AuthCubit>(
    () => AuthCubit(
      supabaseClient: serviceLocator<SupabaseClient>(),
      authRepository: serviceLocator<AuthRepository>(),
      deviceTokenRepository: serviceLocator<DeviceTokenRepository>(),
      notificationService: serviceLocator<NotificationService>(),
      userRepository: serviceLocator<UserRepository>(),
    ),
  );
  serviceLocator.registerLazySingleton<ProfileCubit>(
    () => ProfileCubit(profileRepository: serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<DetoxPointsCubit>(
    () => DetoxPointsCubit(
      detoxPointsRepository: serviceLocator<DetoxPointsRepository>(),
      authCubit: serviceLocator<AuthCubit>(),
    ),
  );
  serviceLocator.registerLazySingleton<ActivityCubit>(
    () => ActivityCubit(
      activityRepository: serviceLocator<ActivityRepository>(),
      notificationService: serviceLocator<NotificationService>(),
      localeProvider: serviceLocator<AppLocaleProvider>(),
      detoxPointsCubit: serviceLocator<DetoxPointsCubit>(),
      authCubit: serviceLocator<AuthCubit>(),
    ),
  );
  serviceLocator.registerLazySingleton<FoodMoodCubit>(
    () => FoodMoodCubit(
      repository: serviceLocator<FoodMoodRepository>(),
      localeProvider: serviceLocator<AppLocaleProvider>(),
    ),
  );
  serviceLocator.registerLazySingleton<DashboardCubit>(
    () => DashboardCubit(
      repository: serviceLocator<DashboardRepository>(),
      activityCubit: serviceLocator<ActivityCubit>(),
      foodMoodCubit: serviceLocator<FoodMoodCubit>(),
    ),
  );
  serviceLocator.registerLazySingleton<RewardCubit>(
    () => RewardCubit(rewardRepository: serviceLocator<RewardRepository>()),
  );
  serviceLocator.registerLazySingleton<MoodQuoteCubit>(
    () => MoodQuoteCubit(moodQuoteRepository: serviceLocator<MoodQuoteRepository>()),
  );

  // Cubits
  serviceLocator.registerFactory<ActivityTimerCubit>(
    () => ActivityTimerCubit(
      completeActivityUseCase: serviceLocator<CompleteActivityUseCase>(),
      getCurrentUserUseCase: serviceLocator<GetCurrentUserUseCase>(),
    ),
  );
}
