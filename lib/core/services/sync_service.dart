import 'dart:math'; // For pow()

// Added for box names
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';
import 'package:detoxme/data/local/models/failed_update_hive_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart'; // Added for Hive exception check
import 'package:supabase_flutter/supabase_flutter.dart';

// Maximum number of sync attempts before moving to dead-letter queue
const _maxSyncRetries = 3;
// Base delay for exponential backoff (e.g., 10 seconds)
const _baseRetryDelaySeconds = 10;

/// Service responsible for processing the offline sync queue for various data types.
class SyncService {
  final LocalSyncQueueRepository localSyncQueueRepository;
  final SupabaseClient supabaseClient;
  final NetworkInfo networkInfo;

  SyncService({
    required this.localSyncQueueRepository,
    required this.supabaseClient,
    required this.networkInfo,
  });

  /// Processes the generic update queue if connected to the network.
  Future<void> processUpdateQueue() async {
    // Renamed method
    if (await networkInfo.isConnected) {
      if (kDebugMode) {
        print('Network connected. Processing sync queue...');
      }
      try {
        final queuedUpdates = await localSyncQueueRepository.getQueuedUpdates();
        final now = DateTime.now();
        int processedCount = 0;

        if (kDebugMode) {
          print(
            'Found ${queuedUpdates.length} items in sync queue. Processing items due for retry...',
          );
        }

        for (final update in queuedUpdates) {
          // Check if item is due for retry
          if (update.nextRetryAt == null || now.isAfter(update.nextRetryAt!)) {
            processedCount++;
            String? targetTable;
            Map<String, dynamic>? upsertData;

            try {
              // --- Determine Supabase target based on updateType ---
              switch (update.updateType) {
                case 'playerStats':
                  targetTable = 'player_stats';
                  // Reconstruct data for Supabase from payload
                  upsertData = {
                    'user_id': update.payload['userId'],
                    'username': update.payload['username'],
                    'total_xp': update.payload['currentXp'],
                    'updated_at':
                        now.toIso8601String(), // Use current time for sync
                    // Add other relevant fields if needed (level, badges etc.)
                  };
                  break;
                case 'childProfile': // Example for future expansion
                  targetTable = 'child_profiles'; // Assuming table name
                  upsertData = {
                    'user_id': update.payload['userId'],
                    'username': update.payload['username'],
                    'avatar_id': update.payload['avatarId'],
                    'updated_at': now.toIso8601String(),
                  };
                  break;
                // Add cases for other update types here
                default:
                  if (kDebugMode) {
                    print(
                      'Unsupported updateType: ${update.updateType} for item key ${update.key}. Skipping.',
                    );
                  }
                  // Optionally move to failed queue immediately or just skip
                  continue; // Skip this iteration
              }
              // --- End Determine Supabase target ---

              // Perform Supabase operation
              await supabaseClient.from(targetTable).upsert(upsertData);
            
              // If upsert successful, remove from queue
              await localSyncQueueRepository.removeUpdateFromQueue(update.key);

              if (kDebugMode) {
                print(
                  'Successfully synced ${update.updateType} and removed item with key ${update.key}',
                );
              }
            } on Exception catch (e) {
              // Handle potential Hive errors during save or other exceptions
              if (e is HiveError) {
                if (kDebugMode) {
                  print(
                    'HiveError during sync processing for key ${update.key}: $e. Skipping retry logic for this run.',
                  );
                }
                continue; // Skip retry logic for Hive errors during processing
              }

              update.retryCount++;

              if (update.retryCount >= _maxSyncRetries) {
                // Max retries reached, move to dead-letter queue
                if (kDebugMode) {
                  print(
                    'Max retries ($_maxSyncRetries) reached for ${update.updateType} item key ${update.key}. Moving to dead-letter queue. Error: $e',
                  );
                }
                final failedUpdate = FailedUpdateHiveModel(
                  updateType: update.updateType,
                  payload: Map.from(update.payload),
                  queuedAt: update.queuedAt,
                  failedAt: now,
                  errorDetails: e.toString(),
                  finalRetryCount: update.retryCount,
                );
                await localSyncQueueRepository.addFailedUpdate(failedUpdate);
                await localSyncQueueRepository.removeUpdateFromQueue(
                  update.key,
                );
              } else {
                // Calculate next retry time and save update
                final delaySeconds =
                    _baseRetryDelaySeconds * pow(2, update.retryCount - 1);
                update.nextRetryAt = now.add(
                  Duration(seconds: delaySeconds.toInt()),
                );
                try {
                  await update
                      .save(); // Save updated retryCount and nextRetryAt
                } on HiveError catch (hiveError) {
                  // Handle error saving the update itself
                  if (kDebugMode) {
                    print(
                      'HiveError saving update for key ${update.key} after failed sync attempt: $hiveError. Item may not retry correctly.',
                    );
                  }
                  // Decide if we should attempt removal or leave potentially unsaved state
                }

                if (kDebugMode) {
                  print(
                    'Error syncing ${update.updateType} item key ${update.key} (attempt ${update.retryCount}/$_maxSyncRetries). Scheduling retry after ${delaySeconds.toInt()}s. Error: $e',
                  );
                }
              }
              continue;
            }
          } else {
            // Item not due for retry
          }
        }
        if (kDebugMode && processedCount > 0) {
          if (kDebugMode) {
            print(
            'Sync queue processing finished for due items ($processedCount processed).',
          );
          }
        } else if (kDebugMode &&
            queuedUpdates.isNotEmpty &&
            processedCount == 0) {
          if (kDebugMode) {
            print('Sync queue checked, no items due for processing right now.');
          }
        }
      } catch (e) {
        // Catch errors getting the queue itself
        if (kDebugMode) {
          print('Error processing sync queue: $e');
        }
      }
    } else {
      if (kDebugMode) {
        print('Network offline. Skipping sync queue processing.');
      }
    }
  }

  // TODO: Consider adding a method to trigger processing for a specific type? 
  // TODO: Add mechanism to view/retry/clear the dead-letter queue?
}
