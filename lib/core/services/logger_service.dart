import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// LogLevel enum for determining severity of log messages
enum LogLevel {
  /// Detailed debug information
  debug,

  /// Interesting runtime events (startup/shutdown)
  info,

  /// Non-critical issues that should be reviewed
  warning,

  /// Critical failures that require immediate attention
  error,

  /// For analytics logging
  analytics,
}

/// A structured logging service with Supabase analytics integration
class LoggerService {
  /// The singleton instance
  static final LoggerService _instance = LoggerService._internal();

  /// SupabaseClient for sending analytics data
  late final SupabaseClient _supabaseClient;

  /// Whether analytics logging is enabled
  bool _analyticsEnabled = true;

  /// Whether debug logging is enabled
  bool _debugLoggingEnabled = true;

  /// The minimum log level to display
  LogLevel _minimumLogLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  /// Factory constructor to return the singleton instance
  factory LoggerService() => _instance;

  /// Private constructor for singleton pattern
  LoggerService._internal();

  /// Initialize the logger with a Supabase client
  void initialize({
    required SupabaseClient supabaseClient,
    bool analyticsEnabled = true,
    bool debugLoggingEnabled = true,
    LogLevel minimumLogLevel = LogLevel.debug,
  }) {
    _supabaseClient = supabaseClient;
    _analyticsEnabled = analyticsEnabled;
    _debugLoggingEnabled = debugLoggingEnabled;
    _minimumLogLevel = minimumLogLevel;

    // Log initialization
    info('LoggerService', 'Initialized with analytics: $_analyticsEnabled');
  }

  /// Log a debug message
  void debug(String tag, String message, [Map<String, dynamic>? data]) {
    _log(LogLevel.debug, tag, message, data);
  }

  /// Log an info message
  void info(String tag, String message, [Map<String, dynamic>? data]) {
    _log(LogLevel.info, tag, message, data);
  }

  /// Log a warning message
  void warning(String tag, String message, [Map<String, dynamic>? data]) {
    _log(LogLevel.warning, tag, message, data);
  }

  /// Log an error message
  void error(
    String tag,
    String message, [
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  ]) {
    final errorData = <String, dynamic>{
      ...?data,
      if (error != null) 'error': error.toString(),
      if (stackTrace != null) 'stackTrace': stackTrace.toString(),
    };

    _log(LogLevel.error, tag, message, errorData);
  }

  /// Log an analytics event
  Future<void> logAnalytics(
    String eventName,
    Map<String, dynamic> properties,
  ) async {
    // Always log analytics events locally
    _log(LogLevel.analytics, 'Analytics', eventName, properties);

    // Skip sending to Supabase if analytics is disabled
    if (!_analyticsEnabled) return;

    try {
      // Add timestamp to analytics data
      final eventData = {
        'event': eventName,
        'properties': properties,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Send to Supabase analytics table
      await _supabaseClient.from('analytics_events').insert(eventData);
    } catch (e) {
      // Don't log to Supabase if there's an error (to avoid infinite loops)
      if (kDebugMode && _debugLoggingEnabled) {
        print('🔴 Failed to send analytics to Supabase: $e');
      }
    }
  }

  /// Internal log method
  void _log(
    LogLevel level,
    String tag,
    String message, [
    Map<String, dynamic>? data,
  ]) {
    // Skip logging if below minimum level
    if (level.index < _minimumLogLevel.index) return;

    // Skip debug logs in production unless explicitly enabled
    if (!kDebugMode && level == LogLevel.debug && !_debugLoggingEnabled) return;

    // Format log with emoji for easier reading
    String emoji;
    switch (level) {
      case LogLevel.debug:
        emoji = '🔍';
        break;
      case LogLevel.info:
        emoji = '📘';
        break;
      case LogLevel.warning:
        emoji = '⚠️';
        break;
      case LogLevel.error:
        emoji = '🔴';
        break;
      case LogLevel.analytics:
        emoji = '📊';
        break;
    }

    // Create formatted log message
    final buffer = StringBuffer('$emoji [$tag] $message');

    // Format data as JSON if provided
    if (data != null && data.isNotEmpty) {
      final jsonData = const JsonEncoder.withIndent('  ').convert(data);
      buffer.write('\n$jsonData');
    }

    // Only print in debug mode or if debug logging is explicitly enabled
    if (kDebugMode || _debugLoggingEnabled) {
      print(buffer.toString());
    }
  }

  /// Toggle analytics logging
  void setAnalyticsEnabled(bool enabled) {
    _analyticsEnabled = enabled;
    info(
      'LoggerService',
      'Analytics logging ${enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle debug logging
  void setDebugLoggingEnabled(bool enabled) {
    _debugLoggingEnabled = enabled;
    info('LoggerService', 'Debug logging ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Set minimum log level
  void setMinimumLogLevel(LogLevel level) {
    _minimumLogLevel = level;
    info('LoggerService', 'Minimum log level set to ${level.name}');
  }
}
