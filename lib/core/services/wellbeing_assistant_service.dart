import 'dart:async';

import 'package:detoxme/core/services/notification_service.dart';
import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/domain/entities/activity.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for providing personalized digital wellbeing assistance
class WellbeingAssistantService {
  /// Service for sending notifications
  final NotificationService _notificationService;
  
  /// The Supabase client for direct data access
  final SupabaseClient _supabaseClient;

  // Note: Currently not using the ActivityRepository directly
  
  // We use shared preferences for tracking last notification time instead
  // of an in-memory variable
  
  /// The cooldown period between notifications in hours
  static const int _notificationCooldownHours = 12;
  
  /// The last notification time preference key
  static const String _lastNotificationTimeKey = 'last_wellbeing_notification_time';
  
  // Test data is now generated directly in the forceTestNotification method
  // The _getTestMotivationData and _getTestActivityOnly methods are kept
  // for reference but should be considered for removal in future cleanup

  /// Creates a new wellbeing assistant service
  WellbeingAssistantService({
    required NotificationService notificationService,
    SupabaseClient? supabaseClient,
  })  : _notificationService = notificationService,
        _supabaseClient = supabaseClient ?? Supabase.instance.client;

  /// Checks if a notification can be sent based on time constraints
  ///
  /// Returns true if:
  /// 1. Current time is between 9:00 and 19:00
  /// 2. Last notification was sent more than 12 hours ago
  Future<bool> canSendNotification() async {
    final now = DateTime.now();
    
    // Check if current time is within allowed time window (9:00-19:00)
    if (now.hour < 9 || now.hour >= 19) {
      return false;
    }

    // Check cooldown period
    final prefs = await SharedPreferences.getInstance();
    final lastNotificationTimeMs = prefs.getInt(_lastNotificationTimeKey) ?? 0;
    final lastNotificationTime = 
        DateTime.fromMillisecondsSinceEpoch(lastNotificationTimeMs);
    
    final hoursSinceLastNotification = 
        now.difference(lastNotificationTime).inHours;
    
    return hoursSinceLastNotification >= _notificationCooldownHours;
  }

  /// Get age group from birth year (18-25, 26-35, 36-45, 46+)
  String _getAgeGroupFromBirthYear(int birthYear) {
    final int age = DateTime.now().year - birthYear;
    if (age < 26) return '18-25';
    if (age < 36) return '26-35';
    if (age < 46) return '36-45';
    return '46+';
  }

  /// Create an activity from a motivation data
  Activity _createActivityFromSuggestion(Map<String, dynamic> motivationData) {
    final title = motivationData['activity_title'] ?? 'Take a Digital Break';
    final description = motivationData['activity_description'] ?? 
        'Step away from your screens for at least 15 minutes. Go for a short walk, stretch, or simply close your eyes and breathe deeply.';
    
    // Default to relaxation if no category provided
    final categoryStr = motivationData['activity_category'] ?? 'relaxation';
    final category = ActivityCategory.values.firstWhere(
      (c) => c.toString().split('.').last == categoryStr.toLowerCase(),
      orElse: () => ActivityCategory.relaxation,
    );
    
    final benefits = motivationData['activity_benefits'] != null 
        ? (motivationData['activity_benefits'] as String).split(',').map((e) => e.trim()).toList() 
        : ['Reduced screen time', 'Increased mindfulness'];
    
    return Activity(
      id: 'activity_${motivationData['id']}',
      title: title,
      description: description,
      category: category,
      duration: 15, // Duration in minutes as an integer
      recommendedFor: [MoodType.anxious, MoodType.tired],
      healthBenefits: benefits,
    );
  }

  /// Get a list of personalized motivational content and related activities for a user (for slider)
  Future<List<({AiMotivation motivation, Activity activity})>> getPersonalizedMotivationAndActivities(UserProfile userProfile, {int limit = 5, String? language}) async {
    try {
      debugPrint('WellbeingAssistantService: Getting personalized motivations for user ${userProfile.id}');
      final String currentLanguage = language ?? 'en';
      final String lifeSituation = userProfile.lifeSituation ?? 'have_children';
      final int birthYear = userProfile.birthYear ?? DateTime.now().year - 30;
      final String ageGroup = _getAgeGroupFromBirthYear(birthYear);
      final String gender = userProfile.gender?.toLowerCase() ?? 'any';
      final String goal = userProfile.goals.isNotEmpty ? userProfile.goals.first : 'better_family_time';
      debugPrint('WellbeingAssistantService: Querying with params - language: $currentLanguage, life: $lifeSituation, age: $ageGroup, gender: $gender, goal: $goal');
      final List<({AiMotivation motivation, Activity activity})> result = [];
      try {
        final response = await _supabaseClient
            .from('ai_motivations')
            .select()
            .eq('language', currentLanguage)
            .eq('gender', gender)
            .limit(limit);
        debugPrint('WellbeingAssistantService: Supabase response length: ${response.length}');
        for (final motivationData in response) {
          final motivation = AiMotivation(
            motivationalText: motivationData['motivational_text'],
            activityId: 'activity_${motivationData['id']}',
            language: motivationData['language'] ?? currentLanguage,
            ageGroup: motivationData['age_group'] ?? '26-35',
            gender: motivationData['gender'] ?? 'any',
            lifeSituation: motivationData['life_situation'] ?? 'any',
            goal: motivationData['goal'] ?? 'better_digital_health',
          );
          final activity = _createActivityFromSuggestion(motivationData);
          result.add((motivation: motivation, activity: activity));
        }
        if (result.isNotEmpty) {
          return result;
        }
      } catch (e) {
        debugPrint('WellbeingAssistantService: Error querying Supabase for slider: $e');
      }
      // Fallback: return a single test entry if no data
      final now = DateTime.now();
      return [
        (
          motivation: AiMotivation(
            motivationalText: 'Taking regular breaks from your digital devices can significantly improve your focus and mental clarity. Even a 10-minute break can reset your mind.',
            activityId: 'test_activity_1',
            language: 'en',
            ageGroup: '26-35',
            gender: 'any',
            lifeSituation: 'professional',
            goal: 'better_digital_health',
          ),
          activity: Activity(
            id: 'test_activity_1',
            title: '10-Minute Digital Detox Break',
            description: 'Step away from your devices for 10 minutes. Go for a short walk, stretch, or simply rest your eyes. Notice how you feel when you return.',
            category: ActivityCategory.mindfulness,
            duration: 10,
            recommendedFor: [MoodType.happy, MoodType.anxious],
            healthBenefits: [
              'Reduces eye strain',
              'Decreases stress levels',
              'Improves focus when returning to work'
            ],
            isSystemActivity: true,
            showCreatorName: false,
            isPrivate: false,
            countryCode: 'en',
            upvotes: 0,
            downvotes: 0,
            createdAt: now,
            updatedAt: now,
          ),
        ),
      ];
    } catch (e) {
      debugPrint('Error getting personalized motivations for slider: $e');
      return [];
    }
  }

  /// Get the most personalized motivational content and related activity for a user
  Future<({AiMotivation motivation, Activity activity})> 
      getPersonalizedMotivationAndActivity(UserProfile userProfile) async {
    try {
      debugPrint('WellbeingAssistantService: Getting personalized motivation for user ${userProfile.id}');
      
      // Get parameters for querying ai_motivations table
      final String language = 'en'; // Default to English
      final String lifeSituation = userProfile.lifeSituation ?? 'have_children';
      final int birthYear = userProfile.birthYear ?? DateTime.now().year - 30; // Default to 30 years old
      final String ageGroup = _getAgeGroupFromBirthYear(birthYear);
      final String gender = userProfile.gender?.toLowerCase() ?? 'any';
      final String goal = userProfile.goals.isNotEmpty ? userProfile.goals.first : 'better_family_time';
      
      debugPrint('WellbeingAssistantService: Querying with params - language: $language, life: $lifeSituation, age: $ageGroup, gender: $gender, goal: $goal');
      
      try {
        // Try to get data from the Supabase ai_motivations table
        final response = await _supabaseClient
            .from('ai_motivations')
            .select()
            .eq('language', language)
            .eq('gender', gender)
            .limit(5);
        
        debugPrint('WellbeingAssistantService: Supabase response length: ${response.length}');
        
        if (response.isNotEmpty) {
          final motivationData = response[0];
          debugPrint('WellbeingAssistantService: Found motivation: ${motivationData['motivational_text']}');
          
          // Create AiMotivation from the database result
          final motivation = AiMotivation(
            motivationalText: motivationData['motivational_text'],
            activityId: 'activity_${motivationData['id']}', 
            language: motivationData['language'] ?? 'en',
            ageGroup: motivationData['age_group'] ?? '26-35',
            gender: motivationData['gender'] ?? 'any',
            lifeSituation: motivationData['life_situation'] ?? 'any',
            goal: motivationData['goal'] ?? 'better_digital_health',
          );
          
          // Create a matching activity using the activity data from the motivation
          final activity = _createActivityFromSuggestion(motivationData);
          
          return (motivation: motivation, activity: activity);
        } else {
          debugPrint('WellbeingAssistantService: No matching motivation found in Supabase, falling back to test data');
        }
      } catch (e) {
        debugPrint('WellbeingAssistantService: Error querying Supabase: $e');
      }
      
      // Fall back to test data if database query fails
      final now = DateTime.now();
      return (
        motivation: AiMotivation(
          motivationalText: 'Taking regular breaks from your digital devices can significantly improve your focus and mental clarity. Even a 10-minute break can reset your mind.',
          activityId: 'test_activity_1',
          language: 'en',
          ageGroup: '26-35',
          gender: 'any',
          lifeSituation: 'professional',
          goal: 'better_digital_health',
        ),
        activity: Activity(
          id: 'test_activity_1',
          title: '10-Minute Digital Detox Break',
          description: 'Step away from your devices for 10 minutes. Go for a short walk, stretch, or simply rest your eyes. Notice how you feel when you return.',
          category: ActivityCategory.mindfulness,
          duration: 10, // Duration in minutes as an integer
          recommendedFor: [MoodType.happy, MoodType.anxious],
          healthBenefits: [
            'Reduces eye strain',
            'Decreases stress levels',
            'Improves focus when returning to work'
          ],
          isSystemActivity: true,
          showCreatorName: false,
          isPrivate: false,
          countryCode: 'en',
          upvotes: 0,
          downvotes: 0,
          createdAt: now,
          updatedAt: now,
        ),
      );
      
      // This would be the regular implementation when database has content
      /*
      final motivationResult = await _getBestMotivationMatchUseCase(
        language: userProfile.language ?? 'en',
        lifeSituation: userProfile.lifeSituation ?? '',
        goals: userProfile.goals,
        gender: userProfile.gender ?? '',
        birthYear: userProfile.birthYear ?? 2000,
      );

      return motivationResult.fold(...)
      */
    } catch (e) {
      debugPrint('Error getting personalized motivation: $e');
      return (
        motivation: AiMotivation.empty(),
        activity: Activity.empty(),
      );
    }
  }

  /// Send a personalized wellbeing notification to the user
  ///
  /// Returns true if the notification was sent
  Future<bool> sendWellbeingNotification(UserProfile userProfile) async {
    try {
      // Check if we should send a notification now
      if (!await canSendNotification()) {
        return false;
      }

      // Get personalized content
      final content = await getPersonalizedMotivationAndActivity(userProfile);
      
      if (content.motivation.motivationalText.isEmpty || 
          content.activity.id.isEmpty) {
        return false;
      }

      // Create a personalized notification message
      final notificationTitle = _createNotificationTitle(
        content.motivation, 
        content.activity,
      );
      
      final notificationBody = _createNotificationBody(
        content.motivation, 
        content.activity,
      );

      // Create payload with activity ID to use when notification is tapped
      final payload = 'activity:${content.activity.id}:autostart';

      // Send the notification
      await _notificationService.showGeneralNotification(
        title: notificationTitle,
        body: notificationBody,
        payload: payload,
      );

      // Update last notification time
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        _lastNotificationTimeKey, 
        DateTime.now().millisecondsSinceEpoch,
      );

      return true;
    } catch (e) {
      debugPrint('Error sending wellbeing notification: $e');
      return false;
    }
  }

  /// Creates a personalized notification title
  String _createNotificationTitle(AiMotivation motivation, Activity activity) {
    // Convert the activity category to an emoji
    final emoji = _categoryToEmoji(activity.category);
    
    // Use the emoji + a short version of the motivation 
    return '$emoji ${_getShortTitle(motivation.motivationalText)}';
  }

  /// Creates a personalized notification body
  String _createNotificationBody(AiMotivation motivation, Activity activity) {
    // Create a call-to-action based on the activity
    return '${motivation.motivationalText} Tap to start "${activity.title}".';
  }

  /// Converts an activity category to an emoji
  String _categoryToEmoji(ActivityCategory category) {
    switch (category) {
      case ActivityCategory.outdoor:
        return '🌳';
      case ActivityCategory.physical:
        return '🏃‍♂️';
      case ActivityCategory.creative:
        return '🎨';
      case ActivityCategory.social:
        return '👨‍👩‍👧';
      case ActivityCategory.relaxation:
        return '🧘‍♀️';
      case ActivityCategory.mindfulness:
        return '🧠';
      case ActivityCategory.learning:
        return '📚';
    }
  }

  /// Gets a short title from a longer motivation text
  String _getShortTitle(String motivationText) {
    // If text contains a dash or colon, use the first part
    if (motivationText.contains(' — ')) {
      return motivationText.split(' — ')[0];
    } else if (motivationText.contains(' - ')) {
      return motivationText.split(' - ')[0];
    } else if (motivationText.contains(': ')) {
      return motivationText.split(': ')[0];
    }
    
    // Otherwise, return the first sentence or truncate
    final firstSentence = motivationText.split('.').first;
    if (firstSentence.length <= 50) {
      return firstSentence;
    }
    
    // Truncate if too long
    return '${motivationText.substring(0, 47)}...';
  }



  
  /// Force test a notification to be sent regardless of time constraints
  /// Returns true if the notification was sent successfully
  Future<bool> forceTestNotification(UserProfile userProfile) async {
    
    try {
      debugPrint('WellbeingAssistantService: Forcing test notification for user ${userProfile.id}');
      
      // Create test data instead of calling getPersonalizedMotivationAndActivity
      final motivation = AiMotivation(
        motivationalText: 'Taking regular breaks from your digital devices can significantly improve your focus and mental clarity.',
        activityId: 'test_activity_1',
        language: 'en',
        ageGroup: '26-35',
        gender: 'any',
        lifeSituation: 'professional',
        goal: 'better_digital_health',
      );
      
      final activity = Activity(
        id: 'test_activity_1',
        title: '10-Minute Digital Detox Break',
        description: 'Step away from all screens for just 10 minutes. You can use this time to stretch, look out a window, or practice deep breathing.',
        category: ActivityCategory.mindfulness,
        duration: 10,
        recommendedFor: [MoodType.anxious, MoodType.tired],
        healthBenefits: [
          'Reduced eye strain',
          'Mental refreshment',
          'Improved focus',
          'Decreased digital dependency',
        ],
        isSystemActivity: true,
        countryCode: 'en',
      );

      // Create notification content
      final notificationTitle = '${_categoryToEmoji(activity.category)} Digital Wellbeing Tip';
      final notificationBody = _getShortTitle(motivation.motivationalText);

      final payload = 'activity:${activity.id}:autostart';

      // Get the notification service from service locator directly
      await serviceLocator<NotificationService>().showGeneralNotification(
        title: notificationTitle,
        body: notificationBody,
        payload: payload,
      );
      
      debugPrint('WellbeingAssistantService: Test notification sent successfully');
      return true;
    } catch (e) {
      debugPrint('WellbeingAssistantService: Error sending test notification: $e');
      return false;
    }
  }
}
