import 'dart:math';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:detoxme/core/constants/app_constants.dart';

/// Types of notifications
enum NotificationType {
  morning, // 6am-12pm
  afternoon, // 12pm-6pm
  evening, // 6pm-10pm
  screenTime, // When screen time is high
  inactivity, // When user hasn't used app for some time
  general, // General encouragement
}

/// Service to provide personalized notification content based on user data
class NotificationContentService {
  final Random _random = Random();

  // Cache the user preferences
  String? _ageGroup;
  String? _familyStatus;
  List<String> _goals = [];

  /// Load user preferences from SharedPreferences
  Future<void> loadUserPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    _ageGroup = prefs.getString(AppConstants.onboardingAgeGroupKey);
    _familyStatus = prefs.getString(AppConstants.onboardingFamilyStatusKey);
    _goals = prefs.getStringList(AppConstants.onboardingGoalsKey) ?? [];
  }

  /// Get a personalized notification based on user profile and time of day
  /// Returns a map with 'title' and 'body' for the notification
  Future<Map<String, String>> getPersonalizedNotification({
    NotificationType type = NotificationType.general,
  }) async {
    // Ensure preferences are loaded
    if (_ageGroup == null) {
      await loadUserPreferences();
    }

    // Determine which set of messages to use based on family status
    if (_familyStatus == 'Have children' ||
        _familyStatus?.contains('children') == true) {
      // Return parent-focused messages
      return _getParentNotification(type);
    } else if (_familyStatus == 'Single' ||
        _familyStatus?.contains('Single') == true) {
      // Return single-focused messages
      return _getSingleNotification(type);
    } else if (_goals.contains('Improve focus & concentration') ||
        _goals.any((goal) => goal.contains('focus'))) {
      // Return professional/focus messages
      return _getProfessionalNotification(type);
    } else {
      // Return general messages
      return _getGeneralNotification(type);
    }
  }

  /// Get a random message from a list
  Map<String, String> _getRandomMessage(List<Map<String, String>> messages) {
    final index = _random.nextInt(messages.length);
    return messages[index];
  }

  /// Get notification for parents
  Map<String, String> _getParentNotification(NotificationType type) {
    switch (type) {
      case NotificationType.morning:
        return _getRandomMessage([
          {
            'title': 'Good Morning!',
            'body':
                'The time with your children is precious. Put your phone down and share a morning moment with them.',
          },
          {
            'title': 'Start the Day Together',
            'body':
                'How about a quick family breakfast without screens? The connection you build now lasts forever.',
          },
          {
            'title': 'Morning Quality Time',
            'body':
                'Your children will remember the moments you were present, not the emails you answered.',
          },
        ]);

      case NotificationType.afternoon:
        return _getRandomMessage([
          {
            'title': 'Afternoon Break',
            'body':
                'Take 15 minutes to be fully present with your family. Your phone can wait.',
          },
          {
            'title': 'Family Connection',
            'body':
                'Childhoods go by quickly. Pause your scrolling and make some afternoon memories instead.',
          },
          {
            'title': 'Present Parenting',
            'body':
                'Your attention is the most valuable gift for your children. Put your device down and connect.',
          },
        ]);

      case NotificationType.evening:
        return _getRandomMessage([
          {
            'title': 'Family Evening',
            'body':
                'The evening hours are golden for family connection. How about a device-free dinner tonight?',
          },
          {
            'title': 'Bedtime Connection',
            'body':
                'Bedtime stories create more lasting memories than social media. Be present tonight.',
          },
          {
            'title': 'Evening Reflection',
            'body':
                'Your children will model your behavior. Show them the value of screen-free family time.',
          },
        ]);

      case NotificationType.screenTime:
        return _getRandomMessage([
          {
            'title': 'Digital Balance',
            'body':
                'Your kids are watching how you use technology. Take a break and show them balance matters.',
          },
          {
            'title': 'Screen Time Check',
            'body':
                'You\'ve been on your device for a while. Your family might appreciate your attention now.',
          },
        ]);

      case NotificationType.inactivity:
        return _getRandomMessage([
          {
            'title': 'Welcome Back',
            'body':
                'Your digital detox journey continues. How about setting a family challenge today?',
          },
          {
            'title': 'Family Challenge',
            'body':
                'Create a phone-free zone at home today. Your children will treasure the focused attention.',
          },
        ]);

      case NotificationType.general:
      return _getRandomMessage([
          {
            'title': 'Family Mindfulness',
            'body':
                'The time with your children is precious. Put your phone down and give them your full attention.',
          },
          {
            'title': 'Digital Parenting',
            'body':
                'Being present creates stronger family bonds. Take a break from screens and connect with your children.',
          },
          {
            'title': 'Family Connection',
            'body':
                'Children spell love T-I-M-E. Set aside your device and create a memory together.',
          },
        ]);
    }
  }

  /// Get notification for singles
  Map<String, String> _getSingleNotification(NotificationType type) {
    switch (type) {
      case NotificationType.morning:
        return _getRandomMessage([
          {
            'title': 'Mindful Morning',
            'body':
                'Start your day intentionally. Try a 10-minute phone-free breakfast to set a peaceful tone.',
          },
          {
            'title': 'Morning for You',
            'body':
                'Give yourself the gift of a distraction-free morning. The world can wait a few minutes.',
          },
        ]);

      case NotificationType.afternoon:
        return _getRandomMessage([
          {
            'title': 'Afternoon Reset',
            'body':
                'The world is fast and loud. Take a moment for yourself, away from your phone.',
          },
          {
            'title': 'Mid-day Pause',
            'body':
                'Your attention is valuable. Step away from the screen and reconnect with yourself.',
          },
        ]);

      case NotificationType.evening:
        return _getRandomMessage([
          {
            'title': 'Evening Peace',
            'body':
                'Create a peaceful evening ritual without screens. Your mind will thank you for the rest.',
          },
          {
            'title': 'Screen-Free Evening',
            'body':
                'Scrolling before bed affects sleep quality. Try a book or gentle stretching instead.',
          },
        ]);

      case NotificationType.screenTime:
        return _getRandomMessage([
          {
            'title': 'Digital Balance',
            'body':
                'You\'ve been scrolling for a while. How about a quick stretch or a change of scenery?',
          },
          {
            'title': 'Attention Check',
            'body':
                'Your focus is precious. Consider directing it to something that nurtures your wellbeing.',
          },
        ]);

      case NotificationType.inactivity:
        return _getRandomMessage([
          {
            'title': 'Welcome Back',
            'body':
                'How about setting a new digital balance goal today? Your wellbeing deserves the attention.',
          },
          {
            'title': 'Reconnect',
            'body':
                'Take a moment to set some boundaries for your digital life today.',
          },
        ]);

      case NotificationType.general:
      return _getRandomMessage([
          {
            'title': 'Time for You',
            'body':
                'The world is fast and loud. Take a moment for yourself, away from your phone.',
          },
          {
            'title': 'Digital Balance',
            'body':
                'Your attention is your most valuable asset. Invest it in your wellbeing, not your screen.',
          },
          {
            'title': 'Self-Care Reminder',
            'body':
                'Social media will still be there later. Your present moment deserves your full attention.',
          },
        ]);
    }
  }

  /// Get notification for professionals/focus-oriented users
  Map<String, String> _getProfessionalNotification(NotificationType type) {
    switch (type) {
      case NotificationType.morning:
        return _getRandomMessage([
          {
            'title': 'Productive Morning',
            'body':
                'Start your day with intention, not distraction. Try 30 minutes of focused work before checking messages.',
          },
          {
            'title': 'Morning Focus',
            'body':
                'Your morning sets the tone for your day. Protect your best hours from digital distractions.',
          },
        ]);

      case NotificationType.afternoon:
        return _getRandomMessage([
          {
            'title': 'Afternoon Clarity',
            'body':
                'Short breaks without your phone help you stay focused and relaxed. Take 10 minutes to reset.',
          },
          {
            'title': 'Focus Reset',
            'body':
                'Combat the afternoon slump with a brief digital detox. Step away from all screens for 15 minutes.',
          },
        ]);

      case NotificationType.evening:
        return _getRandomMessage([
          {
            'title': 'Evening Boundaries',
            'body':
                'Create a mental boundary between work and rest. Put work communications aside for the evening.',
          },
          {
            'title': 'Productivity Reset',
            'body':
                'Allow your brain to truly recharge by stepping away from screens this evening.',
          },
        ]);

      case NotificationType.screenTime:
        return _getRandomMessage([
          {
            'title': 'Focus Check',
            'body':
                'Extended screen time can fragment attention. Take a 5-minute break to reset your focus.',
          },
          {
            'title': 'Productivity Pause',
            'body':
                'Brief, regular breaks from screens can boost productivity by 20%. Time for a quick pause?',
          },
        ]);

      case NotificationType.inactivity:
        return _getRandomMessage([
          {
            'title': 'Welcome Back',
            'body':
                'Ready to enhance your focus today? Set a productivity goal with regular digital breaks.',
          },
          {
            'title': 'Focus Challenge',
            'body':
                'Try the Pomodoro technique today: 25 minutes of focused work, then 5 minutes completely screen-free.',
          },
        ]);

      case NotificationType.general:
      return _getRandomMessage([
          {
            'title': 'Focus Enhancer',
            'body':
                'Short breaks without your phone help you stay focused and relaxed.',
          },
          {
            'title': 'Productivity Boost',
            'body':
                'Your best work happens in a state of flow. Protect your focus by setting healthy boundaries with technology.',
          },
          {
            'title': 'Deep Work Reminder',
            'body':
                'The ability to focus without distraction is increasingly rare and increasingly valuable.',
          },
        ]);
    }
  }

  /// Get general notification for users with no specific profile
  Map<String, String> _getGeneralNotification(NotificationType type) {
    switch (type) {
      case NotificationType.morning:
        return _getRandomMessage([
          {
            'title': 'Morning Mindfulness',
            'body':
                'Start your day intentionally. Take a few moments away from screens to set your priorities.',
          },
          {
            'title': 'Good Morning',
            'body':
                'Consider beginning your day with presence, not pixels. Your attention deserves direction.',
          },
        ]);

      case NotificationType.afternoon:
        return _getRandomMessage([
          {
            'title': 'Afternoon Reset',
            'body':
                'Take a brief digital detox to refresh your mind. Just 10 minutes can make a difference.',
          },
          {
            'title': 'Midday Mindfulness',
            'body':
                'Your attention is being pulled in many directions. Reclaim it with a brief screen break.',
          },
        ]);

      case NotificationType.evening:
        return _getRandomMessage([
          {
            'title': 'Evening Wind Down',
            'body':
                'Digital blue light can affect sleep quality. Consider switching to offline activities now.',
          },
          {
            'title': 'Rest & Recovery',
            'body':
                'Your mind deserves rest from digital stimulation. Create some screen-free time tonight.',
          },
        ]);

      case NotificationType.screenTime:
        return _getRandomMessage([
          {
            'title': 'Digital Balance',
            'body':
                'You\'ve been spending time on screens. How about a short break to stretch or hydrate?',
          },
          {
            'title': 'Screen Time Check',
            'body':
                'Regular breaks from digital devices help maintain focus and reduce eye strain.',
          },
        ]);

      case NotificationType.inactivity:
        return _getRandomMessage([
          {
            'title': 'Welcome Back',
            'body':
                'Small consistent steps create lasting change. What digital wellness goal can you set today?',
          },
          {
            'title': 'Fresh Start',
            'body':
                'Welcome back to your digital wellness journey. Every moment is an opportunity to choose balance.',
          },
        ]);

      case NotificationType.general:
      return _getRandomMessage([
          {
            'title': 'Digital Wellness',
            'body':
                'Your digital life should support your real life, not replace it. Take a moment to reconnect offline.',
          },
          {
            'title': 'Mindful Technology',
            'body':
                'Use technology intentionally, not habitually. Your attention is too valuable to give away unconsciously.',
          },
          {
            'title': 'Balance Reminder',
            'body':
                'Small pauses from screens throughout your day can lead to greater clarity and presence.',
          },
        ]);
    }
  }

  /// Get notification type based on current time
  static NotificationType getNotificationTypeForCurrentTime() {
    final hour = TimeOfDay.now().hour;

    if (hour >= 6 && hour < 12) {
      return NotificationType.morning;
    } else if (hour >= 12 && hour < 18) {
      return NotificationType.afternoon;
    } else if (hour >= 18 && hour < 22) {
      return NotificationType.evening;
    } else {
      return NotificationType.general;
    }
  }
}
