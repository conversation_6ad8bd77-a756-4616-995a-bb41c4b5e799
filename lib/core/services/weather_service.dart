import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:detoxme/domain/entities/weather_data.dart';

/// Service for fetching weather data
class WeatherService {
  /// Base URL for the OpenWeatherMap API
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';

  /// OpenWeatherMap API key - Replace with your actual API key
  static const String _apiKey = 'd4be78a728134d0776203e9206c4d586';

  /// Cache duration in minutes
  static const int _cacheDuration = 30;

  /// Cache key for weather data
  static const String _cacheKey = 'weather_data';

  /// Cache key for timestamp
  static const String _timestampKey = 'weather_timestamp';

  /// Mock data for development/testing
  static final WeatherData _mockWeatherData = WeatherData(
    temperature: 22.5,
    condition: WeatherCondition.sunny,
    description: 'clear sky',
    iconCode: '01d',
    location: 'Munich',
    timestamp: DateTime.now(),
    humidity: 65,
    windSpeed: 3.6,
    visibility: 10000,
  );

  /// Check if location permission is granted
  Future<bool> _checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  /// Get current position
  Future<Position?> _getCurrentPosition() async {
    try {
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) {
        return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      debugPrint('Error getting location: $e');
      return null;
    }
  }

  /// Check if cache is valid (not older than cache duration)
  Future<bool> _isCacheValid() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_timestampKey);
    if (timestamp == null) {
      return false;
    }

    final cachedTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    return now.difference(cachedTime).inMinutes < _cacheDuration;
  }

  /// Cache weather data
  Future<void> _cacheWeatherData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(data);
    await prefs.setString(_cacheKey, jsonString);
    await prefs.setInt(_timestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Get cached weather data
  Future<WeatherData?> _getCachedWeatherData() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_cacheKey);
    if (jsonString == null) {
      return null;
    }

    try {
      final json = jsonDecode(jsonString);
      return WeatherData.fromJson(json);
    } catch (e) {
      debugPrint('Error parsing cached weather data: $e');
      return null;
    }
  }

  /// Get weather condition from OpenWeatherMap main condition
  WeatherCondition _getWeatherCondition(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return WeatherCondition.sunny;
      case 'clouds':
        return WeatherCondition.cloudy;
      case 'rain':
      case 'drizzle':
        return WeatherCondition.rainy;
      case 'thunderstorm':
        return WeatherCondition.stormy;
      case 'snow':
        return WeatherCondition.snowy;
      case 'mist':
      case 'fog':
      case 'haze':
        return WeatherCondition.foggy;
      default:
        return WeatherCondition.unknown;
    }
  }

  /// Fetch weather data from the API
  Future<WeatherData> getWeatherData({bool useMockData = false}) async {
    if (useMockData) {
      return _mockWeatherData;
    }

    try {
      // Check if cache is valid
      if (await _isCacheValid()) {
        final cachedData = await _getCachedWeatherData();
        if (cachedData != null) {
          return cachedData;
        }
      }

      // Get current position
      final position = await _getCurrentPosition();
      if (position == null) {
        return _mockWeatherData;
      }

      // Make API request
      final response = await http.get(
        Uri.parse(
          '$_baseUrl/weather?lat=${position.latitude}&lon=${position.longitude}&units=metric&appid=$_apiKey',
        ),
      );

      if (response.statusCode == 200) {
        final weatherJson = jsonDecode(response.body);
        await _cacheWeatherData(weatherJson);

        // Create WeatherData with proper condition mapping
        final weatherMain = weatherJson['weather'][0]['main'] as String;
        final weatherCondition = _getWeatherCondition(weatherMain);

        return WeatherData(
          temperature: (weatherJson['main']['temp'] as num).toDouble(),
          condition: weatherCondition,
          description: weatherJson['weather'][0]['description'],
          iconCode: weatherJson['weather'][0]['icon'],
          location: weatherJson['name'],
          timestamp: DateTime.now(),
          humidity: weatherJson['main']['humidity'] as int,
          windSpeed: (weatherJson['wind']['speed'] as num).toDouble(),
          visibility:
              (weatherJson['visibility'] as num).toDouble() /
              1000, // Convert to km
        );
      } else {
        debugPrint('Error fetching weather data: ${response.statusCode}');
        return _mockWeatherData;
      }
    } catch (e) {
      debugPrint('Weather service error: $e');
      return _mockWeatherData;
    }
  }
}
