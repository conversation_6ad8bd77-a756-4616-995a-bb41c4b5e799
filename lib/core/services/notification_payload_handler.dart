import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_bloc.dart';
import 'package:detoxme/presentation/bloc/wellbeing/wellbeing_assistant_event.dart';

/// Service to handle notification payloads
class NotificationPayloadHandler {
  /// Navigates to the appropriate screen based on notification payload
  static void handleNotificationPayload(BuildContext context, String? payload) {
    if (payload == null || payload.isEmpty) {
      return;
    }

    // Handle wellbeing assistant activity navigation
    if (payload.startsWith('activity:')) {
      _handleActivityPayload(context, payload);
    }
  }

  /// Handles a payload related to an activity
  /// Format: activity:{activityId}:autostart
  static void _handleActivityPayload(BuildContext context, String payload) {
    try {
      final parts = payload.split(':');
      if (parts.length < 2) return;

      final activityId = parts[1];
      final autoStart = parts.length > 2 && parts[2] == 'autostart';

      // Get the wellbeing assistant bloc
      final wellbeingBloc = context.read<WellbeingAssistantBloc>();

      // Create and add the notification tapped event
      wellbeingBloc.add(
        WellbeingNotificationTapped(
          activityId: activityId,
          autoStartTimer: autoStart,
        ),
      );

      // Navigate to the activity detail screen
      Navigator.of(context).pushNamed('/activity/$activityId');
    } catch (e) {
      debugPrint('Error handling activity payload: $e');
    }
  }
}
