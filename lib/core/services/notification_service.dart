import 'dart:io';

import 'package:detoxme/domain/entities/device_token.dart';
import 'package:detoxme/domain/repositories/device_token_repository.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

/// Service to handle all notification-related functionality
class NotificationService {
  final DeviceTokenRepository _deviceTokenRepository;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  // For handling notification actions
  final void Function(String)? onNotificationTapped;
  final void Function(String, String)? onNotificationAction;

  // Notification channel IDs
  static const String _activityChannelId = 'activity_channel';
  static const String _generalChannelId = 'general_channel';

  // Notification IDs
  static const int activityNotificationId = 1;

  // Action IDs
  static const String pauseActionId = 'pause_activity';
  static const String resumeActionId = 'resume_activity';
  static const String stopActionId = 'stop_activity';

  NotificationService({
    required DeviceTokenRepository deviceTokenRepository,
    this.onNotificationTapped,
    this.onNotificationAction,
  }) : _deviceTokenRepository = deviceTokenRepository;

  /// Initialize the notification service
  Future<void> initialize() async {
    // Firebase should be initialized before this service is created

    // Configure local notifications for foreground messages
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.high,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);

    await _localNotifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Set up foreground message handler
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Set up background/terminated message handlers
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    FirebaseMessaging.instance.getInitialMessage().then(_handleInitialMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // Setup notification channels for Android
    if (Platform.isAndroid) {
      await _setupNotificationChannels();
    }

    // Request permissions on iOS
    if (Platform.isIOS) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin
          >()
          ?.requestPermissions(alert: true, badge: true, sound: true);

      // Note: Flutter Local Notifications plugin may require configuration in native code
      // to support action categories on iOS. This will be documented in the README.
      debugPrint(
        'iOS notification plugin initialized. Native code configuration may be required for actions.',
      );
    }
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    return settings.authorizationStatus == AuthorizationStatus.authorized ||
        settings.authorizationStatus == AuthorizationStatus.provisional;
  }

  /// Register the device token for the current user
  Future<void> registerDeviceToken(String userId) async {
    try {
      final fcmToken = await _firebaseMessaging.getToken();
      if (fcmToken == null) return;

      // Get device info for better identification
      String? deviceName;
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceName = '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceName = '${iosInfo.name} ${iosInfo.model}';
      }

      final deviceToken = DeviceToken.create(
        userId: userId,
        fcmToken: fcmToken,
        deviceInfo: deviceName,
      );

      try {
        await _deviceTokenRepository.saveToken(deviceToken);
      } catch (e) {
        // Just log the error but don't crash the app
        if (kDebugMode) {
          print('Failed to save FCM token to database: $e');
          print(
            'This is expected if the device_tokens table is not set up correctly',
          );
        }
      }

      // Listen for token refreshes
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _updateToken(userId, newToken, deviceName);
      });
    } catch (e) {
      // Gracefully handle any other errors in the token registration process
      if (kDebugMode) {
        print('Error in FCM token registration: $e');
      }
    }
  }

  /// Update token when it changes
  Future<void> _updateToken(
    String userId,
    String newToken,
    String? deviceInfo,
  ) async {
    try {
      final deviceToken = DeviceToken.create(
        userId: userId,
        fcmToken: newToken,
        deviceInfo: deviceInfo,
      );

      await _deviceTokenRepository.saveToken(deviceToken);
    } catch (e) {
      // Gracefully handle database errors
      if (kDebugMode) {
        print('Error updating FCM token: $e');
      }
    }
  }

  /// Handle when a user taps on a notification
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap here
    // You can add navigation logic based on the payload
    final String? payload = response.payload;
    if (payload != null && onNotificationTapped != null) {
      onNotificationTapped!(payload);
    }

    // Handle notification action
    if (response.actionId != null && onNotificationAction != null) {
      onNotificationAction!(response.actionId!, payload ?? '');
    }
  }

  /// Handle messages received while the app is in the foreground
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    // Show a local notification for foreground messages
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    if (notification != null && android != null && !kIsWeb) {
      _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'high_importance_channel',
            'High Importance Notifications',
            channelDescription:
                'This channel is used for important notifications.',
            icon: android.smallIcon,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: message.data['type'],
      );
    }
  }

  /// Handle initial message when app is launched from terminated state
  Future<void> _handleInitialMessage(RemoteMessage? message) async {
    if (message != null) {
      // Handle the initial message, perhaps navigate to a specific screen
    }
  }

  /// Handle when a user taps on a notification that opened the app
  void _handleMessageOpenedApp(RemoteMessage message) {
    // Handle notification tap when app was in background
    // You can add navigation logic based on the payload
  }

  /// Delete all tokens for a user (typically on logout)
  Future<void> clearUserTokens(String userId) async {
    await _deviceTokenRepository.deleteAllUserTokens(userId);
  }

  /// Setup notification channels for Android
  Future<void> _setupNotificationChannels() async {
    // Activity channel - high importance, ongoing
    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(
          const AndroidNotificationChannel(
            _activityChannelId,
            'Activity Progress',
            description: 'Shows progress for ongoing activities',
            importance: Importance.high,
            playSound: true, // Play sound only when first shown
            enableVibration: true, // Vibrate only when first shown
            enableLights: true,
            showBadge: true,
          ),
        );

    // General channel
    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(
          const AndroidNotificationChannel(
            _generalChannelId,
            'General Notifications',
            description: 'General notifications and announcements',
            importance: Importance.defaultImportance,
          ),
        );
  }

  /// Show an ongoing activity notification
  Future<void> showActivityNotification({
    required String title,
    required String body,
    required int remainingMinutes,
    required int remainingSeconds,
    required bool isPaused,
    String? payload,
  }) async {
    // Format time as mm:ss
    final String timeString =
        '${remainingMinutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';

    // Build action buttons based on paused state
    List<AndroidNotificationAction> androidActions = [];
    if (isPaused) {
      androidActions.add(
        const AndroidNotificationAction(
          resumeActionId,
          'Resume',
          icon: DrawableResourceAndroidBitmap('ic_play_arrow'),
          showsUserInterface: false,
        ),
      );
    } else {
      androidActions.add(
        const AndroidNotificationAction(
          pauseActionId,
          'Pause',
          icon: DrawableResourceAndroidBitmap('ic_pause'),
          showsUserInterface: false,
        ),
      );
    }

    // Always add stop action
    androidActions.add(
      const AndroidNotificationAction(
        stopActionId,
        'Stop',
        icon: DrawableResourceAndroidBitmap('ic_stop'),
        showsUserInterface: false,
      ),
    );

    // Set up Android-specific details
    final AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          _activityChannelId,
          'Activity Progress',
          channelDescription: 'Shows progress for ongoing activities',
          importance: Importance.high,
          priority: Priority.high,
          ongoing: true,
          autoCancel: false,
          onlyAlertOnce: true, // Only alert once
          playSound: false, // Don't play sound after the first time
          enableVibration: false, // Don't vibrate after the first time
          showProgress: true,
          maxProgress: remainingMinutes * 60 + remainingSeconds,
          progress: 0, // Starting from zero counts up
          channelShowBadge: true,
          actions: androidActions,
        );

    // Set up iOS-specific details with buttons
    final DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel:
          InterruptionLevel.active, // Use active level for importance
      sound: null, // Don't play sound when updating
      subtitle: '$timeString remaining',
      categoryIdentifier: 'activity',
    );

    // Combined platform-specific details
    final NotificationDetails platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Show the notification
    await _localNotifications.show(
      activityNotificationId,
      title,
      '$body - $timeString remaining',
      platformDetails,
      payload: payload,
    );
  }

  /// Cancel the activity notification
  Future<void> cancelActivityNotification() async {
    await _localNotifications.cancel(activityNotificationId);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Show a general notification
  Future<void> showGeneralNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await _localNotifications.show(
      title.hashCode, // Use hash of title as ID to avoid duplicates
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          _generalChannelId,
          'General Notifications',
          channelDescription: 'General notifications and announcements',
          importance: Importance.high,
          priority: Priority.high,
          icon: 'ic_notification',
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          interruptionLevel: InterruptionLevel.active,
        ),
      ),
      payload: payload,
    );
  }

  /// Schedule daily motivational notifications to encourage activity
  Future<void> scheduleMotivationalNotifications() async {
    // Cancel any existing motivational notifications
    await cancelMotivationalNotifications();

    final List<String> motivationalMessages = [
      "🌟 Time to disconnect! Your real life is calling - try a quick walk outside.",
      "📱➡️🚶‍♀️ Put that phone down and move your body! Even 5 minutes counts.",
      "💪 Break free from the screen! Do some stretches or jumping jacks right now.",
      "🌱 Your mental health will thank you - step away from digital and into nature.",
      "⏰ Screen break time! Call a friend, do some art, or just breathe deeply.",
      "🎯 Challenge yourself: 10 minutes without your phone. You've got this!",
      "🌈 Real life is happening now - don't miss it scrolling through your phone.",
      "🧘‍♀️ Mindful moment: Put your device aside and focus on the present.",
      "🏃‍♂️ Your body needs movement more than your thumb needs scrolling.",
      "💎 You're worth more than endless notifications - prioritize yourself!",
    ];

    // Schedule notifications at different times throughout the day
    final List<int> notificationHours = [
      9,
      12,
      15,
      18,
      21,
    ]; // 9am, 12pm, 3pm, 6pm, 9pm

    for (int i = 0; i < notificationHours.length; i++) {
      final hour = notificationHours[i];
      final message = motivationalMessages[i % motivationalMessages.length];

      await _localNotifications.zonedSchedule(
        1000 + i, // Unique ID for motivational notifications (1000-1004)
        "DetoxMe Reminder",
        message,
        _nextInstanceOfTime(hour, 0),
        NotificationDetails(
          android: AndroidNotificationDetails(
            _generalChannelId,
            'Motivational Reminders',
            channelDescription:
                'Daily reminders to stay active and reduce screen time',
            importance: Importance.defaultImportance,
            priority: Priority.defaultPriority,
            icon: '@mipmap/ic_launcher',
            styleInformation: BigTextStyleInformation(message),
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time, // Repeat daily
      );
    }

    if (kDebugMode) {
      print(
        'Scheduled ${notificationHours.length} daily motivational notifications',
      );
    }
  }

  /// Cancel all motivational notifications
  Future<void> cancelMotivationalNotifications() async {
    // Cancel motivational notification IDs (1000-1004)
    for (int i = 0; i < 5; i++) {
      await _localNotifications.cancel(1000 + i);
    }
  }

  /// Helper method to get the next instance of a specific time
  tz.TZDateTime _nextInstanceOfTime(int hour, int minute) {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate = tz.TZDateTime(
      tz.local,
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );

    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    return scheduledDate;
  }

  /// Schedule a one-time motivational notification (for testing)
  Future<void> scheduleTestMotivationalNotification() async {
    const String message =
        "🌟 Test notification: Time to disconnect and be active!";

    await _localNotifications.show(
      9999, // Test notification ID
      "DetoxMe Test Reminder",
      message,
      NotificationDetails(
        android: AndroidNotificationDetails(
          _generalChannelId,
          'Test Notifications',
          channelDescription: 'Test motivational notifications',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          styleInformation: const BigTextStyleInformation(message),
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
    );

    if (kDebugMode) {
      print('Scheduled test motivational notification');
    }
  }
}

/// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // Here we can do any background processing needed
  // print('Handling a background message: ${message.messageId}');
}
