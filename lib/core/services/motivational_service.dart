import 'dart:math';
import 'package:detoxme/domain/entities/weather_data.dart';

/// Service for generating motivational messages based on user context
class MotivationalService {
  /// Random generator for message selection
  final Random _random = Random();

  /// Generate a motivational message based on user profile and weather
  String generateMessage({
    required bool hasFamily,
    required bool hasChildren,
    required String? userGoal,
    WeatherData? weatherData,
    int? detoxPoints,
  }) {
    // Base messages for different goals
    final List<String> baseMessages = _getBaseMessages(userGoal);

    // Messages specific to family status
    final List<String> familyMessages = _getFamilyMessages(
      hasFamily,
      hasChildren,
    );

    // Weather-based messages
    final List<String> weatherMessages = _getWeatherMessages(weatherData);

    // Progress-based messages
    final List<String> progressMessages = _getProgressMessages(detoxPoints);

    // Combine all message categories
    final List<String> allMessages = [
      ...baseMessages,
      ...familyMessages,
      ...weatherMessages,
      ...progressMessages,
    ];

    // Get random message
    final int randomIndex = _random.nextInt(allMessages.length);
    return allMessages[randomIndex];
  }

  /// Get base motivational messages based on user goal
  List<String> _getBaseMessages(String? userGoal) {
    if (userGoal == null || userGoal.isEmpty) {
      return [
        "Each moment of digital detox is a gift to yourself.",
        "Small steps lead to big changes in your digital habits.",
        "Today is another chance to reconnect with what matters.",
        "Digital minimalism creates space for real-life maximalism.",
        "You're building a healthier relationship with technology.",
      ];
    }

    switch (userGoal.toLowerCase()) {
      case 'productivity':
        return [
          "Focus creates results. You're on the right track.",
          "Productivity isn't about doing more, but doing what matters.",
          "Each distraction avoided is a step toward your goals.",
          "Your future self will thank you for the focus you're creating today.",
          "Deep work requires digital distance. You're making it happen.",
        ];
      case 'mental health':
        return [
          "Mental clarity comes from digital simplicity.",
          "Your mind deserves the peace you're giving it today.",
          "Each notification ignored is a moment of peace gained.",
          "You're creating mental space for what truly matters.",
          "Digital boundaries create emotional well-being.",
        ];
      case 'social connection':
        return [
          "Real connections happen eye-to-eye, not screen-to-screen.",
          "Today's face-to-face moments create tomorrow's memories.",
          "Being present is the greatest gift you can give others.",
          "Quality time is built on quality attention.",
          "Authentic connections require authentic presence.",
        ];
      case 'sleep improvement':
        return [
          "Better sleep begins with better screen habits.",
          "Your brain is preparing for restful sleep thanks to your efforts.",
          "Tonight's rest is being improved by today's choices.",
          "Sleep quality improves as screen time decreases.",
          "Your body thanks you for the screen-free wind-down time.",
        ];
      default:
        return [
          "Your commitment to change is inspiring.",
          "Each day of mindful technology use builds toward your goals.",
          "Progress isn't perfect, but your efforts matter.",
          "Small changes lead to significant transformations.",
          "You're creating a life where technology serves you, not the reverse.",
        ];
    }
  }

  /// Get family-focused motivational messages
  List<String> _getFamilyMessages(bool hasFamily, bool hasChildren) {
    if (hasChildren) {
      return [
        "Your children are learning healthy tech habits from your example.",
        "The attention you're giving your kids today shapes their tomorrow.",
        "Family memories are made in screen-free moments together.",
        "Your children value your presence more than any digital distraction.",
        "Today's family time creates tomorrow's cherished memories.",
      ];
    }

    if (hasFamily) {
      return [
        "Quality time with loved ones is the true measure of a day well spent.",
        "Your family appreciates your undivided attention.",
        "Relationships thrive when screens don't come between people.",
        "The connections you nurture today strengthen your family bonds.",
        "Being fully present with family creates meaningful moments.",
      ];
    }

    return [
      "Self-investment today creates better relationships tomorrow.",
      "Your personal growth journey benefits everyone around you.",
      "Building healthy habits now prepares you for future relationships.",
      "The relationship with yourself improves as digital distractions decrease.",
      "Personal boundaries with technology create space for meaningful connections.",
    ];
  }

  /// Get weather-based motivational messages
  List<String> _getWeatherMessages(WeatherData? weatherData) {
    if (weatherData == null) {
      return [
        "Each day offers new opportunities to disconnect to reconnect.",
        "Your digital habits are improving, whatever the weather outside.",
        "Inside weather is always what you make it - create your calm today.",
      ];
    }

    final condition = weatherData.condition;
    final isNight = weatherData.iconCode.endsWith('n');
    final temp = weatherData.temperature;

    if (condition == WeatherCondition.sunny && !isNight) {
      return [
        "The sunshine outside is a perfect reminder to brighten your day offline.",
        "Beautiful day outside! Take your break in nature, not on a screen.",
        "Let today's sunshine inspire your mood, not your screen time.",
      ];
    } else if (condition == WeatherCondition.sunny && isNight) {
      return [
        "Stars shine brighter when you look up from your screen.",
        "Use this beautiful night for stargazing, not screen gazing.",
        "The night sky offers wonders no screen can match.",
      ];
    } else if (condition == WeatherCondition.rainy) {
      return [
        "Rainy days are perfect for analog activities - reading, reflecting, relaxing.",
        "Let the rain wash away digital distractions today.",
        "Find the cozy comfort of a book instead of a screen on this rainy day.",
      ];
    } else if (condition == WeatherCondition.snowy) {
      return [
        "Snow creates a peaceful world outside - match it with digital quiet inside.",
        "Winter wonderlands are best experienced firsthand, not through a screen.",
        "The stillness of snow invites us to slow down our digital pace.",
      ];
    } else if (condition == WeatherCondition.cloudy ||
        condition == WeatherCondition.partlyCloudy) {
      return [
        "Even on cloudy days, clarity comes from less screen time.",
        "Clouds outside needn't mean cloudiness in your mind - clear it with less screen time.",
        "A cloudy sky is still more inspiring than a glowing screen.",
      ];
    } else if (temp > 28) {
      // Hot day
      return [
        "Stay cool and collected by reducing digital heat today.",
        "Hot weather calls for cool thinking - digital minimalism helps.",
        "Beat the heat by chilling with real people, not hot devices.",
      ];
    } else if (temp < 5) {
      // Cold day
      return [
        "Warm connections matter more than cold technology on days like this.",
        "Cozy up with real activities instead of your device today.",
        "The warmth of real experiences beats the cold glow of screens.",
      ];
    }

    return [
      "Whatever the weather, your commitment to digital balance shines through.",
      "Today's forecast: Increased well-being with decreased screen time.",
      "Weather changes constantly; let your improved digital habits be your constant.",
    ];
  }

  /// Get progress-based motivational messages
  List<String> _getProgressMessages(int? detoxPoints) {
    if (detoxPoints == null) {
      return [
        "Every moment of mindful tech use counts.",
        "Your commitment to digital balance is making a difference.",
        "Progress comes one mindful decision at a time.",
      ];
    }

    if (detoxPoints < 100) {
      return [
        "You're at the beginning of an important journey. Keep going!",
        "Starting is often the hardest part - and you've already begun!",
        "The first steps of any journey require courage. You've shown it.",
      ];
    } else if (detoxPoints < 500) {
      return [
        "Your consistency is building momentum. Feel the difference?",
        "Progress is evident in your growing detox points!",
        "You're establishing patterns that will serve you well going forward.",
      ];
    } else if (detoxPoints < 1000) {
      return [
        "You've built solid foundations for lasting digital habits.",
        "Your commitment is impressive and the results will follow.",
        "Halfway points are powerful moments - celebrate your progress!",
      ];
    } else {
      return [
        "Your dedication to digital balance is truly inspirational.",
        "You're becoming a master of mindful technology use!",
        "The healthy habits you've built are transforming your daily life.",
        "You've achieved what many find impossible - control over digital life.",
      ];
    }
  }
}
