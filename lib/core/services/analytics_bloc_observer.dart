import 'package:detoxme/core/services/logger_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// A BlocObserver that logs events and state changes for analytics
class AnalyticsBlocObserver extends BlocObserver {
  /// The logger service instance
  final LoggerService _logger;

  /// Whether to log all state changes (even minor ones)
  final bool _logAllStateChanges;

  /// Whether to log events
  final bool _logEvents;

  /// Constructor
  AnalyticsBlocObserver({
    required LoggerService logger,
    bool logAllStateChanges = false,
    bool logEvents = true,
  }) : _logger = logger,
       _logAllStateChanges = logAllStateChanges,
       _logEvents = logEvents;

  @override
  void onChange(BlocBase<dynamic> bloc, Change<dynamic> change) {
    super.onChange(bloc, change);

    // Skip if we're not logging all state changes
    if (!_logAllStateChanges) return;

    final blocName = bloc.runtimeType.toString();
    final currentStateName = change.currentState.runtimeType.toString();
    final nextStateName = change.nextState.runtimeType.toString();

    // Only log details if state types are different or if we're in verbose mode
    if (currentStateName != nextStateName) {
      _logger.debug('Bloc', '$blocName: $currentStateName → $nextStateName', {
        'bloc': blocName,
        'currentState': currentStateName,
        'nextState': nextStateName,
      });
    }
  }

  @override
  void onEvent(Bloc<dynamic, dynamic> bloc, Object? event) {
    super.onEvent(bloc, event);

    // Skip if we're not logging events
    if (!_logEvents) return;

    final blocName = bloc.runtimeType.toString();
    final eventName = event.runtimeType.toString();

    _logger.debug('BlocEvent', '$blocName received $eventName', {
      'bloc': blocName,
      'event': eventName,
    });
  }

  @override
  void onTransition(
    Bloc<dynamic, dynamic> bloc,
    Transition<dynamic, dynamic> transition,
  ) {
    super.onTransition(bloc, transition);

    // Always log transitions for analytics
    final blocName = bloc.runtimeType.toString();
    final eventName = transition.event.runtimeType.toString();
    final currentStateName = transition.currentState.runtimeType.toString();
    final nextStateName = transition.nextState.runtimeType.toString();

    // Log detailed debug data
    _logger.debug(
      'BlocTransition',
      '$blocName: $currentStateName → $nextStateName via $eventName',
      {
        'bloc': blocName,
        'event': eventName,
        'currentState': currentStateName,
        'nextState': nextStateName,
      },
    );

    // For analytics, only log important state transitions
    if (_shouldLogTransitionForAnalytics(transition)) {
      _logger.logAnalytics('bloc_transition', {
        'bloc': blocName,
        'event': eventName,
        'fromState': currentStateName,
        'toState': nextStateName,
      });
    }
  }

  @override
  void onError(BlocBase<dynamic> bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);

    // Always log errors
    final blocName = bloc.runtimeType.toString();

    _logger.error('BlocError', 'Error in $blocName', error, stackTrace, {
      'bloc': blocName,
    });

    // Also log for analytics to track error rates
    _logger.logAnalytics('bloc_error', {
      'bloc': blocName,
      'error': error.toString(),
    });
  }

  /// Determines if a transition should be logged for analytics
  /// This helps reduce noise by only logging important state changes
  bool _shouldLogTransitionForAnalytics(
    Transition<dynamic, dynamic> transition,
  ) {
    final currentState = transition.currentState;
    final nextState = transition.nextState;

    // Different types of states are significant
    if (currentState.runtimeType != nextState.runtimeType) {
      return true;
    }

    // Check for some common important state patterns
    final nextStateName = nextState.runtimeType.toString();

    // Log loading states
    if (nextStateName.contains('Loading')) {
      return true;
    }

    // Log error states
    if (nextStateName.contains('Error') || nextStateName.contains('Failure')) {
      return true;
    }

    // Log success states
    if (nextStateName.contains('Success') ||
        nextStateName.contains('Loaded') ||
        nextStateName.contains('Completed')) {
      return true;
    }

    // Skip other transitions to reduce noise
    return false;
  }
}
