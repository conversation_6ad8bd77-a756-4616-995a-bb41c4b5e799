import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/entities/activity.dart';
import '../../domain/entities/activity_comment.dart';
import '../../domain/entities/mood.dart';
import '../../domain/repositories/activity_repository.dart';
import 'activity_remote_data_source.dart';
import 'models/activity_model.dart';

/// Implementation of ActivityRemoteDataSource using Supabase
class ActivityRemoteDataSourceImpl implements ActivityRemoteDataSource {
  /// Supabase client for API calls
  final SupabaseClient _supabaseClient;

  /// Activities table name
  static const String _activitiesTable = 'activities';

  /// Mood history table name
  static const String _moodHistoryTable = 'mood_history';

  /// User votes table name
  static const String _userVotesTable = 'activity_votes';

  /// Creates an ActivityRemoteDataSourceImpl
  ActivityRemoteDataSourceImpl({required SupabaseClient supabaseClient})
    : _supabaseClient = supabaseClient;

  @override
  Future<List<ActivityModel>> getActivities() async {
    final response = await _supabaseClient
        .from(_activitiesTable)
        .select()
        .order('created_at', ascending: false);

    return response
        .map<ActivityModel>((json) => ActivityModel.fromJson(json))
        .toList();
  }

  @override
  Future<List<ActivityModel>> getActivitiesForMood(MoodType mood) async {
    final response = await _supabaseClient
        .from(_activitiesTable)
        .select()
        .containedBy('recommended_for', [mood.name]);

    return response
        .map<ActivityModel>((json) => ActivityModel.fromJson(json))
        .toList();
  }

  @override
  Future<List<Mood>> getMoodHistory(String userId) async {
    final response = await _supabaseClient
        .from(_moodHistoryTable)
        .select()
        .eq('user_id', userId)
        .order('timestamp', ascending: false);

    return response.map<Mood>((json) {
      return Mood(
        type: _stringToMoodType(json['mood_type']),
        timestamp: DateTime.parse(json['timestamp']),
        note: json['note'],
      );
    }).toList();
  }

  @override
  Future<void> logMood(String userId, Mood mood) async {
    await _supabaseClient.from(_moodHistoryTable).insert({
      'user_id': userId,
      'mood_type': mood.type.name,
      'timestamp': mood.timestamp.toIso8601String(),
      'note': mood.note,
    });
  }

  @override
  Future<ActivityModel> createActivity({
    required String userId,
    required String title,
    required String description,
    required ActivityCategory category,
    required int duration,
    required List<MoodType> recommendedFor,
    List<String> healthBenefits = const [],
    String? imageUrl,
    String? creatorName,
    bool showCreatorName = true,
    bool isPrivate = false,
    String countryCode = 'en',
  }) async {
    final activityData = {
      'title': title,
      'description': description,
      'category': category.name,
      'duration': duration,
      'recommended_for': recommendedFor.map((m) => m.name).toList(),
      'health_benefits': healthBenefits,
      'image_url': imageUrl,
      'is_system_activity': false,
      'created_by': userId,
      'creator_name': creatorName,
      'show_creator_name': showCreatorName,
      'is_private': isPrivate,
      'country_code': countryCode,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    final response =
        await _supabaseClient
            .from(_activitiesTable)
            .insert(activityData)
            .select()
            .single();

    return ActivityModel.fromJson(response);
  }

  @override
  Future<List<ActivityModel>> getUserActivities(String userId) async {
    final response = await _supabaseClient
        .from(_activitiesTable)
        .select()
        .eq('created_by', userId)
        .order('created_at', ascending: false);

    return response
        .map<ActivityModel>((json) => ActivityModel.fromJson(json))
        .toList();
  }

  @override
  Future<ActivityModel> updateActivity(ActivityModel activity) async {
    // Add updated timestamp
    final updatedActivity = {
      ...activity.toJson(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    final response =
        await _supabaseClient
            .from(_activitiesTable)
            .update(updatedActivity)
            .eq('id', activity.id)
            .select()
            .single();

    return ActivityModel.fromJson(response);
  }

  @override
  Future<void> deleteActivity(String activityId, String userId) async {
    await _supabaseClient
        .from(_activitiesTable)
        .delete()
        .eq('id', activityId)
        .eq('created_by', userId);
  }

  @override
  Future<ActivityModel> upvoteActivity(String activityId, String userId) async {
    // First check if the user has already voted
    final existingVotes = await _supabaseClient
        .from(_userVotesTable)
        .select()
        .eq('activity_id', activityId)
        .eq('user_id', userId);

    if (existingVotes.isNotEmpty) {
      final isCurrentlyUpvoted = existingVotes[0]['is_upvote'] == true;

      if (isCurrentlyUpvoted) {
        // User already upvoted, so do nothing
        final activity = await _getActivityById(activityId);
        return ActivityModel.fromJson(activity);
      } else {
        // User previously downvoted, switch to upvote
        await _switchVote(activityId, userId, true);
        return _getUpdatedActivity(activityId);
      }
    } else {
      // No previous vote, add new upvote
      await _addVote(activityId, userId, true);
      return _getUpdatedActivity(activityId);
    }
  }

  @override
  Future<ActivityModel> downvoteActivity(
    String activityId,
    String userId,
  ) async {
    // First check if the user has already voted
    final existingVotes = await _supabaseClient
        .from(_userVotesTable)
        .select()
        .eq('activity_id', activityId)
        .eq('user_id', userId);

    if (existingVotes.isNotEmpty) {
      final isCurrentlyUpvoted = existingVotes[0]['is_upvote'] == true;

      if (!isCurrentlyUpvoted) {
        // User already downvoted, so do nothing
        final activity = await _getActivityById(activityId);
        return ActivityModel.fromJson(activity);
      } else {
        // User previously upvoted, switch to downvote
        await _switchVote(activityId, userId, false);
        return _getUpdatedActivity(activityId);
      }
    } else {
      // No previous vote, add new downvote
      await _addVote(activityId, userId, false);
      return _getUpdatedActivity(activityId);
    }
  }

  @override
  Future<ActivityModel> removeVote(String activityId, String userId) async {
    // First check if the user has voted
    final existingVotes = await _supabaseClient
        .from(_userVotesTable)
        .select()
        .eq('activity_id', activityId)
        .eq('user_id', userId);

    if (existingVotes.isNotEmpty) {
      final isUpvote = existingVotes[0]['is_upvote'] == true;

      // Remove the vote
      await _removeVote(activityId, userId, isUpvote);
      return _getUpdatedActivity(activityId);
    } else {
      // No vote to remove
      final activity = await _getActivityById(activityId);
      return ActivityModel.fromJson(activity);
    }
  }

  @override
  Future<List<ActivityModel>> getFilteredActivities({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    bool includePrivate = false,
    String? userId,
    bool hideDownvoted = true,
    List<String>? countryCodes,
  }) async {
    var query = _supabaseClient.from(_activitiesTable).select();

    // Apply filters
    if (mood != null) {
      // Use contains to check if the recommended_for array contains the mood
      query = query.contains('recommended_for', [mood.name]);
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query = query.or(
        'title.ilike.%$searchQuery%,description.ilike.%$searchQuery%',
      );
    }

    if (category != null) {
      query = query.eq('category', category.name);
    }

    if (!includePrivate) {
      query = query.eq('is_private', false);
    } else if (userId != null) {
      // Include private activities only if they're owned by this user
      query = query.or('is_private.eq.false,created_by.eq.$userId');
    }

    if (hideDownvoted) {
      query = query.lt('downvotes', 20); // Hide heavily downvoted
    }

    if (countryCodes != null && countryCodes.isNotEmpty) {
      query = query.filter(
        'country_code',
        'in',
        '(${countryCodes.map((c) => '"$c"').join(',')})',
      );
    }

    // Apply sorting
    List<Map<String, dynamic>> response;

    switch (sortOption) {
      case ActivitySortOption.newest:
        response = await query.order('created_at', ascending: false);
        break;
      case ActivitySortOption.oldest:
        response = await query.order('created_at', ascending: true);
        break;
      case ActivitySortOption.mostUpvoted:
        response = await query.order('upvotes', ascending: false);
        break;
      case ActivitySortOption.highestRated:
        // Approximate with a computed column or separate query
        response = await query.order('upvotes', ascending: false);
        break;
      case ActivitySortOption.mostPopular:
        // Sort by total votes (upvotes + downvotes)
        response = await query.order('upvotes', ascending: false);
        break;
    }

    return response
        .map<ActivityModel>((json) => ActivityModel.fromJson(json))
        .toList();
  }

  @override
  Future<void> setFavorite(
    String activityId,
    String userId,
    bool isFavorite,
  ) async {
    if (isFavorite) {
      // Save to favorites table
      await _supabaseClient.from('favorite_activities').upsert({
        'user_id': userId,
        'activity_id': activityId,
        'created_at': DateTime.now().toIso8601String(),
      });
    } else {
      // Remove from favorites table
      await _supabaseClient.from('favorite_activities').delete().match({
        'user_id': userId,
        'activity_id': activityId,
      });
    }
  }

  @override
  Future<List<ActivityModel>> getFavoriteActivities(String userId) async {
    // Get favorite activity IDs for this user
    final favoriteData = await _supabaseClient
        .from('favorite_activities')
        .select('activity_id')
        .eq('user_id', userId);

    // Extract activity IDs from the data
    final activityIds =
        favoriteData
            .map<String>((item) => item['activity_id'] as String)
            .toList();

    if (activityIds.isEmpty) {
      return [];
    }

    // Fetch the actual activities
    final activitiesData = await _supabaseClient
        .from('activities')
        .select()
        .filter('id', 'in', activityIds);

    // Convert to activity models and set isFavorite flag internally
    return activitiesData.map((data) => ActivityModel.fromJson(data)).toList();
  }

  @override
  Future<void> setFoodFavorite(
    String foodId,
    String userId,
    bool isFavorite,
  ) async {
    if (isFavorite) {
      // Save to favorites table
      await _supabaseClient.from('favorite_foods').upsert({
        'user_id': userId,
        'food_id': foodId,
        'created_at': DateTime.now().toIso8601String(),
      });
    } else {
      // Remove from favorites table
      await _supabaseClient.from('favorite_foods').delete().match({
        'user_id': userId,
        'food_id': foodId,
      });
    }
  }

  @override
  Future<List<dynamic>> getFavoriteFoods(String userId) async {
    // Get favorite food IDs from Supabase
    final favoritesData = await _supabaseClient
        .from('favorite_foods')
        .select('food_id, foods!inner(*)')
        .eq('user_id', userId);

    if (favoritesData.isEmpty) {
      return [];
    }

    // Extract food data
    return favoritesData.map((item) => item['foods']).toList();
  }

  @override
  Future<ActivityComment> addComment({
    required String activityId,
    required String userId,
    required String userName,
    required String text,
    String? userAvatarUrl,
  }) async {
    final response =
        await _supabaseClient
            .from('activity_comments')
            .insert({
              'activity_id': activityId,
              'user_id': userId,
              'user_name': userName,
              'user_avatar_url': userAvatarUrl,
              'text': text,
              'created_at': DateTime.now().toIso8601String(),
            })
            .select()
            .single();

    return ActivityComment.fromMap(response);
  }

  @override
  Future<List<ActivityComment>> getComments(
    String activityId,
    String? currentUserId,
  ) async {
    final response = await _supabaseClient
        .from('activity_comments')
        .select()
        .eq('activity_id', activityId)
        .order('created_at', ascending: false);

    // If we have a current user, we need to check which comments they've liked
    if (currentUserId != null && response.isNotEmpty) {
      final commentIds = response.map((comment) => comment['id']).toList();

      final likedComments = await _supabaseClient
          .from('activity_comment_likes')
          .select('comment_id')
          .eq('user_id', currentUserId)
          .filter('comment_id', 'in', commentIds);

      final likedCommentIds =
          likedComments.map((like) => like['comment_id']).toSet();

      return response.map((commentData) {
        final isLikedByUser = likedCommentIds.contains(commentData['id']);
        final commentMap = Map<String, dynamic>.from(commentData);
        commentMap['is_liked_by_user'] = isLikedByUser;
        return ActivityComment.fromMap(commentMap);
      }).toList();
    }

    return response
        .map((commentData) => ActivityComment.fromMap(commentData))
        .toList();
  }

  @override
  Stream<List<ActivityComment>> getCommentsStream(
    String activityId,
    String? currentUserId,
  ) {
    return _supabaseClient
        .from('activity_comments')
        .stream(primaryKey: ['id'])
        .eq('activity_id', activityId)
        .order('created_at', ascending: false)
        .asyncMap((List<Map<String, dynamic>> response) async {
          // If we have a current user, we need to check which comments they've liked
          if (currentUserId != null && response.isNotEmpty) {
            final commentIds =
                response.map((comment) => comment['id']).toList();

            final likedComments = await _supabaseClient
                .from('activity_comment_likes')
                .select('comment_id')
                .eq('user_id', currentUserId)
                .filter('comment_id', 'in', commentIds);

            final likedCommentIds =
                likedComments.map((like) => like['comment_id']).toSet();

            return response.map((commentData) {
              final isLikedByUser = likedCommentIds.contains(commentData['id']);
              final commentMap = Map<String, dynamic>.from(commentData);
              commentMap['is_liked_by_user'] = isLikedByUser;
              return ActivityComment.fromMap(commentMap);
            }).toList();
          }

          return response
              .map((commentData) => ActivityComment.fromMap(commentData))
              .toList();
        });
  }

  @override
  Future<void> deleteComment({
    required String commentId,
    required String userId,
  }) async {
    await _supabaseClient
        .from('activity_comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', userId);
  }

  @override
  Future<ActivityComment> editComment({
    required String commentId,
    required String userId,
    required String newText,
  }) async {
    final response =
        await _supabaseClient
            .from('activity_comments')
            .update({
              'text': newText,
              'is_edited': true,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', commentId)
            .eq('user_id', userId)
            .select()
            .single();

    return ActivityComment.fromMap(response);
  }

  @override
  Future<ActivityComment> toggleCommentLike({
    required String commentId,
    required String userId,
  }) async {
    // Check if user already liked this comment
    final existingLike =
        await _supabaseClient
            .from('activity_comment_likes')
            .select()
            .eq('comment_id', commentId)
            .eq('user_id', userId)
            .maybeSingle();

    if (existingLike != null) {
      // Remove the like
      await _supabaseClient
          .from('activity_comment_likes')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', userId);
    } else {
      // Add the like
      await _supabaseClient.from('activity_comment_likes').insert({
        'comment_id': commentId,
        'user_id': userId,
      });
    }

    // Get the updated comment with the new like count (updated by trigger)
    final updatedComment =
        await _supabaseClient
            .from('activity_comments')
            .select()
            .eq('id', commentId)
            .single();

    // Add the is_liked_by_user field
    final commentMap = Map<String, dynamic>.from(updatedComment);
    commentMap['is_liked_by_user'] =
        existingLike == null; // true if we added a like, false if we removed it

    return ActivityComment.fromMap(commentMap);
  }

  @override
  Future<void> reportComment({
    required String commentId,
    required String userId,
    required String reason,
  }) async {
    // For now, we'll just log the report. In a real implementation,
    // you might want to create a comment_reports table
    await _supabaseClient.from('analytics_events').insert({
      'event': 'comment_reported',
      'user_id': userId,
      'properties': {'comment_id': commentId, 'reason': reason},
    });
  }

  // Helper methods

  Future<Map<String, dynamic>> _getActivityById(String activityId) async {
    final response =
        await _supabaseClient
            .from(_activitiesTable)
            .select()
            .eq('id', activityId)
            .single();

    return response;
  }

  Future<ActivityModel> _getUpdatedActivity(String activityId) async {
    final activity = await _getActivityById(activityId);
    return ActivityModel.fromJson(activity);
  }

  Future<void> _addVote(String activityId, String userId, bool isUpvote) async {
    // Add vote to user_votes table
    await _supabaseClient.from(_userVotesTable).insert({
      'activity_id': activityId,
      'user_id': userId,
      'is_upvote': isUpvote,
    });

    // Update vote count in activities table
    await _supabaseClient
        .from(_activitiesTable)
        .update({
          isUpvote ? 'upvotes' : 'downvotes': _supabaseClient.rpc(
            'increment_counter',
            params: {
              'row_id': activityId,
              'counter_name': isUpvote ? 'upvotes' : 'downvotes',
            },
          ),
        })
        .eq('id', activityId);
  }

  Future<void> _switchVote(
    String activityId,
    String userId,
    bool newIsUpvote,
  ) async {
    // Update vote in user_votes table
    await _supabaseClient
        .from(_userVotesTable)
        .update({'is_upvote': newIsUpvote})
        .eq('activity_id', activityId)
        .eq('user_id', userId);

    // Update vote counts in activities table
    await _supabaseClient
        .from(_activitiesTable)
        .update({
          'upvotes': _supabaseClient.rpc(
            newIsUpvote ? 'increment_counter' : 'decrement_counter',
            params: {'row_id': activityId, 'counter_name': 'upvotes'},
          ),
          'downvotes': _supabaseClient.rpc(
            newIsUpvote ? 'decrement_counter' : 'increment_counter',
            params: {'row_id': activityId, 'counter_name': 'downvotes'},
          ),
        })
        .eq('id', activityId);
  }

  Future<void> _removeVote(
    String activityId,
    String userId,
    bool wasUpvote,
  ) async {
    // Remove vote from user_votes table
    await _supabaseClient
        .from(_userVotesTable)
        .delete()
        .eq('activity_id', activityId)
        .eq('user_id', userId);

    // Update vote count in activities table
    await _supabaseClient
        .from(_activitiesTable)
        .update({
          wasUpvote ? 'upvotes' : 'downvotes': _supabaseClient.rpc(
            'decrement_counter',
            params: {
              'row_id': activityId,
              'counter_name': wasUpvote ? 'upvotes' : 'downvotes',
            },
          ),
        })
        .eq('id', activityId);
  }

  /// Convert string to MoodType enum
  MoodType _stringToMoodType(String moodString) {
    switch (moodString) {
      case 'happy':
        return MoodType.happy;
      case 'sad':
        return MoodType.sad;
      case 'angry':
        return MoodType.angry;
      case 'anxious':
        return MoodType.anxious;
      case 'tired':
        return MoodType.tired;
      case 'stressed': // Map to closest equivalent
        return MoodType.anxious;
      case 'depressed': // Map to closest equivalent
        return MoodType.sad; 
      case 'excited':
        return MoodType.excited;
      case 'calm':
        return MoodType.calm;
      case 'bored': // Map to closest equivalent
        return MoodType.neutral;
      case 'neutral':
        return MoodType.neutral;
      case 'inspired':
        return MoodType.inspired;
      default:
        return MoodType.neutral; // Default fallback
    }
  }
}
