import 'package:detoxme/domain/entities/reward.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Interface for remote reward history data operations
abstract class RewardRemoteDataSource {
  /// Get reward history for the current user
  Future<List<Reward>> getRewardHistory();

  /// Get reward summary by type for the current user
  Future<Map<String, dynamic>> getRewardSummary();

  /// Add a custom reward
  Future<Reward> addCustomReward({
    required String title,
    required String description,
    required int pointsEarned,
    required String rewardType,
    Map<String, dynamic>? metadata,
  });
}

/// Implementation of remote data source for reward history
class RewardRemoteDataSourceImpl implements RewardRemoteDataSource {
  /// Supabase client for database operations
  final SupabaseClient supabaseClient;

  /// Constructor
  RewardRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<Reward>> getRewardHistory() async {
    try {
      // Get the current user
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Call the RPC function to get reward history
      final response = await supabaseClient.rpc(
        'get_user_reward_history',
        params: {'user_id': user.id, 'limit_count': 100, 'page': 0},
      );

      // Convert to list of rewards
      final List<dynamic> data = response as List<dynamic>;
      return data
          .map((json) => _mapToReward(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch reward history: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getRewardSummary() async {
    try {
      // Get the current user
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Call the RPC function to get reward summary
      final response = await supabaseClient.rpc(
        'get_user_reward_summary',
        params: {'user_id': user.id},
      );

      // Return the summary data
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to fetch reward summary: $e');
    }
  }

  @override
  Future<Reward> addCustomReward({
    required String title,
    required String description,
    required int pointsEarned,
    required String rewardType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get the current user
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Insert new reward
      final response =
          await supabaseClient
              .from('rewards_history')
              .insert({
                'user_id': user.id,
                'title': title,
                'description': description,
                'points_earned': pointsEarned,
                'reward_type': rewardType,
                'source_type': 'manual',
                'metadata': metadata,
              })
              .select()
              .single();

      // Convert to reward object
      return _mapToReward(response);
    } catch (e) {
      throw Exception('Failed to add custom reward: $e');
    }
  }

  /// Helper method to map database row to Reward entity
  Reward _mapToReward(Map<String, dynamic> json) {
    try {
      return Reward(
        id: json['id'] as String,
        userId: json['user_id'] as String,
        title: json['title'] as String,
        description: json['description'] as String?,
        pointsEarned: json['points_earned'] as int,
        rewardType: json['reward_type'] as String,
        sourceId: json['source_id'] as String?,
        sourceType: json['source_type'] as String?,
        createdAt: DateTime.parse(json['created_at'] as String),
        metadata:
            json['metadata'] != null
                ? json['metadata'] as Map<String, dynamic>
                : null,
      );
    } catch (e) {
      throw Exception('Error mapping reward data: $e');
    }
  }
}
