import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:detoxme/data/remote/models/partner_model.dart';
import 'package:detoxme/data/remote/models/coupon_model.dart';
import 'package:detoxme/data/remote/models/redemption_model.dart';
import 'package:detoxme/core/errors/exceptions.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:uuid/uuid.dart';

/// Interface for the remote data source for coupon operations
abstract class CouponRemoteDataSource {
  /// Fetches all coupons from the remote source
  Future<List<CouponModel>> getAllCoupons();

  /// Fetches coupons for a specific mood type
  Future<List<CouponModel>> getCouponsForMood(String moodType);

  /// Fetches a specific coupon by ID
  Future<CouponModel> getCouponById(String couponId);

  /// Redeems a coupon for the current user
  Future<RedemptionModel> redeemCoupon(String couponId);

  /// Provides feedback for a redeemed coupon
  Future<void> provideFeedback(
    String redemptionId,
    int rating, {
    String? comment,
  });

  /// Fetches all redemptions for the current user
  Future<List<RedemptionModel>> getUserRedemptions();

  /// Fetches all partners
  Future<List<PartnerModel>> getAllPartners();

  /// Fetches partners near a location
  Future<List<PartnerModel>> getNearbyPartners(
    double latitude,
    double longitude,
    double radiusKm,
  );
}

/// Implementation of [CouponRemoteDataSource] using Supabase
class CouponRemoteDataSourceImpl implements CouponRemoteDataSource {
  /// Supabase client instance
  final SupabaseClient supabaseClient;
  
  /// UUID generator for QR codes
  final Uuid _uuid = const Uuid();

  /// Constructor
  CouponRemoteDataSourceImpl({required this.supabaseClient});
  
  /// Helper method to parse partner category from string
  PartnerCategory _parsePartnerCategory(String category) {
    switch (category.toLowerCase()) {
      case 'food':
      case 'restaurant':
        return PartnerCategory.restaurant;
      case 'wellness':
      case 'fitness':
        return PartnerCategory.wellness;
      case 'retail':
      case 'shop':
        return PartnerCategory.retail;
      default:
        return PartnerCategory.retail; // Default fallback
    }
  }

  @override
  Future<List<CouponModel>> getAllCoupons() async {
    try {
      final response = await supabaseClient
          .from('coupons')
          .select('*, partner:partners(*)')
          .eq('is_active', true)
          .order('expiration_date');

      final List<CouponModel> coupons = [];
      for (final coupon in response) {
        final partnerData = coupon['partner'];
        final partnerModel = PartnerModel.fromJson(partnerData);
        
        final couponModel = CouponModel.fromJson(coupon);
        // Create a new CouponModel with the partner data
        coupons.add(CouponModel(
          id: couponModel.id,
          partnerId: couponModel.partnerId,
          partnerModel: partnerModel,
          description: couponModel.description,
          discount: couponModel.discount,
          pointsRequired: couponModel.pointsRequired,
          expirationDate: couponModel.expirationDate,
          recommendedForMoods: couponModel.recommendedForMoods,
          quantity: couponModel.quantity,
          redemptionsCount: couponModel.redemptionsCount,
          pointsEarned: couponModel.pointsEarned,
          isActive: couponModel.isActive,
        ));
      }

      return coupons;
    } catch (e) {
      throw ServerException('Failed to fetch coupons: $e');
    }
  }

  @override
  Future<List<CouponModel>> getCouponsForMood(String moodType) async {
    try {
      // Get user's current location - would typically come from a location service
      // Using Munich center as default location for testing
      final userLat = 48.137154;
      final userLng = 11.576124;
      final maxRadius = 50.0; // 50km radius by default
      
      // Use the RPC function to find coupons by mood and location
      final response = await supabaseClient.rpc(
        'find_coupons_by_mood',
        params: {
          'mood_type': moodType,
          'user_lat': userLat,
          'user_lng': userLng,
          'radius_km': maxRadius
        },
      );

      final List<CouponModel> coupons = [];
      for (final coupon in response) {
        final partnerData = coupon['partner'];
        final partnerModel = PartnerModel.fromJson(partnerData);
        
        final couponModel = CouponModel.fromJson(coupon);
        // Create a new CouponModel with the partner data
        coupons.add(CouponModel(
          id: couponModel.id,
          partnerId: couponModel.partnerId,
          partnerModel: partnerModel,
          description: couponModel.description,
          discount: couponModel.discount,
          pointsRequired: couponModel.pointsRequired,
          expirationDate: couponModel.expirationDate,
          recommendedForMoods: couponModel.recommendedForMoods,
          quantity: couponModel.quantity,
          redemptionsCount: couponModel.redemptionsCount,
          pointsEarned: couponModel.pointsEarned,
          isActive: couponModel.isActive,
        ));
      }

      return coupons;
    } catch (e) {
      throw ServerException('Failed to fetch coupons for mood: $e');
    }
  }

  @override
  Future<CouponModel> getCouponById(String couponId) async {
    try {
      final response = await supabaseClient
          .from('coupons')
          .select('*, partner:partners(*)')
          .eq('id', couponId)
          .single();

      final partnerData = response['partner'];
      final partnerModel = PartnerModel.fromJson(partnerData);
      
      final couponModel = CouponModel.fromJson(response);
      // Create a new CouponModel with the partner data
      return CouponModel(
        id: couponModel.id,
        partnerId: couponModel.partnerId,
        partnerModel: partnerModel,
        description: couponModel.description,
        discount: couponModel.discount,
        pointsRequired: couponModel.pointsRequired,
        expirationDate: couponModel.expirationDate,
        recommendedForMoods: couponModel.recommendedForMoods,
        quantity: couponModel.quantity,
        redemptionsCount: couponModel.redemptionsCount,
        pointsEarned: couponModel.pointsEarned,
        isActive: couponModel.isActive,
      );
    } catch (e) {
      throw ServerException('Failed to fetch coupon: $e');
    }
  }

  @override
  Future<RedemptionModel> redeemCoupon(String couponId) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        throw ServerException('User not authenticated');
      }

      // Generate a unique QR code
      final qrCode = _generateQRCode();

      // Create the redemption record
      final redemption = {
        'coupon_id': couponId,
        'user_id': userId,
        'redeemed_at': DateTime.now().toIso8601String(),
        'qr_code': qrCode,
        'has_feedback': false,
      };

      final response = await supabaseClient
          .from('redemptions')
          .insert(redemption)
          .select()
          .single();

      // Increment the redemption count for the coupon
      await supabaseClient.rpc('increment_coupon_redemption', params: {
        'coupon_id': couponId,
      });

      return RedemptionModel.fromJson(response);
    } catch (e) {
      throw ServerException('Failed to redeem coupon: $e');
    }
  }

  @override
  Future<void> provideFeedback(
    String redemptionId,
    int rating, {
    String? comment,
  }) async {
    try {
      await supabaseClient.from('redemptions').update({
        'has_feedback': true,
        'feedback_rating': rating,
        'feedback_comment': comment,
      }).eq('id', redemptionId);
    } catch (e) {
      throw ServerException('Failed to provide feedback: $e');
    }
  }

  @override
  Future<List<RedemptionModel>> getUserRedemptions() async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        throw ServerException('User not authenticated');
      }

      final response = await supabaseClient
          .from('redemptions')
          .select()
          .eq('user_id', userId)
          .order('redeemed_at', ascending: false);

      return response
          .map((redemption) => RedemptionModel.fromJson(redemption))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch redemptions: $e');
    }
  }

  @override
  Future<List<PartnerModel>> getAllPartners() async {
    try {
      final response = await supabaseClient
          .from('partners')
          .select()
          .order('name');

      return response
          .map((partner) => PartnerModel.fromJson(partner))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch partners: $e');
    }
  }

  @override
  Future<List<PartnerModel>> getNearbyPartners(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    try {
      // Use our custom RPC function to find nearby coupons with partner info
      final response = await supabaseClient.rpc(
        'find_nearby_coupons',
        params: {
          'user_lat': latitude,
          'user_lng': longitude,
          'radius_km': radiusKm,
        },
      );

      // Extract unique partners from the coupons response
      final Map<String, PartnerModel> partnersMap = {};
      
      for (final item in response) {
        final partnerId = item['partner_id'];
        
        // Only add each partner once (they might have multiple coupons)
        if (!partnersMap.containsKey(partnerId)) {
          partnersMap[partnerId] = PartnerModel(
            id: partnerId,
            name: item['partner_name'],
            category: _parsePartnerCategory(item['partner_category']),
            latitude: item['partner_latitude'],
            longitude: item['partner_longitude'],
            address: item['partner_address'],
            commissionRate: 0.0, // Default value, not returned by RPC
            distance: item['distance_km'],
          );
        }
      }
      
      // Convert map to list and sort by distance
      return partnersMap.values.toList()
        ..sort((a, b) => (a.distance ?? double.infinity)
            .compareTo(b.distance ?? double.infinity));
    } catch (e) {
      throw ServerException('Failed to fetch nearby partners: $e');
    }
  }

  /// Generates a unique QR code
  String _generateQRCode() {
    return _uuid.v4();
  }
}
