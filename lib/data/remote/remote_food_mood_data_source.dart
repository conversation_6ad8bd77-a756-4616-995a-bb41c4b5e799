import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Interface for remote food mood data source
abstract class RemoteFoodMoodDataSource {
  /// Get all food recommendations from the remote source
  Future<List<FoodRecommendation>> getAllFoodRecommendations();

  /// Get food recommendations for a specific mood from the remote source
  Future<List<FoodRecommendation>> getFoodRecommendationsForMood(
    MoodType mood, {
    String? countryCode,
  });

  /// Get a specific food recommendation from the remote source
  Future<FoodRecommendation?> getFoodRecommendationById(String id);

  /// Submit a vote for a specific food recommendation
  Future<void> submitVote(String recommendationId, VoteType voteType);

  /// Remove a vote for a specific food recommendation
  Future<void> removeVote(String recommendationId);

  /// Get food recommendations from the remote source
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  });
}
