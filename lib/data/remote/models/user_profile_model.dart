import 'package:detoxme/domain/entities/user_profile.dart';

class UserProfileModel extends UserProfile {
  UserProfileModel({
    required super.id,
    String? username,
    required super.email,
    super.avatarUrl,
    super.bio,
    List<String>? goals,
    super.lifeSituation,
    super.birthYear,
    super.gender,
  }) : super(username: username ?? '', goals: goals ?? []);

  factory UserProfileModel.fromUserProfile(UserProfile profile) {
    return UserProfileModel(
      id: profile.id,
      username: profile.username,
      email: profile.email,
      avatarUrl: profile.avatarUrl,
      bio: profile.bio,
      goals: profile.goals,
      lifeSituation: profile.lifeSituation,
      birthYear: profile.birthYear,
      gender: profile.gender,
    );
  }

  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      avatarUrl: json['avatar_url'],
      bio: json['bio'],
      goals: json['goals'] != null ? List<String>.from(json['goals']) : null,
      lifeSituation: json['life_situation'],
      birthYear: json['birth_year'],
      gender: json['gender'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'avatar_url': avatarUrl,
      'bio': bio,
      'goals': goals,
      'life_situation': lifeSituation,
      'birth_year': birthYear,
      'gender': gender,
    };
  }
}
