import 'package:detoxme/domain/entities/partner.dart';
import 'package:equatable/equatable.dart';

/// Data model for Partner entity from remote data source
class PartnerModel extends Equatable {
  /// Unique identifier for the partner
  final String id;

  /// Name of the partner business
  final String name;

  /// Category of the partner
  final PartnerCategory category;

  /// Latitude of the partner location
  final double latitude;

  /// Longitude of the partner location
  final double longitude;

  /// Commission rate for the partner (as a percentage)
  final double commissionRate;

  /// Logo URL for the partner
  final String? logoUrl;

  /// Address of the partner
  final String address;
  
  /// Distance to the partner in kilometers (optional, used for nearby queries)
  final double? distance;

  /// Constructor for PartnerModel
  const PartnerModel({
    required this.id,
    required this.name,
    required this.category,
    required this.latitude,
    required this.longitude,
    required this.commissionRate,
    this.logoUrl,
    required this.address,
    this.distance,
  });

  /// Creates a PartnerModel from JSON
  factory PartnerModel.fromJson(Map<String, dynamic> json) {
    // Parse category string to PartnerCategory enum
    PartnerCategory parseCategory(String? categoryStr) {
      switch (categoryStr?.toLowerCase()) {
        case 'food':
        case 'restaurant':
          return PartnerCategory.restaurant;
        case 'wellness':
        case 'fitness':
          return PartnerCategory.wellness;
        case 'retail':
        case 'shop':
          return PartnerCategory.retail;
        default:
          return PartnerCategory.retail;
      }
    }
    
    return PartnerModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      category: parseCategory(json['category']),
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      commissionRate: (json['commission_rate'] ?? 0.0).toDouble(),
      logoUrl: json['logo_url'],
      address: json['address'] ?? '',
      distance: json['distance_km'] != null 
          ? (json['distance_km']).toDouble() 
          : null,
    );
  }

  /// Converts this model to a domain entity
  Partner toDomain() {
    return Partner(
      id: id,
      name: name,
      category: category,
      latitude: latitude,
      longitude: longitude,
      commissionRate: commissionRate,
      logoUrl: logoUrl,
      address: address,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    category,
    latitude,
    longitude,
    commissionRate,
    logoUrl,
    address,
    distance,
  ];

  /// Converts this model to JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'location': {
        'latitude': latitude,
        'longitude': longitude,
      },
      'commission_rate': commissionRate,
      'logo_url': logoUrl,
      'address': address,
    };
  }
}
