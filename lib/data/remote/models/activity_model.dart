import 'package:uuid/uuid.dart';

import '../../../domain/entities/activity.dart';
import '../../../domain/entities/mood.dart';

/// Activity model for Supabase interactions
class ActivityModel {
  /// Unique ID for the activity
  final String id;

  /// Title of the activity
  final String title;

  /// Description of the activity
  final String description;

  /// Category of the activity
  final String category;

  /// Duration in minutes
  final int duration;

  /// List of moods this activity is recommended for
  final List<MoodType> recommendedFor;

  /// List of health benefits for this activity
  final List<String> healthBenefits;

  /// URL for the activity image
  final String? imageUrl;

  /// Whether this is a system-defined activity
  final bool isSystemActivity;

  /// ID of the user who created this activity
  final String? createdBy;

  /// Name of the creator
  final String? creatorName;

  /// Whether to show the creator's name
  final bool showCreatorName;

  /// Whether this activity is only visible to its creator
  final bool isPrivate;

  /// Language/country code for the activity
  final String countryCode;

  /// Number of upvotes
  final int upvotes;

  /// Number of downvotes
  final int downvotes;

  /// When the activity was created
  final DateTime createdAt;

  /// When the activity was last updated
  final DateTime updatedAt;
  
  /// Difficulty level of the activity
  final String difficultyLevel;

  /// Creates a new ActivityModel
  ActivityModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.duration,
    required this.recommendedFor,
    this.healthBenefits = const [],
    this.imageUrl,
    this.isSystemActivity = true,
    this.createdBy,
    this.creatorName,
    this.showCreatorName = true,
    this.isPrivate = false,
    this.countryCode = 'en',
    this.upvotes = 0,
    this.downvotes = 0,
    required this.createdAt,
    required this.updatedAt,
    this.difficultyLevel = 'intermediate',
  });

  /// Creates an ActivityModel from JSON
  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      id: json['id'] ?? const Uuid().v4(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? 'mindfulness',
      duration: json['duration'] ?? 15,
      recommendedFor: ActivityModel._mapStringsToMoodTypes(
        List<String>.from(json['recommended_for'] ?? []),
      ),
      healthBenefits: List<String>.from(json['health_benefits'] ?? []),
      imageUrl: json['image_url'],
      isSystemActivity: json['is_system_activity'] ?? true,
      createdBy: json['created_by'],
      creatorName: json['creator_name'],
      showCreatorName: json['show_creator_name'] ?? true,
      isPrivate: json['is_private'] ?? false,
      countryCode: json['country_code'] ?? 'en',
      upvotes: json['upvotes'] ?? 0,
      downvotes: json['downvotes'] ?? 0,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
      difficultyLevel: json['difficulty_level'] ?? 'intermediate',
    );
  }

  /// Converts the ActivityModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'duration': duration,
      'recommended_for': recommendedFor.map((mood) => mood.name).toList(),
      'health_benefits': healthBenefits,
      'image_url': imageUrl,
      'is_system_activity': isSystemActivity,
      'created_by': createdBy,
      'creator_name': creatorName,
      'show_creator_name': showCreatorName,
      'is_private': isPrivate,
      'country_code': countryCode,
      'upvotes': upvotes,
      'downvotes': downvotes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'difficulty_level': difficultyLevel,
    };
  }

  /// Converts ActivityModel to domain Activity entity
  Activity toDomain({bool isFavorite = false}) {
    return Activity(
      id: id,
      title: title,
      description: description,
      category: _mapStringToCategory(category),
      duration: duration,
      recommendedFor: recommendedFor,
      healthBenefits: healthBenefits,
      imageUrl: imageUrl,
      isSystemActivity: isSystemActivity,
      createdBy: createdBy,
      creatorName: creatorName,
      showCreatorName: showCreatorName,
      isPrivate: isPrivate,
      countryCode: countryCode,
      upvotes: upvotes,
      downvotes: downvotes,
      createdAt: createdAt,
      updatedAt: updatedAt,
      difficultyLevel: _mapStringToDifficultyLevel(difficultyLevel),
      isFavorite: isFavorite,
    );
  }

  /// Creates a copy of this ActivityModel with the given fields replaced
  ActivityModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    int? duration,
    List<MoodType>? recommendedFor,
    List<String>? healthBenefits,
    String? imageUrl,
    bool? isSystemActivity,
    String? createdBy,
    String? creatorName,
    bool? showCreatorName,
    bool? isPrivate,
    String? countryCode,
    int? upvotes,
    int? downvotes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? difficultyLevel,
    bool? isFavorite,
  }) {
    return ActivityModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      recommendedFor: recommendedFor ?? this.recommendedFor,
      healthBenefits: healthBenefits ?? this.healthBenefits,
      imageUrl: imageUrl ?? this.imageUrl,
      isSystemActivity: isSystemActivity ?? this.isSystemActivity,
      createdBy: createdBy ?? this.createdBy,
      creatorName: creatorName ?? this.creatorName,
      showCreatorName: showCreatorName ?? this.showCreatorName,
      isPrivate: isPrivate ?? this.isPrivate,
      countryCode: countryCode ?? this.countryCode,
      upvotes: upvotes ?? this.upvotes,
      downvotes: downvotes ?? this.downvotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
    );
  }

  /// Creates ActivityModel from domain Activity entity
  factory ActivityModel.fromDomain(Activity activity) {
    return ActivityModel(
      id: activity.id,
      title: activity.title,
      description: activity.description,
      category: activity.category.name,
      duration: activity.duration,
      recommendedFor: activity.recommendedFor,
      healthBenefits: activity.healthBenefits,
      imageUrl: activity.imageUrl,
      isSystemActivity: activity.isSystemActivity,
      createdBy: activity.createdBy,
      creatorName: activity.creatorName,
      showCreatorName: activity.showCreatorName,
      isPrivate: activity.isPrivate,
      countryCode: activity.countryCode,
      upvotes: activity.upvotes,
      downvotes: activity.downvotes,
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      difficultyLevel: activity.difficultyLevel.name,
    );
  }

  /// Maps string category to ActivityCategory enum
  ActivityCategory _mapStringToCategory(String categoryString) {
    switch (categoryString) {
      case 'outdoor':
        return ActivityCategory.outdoor;
      case 'physical':
        return ActivityCategory.physical;
      case 'creative':
        return ActivityCategory.creative;
      case 'social':
        return ActivityCategory.social;
      case 'relaxation':
        return ActivityCategory.relaxation;
      case 'learning':
        return ActivityCategory.learning;
      case 'mindfulness':
      default:
        return ActivityCategory.mindfulness;
    }
  }
  
  /// Maps string difficulty level to DifficultyLevel enum
  DifficultyLevel _mapStringToDifficultyLevel(String levelString) {
    switch (levelString) {
      case 'beginner':
        return DifficultyLevel.beginner;
      case 'expert':
        return DifficultyLevel.expert;
      case 'intermediate':
      default:
        return DifficultyLevel.intermediate;
    }
  }

  /// Maps string mood types to MoodType enum values
  static List<MoodType> _mapStringsToMoodTypes(List<String> moodStrings) {
    return moodStrings.map((s) {
      switch (s) {
        case 'happy':
          return MoodType.happy;
        case 'sad':
          return MoodType.sad;
        case 'angry':
          return MoodType.angry;
        case 'anxious':
          return MoodType.anxious;
        case 'tired':
          return MoodType.tired;
        case 'stressed': // Map to closest equivalent
          return MoodType.anxious;
        case 'depressed': // Map to closest equivalent
          return MoodType.sad;
        case 'excited':
          return MoodType.excited;
        case 'calm':
          return MoodType.calm;
        case 'bored': // Map to closest equivalent
          return MoodType.neutral;
        case 'neutral':
          return MoodType.neutral;
        case 'inspired':
          return MoodType.inspired;
        default:
          return MoodType.neutral; // Default fallback
      }
    }).toList();
  }
}
