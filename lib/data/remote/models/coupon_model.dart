import 'package:detoxme/data/remote/models/partner_model.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Data model for Coupon entity from remote data source
class CouponModel {
  /// Unique identifier for the coupon
  final String id;

  /// Partner ID that offers this coupon
  final String partnerId;

  /// Partner model data
  final PartnerModel? partnerModel;

  /// Description of the coupon
  final String description;

  /// Discount value (percentage or fixed amount)
  final String discount;

  /// Points required to unlock this coupon
  final int pointsRequired;

  /// Expiration date of the coupon
  final DateTime expirationDate;

  /// List of mood types this coupon is recommended for (as strings)
  final List<String> recommendedForMoods;

  /// Number of coupons available
  final int? quantity;

  /// Number of redemptions so far
  final int redemptionsCount;

  /// Points earned when coupon is redeemed
  final int pointsEarned;

  /// Whether the coupon is active
  final bool isActive;

  /// Constructor for CouponModel
  const CouponModel({
    required this.id,
    required this.partnerId,
    this.partnerModel,
    required this.description,
    required this.discount,
    required this.pointsRequired,
    required this.expirationDate,
    required this.recommendedForMoods,
    this.quantity,
    this.redemptionsCount = 0,
    this.pointsEarned = 50,
    this.isActive = true,
  });

  /// Creates a CouponModel from JSON
  factory CouponModel.fromJson(Map<String, dynamic> json) {
    return CouponModel(
      id: json['id'] as String,
      partnerId: json['partner_id'] as String,
      partnerModel: json['partner'] != null
          ? PartnerModel.fromJson(json['partner'] as Map<String, dynamic>)
          : null,
      description: json['description'] as String,
      discount: json['discount'] as String,
      pointsRequired: json['points_required'] as int,
      expirationDate: DateTime.parse(json['expiration_date'] as String),
      recommendedForMoods:
          (json['recommended_for_moods'] as List).map((e) => e as String).toList(),
      quantity: json['quantity'] as int?,
      redemptionsCount: json['redemptions_count'] as int? ?? 0,
      pointsEarned: json['points_earned'] as int? ?? 50,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Converts this model to a domain entity
  Coupon toDomain() {
    if (partnerModel == null) {
      throw Exception('Partner data is required to convert to domain entity');
    }

    return Coupon(
      id: id,
      partner: partnerModel!.toDomain(),
      description: description,
      discount: discount,
      pointsRequired: pointsRequired,
      expirationDate: expirationDate,
      recommendedForMoods: _mapMoodStringsToEnums(recommendedForMoods),
      quantity: quantity,
      redemptionsCount: redemptionsCount,
      pointsEarned: pointsEarned,
      isActive: isActive,
    );
  }

  /// Maps mood strings to MoodType enums
  List<MoodType> _mapMoodStringsToEnums(List<String> moodStrings) {
    return moodStrings.map((moodStr) {
      switch (moodStr.toLowerCase()) {
        case 'happy':
          return MoodType.happy;
        case 'sad':
          return MoodType.sad;
        case 'angry':
          return MoodType.angry;
        case 'anxious':
          return MoodType.anxious;
        case 'calm':
          return MoodType.calm;
        case 'bored': // Map to closest equivalent
          return MoodType.neutral;
        case 'excited':
          return MoodType.excited;
        case 'neutral':
          return MoodType.neutral;
        case 'inspired':
          return MoodType.inspired;
        case 'tired':
          return MoodType.tired;
        default:
          // Default to neutral if no match
          return MoodType.neutral;
      }
    }).toList();
  }

  /// Converts this model to JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'partner_id': partnerId,
      'description': description,
      'discount': discount,
      'points_required': pointsRequired,
      'expiration_date': expirationDate.toIso8601String(),
      'recommended_for_moods': recommendedForMoods,
      'quantity': quantity,
      'redemptions_count': redemptionsCount,
      'points_earned': pointsEarned,
      'is_active': isActive,
    };
  }
}
