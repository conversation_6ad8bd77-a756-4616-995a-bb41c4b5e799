import 'package:detoxme/domain/entities/device_token.dart';

/// Data model representing a device's FCM token for Supabase
class DeviceTokenModel {
  final String id;
  final String userId;
  final String fcmToken;
  final DateTime lastUpdated;
  final String? deviceInfo;

  const DeviceTokenModel({
    required this.id,
    required this.userId,
    required this.fcmToken,
    required this.lastUpdated,
    this.deviceInfo,
  });

  /// Convert entity to model
  factory DeviceTokenModel.fromEntity(DeviceToken entity) {
    return DeviceTokenModel(
      id: entity.id,
      userId: entity.userId,
      fcmToken: entity.fcmToken,
      lastUpdated: entity.lastUpdated,
      deviceInfo: entity.deviceInfo,
    );
  }

  /// Convert model to entity
  DeviceToken toEntity() {
    return DeviceToken(
      id: id,
      userId: userId,
      fcmToken: fcmToken,
      lastUpdated: lastUpdated,
      deviceInfo: deviceInfo,
    );
  }

  /// Create from JSON data
  factory DeviceTokenModel.fromJson(Map<String, dynamic> json) {
    return DeviceTokenModel(
      id: json['id'],
      userId: json['user_id'],
      fcmToken: json['fcm_token'],
      lastUpdated: DateTime.parse(json['last_updated']),
      deviceInfo: json['device_info'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'fcm_token': fcmToken,
      'last_updated': lastUpdated.toIso8601String(),
      'device_info': deviceInfo,
    };
  }
}
