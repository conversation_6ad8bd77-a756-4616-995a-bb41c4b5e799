import 'package:detoxme/domain/entities/redemption.dart';

/// Data model for Redemption entity from remote data source
class RedemptionModel {
  /// Unique identifier for the redemption
  final String id;
  
  /// The ID of the coupon that was redeemed
  final String couponId;
  
  /// The ID of the user who redeemed the coupon
  final String userId;
  
  /// Timestamp when the coupon was redeemed
  final DateTime redeemedAt;
  
  /// Unique QR code for this redemption
  final String qrCode;
  
  /// Whether the user provided feedback after redemption
  final bool hasFeedback;
  
  /// User's feedback rating (1-5), null if no feedback
  final int? feedbackRating;
  
  /// User's feedback comment, null if no comment
  final String? feedbackComment;

  /// Constructor for RedemptionModel
  const RedemptionModel({
    required this.id,
    required this.couponId,
    required this.userId,
    required this.redeemedAt,
    required this.qrCode,
    this.hasFeedback = false,
    this.feedbackRating,
    this.feedbackComment,
  });

  /// Creates a RedemptionModel from JSON
  factory RedemptionModel.fromJson(Map<String, dynamic> json) {
    return RedemptionModel(
      id: json['id'] as String,
      couponId: json['coupon_id'] as String,
      userId: json['user_id'] as String,
      redeemedAt: DateTime.parse(json['redeemed_at'] as String),
      qrCode: json['qr_code'] as String,
      hasFeedback: json['has_feedback'] as bool? ?? false,
      feedbackRating: json['feedback_rating'] as int?,
      feedbackComment: json['feedback_comment'] as String?,
    );
  }

  /// Converts this model to a domain entity
  Redemption toDomain() {
    return Redemption(
      id: id,
      couponId: couponId,
      userId: userId,
      redeemedAt: redeemedAt,
      qrCode: qrCode,
      hasFeedback: hasFeedback,
      feedbackRating: feedbackRating,
      feedbackComment: feedbackComment,
    );
  }

  /// Converts this model to JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'coupon_id': couponId,
      'user_id': userId,
      'redeemed_at': redeemedAt.toIso8601String(),
      'qr_code': qrCode,
      'has_feedback': hasFeedback,
      'feedback_rating': feedbackRating,
      'feedback_comment': feedbackComment,
    };
  }
}
