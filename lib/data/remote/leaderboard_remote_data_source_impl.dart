import 'package:detoxme/core/errors/exceptions.dart'; // Assuming ServerException is here
import 'package:detoxme/data/remote/leaderboard_remote_data_source.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
class LeaderboardRemoteDataSourceImpl implements LeaderboardRemoteDataSource {
  final SupabaseClient supabaseClient;

  LeaderboardRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<Map<String, dynamic>>> getLeaderboardData() async {
    try {
      // Fetch top 20 users based on total_xp
      final response = await supabaseClient
          .from('player_stats') // Assuming table name
          .select('username, total_xp') // Select relevant columns
          .order('total_xp', ascending: false) // Order by XP descending
          .limit(20); // Limit to top 20

      // Supabase returns List<dynamic> which needs casting
      // The elements are already Map<String, dynamic>
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      // Catch Supabase errors or other exceptions
      if (kDebugMode) {
        print('Supabase error fetching leaderboard: $e');
      }
      throw ServerException(
        'Failed to fetch leaderboard data: ${e.toString()}',
      );
    }
  }
}
