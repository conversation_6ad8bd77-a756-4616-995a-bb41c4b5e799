import 'package:detoxme/domain/entities/user_notification.dart';

/// Remote data source for notifications
abstract class NotificationRemoteDataSource {
  /// Get notifications for a user
  Stream<List<UserNotification>> getUserNotificationsStream(String userId);

  /// Get notifications for a user (one-time fetch)
  Future<List<UserNotification>> getUserNotifications(String userId);

  /// Mark a notification as read
  Future<void> markNotificationAsRead(String notificationId, String userId);

  /// Mark all notifications as read for a user
  Future<void> markAllNotificationsAsRead(String userId);

  /// Delete a notification
  Future<void> deleteNotification(String notificationId, String userId);

  /// Create a new notification
  Future<UserNotification> createNotification({
    required String userId,
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? actionData,
  });

  /// Get count of unread notifications
  Future<int> getUnreadNotificationCount(String userId);

  /// Stream of unread notification count
  Stream<int> getUnreadNotificationCountStream(String userId);
}
