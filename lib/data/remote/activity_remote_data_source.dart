import '../../domain/entities/activity.dart';
import '../../domain/entities/activity_comment.dart';
import '../../domain/entities/mood.dart';
import '../../domain/repositories/activity_repository.dart';
import 'models/activity_model.dart';

/// Interface for accessing activity data from remote sources
abstract class ActivityRemoteDataSource {
  /// Get all activities
  Future<List<ActivityModel>> getActivities();

  /// Get activities filtered by mood
  Future<List<ActivityModel>> getActivitiesForMood(MoodType mood);

  /// Get user's mood history
  Future<List<Mood>> getMoodHistory(String userId);

  /// Log user's mood
  Future<void> logMood(String userId, Mood mood);

  /// Create a new activity
  Future<ActivityModel> createActivity({
    required String userId,
    required String title,
    required String description,
    required ActivityCategory category,
    required int duration,
    required List<MoodType> recommendedFor,
    List<String> healthBenefits = const [],
    String? imageUrl,
    String? creatorName,
    bool showCreatorName = true,
    bool isPrivate = false,
    String countryCode = 'en',
  });

  /// Get activities created by a specific user
  Future<List<ActivityModel>> getUserActivities(String userId);

  /// Update an existing activity
  Future<ActivityModel> updateActivity(ActivityModel activity);

  /// Delete an activity
  Future<void> deleteActivity(String activityId, String userId);

  /// Upvote an activity
  Future<ActivityModel> upvoteActivity(String activityId, String userId);

  /// Downvote an activity
  Future<ActivityModel> downvoteActivity(String activityId, String userId);

  /// Remove vote from an activity
  Future<ActivityModel> removeVote(String activityId, String userId);

  /// Get filtered activities
  Future<List<ActivityModel>> getFilteredActivities({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    bool includePrivate = false,
    String? userId,
    bool hideDownvoted = true,
    List<String>? countryCodes,
  });

  /// Set an activity as favorite
  Future<void> setFavorite(String activityId, String userId, bool isFavorite);

  /// Get favorite activities for a user
  Future<List<ActivityModel>> getFavoriteActivities(String userId);

  /// Set a food as favorite
  Future<void> setFoodFavorite(String foodId, String userId, bool isFavorite);

  /// Get favorite foods for a user
  Future<List<dynamic>> getFavoriteFoods(String userId);

  /// Add a comment to an activity
  Future<ActivityComment> addComment({
    required String activityId,
    required String userId,
    required String userName,
    required String text,
    String? userAvatarUrl,
  });

  /// Get comments for an activity (one-time fetch)
  Future<List<ActivityComment>> getComments(
    String activityId,
    String? currentUserId,
  );

  /// Get comments for an activity as a stream
  Stream<List<ActivityComment>> getCommentsStream(
    String activityId,
    String? currentUserId,
  );

  /// Delete a comment
  Future<void> deleteComment({
    required String commentId,
    required String userId,
  });

  /// Edit a comment
  Future<ActivityComment> editComment({
    required String commentId,
    required String userId,
    required String newText,
  });

  /// Like or unlike a comment
  Future<ActivityComment> toggleCommentLike({
    required String commentId,
    required String userId,
  });

  /// Report a comment
  Future<void> reportComment({
    required String commentId,
    required String userId,
    required String reason,
  });
}
