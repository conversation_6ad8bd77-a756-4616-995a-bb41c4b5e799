import 'package:detoxme/data/remote/remote_food_mood_data_source.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of [RemoteFoodMoodDataSource] using Supabase
class RemoteFoodMoodDataSourceImpl implements RemoteFoodMoodDataSource {
  /// Supabase client for database operations
  final SupabaseClient supabaseClient;

  /// The table name for food recommendations
  static const String _tableFood = 'food_recommendations';

  /// The table name for votes
  static const String _tableVotes = 'food_votes';

  /// Creates a new instance of [RemoteFoodMoodDataSourceImpl]
  RemoteFoodMoodDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<FoodRecommendation>> getAllFoodRecommendations() async {
    final response = await supabaseClient
        .from(_tableFood)
        .select('*, votes:$_tableVotes(vote_type, user_id)')
        .order('created_at', ascending: false);

    return _processFoodRecommendations(response);
  }

  @override
  Future<List<FoodRecommendation>> getFoodRecommendationsForMood(
    MoodType mood, {
    String? countryCode,
  }) async {
    try {
      // Use the country code filter if provided
      final query = supabaseClient
          .from(_tableFood)
          .select('*, votes:$_tableVotes(vote_type, user_id)');

      // Apply country code filter if provided
      final filteredQuery =
          countryCode != null ? query.eq('country_code', countryCode) : query;

      // We'll skip this step and directly apply the mood filter in the query

      // Apply mood filter directly in the query
      final moodStr = mood.toString().split('.').last;

      // Modify the query to filter by mood_types
      final moodFilteredQuery = filteredQuery.contains('mood_types', [moodStr]);

      // Execute the query with mood filter
      final moodFilteredResponse = await moodFilteredQuery.order(
        'created_at',
        ascending: false,
      );

      // Process the response
      final filteredRecommendations = _processFoodRecommendations(
        moodFilteredResponse,
      );

      return filteredRecommendations;
    } catch (e) {
      throw Exception('Failed to fetch food recommendations for mood: $e');
    }
  }

  @override
  Future<FoodRecommendation?> getFoodRecommendationById(String id) async {
    final response =
        await supabaseClient
            .from(_tableFood)
            .select('*, votes:$_tableVotes(vote_type, user_id)')
            .eq('id', id)
            .maybeSingle();

    if (response == null) {
      return null;
    }

    final recommendations = _processFoodRecommendations([response]);
    return recommendations.isNotEmpty ? recommendations.first : null;
  }

  @override
  Future<void> submitVote(String recommendationId, VoteType voteType) async {
    final userId = supabaseClient.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    // First, check if there's an existing vote and delete it
    await removeVote(recommendationId);

    // Insert the new vote
    await supabaseClient.from(_tableVotes).insert({
      'food_id': recommendationId,
      'user_id': userId,
      'vote_type': voteType == VoteType.upvote ? 'upvote' : 'downvote',
    });
  }

  @override
  Future<void> removeVote(String recommendationId) async {
    final userId = supabaseClient.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    await supabaseClient
        .from(_tableVotes)
        .delete()
        .eq('food_id', recommendationId)
        .eq('user_id', userId);
  }

  @override
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  }) async {
    try {
      // Start building the query
      var query = supabaseClient
          .from(_tableFood)
          .select('*, votes:$_tableVotes(vote_type, user_id)');

      // Apply country code filter
      if (countryCode != null) {
        query = query.eq('country_code', countryCode);
      }

      // Apply mood filter if provided
      if (mood != null) {
        final moodStr = mood.toString().split('.').last;
        // Use contains operator to check if the mood is in the mood_types array
        query = query.contains('mood_types', [moodStr]);
      }

      // Apply ordering
      final response = await query.order('created_at', ascending: false);

      // Process the response
      final recommendations = _processFoodRecommendations(response);

      // Apply pagination if needed
      if (offset != null && limit != null) {
        final start = offset;
        final end = offset + limit;
        if (start < recommendations.length) {
          return recommendations.sublist(
            start,
            end < recommendations.length ? end : recommendations.length,
          );
        }
        return [];
      }

      return recommendations;
    } catch (e) {
      throw Exception('Failed to fetch food recommendations: $e');
    }
  }

  /// Process the response from Supabase into a list of [FoodRecommendation] objects
  List<FoodRecommendation> _processFoodRecommendations(List<dynamic> response) {
    return response.map<FoodRecommendation>((item) {
      // Convert moods from strings to MoodType enum values
      final moodsList =
          (item['moods'] as List<dynamic>?)?.map((moodStr) {
            return MoodType.values.firstWhere(
              (mood) => mood.toString().split('.').last == moodStr,
              orElse: () => MoodType.calm,
            );
          }).toList() ??
          [];

      // Process votes
      final votes = item['votes'] as List<dynamic>? ?? [];
      int upvotes = 0;
      int downvotes = 0;
      VoteType? userVote;
      final currentUserId = supabaseClient.auth.currentUser?.id;

      for (final vote in votes) {
        if (vote['vote_type'] == 'upvote') {
          upvotes++;
          if (vote['user_id'] == currentUserId) {
            userVote = VoteType.upvote;
          }
        } else if (vote['vote_type'] == 'downvote') {
          downvotes++;
          if (vote['user_id'] == currentUserId) {
            userVote = VoteType.downvote;
          }
        }
      }

      return FoodRecommendation(
        id: item['id'] as String,
        name: item['name'] as String,
        description: item['description'] as String,
        imageUrl: item['image_url'] as String,
        createdAt: DateTime.parse(item['created_at'] as String),
        updatedAt: DateTime.parse(item['updated_at'] as String),
        upvotes: upvotes,
        downvotes: downvotes,
        userVote: userVote,
        userId: item['user_id'] as String? ?? '',
        username: item['username'] as String? ?? '',
        userAvatarUrl: item['user_avatar_url'] as String?,
        moodTypes: moodsList,
        countryCode: item['country_code'] as String? ?? '',
      );
    }).toList();
  }
}
