import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:detoxme/domain/entities/user_notification.dart';
import 'notification_remote_data_source.dart';

/// Implementation of [NotificationRemoteDataSource] using Supabase
class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  final SupabaseClient _supabaseClient;
  final Uuid _uuid = const Uuid();

  /// Creates a new [NotificationRemoteDataSourceImpl]
  NotificationRemoteDataSourceImpl({required SupabaseClient supabaseClient})
    : _supabaseClient = supabaseClient;

  @override
  Stream<List<UserNotification>> getUserNotificationsStream(String userId) {
    return _supabaseClient
        .from('user_notifications')
        .stream(primaryKey: ['id'])
        .eq('user_id', userId)
        .order('timestamp', ascending: false)
        .map((List<Map<String, dynamic>> data) {
          return data.map((item) => UserNotification.fromMap(item)).toList();
        });
  }

  @override
  Future<List<UserNotification>> getUserNotifications(String userId) async {
    final response = await _supabaseClient
        .from('user_notifications')
        .select()
        .eq('user_id', userId)
        .order('timestamp', ascending: false);

    return response.map((item) => UserNotification.fromMap(item)).toList();
  }

  @override
  Future<void> markNotificationAsRead(
    String notificationId,
    String userId,
  ) async {
    await _supabaseClient
        .from('user_notifications')
        .update({'is_read': true})
        .eq('id', notificationId)
        .eq('user_id', userId);
  }

  @override
  Future<void> markAllNotificationsAsRead(String userId) async {
    await _supabaseClient
        .from('user_notifications')
        .update({'is_read': true})
        .eq('user_id', userId)
        .eq('is_read', false);
  }

  @override
  Future<void> deleteNotification(String notificationId, String userId) async {
    await _supabaseClient
        .from('user_notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId);
  }

  @override
  Future<UserNotification> createNotification({
    required String userId,
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? actionData,
  }) async {
    final notificationData = {
      'id': _uuid.v4(),
      'user_id': userId,
      'title': title,
      'message': message,
      'type': type.name,
      'action_data': actionData,
      'timestamp': DateTime.now().toIso8601String(),
      'is_read': false,
    };

    final response =
        await _supabaseClient
            .from('user_notifications')
            .insert(notificationData)
            .select()
            .single();

    return UserNotification.fromMap(response);
  }

  @override
  Future<int> getUnreadNotificationCount(String userId) async {
    final response = await _supabaseClient
        .from('user_notifications')
        .select('id')
        .eq('user_id', userId)
        .eq('is_read', false);

    return response.length;
  }

  @override
  Stream<int> getUnreadNotificationCountStream(String userId) {
    return _supabaseClient
        .from('user_notifications')
        .stream(primaryKey: ['id'])
        .map((List<Map<String, dynamic>> data) {
          // Filter manually since stream filters might not work as expected
          final filteredData =
              data
                  .where(
                    (item) =>
                        item['user_id'] == userId && item['is_read'] == false,
                  )
                  .toList();
          return filteredData.length;
        });
  }
}
