import 'package:detoxme/data/local/models/task_hive_model.dart';
import 'package:detoxme/domain/entities/task.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

abstract class LocalTaskDataSource {
  Future<List<Task>> getTasks();
  Future<void> cacheTasks(
    List<Task> tasks,
  ); // For caching tasks fetched from remote
  // Maybe add getTaskById later if needed
}

class LocalTaskDataSourceImpl implements LocalTaskDataSource {
  final String _boxName = 'tasks';

  Future<Box<TaskHiveModel>> _getBox() async {
    return await Hive.openBox<TaskHiveModel>(_boxName);
  }

  @override
  Future<List<Task>> getTasks() async {
    try {
      final box = await _getBox();
      // Return all tasks stored in the box, converted to entities
      return box.values.map((model) => model.toEntity()).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error getting tasks: $e');
      }
      return []; // Return empty list on error
    }
  }

  @override
  Future<void> cacheTasks(List<Task> tasks) async {
    try {
      final box = await _getBox();
      // Clear existing cache before adding new ones
      await box.clear();
      // Use a Map for putAll, converting entities to Hive models
      final Map<String, TaskHiveModel> tasksMap = {
        for (var task in tasks) task.id: TaskHiveModel.fromEntity(task),
      };
      await box.putAll(tasksMap);
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error caching tasks: $e');
      }
      rethrow; // Rethrow for repository to handle
    }
  }
}
