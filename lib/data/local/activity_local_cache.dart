import 'dart:async';
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../domain/entities/activity.dart';
import '../../../domain/entities/mood.dart';
import '../../../domain/repositories/activity_repository.dart';
import '../remote/models/activity_model.dart';

/// Local cache for activities to reduce network calls
class ActivityLocalCache {
  /// SharedPreferences instance for persistent storage
  final SharedPreferences _prefs;

  /// Key for all activities cache
  static const String _allActivitiesKey = 'activities_all';

  /// Key prefix for mood-filtered activities
  static const String _moodActivitiesPrefix = 'activities_mood_';

  /// Key prefix for user activities
  static const String _userActivitiesPrefix = 'activities_user_';

  /// Key for activities cache timestamp
  static const String _lastCacheTimeKey = 'activities_cache_time';

  /// Cache expiration duration (6 hours)
  static const Duration _cacheExpiration = Duration(hours: 6);

  /// Create a new ActivityLocalCache
  ActivityLocalCache({required SharedPreferences prefs}) : _prefs = prefs;

  /// Save all activities to cache
  Future<void> cacheAllActivities(List<ActivityModel> activities) async {
    final activitiesJson =
        activities.map((activity) => activity.toJson()).toList();

    await _prefs.setString(_allActivitiesKey, jsonEncode(activitiesJson));

    // Update cache timestamp
    await _updateCacheTimestamp();
  }

  /// Get all activities from cache if available and not expired
  Future<List<ActivityModel>?> getAllActivities() async {
    if (await _isCacheExpired()) {
      return null;
    }

    final cachedData = _prefs.getString(_allActivitiesKey);
    if (cachedData == null) {
      return null;
    }

    try {
      final List<dynamic> decodedData = jsonDecode(cachedData);
      return decodedData.map((json) => ActivityModel.fromJson(json)).toList();
    } catch (e) {
      // Cache may be corrupted, return null to fetch fresh data
      return null;
    }
  }

  /// Save mood-filtered activities to cache
  Future<void> cacheMoodActivities(
    MoodType mood,
    List<ActivityModel> activities,
  ) async {
    final activitiesJson =
        activities.map((activity) => activity.toJson()).toList();

    await _prefs.setString(_getMoodKey(mood), jsonEncode(activitiesJson));
  }

  /// Get mood-filtered activities from cache if available
  Future<List<ActivityModel>?> getMoodActivities(MoodType mood) async {
    if (await _isCacheExpired()) {
      return null;
    }

    final cachedData = _prefs.getString(_getMoodKey(mood));
    if (cachedData == null) {
      return null;
    }

    try {
      final List<dynamic> decodedData = jsonDecode(cachedData);
      return decodedData.map((json) => ActivityModel.fromJson(json)).toList();
    } catch (e) {
      return null;
    }
  }

  /// Save user activities to cache
  Future<void> cacheUserActivities(
    String userId,
    List<ActivityModel> activities,
  ) async {
    final activitiesJson =
        activities.map((activity) => activity.toJson()).toList();

    await _prefs.setString(_getUserKey(userId), jsonEncode(activitiesJson));
  }

  /// Get user activities from cache if available
  Future<List<ActivityModel>?> getUserActivities(String userId) async {
    if (await _isCacheExpired()) {
      return null;
    }

    final cachedData = _prefs.getString(_getUserKey(userId));
    if (cachedData == null) {
      return null;
    }

    try {
      final List<dynamic> decodedData = jsonDecode(cachedData);
      return decodedData.map((json) => ActivityModel.fromJson(json)).toList();
    } catch (e) {
      return null;
    }
  }

  /// Save filtered activities to cache using custom key based on filter parameters
  Future<void> cacheFilteredActivities({
    required List<ActivityModel> activities,
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    List<String>? countryCodes,
  }) async {
    final key = _getFilterKey(
      mood: mood,
      searchQuery: searchQuery,
      category: category,
      sortOption: sortOption,
      countryCodes: countryCodes,
    );

    final activitiesJson =
        activities.map((activity) => activity.toJson()).toList();

    await _prefs.setString(key, jsonEncode(activitiesJson));
  }

  /// Get filtered activities from cache if available
  Future<List<ActivityModel>?> getFilteredActivities({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    List<String>? countryCodes,
  }) async {
    if (await _isCacheExpired()) {
      return null;
    }

    final key = _getFilterKey(
      mood: mood,
      searchQuery: searchQuery,
      category: category,
      sortOption: sortOption,
      countryCodes: countryCodes,
    );

    final cachedData = _prefs.getString(key);
    if (cachedData == null) {
      return null;
    }

    try {
      final List<dynamic> decodedData = jsonDecode(cachedData);
      return decodedData.map((json) => ActivityModel.fromJson(json)).toList();
    } catch (e) {
      return null;
    }
  }

  /// Clear all cached activities
  Future<void> clearCache() async {
    final keys = _prefs.getKeys();

    for (final key in keys) {
      if (key.startsWith('activities_')) {
        await _prefs.remove(key);
      }
    }
  }

  /// Invalidate the cache for a specific activity when it's modified
  Future<void> invalidateActivity(String activityId) async {
    // This will force a reload of all caches on next access
    await _prefs.remove(_lastCacheTimeKey);
  }

  // Helper methods

  /// Get cache key for mood-filtered activities
  String _getMoodKey(MoodType mood) {
    return '$_moodActivitiesPrefix${mood.name}';
  }

  /// Get cache key for user activities
  String _getUserKey(String userId) {
    return '$_userActivitiesPrefix$userId';
  }

  /// Create a unique key for filtered activities
  String _getFilterKey({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    List<String>? countryCodes,
  }) {
    final buffer = StringBuffer('activities_filtered');

    if (mood != null) {
      buffer.write('_mood_${mood.name}');
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      // Add a hash of the search query to avoid long keys
      buffer.write('_search_${searchQuery.hashCode}');
    }

    if (category != null) {
      buffer.write('_cat_${category.name}');
    }

    buffer.write('_sort_${sortOption.name}');

    if (countryCodes != null && countryCodes.isNotEmpty) {
      buffer.write('_countries_${countryCodes.join('_')}');
    }

    return buffer.toString();
  }

  /// Update cache timestamp
  Future<void> _updateCacheTimestamp() async {
    await _prefs.setInt(
      _lastCacheTimeKey,
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Check if cache is expired
  Future<bool> _isCacheExpired() async {
    final lastCacheTime = _prefs.getInt(_lastCacheTimeKey);
    if (lastCacheTime == null) {
      return true;
    }

    final cacheDateTime = DateTime.fromMillisecondsSinceEpoch(lastCacheTime);
    final now = DateTime.now();

    return now.difference(cacheDateTime) > _cacheExpiration;
  }
}
