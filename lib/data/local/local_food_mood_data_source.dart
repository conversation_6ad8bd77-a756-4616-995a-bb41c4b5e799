import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';

/// Interface for the local food mood data source
abstract class LocalFoodMoodDataSource {
  /// Get all food recommendations from cache
  Future<List<FoodRecommendation>> getAllFoodRecommendations({
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
  });

  /// Get food recommendations for a specific mood from cache
  Future<List<FoodRecommendation>> getFoodRecommendationsForMood(
    MoodType mood, {
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
    String? countryCode,
  });

  /// Get a food recommendation by ID from cache
  Future<FoodRecommendation?> getFoodRecommendationById(String id);

  /// Update food recommendations in cache
  Future<void> updateFoodRecommendations(
    List<FoodRecommendation> recommendations,
  );

  /// Update a single food recommendation in cache
  Future<void> updateFoodRecommendation(FoodRecommendation recommendation);

  /// Get food recommendations from cache
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  });
}
