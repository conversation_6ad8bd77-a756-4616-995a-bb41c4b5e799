import 'package:detoxme/data/local/models/user_profile_hive_model.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

abstract class LocalProfileDataSource {
  Future<UserProfile?> getProfile();
  Future<void> saveProfile(UserProfile profile);
}

class LocalProfileDataSourceImpl implements LocalProfileDataSource {
  final String _boxName = 'user_profile';
  final String _profileKey = 'user_profile';

  Future<Box<UserProfileHiveModel>> _getBox() async {
    // Ensure the box is open and specify the type
    return await Hive.openBox<UserProfileHiveModel>(_boxName);
  }

  @override
  Future<UserProfile?> getProfile() async {
    try {
      final box = await _getBox();
      final profileModel = box.get(_profileKey);
      // Convert Hive model to domain model
      return profileModel?.toDomainModel();
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error getting profile: $e');
      }
      // Depending on requirements, could return null or throw a custom exception
      return null;
    }
  }

  @override
  Future<void> saveProfile(UserProfile profile) async {
    try {
      final box = await _getBox();
      // Convert domain model to Hive model
      final profileModel = UserProfileHiveModel.fromDomainModel(profile);
      await box.put(_profileKey, profileModel);
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error saving profile: $e');
      }
      // Rethrow or handle as needed
      rethrow;
    }
  }
}
