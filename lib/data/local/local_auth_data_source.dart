import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

abstract class LocalAuthDataSource {
  Future<bool> hasPin();
  Future<void> savePinHash(String pinHash);
  Future<String?> getPinHash();
}

class LocalAuthDataSourceImpl implements LocalAuthDataSource {
  final String _boxName = 'auth';
  final String _pinKey = 'parent_pin_hash';

  Future<Box> _getBox() async {
    return await Hive.openBox(_boxName);
  }

  @override
  Future<bool> hasPin() async {
    try {
      final box = await _getBox();
      return box.containsKey(_pinKey);
    } catch (e) {
      // Handle potential Hive errors (e.g., initialization issues)
      if (kDebugMode) {
        print('Hive Error checking for PIN: $e');
      }
      return false; // Assume no PIN if error occurs
    }
  }

  @override
  Future<String?> getPinHash() async {
    try {
      final box = await _getBox();
      return box.get(_pinKey) as String?;
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error getting PIN hash: $e');
      }
      return null;
    }
  }

  @override
  Future<void> savePinHash(String pinHash) async {
    try {
      final box = await _getBox();
      await box.put(_pinKey, pinHash);
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error saving PIN hash: $e');
      }
      // Consider throwing a specific exception or returning a failure state
      rethrow; // Rethrow for repository layer to handle
    }
  }
}
