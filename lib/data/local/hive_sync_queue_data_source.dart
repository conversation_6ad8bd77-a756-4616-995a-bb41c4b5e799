import 'package:detoxme/core/constants/hive_boxes.dart';
import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';
import 'package:detoxme/data/local/models/failed_update_hive_model.dart';
import 'package:detoxme/data/local/models/queued_update_hive_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:detoxme/core/init/storage_initializer.dart';

// Implements the repository interface defined in the domain layer
class HiveSyncQueueDataSource implements LocalSyncQueueRepository {
  // Lazy initialization for boxes
  LazyBox<QueuedUpdateHiveModel>? _queueBox;
  LazyBox<FailedUpdateHiveModel>? _failedQueueBox;

  // Ensure main queue box is open
  Future<LazyBox<QueuedUpdateHiveModel>> _ensureQueueBoxOpen() async {
    if (_queueBox == null || !_queueBox!.isOpen) {
      try {
        _queueBox = StorageInitializer.getLazyBox<QueuedUpdateHiveModel>(
          HiveBoxes.syncQueueBox,
        );
      } catch (e) {
        if (kDebugMode) {
          print('Error getting sync queue box: $e');
        }
        // Try opening it if not already available
        try {
          _queueBox = await Hive.openLazyBox<QueuedUpdateHiveModel>(
            HiveBoxes.syncQueueBox,
          );
        } catch (openError) {
          if (kDebugMode) {
            print('Failed to open sync queue box: $openError');
          }
          // If box can't be opened due to corrupted data, try deleting and recreating it
          if (openError.toString().contains('corrupted') ||
              openError.toString().contains('Failed to load box')) {
            try {
              if (kDebugMode) {
                print(
                  'Attempting to delete corrupted sync queue box and recreate it',
                );
              }
              await Hive.deleteBoxFromDisk(HiveBoxes.syncQueueBox);
              _queueBox = await Hive.openLazyBox<QueuedUpdateHiveModel>(
                HiveBoxes.syncQueueBox,
              );
            } catch (deleteError) {
              if (kDebugMode) {
                print('Fatal error handling sync queue box: $deleteError');
              }
              rethrow;
            }
          } else {
            rethrow;
          }
        }
      }
    }
    return _queueBox!;
  }

  // Ensure dead-letter queue box is open
  Future<LazyBox<FailedUpdateHiveModel>> _ensureFailedQueueBoxOpen() async {
    if (_failedQueueBox == null || !_failedQueueBox!.isOpen) {
      try {
        _failedQueueBox = StorageInitializer.getLazyBox<FailedUpdateHiveModel>(
          HiveBoxes.failedQueueBox,
        );
      } catch (e) {
        if (kDebugMode) {
          print('Error getting failed queue box: $e');
        }
        // Try opening it if not already available
        try {
          _failedQueueBox = await Hive.openLazyBox<FailedUpdateHiveModel>(
            HiveBoxes.failedQueueBox,
          );
        } catch (openError) {
          if (kDebugMode) {
            print('Failed to open failed queue box: $openError');
          }
          // If box can't be opened due to corrupted data, try deleting and recreating it
          if (openError.toString().contains('corrupted') ||
              openError.toString().contains('Failed to load box')) {
            try {
              if (kDebugMode) {
                print(
                  'Attempting to delete corrupted failed queue box and recreate it',
                );
              }
              await Hive.deleteBoxFromDisk(HiveBoxes.failedQueueBox);
              _failedQueueBox = await Hive.openLazyBox<FailedUpdateHiveModel>(
                HiveBoxes.failedQueueBox,
              );
            } catch (deleteError) {
              if (kDebugMode) {
                print('Fatal error handling failed queue box: $deleteError');
              }
              rethrow;
            }
          } else {
            rethrow;
          }
        }
      }
    }
    return _failedQueueBox!;
  }

  @override
  Future<void> addUpdateToQueue(QueuedUpdateHiveModel update) async {
    try {
      final box = await _ensureQueueBoxOpen();
      await box.add(update);
      if (kDebugMode) {
        print('Added update of type ${update.updateType} to sync queue.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding update to queue: $e');
      }
      rethrow;
    }
  }

  @override
  Future<List<QueuedUpdateHiveModel>> getQueuedUpdates() async {
    try {
      final box = await _ensureQueueBoxOpen();
      final List<QueuedUpdateHiveModel> updates = [];
      for (var i = 0; i < box.length; i++) {
        try {
          final item = await box.getAt(i);
          if (item != null) {
            updates.add(item);
          }
        } catch (itemError) {
          if (kDebugMode) {
            print('Error retrieving item at index $i: $itemError');
          }
          // Skip corrupted items and continue processing others
          continue;
        }
      }
      return updates;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting queued updates: $e');
      }
      return []; // Return empty list on error
    }
  }

  @override
  Future<void> removeUpdateFromQueue(dynamic key) async {
    try {
      final box = await _ensureQueueBoxOpen();
      await box.delete(key);
      if (kDebugMode) {
        print('Removed update with key $key from sync queue.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error removing update from queue: $e');
      }
      // Don't rethrow - if we can't remove it, we'll try again later
    }
  }

  @override
  Future<void> addFailedUpdate(FailedUpdateHiveModel failedUpdate) async {
    try {
      final box = await _ensureFailedQueueBoxOpen();
      await box.add(failedUpdate);
      if (kDebugMode) {
        print(
          'Added failed update of type ${failedUpdate.updateType} to dead-letter queue.',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding failed update to queue: $e');
      }
      // Don't rethrow - if we can't add it to failed queue, we don't want to block other operations
    }
  }
}
