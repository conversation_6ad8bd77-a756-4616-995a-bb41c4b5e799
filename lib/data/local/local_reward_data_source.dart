import 'package:detoxme/data/local/models/player_stats_hive_model.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart';

abstract class LocalRewardDataSource {
  Future<PlayerStats?> getPlayerStats();
  Future<void> savePlayerStats(PlayerStats stats);
  Future<List<Reward>?> getRedeemedRewards();
  Future<void> saveRedeemedRewards(List<Reward> rewards);
}

class LocalRewardDataSourceImpl implements LocalRewardDataSource {
  final String _boxName = 'player_stats';
  final String _statsKey = 'current_stats';
  final String _rewardsBoxName = 'redeemed_rewards';
  final String _rewardsKey = 'user_rewards';

  Future<Box<PlayerStatsHiveModel>> _getBox() async {
    return await Hive.openBox<PlayerStatsHiveModel>(_boxName);
  }
  
  Future<Box> _getRewardsBox() async {
    return await Hive.openBox(_rewardsBoxName);
  }

  @override
  Future<PlayerStats?> getPlayerStats() async {
    try {
      final box = await _getBox();
      final statsModel = box.get(_statsKey);
      // Return initial stats if nothing is found in the box
      return statsModel?.toEntity() ?? PlayerStats.initial;
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error getting player stats: $e');
      }
      // Return initial stats as a fallback on error
      return PlayerStats.initial;
    }
  }

  @override
  Future<void> savePlayerStats(PlayerStats stats) async {
    try {
      final box = await _getBox();
      final statsModel = PlayerStatsHiveModel.fromEntity(stats);
      await box.put(_statsKey, statsModel);
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error saving player stats: $e');
      }
      rethrow;
    }
  }

  @override
  Future<List<Reward>?> getRedeemedRewards() async {
    try {
      final box = await _getRewardsBox();
      final rewards = box.get(_rewardsKey);

      if (rewards == null) {
        return [];
      }

      // Convert from map to Reward entities
      return (rewards as List).map<Reward>((item) {
        // Convert DateTime strings back to DateTime objects
        Map<String, dynamic> rewardMap = Map<String, dynamic>.from(item);

        // Parse DateTime fields
        DateTime createdAt = DateTime.now();
        if (rewardMap['createdAt'] != null) {
          createdAt = DateTime.parse(rewardMap['createdAt']);
        }
        
        // Create Reward entity from map
        return Reward(
          id: rewardMap['id'] as String,
          userId: rewardMap['userId'] as String,
          title: rewardMap['title'] as String,
          description: rewardMap['description'] as String?,
          pointsEarned: rewardMap['pointsEarned'] as int,
          rewardType: rewardMap['rewardType'] as String,
          sourceId: rewardMap['sourceId'] as String?,
          sourceType: rewardMap['sourceType'] as String?,
          createdAt: createdAt,
          metadata:
              rewardMap['metadata'] != null
                  ? Map<String, dynamic>.from(rewardMap['metadata'])
                  : null,
        );
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error getting redeemed rewards: $e');
      }
      return [];
    }
  }

  @override
  Future<void> saveRedeemedRewards(List<Reward> rewards) async {
    try {
      final box = await _getRewardsBox();

      // Convert Reward entities to serializable maps
      final rewardMaps =
          rewards
              .map(
                (reward) => {
                  'id': reward.id,
                  'userId': reward.userId,
                  'title': reward.title,
                  'description': reward.description,
                  'pointsEarned': reward.pointsEarned,
                  'rewardType': reward.rewardType,
                  'sourceId': reward.sourceId,
                  'sourceType': reward.sourceType,
                  'createdAt': reward.createdAt.toIso8601String(),
                  'metadata': reward.metadata,
                },
              )
              .toList();

      await box.put(_rewardsKey, rewardMaps);
    } catch (e) {
      if (kDebugMode) {
        print('Hive Error saving redeemed rewards: $e');
      }
      rethrow;
    }
  }
}
