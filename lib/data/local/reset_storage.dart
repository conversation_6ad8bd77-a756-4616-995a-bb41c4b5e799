import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart';

/// Utility class to help with resetting storage when model structures change
class StorageReset {
  /// Resets all reward-related Hive boxes
  static Future<void> resetRewardBoxes() async {
    try {
      // Clear the redeemed_rewards box
      if (Hive.isBoxOpen('redeemed_rewards')) {
        final box = Hive.box('redeemed_rewards');
        await box.clear();
        if (kDebugMode) {
          print('Successfully cleared redeemed_rewards box');
        }
      }

      // Clear other related boxes if needed
      if (Hive.isBoxOpen('player_stats')) {
        final box = Hive.box('player_stats');
        await box.clear();
        if (kDebugMode) {
          print('Successfully cleared player_stats box');
        }
      }

      // If there are other reward-related boxes, clear them here

      if (kDebugMode) {
          print('Successfully reset all reward-related boxes');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error resetting reward boxes: $e');
      }
      rethrow;
    }
  }
}
