// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queued_update_hive_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QueuedUpdateHiveModelAdapter extends TypeAdapter<QueuedUpdateHiveModel> {
  @override
  final int typeId = 3;

  @override
  QueuedUpdateHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QueuedUpdateHiveModel(
      updateType: fields[0] as String,
      payload: (fields[1] as Map).cast<dynamic, dynamic>(),
      queuedAt: fields[2] as DateTime,
      retryCount: fields[3] as int,
      nextRetryAt: fields[4] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, QueuedUpdateHiveModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.updateType)
      ..writeByte(1)
      ..write(obj.payload)
      ..writeByte(2)
      ..write(obj.queuedAt)
      ..writeByte(3)
      ..write(obj.retryCount)
      ..writeByte(4)
      ..write(obj.nextRetryAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QueuedUpdateHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
