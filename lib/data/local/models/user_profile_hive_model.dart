import 'package:hive/hive.dart';
import 'package:detoxme/domain/entities/user_profile.dart';

part 'user_profile_hive_model.g.dart';

@HiveType(typeId: 7) 
class UserProfileHiveModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String? username;

  @HiveField(2)
  final int? birthYear;

  @HiveField(3)
  final String? gender;

  @HiveField(4)
  final String? lifeSituation;

  @HiveField(5)
  final List<String>? goals; // Made nullable to match model possibility

  @HiveField(6)
  final String? avatarUrl;

  @HiveField(7)
  final String? bio;

  @HiveField(8)
  final String email;

  UserProfileHiveModel({
    required this.id,
    this.username,
    required this.email,
    this.birthYear,
    this.gender,
    this.lifeSituation,
    this.goals,
    this.avatarUrl,
    this.bio,
  });

  // Conversion from Domain Model to Hive Model
  factory UserProfileHiveModel.fromDomainModel(UserProfile model) {
    return UserProfileHiveModel(
      id: model.id,
      username: model.username,
      email: model.email,
      birthYear: model.birthYear,
      gender: model.gender,
      lifeSituation: model.lifeSituation,
      goals: model.goals.isNotEmpty ? model.goals : null, // Store null if empty
      avatarUrl: model.avatarUrl,
      bio: model.bio,
    );
  }

  // Conversion from Hive Model to Domain Model
  UserProfile toDomainModel() {
    return UserProfile(
      id: id,
      username: username,
      email: email,
      birthYear: birthYear,
      gender: gender,
      lifeSituation: lifeSituation,
      goals: goals ?? [], // Convert null back to empty list
      avatarUrl: avatarUrl,
      bio: bio,
    );
  }
}
