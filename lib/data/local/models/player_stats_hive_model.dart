import 'package:hive/hive.dart';
import 'package:detoxme/domain/entities/player_stats.dart';

// *** RUN build_runner AFTER CREATING/MODIFYING THIS FILE ***
// command: flutter packages pub run build_runner build --delete-conflicting-outputs

part 'player_stats_hive_model.g.dart'; // Generated part file

@HiveType(typeId: 2) // Next available typeId (0: Profile, 1: Task)
class PlayerStatsHiveModel extends HiveObject {
  // Field indices need to be unique and sequential within this model
  @HiveField(0)
  final String userId;

  @HiveField(1)
  final String username;

  @HiveField(2)
  final int currentLevel;

  @HiveField(3)
  final int currentXp;

  @HiveField(4)
  final List<String> earnedBadgeIds;

  @HiveField(5)
  final List<String> earnedCardIds;

  PlayerStatsHiveModel({
    required this.userId,
    required this.username,
    required this.currentLevel,
    required this.currentXp,
    required this.earnedBadgeIds,
    required this.earnedCardIds,
  });

  // Conversion from Entity to Hive Model
  factory PlayerStatsHiveModel.fromEntity(PlayerStats entity) {
    return PlayerStatsHiveModel(
      userId: entity.userId,
      username: entity.username,
      currentLevel: entity.currentLevel,
      currentXp: entity.currentXp,
      earnedBadgeIds: List.from(entity.earnedBadgeIds), // Create copy
      earnedCardIds: List.from(entity.earnedCardIds), // Create copy
    );
  }

  // Conversion from Hive Model to Entity
  PlayerStats toEntity() {
    return PlayerStats(
      userId: userId,
      username: username,
      currentLevel: currentLevel,
      currentXp: currentXp,
      earnedBadgeIds: List.from(earnedBadgeIds), // Create copy
      earnedCardIds: List.from(earnedCardIds), // Create copy
    );
  }
}
