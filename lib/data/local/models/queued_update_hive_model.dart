import 'package:hive/hive.dart';

// *** RUN build_runner AFTER CREATING/MODIFYING THIS FILE ***
part 'queued_update_hive_model.g.dart';

@HiveType(
  typeId: 3, // Ensure this TypeId is unique across all models
)
class QueuedUpdateHiveModel extends HiveObject {
  @HiveField(0)
  final String updateType; // e.g., 'playerStats', 'childProfile'

  @HiveField(1)
  final Map<dynamic, dynamic> payload; // Use <dynamic, dynamic> for broad compatibility

  @HiveField(2)
  final DateTime queuedAt;

  @HiveField(3)
  int retryCount;

  @HiveField(4)
  DateTime? nextRetryAt; // For exponential backoff

  QueuedUpdateHiveModel({
    required this.updateType,
    required this.payload,
    required this.queuedAt,
    this.retryCount = 0,
    this.nextRetryAt,
  });
}
