import 'package:hive/hive.dart';

// *** RUN build_runner AFTER CREATING/MODIFYING THIS FILE ***
part 'failed_update_hive_model.g.dart';

@HiveType(
  typeId: 4, // Ensure this TypeId is unique
)
class FailedUpdateHiveModel extends HiveObject {
  @HiveField(0)
  final String updateType;

  @HiveField(1)
  final Map<dynamic, dynamic> payload;

  @HiveField(2)
  final DateTime queuedAt;

  @HiveField(3)
  final DateTime failedAt;

  @HiveField(4)
  final String errorDetails;

  @HiveField(5)
  final int finalRetryCount;

  FailedUpdateHiveModel({
    required this.updateType,
    required this.payload,
    required this.queuedAt,
    required this.failedAt,
    required this.errorDetails,
    required this.finalRetryCount,
  });
}
