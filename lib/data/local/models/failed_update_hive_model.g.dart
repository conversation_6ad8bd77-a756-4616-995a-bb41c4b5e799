// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'failed_update_hive_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FailedUpdateHiveModelAdapter extends TypeAdapter<FailedUpdateHiveModel> {
  @override
  final int typeId = 4;

  @override
  FailedUpdateHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FailedUpdateHiveModel(
      updateType: fields[0] as String,
      payload: (fields[1] as Map).cast<dynamic, dynamic>(),
      queuedAt: fields[2] as DateTime,
      failedAt: fields[3] as DateTime,
      errorDetails: fields[4] as String,
      finalRetryCount: fields[5] as int,
    );
  }

  @override
  void write(BinaryWriter writer, FailedUpdateHiveModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.updateType)
      ..writeByte(1)
      ..write(obj.payload)
      ..writeByte(2)
      ..write(obj.queuedAt)
      ..writeByte(3)
      ..write(obj.failedAt)
      ..writeByte(4)
      ..write(obj.errorDetails)
      ..writeByte(5)
      ..write(obj.finalRetryCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FailedUpdateHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
