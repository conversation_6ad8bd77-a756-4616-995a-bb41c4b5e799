import 'package:hive/hive.dart';
import 'package:detoxme/domain/entities/task.dart';

// *** RUN build_runner AFTER CREATING/MODIFYING THIS FILE ***
// command: flutter packages pub run build_runner build --delete-conflicting-outputs

part 'task_hive_model.g.dart'; // Generated part file

@HiveType(typeId: 1) // Next available typeId
class TaskHiveModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final int durationInSeconds; // Store duration as integer

  TaskHiveModel({
    required this.id,
    required this.title,
    required this.description,
    required this.durationInSeconds,
  });

  // Conversion from Entity to Hive Model
  factory TaskHiveModel.fromEntity(Task entity) {
    return TaskHiveModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      durationInSeconds:
          entity.duration.inSeconds, // Convert Duration to seconds
    );
  }

  // Conversion from Hive Model to Entity
  Task toEntity() {
    return Task(
      id: id,
      title: title,
      description: description,
      duration: Duration(
        seconds: durationInSeconds,
      ), // Convert seconds back to Duration
    );
  }
}
