// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile_hive_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserProfileHiveModelAdapter extends TypeAdapter<UserProfileHiveModel> {
  @override
  final int typeId = 7;

  @override
  UserProfileHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserProfileHiveModel(
      id: fields[0] as String,
      username: fields[1] as String?,
      email: fields[8] as String,
      birthYear: fields[2] as int?,
      gender: fields[3] as String?,
      lifeSituation: fields[4] as String?,
      goals: (fields[5] as List?)?.cast<String>(),
      avatarUrl: fields[6] as String?,
      bio: fields[7] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, UserProfileHiveModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.username)
      ..writeByte(2)
      ..write(obj.birthYear)
      ..writeByte(3)
      ..write(obj.gender)
      ..writeByte(4)
      ..write(obj.lifeSituation)
      ..writeByte(5)
      ..write(obj.goals)
      ..writeByte(6)
      ..write(obj.avatarUrl)
      ..writeByte(7)
      ..write(obj.bio)
      ..writeByte(8)
      ..write(obj.email);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfileHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
