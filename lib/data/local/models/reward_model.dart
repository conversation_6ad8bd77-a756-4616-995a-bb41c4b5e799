import 'package:hive_flutter/hive_flutter.dart';
import '../../../domain/entities/reward.dart';

part 'reward_model.g.dart';

/// Model class for storing rewards in local storage
@HiveType(typeId: 6) // Assign a unique Hive type ID
class RewardModel extends HiveObject {
  /// Unique identifier for the reward
  @HiveField(0)
  final String id;

  /// User ID associated with this reward
  @HiveField(1)
  final String userId;

  /// Title of the reward
  @HiveField(2)
  final String title;

  /// Detailed description of the reward
  @HiveField(3)
  final String? description;

  /// Points earned from this reward
  @HiveField(4)
  final int pointsEarned;

  /// Type of reward (e.g., activity_completion, referral_bonus)
  @HiveField(5)
  final String rewardType;

  /// Optional ID of the source that generated this reward
  @HiveField(6)
  final String? sourceId;

  /// Type of source (e.g., 'coupon', 'activity')
  @HiveField(7)
  final String? sourceType;

  /// Timestamp when the reward was created
  @HiveField(8)
  final DateTime createdAt;

  /// Additional metadata as a JSON Map
  @HiveField(9)
  final Map<String, dynamic>? metadata;

  /// Constructor
  RewardModel({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    required this.pointsEarned,
    required this.rewardType,
    this.sourceId,
    this.sourceType,
    required this.createdAt,
    this.metadata,
  });

  /// Convert this model to a domain entity
  Reward toEntity() {
    return Reward(
      id: id,
      userId: userId,
      title: title,
      description: description,
      pointsEarned: pointsEarned,
      rewardType: rewardType,
      sourceId: sourceId,
      sourceType: sourceType,
      createdAt: createdAt,
      metadata: metadata,
    );
  }

  /// Create a model from a domain entity
  factory RewardModel.fromEntity(Reward reward) {
    return RewardModel(
      id: reward.id,
      userId: reward.userId,
      title: reward.title,
      description: reward.description,
      pointsEarned: reward.pointsEarned,
      rewardType: reward.rewardType,
      sourceId: reward.sourceId,
      sourceType: reward.sourceType,
      createdAt: reward.createdAt,
      metadata: reward.metadata,
    );
  }

  /// Create from JSON map
  factory RewardModel.fromJson(Map<String, dynamic> json) {
    // Ensure proper type conversion for all fields
    return RewardModel(
      id: json['id']?.toString() ?? '',
      userId: json['user_id']?.toString() ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      pointsEarned:
          json['points_earned'] is int
              ? json['points_earned']
              : int.tryParse(json['points_earned']?.toString() ?? '0') ?? 0,
      rewardType: json['reward_type'] ?? 'other',
      sourceId: json['source_id']?.toString(),
      sourceType: json['source_type'],
      createdAt:
          json['created_at'] is DateTime
              ? json['created_at']
              : DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      metadata:
          json['metadata'] is Map
              ? Map<String, dynamic>.from(json['metadata'])
              : null,
    );
  }

  /// Convert to JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'points_earned': pointsEarned,
      'reward_type': rewardType,
      'source_id': sourceId,
      'source_type': sourceType,
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}
