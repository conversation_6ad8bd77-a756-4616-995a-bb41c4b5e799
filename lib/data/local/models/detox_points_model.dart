import 'package:hive_flutter/hive_flutter.dart';
import '../../../domain/entities/detox_points.dart';

part 'detox_points_model.g.dart';

/// Model class for storing detox points in local storage
@HiveType(typeId: 5) // Assign a unique Hive type ID
class DetoxPointsModel extends HiveObject {
  /// Unique identifier for the detox points record
  @HiveField(0)
  final String id;

  /// ID of the user these points belong to
  @HiveField(1)
  final String userId;

  /// User's display name for leaderboard
  @HiveField(2)
  final String userName;

  /// Optional user avatar URL
  @HiveField(3)
  final String? avatarUrl;

  /// Total accumulated points
  @HiveField(4)
  final int totalPoints;

  /// Current user level based on points
  @HiveField(5)
  final int level;

  /// Available points that can be redeemed
  @HiveField(6)
  final int availablePoints;

  /// Points needed to reach the next level
  @HiveField(7)
  final int pointsToNextLevel;

  /// Timestamp of last points update
  @HiveField(8)
  final DateTime lastUpdated;

  /// Points earned today
  @HiveField(9)
  final int dailyPoints;

  /// User's daily goal target
  @HiveField(10)
  final int dailyGoalTarget;

  /// Date for the daily points/goal
  @HiveField(11)
  final DateTime dailyDate;

  /// Constructor for DetoxPointsModel
  DetoxPointsModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.avatarUrl,
    required this.totalPoints,
    required this.level,
    required this.availablePoints,
    required this.pointsToNextLevel,
    required this.lastUpdated,
    required this.dailyPoints,
    required this.dailyGoalTarget,
    required this.dailyDate,
  });

  /// Factory constructor to create a DetoxPointsModel from JSON
  factory DetoxPointsModel.fromJson(Map<String, dynamic> json) {
    return DetoxPointsModel(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      userName: json['user_name'],
      avatarUrl: json['avatar_url'],
      totalPoints: json['total_points'],
      level: json['level'],
      availablePoints: json['available_points'],
      pointsToNextLevel: json['points_to_next_level'],
      lastUpdated: DateTime.parse(json['last_updated']),
      dailyPoints: json['daily_points'] ?? 0,
      dailyGoalTarget: json['daily_goal_target'] ?? 50,
      dailyDate:
          json['daily_date'] != null
              ? DateTime.parse(json['daily_date'])
              : DateTime.now(),
    );
  }

  /// Factory constructor to create a DetoxPointsModel from a domain entity
  factory DetoxPointsModel.fromEntity(DetoxPoints entity) {
    return DetoxPointsModel(
      id: entity.id,
      userId: entity.userId,
      userName: entity.userName,
      avatarUrl: entity.avatarUrl,
      totalPoints: entity.totalPoints,
      level: entity.level,
      availablePoints: entity.availablePoints,
      pointsToNextLevel: entity.pointsToNextLevel,
      lastUpdated: entity.lastUpdated,
      dailyPoints: entity.dailyPoints,
      dailyGoalTarget: entity.dailyGoalTarget,
      dailyDate: entity.dailyDate,
    );
  }

  /// Convert this model to a Map for JSON serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'avatar_url': avatarUrl,
      'total_points': totalPoints,
      'level': level,
      'available_points': availablePoints,
      'points_to_next_level': pointsToNextLevel,
      'last_updated': lastUpdated.toIso8601String(),
      'daily_points': dailyPoints,
      'daily_goal_target': dailyGoalTarget,
      'daily_date': dailyDate.toIso8601String(),
    };
  }

  /// Convert this model to a domain entity
  DetoxPoints toEntity() {
    return DetoxPoints(
      id: id,
      userId: userId,
      userName: userName,
      dailyPoints: dailyPoints,
      dailyGoalTarget: dailyGoalTarget,
      dailyDate: dailyDate,
      avatarUrl: avatarUrl,
      totalPoints: totalPoints,
      level: level,
      availablePoints: availablePoints,
      pointsToNextLevel: pointsToNextLevel,
      lastUpdated: lastUpdated,
      usedPoints: totalPoints - availablePoints,
    );
  }
}
