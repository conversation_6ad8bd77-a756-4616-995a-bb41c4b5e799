// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player_stats_hive_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PlayerStatsHiveModelAdapter extends TypeAdapter<PlayerStatsHiveModel> {
  @override
  final int typeId = 2;

  @override
  PlayerStatsHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PlayerStatsHiveModel(
      userId: fields[0] as String,
      username: fields[1] as String,
      currentLevel: fields[2] as int,
      currentXp: fields[3] as int,
      earnedBadgeIds: (fields[4] as List).cast<String>(),
      earnedCardIds: (fields[5] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, PlayerStatsHiveModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.username)
      ..writeByte(2)
      ..write(obj.currentLevel)
      ..writeByte(3)
      ..write(obj.currentXp)
      ..writeByte(4)
      ..write(obj.earnedBadgeIds)
      ..writeByte(5)
      ..write(obj.earnedCardIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PlayerStatsHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
