// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detox_points_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DetoxPointsModelAdapter extends TypeAdapter<DetoxPointsModel> {
  @override
  final int typeId = 5;

  @override
  DetoxPointsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DetoxPointsModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      userName: fields[2] as String,
      avatarUrl: fields[3] as String?,
      totalPoints: fields[4] as int,
      level: fields[5] as int,
      availablePoints: fields[6] as int,
      pointsToNextLevel: fields[7] as int,
      lastUpdated: fields[8] as DateTime,
      dailyPoints: fields.containsKey(9) ? fields[9] as int : 0,
      dailyGoalTarget: fields.containsKey(10) ? fields[10] as int : 50,
      dailyDate:
          fields.containsKey(11) ? fields[11] as DateTime : DateTime.now(),
    );
  }

  @override
  void write(BinaryWriter writer, DetoxPointsModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.userName)
      ..writeByte(3)
      ..write(obj.avatarUrl)
      ..writeByte(4)
      ..write(obj.totalPoints)
      ..writeByte(5)
      ..write(obj.level)
      ..writeByte(6)
      ..write(obj.availablePoints)
      ..writeByte(7)
      ..write(obj.pointsToNextLevel)
      ..writeByte(8)
      ..write(obj.lastUpdated)
      ..writeByte(9)
      ..write(obj.dailyPoints)
      ..writeByte(10)
      ..write(obj.dailyGoalTarget)
      ..writeByte(11)
      ..write(obj.dailyDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DetoxPointsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
