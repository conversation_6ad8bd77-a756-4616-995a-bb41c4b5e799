import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/ai_motivation.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/ai_motivation_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of the AI motivation repository
class AiMotivationRepositoryImpl implements AiMotivationRepository {
  /// The Supabase client
  final SupabaseClient _supabaseClient;

  /// Creates a new AI motivation repository
  AiMotivationRepositoryImpl({required SupabaseClient supabaseClient}) 
      : _supabaseClient = supabaseClient;
  
  /// Creates a new repository with the default Supabase client
  static Future<AiMotivationRepositoryImpl> create() async {
    final supabaseClient = Supabase.instance.client;
    return AiMotivationRepositoryImpl(supabaseClient: supabaseClient);
  }

  @override
  Future<Either<Failure, AiMotivation>> getBestMotivationMatch({
    required String language,
    required String lifeSituation,
    required List<String> goals,
    required String gender,
    required int birthYear,
  }) async {
    try {
      // Get the age group from birth year
      final String ageGroup = _getAgeGroupFromBirthYear(birthYear);

      // Try to get records with perfect match
      final response = await _supabaseClient
          .from('ai_motivations')
          .select()
          .eq('language', language)
          .eq('life_situation', lifeSituation)
          .eq('gender', gender)
          .eq('age_group', ageGroup);
      
      // Manually filter for goals since Supabase's 'in' operator can be tricky
      final List<Map<String, dynamic>> rawData = List<Map<String, dynamic>>.from(response);
      List<Map<String, dynamic>> data = rawData;
      
      // Only filter by goals if we have goals to filter by
      if (goals.isNotEmpty) {
        data = rawData.where((item) => 
            goals.contains(item['goal'] as String)).toList();
      }

      // If no perfect match, try with just language and life situation
      if (data.isEmpty) {
        final fallbackResponse = await _supabaseClient
            .from('ai_motivations')
            .select()
            .eq('language', language)
            .eq('life_situation', lifeSituation);

        data = List<Map<String, dynamic>>.from(fallbackResponse);
      }

      // If still no match, try with just language
      if (data.isEmpty) {
        final fallbackResponse = await _supabaseClient
            .from('ai_motivations')
            .select()
            .eq('language', language);

        data = List<Map<String, dynamic>>.from(fallbackResponse);
      }

      // If still no match, use default language (English)
      if (data.isEmpty) {
        final fallbackResponse = await _supabaseClient
            .from('ai_motivations')
            .select()
            .eq('language', 'en');

        data = List<Map<String, dynamic>>.from(fallbackResponse);
      }

      // If we have matches, randomly select one
      if (data.isNotEmpty) {
        final randomIndex = DateTime.now().millisecondsSinceEpoch % data.length;
        return Right(AiMotivation.fromMap(data[randomIndex]));
      }

      return Left(NotFoundFailure('No motivational content available'));
    } catch (e) {
      debugPrint('Error fetching motivational content: $e');
      return Left(ServerFailure('Failed to fetch motivational content: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AiMotivation>>> getAllMotivations() async {
    try {
      final response = await _supabaseClient.from('ai_motivations').select();

      final List<Map<String, dynamic>> data =
          List<Map<String, dynamic>>.from(response);
      final motivations = data.map(AiMotivation.fromMap).toList();

      return Right(motivations);
    } catch (e) {
      debugPrint('Error fetching all motivational content: $e');
      return Left(ServerFailure('Failed to fetch motivational content: $e'));
    }
  }

  /// Convert birth year to age group
  String _getAgeGroupFromBirthYear(int birthYear) {
    final currentYear = DateTime.now().year;
    final age = currentYear - birthYear;

    if (age < 30) {
      return 'young_adult'; // 18-30
    } else if (age < 45) {
      return 'working_adult'; // 30-45
    } else {
      return 'senior_adult'; // 45+
    }
  }
}
