import 'package:dartz/dartz.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/data/remote/coupon_remote_data_source.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/redemption.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/coupon_repository.dart';
import 'package:detoxme/core/errors/exceptions.dart';

/// Implementation of [CouponRepository]
class CouponRepositoryImpl implements CouponRepository {
  /// Remote data source for coupon operations
  final CouponRemoteDataSource remoteDataSource;

  /// Network information to check connectivity
  final NetworkInfo networkInfo;

  /// Constructor
  CouponRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Coupon>>> getAllCoupons() async {
    if (await networkInfo.isConnected) {
      try {
        final couponModels = await remoteDataSource.getAllCoupons();
        return Right(couponModels.map((model) => model.toDomain()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Coupon>>> getCouponsForMood(MoodType mood) async {
    if (await networkInfo.isConnected) {
      try {
        // Convert the MoodType enum to a string for the data source
        final moodString = mood.toString().split('.').last.toLowerCase();
        final couponModels = await remoteDataSource.getCouponsForMood(moodString);
        return Right(couponModels.map((model) => model.toDomain()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Coupon>> getCouponById(String couponId) async {
    if (await networkInfo.isConnected) {
      try {
        final couponModel = await remoteDataSource.getCouponById(couponId);
        return Right(couponModel.toDomain());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Redemption>> redeemCoupon(String couponId) async {
    if (await networkInfo.isConnected) {
      try {
        final redemptionModel = await remoteDataSource.redeemCoupon(couponId);
        return Right(redemptionModel.toDomain());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String>> generateQRCode(String redemptionId) async {
    // This is handled within the redeemCoupon method in the data source
    // The QR code is generated when a redemption is created
    if (await networkInfo.isConnected) {
      try {
        final redemptions = await remoteDataSource.getUserRedemptions();
        final redemption = redemptions.firstWhere(
          (r) => r.id == redemptionId,
          orElse: () => throw ServerException('Redemption not found'),
        );
        return Right(redemption.qrCode);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, void>> provideFeedback(
    String redemptionId,
    int rating, {
    String? comment,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.provideFeedback(
          redemptionId,
          rating,
          comment: comment,
        );
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Redemption>>> getUserRedemptions() async {
    if (await networkInfo.isConnected) {
      try {
        final redemptionModels = await remoteDataSource.getUserRedemptions();
        return Right(
          redemptionModels.map((model) => model.toDomain()).toList(),
        );
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Partner>>> getAllPartners() async {
    if (await networkInfo.isConnected) {
      try {
        final partnerModels = await remoteDataSource.getAllPartners();
        return Right(partnerModels.map((model) => model.toDomain()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Partner>>> getNearbyPartners(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final partnerModels = await remoteDataSource.getNearbyPartners(
          latitude,
          longitude,
          radiusKm,
        );
        return Right(partnerModels.map((model) => model.toDomain()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
