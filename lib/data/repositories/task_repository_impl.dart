import 'package:dartz/dartz.dart' hide Task; // Hide dartz's Task
import 'package:detoxme/data/local/local_task_data_source.dart';
import 'package:detoxme/domain/entities/task.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/task_repository.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode

// TODO: Implement remote data source and network checking later

class TaskRepositoryImpl implements TaskRepository {
  final LocalTaskDataSource localDataSource;
  // final RemoteTaskDataSource remoteDataSource; // Add later
  // final NetworkInfo networkInfo; // Add later

  TaskRepositoryImpl({
    required this.localDataSource,
    // required this.remoteDataSource, // Add later
    // required this.networkInfo, // Add later
  });

  @override
  Future<Either<Failure, List<Task>>> getTasks() async {
    // --- Simplified Logic (Local Only for now) ---
    // TODO: Implement network checking and remote fetch logic
    // if (await networkInfo.isConnected) {
    //   try {
    //     final remoteTasks = await remoteDataSource.getTasks();
    //     await localDataSource.cacheTasks(remoteTasks);
    //     return Right(remoteTasks);
    //   } catch (e) {
    //     // If remote fetch fails, fall back to local cache
    //     return _getTasksFromLocalDataSource();
    //   }
    // } else {
    // If offline, get from local cache
    return _getTasksFromLocalDataSource();
    // }
  }

  // Helper to get from local source and handle errors
  Future<Either<Failure, List<Task>>> _getTasksFromLocalDataSource() async {
    try {
      var localTasks = await localDataSource.getTasks(); // Make mutable

      if (localTasks.isEmpty) {
        if (kDebugMode) {
          print("Local task cache is empty. Seeding initial tasks.");
        }
        // Seed initial tasks
        localTasks = _getInitialTasks();
        // Save seeded tasks to cache for next time
        await localDataSource.cacheTasks(localTasks);
      }
      return Right(localTasks);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting tasks from local data source: $e');
      }
      return const Left(CacheFailure('Could not load tasks from cache.'));
    }
  }

  // Define initial tasks (replace with actual tasks later)
  List<Task> _getInitialTasks() {
    return [
      const Task(
        id: 'task_01',
        title: 'Family Hug Time',
        description: 'Give someone in your family a big hug for 2 minutes!',
        duration: Duration(minutes: 2),
      ),
      const Task(
        id: 'task_02',
        title: 'Drawing Challenge',
        description: 'Draw a picture of your favorite animal.',
        duration: Duration(minutes: 10),
      ),
      const Task(
        id: 'task_03',
        title: 'Tidy Up',
        description: 'Help tidy up your toys or books for 5 minutes.',
        duration: Duration(minutes: 5),
      ),
      const Task(
        id: 'task_04',
        title: 'Jumping Jacks',
        description: 'Do 10 jumping jacks!',
        duration: Duration(minutes: 1),
      ),
    ];
  }
}
