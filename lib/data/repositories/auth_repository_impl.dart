import 'dart:convert'; // For utf8 encoding
import 'package:crypto/crypto.dart'; // For sha256
import 'package:dartz/dartz.dart';
import 'package:detoxme/data/local/local_auth_data_source.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/auth_repository.dart';
import 'package:flutter/foundation.dart';
class AuthRepositoryImpl implements AuthRepository {
  final LocalAuthDataSource localDataSource;

  AuthRepositoryImpl({required this.localDataSource});

  String _hashPin(String pin) {
    final bytes = utf8.encode(pin); // Convert pin to bytes
    final digest = sha256.convert(bytes);
    return digest
        .toString(); // Return the hex string representation of the hash
  }

  @override
  Future<bool> isPinSet() async {
    // Directly forward the call to the data source
    return await localDataSource.hasPin();
  }

  @override
  Future<Either<AuthFailure, Unit>> setPin(String pin) async {
    try {
      final pinHash = _hashPin(pin);
      await localDataSource.savePinHash(pinHash);
      return right(unit); // Indicate success
    } catch (e) {
      // Handle potential storage errors
      if (kDebugMode) {
        print('Error setting PIN: $e');
      }
      return left(const StorageFailure('Could not save PIN.'));
    }
  }

  @override
  Future<Either<AuthFailure, Unit>> verifyPin(String pin) async {
    try {
      final storedHash = await localDataSource.getPinHash();
      if (storedHash == null) {
        return left(const PinNotSetFailure());
      }

      final inputHash = _hashPin(pin);

      if (inputHash == storedHash) {
        return right(unit); // PIN is correct
      } else {
        return left(const IncorrectPinFailure());
      }
    } catch (e) {
      // Handle potential storage errors during retrieval
      if (kDebugMode) {
        print('Error verifying PIN: $e');
      }
      return left(const StorageFailure('Could not verify PIN.'));
    }
  }
}
