import 'package:dartz/dartz.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/data/local/local_profile_data_source.dart';
import 'package:detoxme/data/local/models/queued_update_hive_model.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of UserRepository for managing user profiles with
/// both local caching and remote Supabase synchronization
class ProfileRepositoryImpl implements UserRepository {
  final LocalProfileDataSource localDataSource;
  final SupabaseClient supabaseClient;
  final NetworkInfo networkInfo;
  final LocalSyncQueueRepository localSyncQueueRepository;
  final String _tableName = 'user_profiles';

  ProfileRepositoryImpl({
    required this.localDataSource,
    required this.supabaseClient,
    required this.networkInfo,
    required this.localSyncQueueRepository,
  });

  @override
  Future<Either<Failure, UserProfile>> getProfile() async {
    final currentUserId = supabaseClient.auth.currentUser?.id;
    if (currentUserId == null) {
      return const Left(AuthFailure('User not authenticated.'));
    }

    try {
      // Try fetching from Supabase first if online
      if (await networkInfo.isConnected) {
        try {
          final data =
              await supabaseClient
                  .from(_tableName)
                  .select()
                  .eq('id', currentUserId)
                  .maybeSingle();

          if (data != null) {
            final profile = UserProfile.fromMap(data);
            // Save the fetched profile locally (overwrites previous cache)
            await localDataSource.saveProfile(profile);
            return Right(profile);
          } else {
            // No profile in Supabase, proceed to check local cache
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error fetching profile from Supabase: $e');
          }
          // Fallback to local cache if Supabase fetch fails
        }
      }

      // Fetch from local cache if offline or Supabase failed/returned null
      // Local source doesn't take ID, gets the single cached profile
      final localProfile = await localDataSource.getProfile();
      if (localProfile != null) {
        // Important: Check if the cached profile ID matches the current user.
        // If not, it's stale data from a different user.
        if (localProfile.id == currentUserId) {
          return Right(localProfile);
        } else {
          // Cached profile is for a different user, treat as no profile found
          if (kDebugMode) {
            print(
              'Local profile cache mismatch. Expected: $currentUserId, Found: ${localProfile.id}',
            );
          }
        }
      }

      // If no profile exists locally/remotely or local cache mismatched,
      // return a new empty profile associated with the current user ID.
      return Right(UserProfile.empty(currentUserId));
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile from repository: $e');
      }
      return const Left(CacheFailure('Failed to retrieve profile data.'));
    }
  }

  @override
  Future<Either<Failure, SyncStatus>> saveProfile(UserProfile profile) async {
    // Ensure the profile being saved has the correct user ID
    final currentUserId = supabaseClient.auth.currentUser?.id;
    if (currentUserId == null || profile.id != currentUserId) {
      return const Left(AuthFailure('User mismatch or not authenticated.'));
    }

    // 1. Save locally first (overwrites any previous cache)
    try {
      await localDataSource.saveProfile(profile);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving profile locally: $e');
      }
      return const Left(CacheFailure('Failed to save profile locally.'));
    }

    // 2. Always attempt to upsert user_profiles in Supabase
    try {
      // First check if the profile exists in Supabase
      bool profileExists = false;
      try {
        final existingProfile =
            await supabaseClient
                .from(_tableName)
                .select('id')
                .eq('id', currentUserId)
                .maybeSingle();
        profileExists = existingProfile != null;
      } catch (e) {
        if (kDebugMode) {
          print('Error checking if profile exists: $e');
        }
        // Continue with upsert attempt even if check fails
      }

      // If profile doesn't exist, try to create it using the RPC function first
      if (!profileExists) {
        try {
          await supabaseClient.rpc(
            'initialize_user_profile',
            params: {
              'user_id_param': currentUserId,
              'username_param': profile.username ?? 'User',
              'email_param': profile.email,
            },
          );
          if (kDebugMode) {
            print(
              'Created initial user profile via RPC for user: $currentUserId',
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('RPC for profile initialization failed: $e');
            print('Will attempt direct upsert instead');
          }
          // Continue with upsert attempt even if RPC fails
        }
      }

      // Get table columns to determine which fields to include
      List<String> existingColumns = [];
      try {
        final response = await supabaseClient.rpc(
          'get_table_columns',
          params: {'table_name_param': _tableName},
        );

        if (response != null) {
          existingColumns = List<String>.from(response);
          if (kDebugMode) {
            print('DEBUG: Existing columns in $_tableName: $existingColumns');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('WARNING: Could not get columns for $_tableName: $e');
          print('INFO: Will attempt upsert with minimal fields');
        }
        // Default to minimal set of columns that should exist
        existingColumns = [
          'id',
          'username',
          'birth_year',
          'gender',
          'life_situation',
          'goals',
        ];
      }

      // Proceed with upsert to update all fields
      final Map<String, dynamic> upsertData = {'id': currentUserId};

      // Only include fields that exist in the table
      final profileMap = profile.toMap();
      for (final key in profileMap.keys) {
        // Convert camelCase to snake_case for database column names
        final String dbKey = key.replaceAllMapped(
          RegExp(r'([A-Z])'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        );

        if (existingColumns.contains(dbKey)) {
          upsertData[dbKey] = profileMap[key];
        }
      }

      // Ensure all required fields are present
      if (existingColumns.contains('email') &&
          (upsertData['email'] == null || upsertData['email'] == '')) {
        upsertData['email'] = supabaseClient.auth.currentUser?.email ?? '';
      }

      if (kDebugMode) {
        print('DEBUG: Upserting profile with data: $upsertData');
      }

      await supabaseClient
          .from(_tableName)
          .upsert(upsertData, onConflict: 'id');

      if (kDebugMode) {
        print(
          'User profile synced to Supabase for user: ${profile.username ?? profile.id}',
        );
      }
      return Right(SyncStatus.syncedRemotely);
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing user profile to Supabase: $e');
      }
      // Queue the update for later synchronization
      await _queueUpdate(profile);
      return Right(SyncStatus.syncedLocally);
    }
  }

  // Helper method to queue the update using the consolidated model
  Future<void> _queueUpdate(UserProfile profile) async {
    try {
      // Use the profile's ID
      final payload = profile.toMap();

      final queuedUpdate = QueuedUpdateHiveModel(
        updateType: 'userProfile', // Specify the type
        payload: payload,
        queuedAt: DateTime.now(),
      );

      await localSyncQueueRepository.addUpdateToQueue(queuedUpdate);
    } catch (e) {
      // Log error if queuing fails
      if (kDebugMode) {
        print('Error adding user profile update to sync queue: $e');
      }
    }
  }

  /// Gets profile by ID (for getting other users' profiles)
  /// Useful for leaderboards, friends, etc.
  @override
  Future<Either<Failure, UserProfile?>> getProfileById(String userId) async {
    try {
      // Only attempt to get by ID when online
      if (await networkInfo.isConnected) {
        final response =
            await supabaseClient
                .from(_tableName)
                .select()
                .eq('id', userId)
                .maybeSingle();

        if (response == null) return const Right(null);

        return Right(UserProfile.fromMap(response));
      } else {
        return Left(ServerFailure('Cannot fetch other profiles while offline'));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile by ID: $e');
      }
      return Left(ServerFailure('Failed to retrieve profile: $e'));
    }
  }

  /// Checks if a profile exists in Supabase
  @override
  Future<Either<Failure, bool>> profileExists(String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final response =
            await supabaseClient
                .from(_tableName)
                .select('id')
                .eq('id', userId)
                .maybeSingle();

        return Right(response != null);
      } else {
        return Left(
          ServerFailure('Cannot check profile existence while offline'),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if profile exists: $e');
      }
      return Left(ServerFailure('Failed to check profile: $e'));
    }
  }
}
