import 'package:detoxme/core/services/weather_service.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/weather_data.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

/// Repository for dashboard data
class DashboardRepository {
  /// Weather service for fetching weather data
  final WeatherService _weatherService;
  
  /// Supabase client for accessing user profile data
  final SupabaseClient _supabaseClient;

  /// User preferences key for detox points
  static const String _detoxPointsKey = 'detox_points';

  /// User preferences key for detox max points
  static const String _detoxMaxPointsKey = 'detox_max_points';

  /// User preferences key for detox target points
  static const String _detoxTargetPointsKey = 'detox_target_points';

  /// User preferences key for user name
  static const String _userNameKey = 'user_name';

  /// User preferences key for user has family
  static const String _userHasFamilyKey = 'user_has_family';

  /// User preferences key for user has children
  static const String _userHasChildrenKey = 'user_has_children';

  /// User preferences key for user goal
  static const String _userGoalKey = 'user_goal';

  /// User preferences key for current mood
  static const String _currentMoodKey = 'current_mood';

  /// Default detox points
  static const int _defaultDetoxPoints = 0;

  /// Default detox max points
  static const int _defaultMaxPoints = 1000;

  /// Default detox target points
  static const int _defaultTargetPoints = 750;

  /// Default user name
  static const String _defaultUserName = 'User';

  /// Default user goal
  static const String _defaultUserGoal = 'Focus and productivity';

  /// Creates a [DashboardRepository]
  DashboardRepository({
    WeatherService? weatherService,
    SupabaseClient? supabaseClient,
  }) : _weatherService = weatherService ?? WeatherService(),
       _supabaseClient = supabaseClient ?? Supabase.instance.client;

  /// Get the current weather data
  Future<WeatherData> getWeatherData() async {
    return await _weatherService.getWeatherData();
  }

  /// Get weather condition from weather data
  WeatherCondition getWeatherCondition(WeatherData weatherData) {
    // Simply return the condition from the weather data
    // since it's already a domain WeatherCondition enum
    return weatherData.condition;
  }

  /// Get the current detox points
  Future<int> getDetoxPoints() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_detoxPointsKey) ?? _defaultDetoxPoints;
  }

  /// Get the max detox points
  Future<int> getMaxPoints() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_detoxMaxPointsKey) ?? _defaultMaxPoints;
  }

  /// Get the target detox points
  Future<int> getTargetPoints() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_detoxTargetPointsKey) ?? _defaultTargetPoints;
  }

  /// Update detox points
  Future<void> updateDetoxPoints(int points) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_detoxPointsKey, points);
  }

  /// Get user name - first tries to get from Supabase, falls back to SharedPreferences
  Future<String> getUserName() async {
    // Try to get username from Supabase first (if authenticated)
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    if (currentUserId != null) {
      try {
        final response =
            await _supabaseClient
                .from('user_profiles')
                .select('username')
                .eq('id', currentUserId)
                .maybeSingle();

        // If we found a valid username in Supabase, return it
        if (response != null && response['username'] != null) {
          final username = response['username'] as String;

          // Also update local SharedPreferences for caching
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(_userNameKey, username);

          return username;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error fetching username from Supabase: $e');
        }
        // Fall back to SharedPreferences on error
      }
    }

    // Fallback to SharedPreferences (for offline or error cases)
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userNameKey) ?? _defaultUserName;
  }

  /// Get user has family
  Future<bool> getUserHasFamily() async {
    // Try to get from Supabase first (if authenticated)
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    if (currentUserId != null) {
      try {
        final response =
            await _supabaseClient
                .from('user_profiles')
                .select('life_situation')
                .eq('id', currentUserId)
                .maybeSingle();

        // Check if life situation indicates family
        if (response != null && response['life_situation'] != null) {
          final lifeSituation = response['life_situation'] as String;
          final hasFamily =
              lifeSituation.contains('family') ||
              lifeSituation.contains('partner') ||
              lifeSituation.contains('married');

          // Update local cache
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_userHasFamilyKey, hasFamily);

          return hasFamily;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error fetching life situation from Supabase: $e');
        }
        // Fall back to SharedPreferences on error
      }
    }

    // Fallback to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_userHasFamilyKey) ?? false;
  }

  /// Get user has children
  Future<bool> getUserHasChildren() async {
    // Try to get from Supabase first if authenticated
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    if (currentUserId != null) {
      try {
        final response =
            await _supabaseClient
                .from('user_profiles')
                .select('life_situation')
                .eq('id', currentUserId)
                .maybeSingle();

        // Check if life situation indicates children
        if (response != null && response['life_situation'] != null) {
          final lifeSituation = response['life_situation'] as String;
          final hasChildren =
              lifeSituation.contains('children') ||
              lifeSituation.contains('kids');

          // Update local cache
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_userHasChildrenKey, hasChildren);

          return hasChildren;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error fetching life situation from Supabase: $e');
        }
        // Fall back to SharedPreferences on error
      }
    }

    // Fallback to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_userHasChildrenKey) ?? false;
  }

  /// Get user goal
  Future<String> getUserGoal() async {
    // Try to get from Supabase first if authenticated
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    if (currentUserId != null) {
      try {
        final response =
            await _supabaseClient
                .from('user_profiles')
                .select('goals')
                .eq('id', currentUserId)
                .maybeSingle();

        // If we found valid goals in Supabase, return the first one
        if (response != null && response['goals'] != null) {
          final goals = List<String>.from(response['goals']);
          if (goals.isNotEmpty) {
            // Update local cache
            final prefs = await SharedPreferences.getInstance();
            await prefs.setString(_userGoalKey, goals.first);

            return goals.first;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error fetching goals from Supabase: $e');
        }
        // Fall back to SharedPreferences on error
      }
    }

    // Fallback to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userGoalKey) ?? _defaultUserGoal;
  }

  /// Save user profile
  Future<void> saveUserProfile({
    required String userName,
    required bool hasFamily,
    required bool hasChildren,
    required String goal,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userNameKey, userName);
    await prefs.setBool(_userHasFamilyKey, hasFamily);
    await prefs.setBool(_userHasChildrenKey, hasChildren);
    await prefs.setString(_userGoalKey, goal);
    
    // Also update Supabase if authenticated
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    if (currentUserId != null) {
      try {
        // Determine life situation based on family and children flags
        String lifeSituation = "Single";
        if (hasFamily && hasChildren) {
          lifeSituation = "Family with children";
        } else if (hasFamily) {
          lifeSituation = "Family";
        }

        // Update the user profile in Supabase
        await _supabaseClient.from('user_profiles').upsert({
          'id': currentUserId,
          'username': userName,
          'life_situation': lifeSituation,
          'goals': [goal],
        });
      } catch (e) {
        if (kDebugMode) {
          print('Error updating profile in Supabase: $e');
        }
        // Continue even if Supabase update fails - data is still in SharedPreferences
      }
    }
  }

  /// Get the last saved mood
  Future<MoodType?> getLastMood() async {
    final prefs = await SharedPreferences.getInstance();
    final moodIndex = prefs.getInt(_currentMoodKey);

    // If no mood is saved, return null
    if (moodIndex == null) return null;

    // Check if the mood index is valid
    if (moodIndex >= 0 && moodIndex < MoodType.values.length) {
      return MoodType.values[moodIndex];
    }

    return null;
  }

  /// Save the current mood
  Future<void> saveMood(MoodType mood) async {
    final prefs = await SharedPreferences.getInstance();
    // Store the mood index in shared preferences
    await prefs.setInt(_currentMoodKey, mood.index);
  }

  /// Clear the current mood
  Future<void> clearMood() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentMoodKey);
  }
}
