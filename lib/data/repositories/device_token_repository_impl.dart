import 'package:detoxme/data/remote/models/device_token_model.dart';
import 'package:detoxme/domain/entities/device_token.dart';
import 'package:detoxme/domain/repositories/device_token_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of [DeviceTokenRepository] using Supabase
class DeviceTokenRepositoryImpl implements DeviceTokenRepository {
  final SupabaseClient _supabaseClient;

  // Table name in Supabase
  static const String _tableName = 'device_tokens';

  DeviceTokenRepositoryImpl({required SupabaseClient supabaseClient})
    : _supabaseClient = supabaseClient;

  @override
  Future<DeviceToken> saveToken(DeviceToken token) async {
    // Check if this FCM token already exists for this user
    final existingTokens = await _supabaseClient
        .from(_tableName)
        .select()
        .eq('user_id', token.userId)
        .eq('fcm_token', token.fcmToken);

    final tokenModel = DeviceTokenModel.fromEntity(token);

    if (existingTokens.isNotEmpty) {
      // Update existing token
      final existingId = existingTokens[0]['id'];
      await _supabaseClient
          .from(_tableName)
          .update(tokenModel.toJson())
          .eq('id', existingId);

      return token.copyWith(id: existingId);
    } else {
      // Insert new token
      await _supabaseClient.from(_tableName).insert(tokenModel.toJson());

      return token;
    }
  }

  @override
  Future<List<DeviceToken>> getTokensForUser(String userId) async {
    final tokens = await _supabaseClient
        .from(_tableName)
        .select()
        .eq('user_id', userId);

    return tokens
        .map((json) => DeviceTokenModel.fromJson(json).toEntity())
        .toList();
  }

  @override
  Future<void> deleteToken(String tokenId) async {
    await _supabaseClient.from(_tableName).delete().eq('id', tokenId);
  }

  @override
  Future<void> deleteAllUserTokens(String userId) async {
    await _supabaseClient.from(_tableName).delete().eq('user_id', userId);
  }
}
