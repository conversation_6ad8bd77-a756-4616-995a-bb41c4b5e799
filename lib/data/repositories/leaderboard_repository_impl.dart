import 'package:dartz/dartz.dart';
import 'package:detoxme/core/errors/exceptions.dart';
import 'package:detoxme/core/network/network_info.dart'; // Assuming NetworkInfo exists
import 'package:detoxme/domain/entities/leaderboard_entry.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/leaderboard_repository.dart';
import 'package:detoxme/data/remote/leaderboard_remote_data_source.dart';

class LeaderboardRepositoryImpl implements LeaderboardRepository {
  final LeaderboardRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo; // To check connectivity

  LeaderboardRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<LeaderboardEntry>>> getLeaderboard() async {
    // Check network connection first
    if (await networkInfo.isConnected) {
      try {
        final remoteData = await remoteDataSource.getLeaderboardData();
        // Map the raw data to entities and add ranks
        final leaderboardEntries =
            remoteData.asMap().entries.map((entry) {
              final index = entry.key;
              final data = entry.value;
              return LeaderboardEntry(
                rank: index + 1, // Rank based on position (1-based)
                username:
                    data['username'] as String? ??
                    'Anonymous', // Handle potential null username
                totalXp:
                    data['total_xp'] as int? ?? 0, // Handle potential null XP
              );
            }).toList();
        return Right(leaderboardEntries);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(e.message),
        ); // Map ServerException to ServerFailure
      } catch (e) {
        // Catch any other unexpected errors during mapping/processing
        return Left(
          ServerFailure('An unexpected error occurred: ${e.toString()}'),
        );
      }
    } else {
      // No network connection
      return Left(ServerFailure('No internet connection'));
    }
  }
}
