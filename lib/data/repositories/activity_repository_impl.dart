import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/entities/activity.dart';
import '../../domain/entities/activity_comment.dart';
import '../../domain/entities/mood.dart';
import '../../domain/failure/failure.dart';
import '../../domain/repositories/activity_repository.dart';
import '../local/activity_local_cache.dart';
import '../remote/activity_remote_data_source.dart';
import '../remote/activity_remote_data_source_impl.dart';
import '../remote/models/activity_model.dart';

/// Implementation of the ActivityRepository interface
class ActivityRepositoryImpl implements ActivityRepository {
  /// Remote data source for activities
  final ActivityRemoteDataSource _remoteDataSource;

  /// Local cache for activities
  final ActivityLocalCache _localCache;

  /// Supabase client
  final SupabaseClient _supabaseClient;

  /// Creates a new ActivityRepositoryImpl instance
  ActivityRepositoryImpl({
    required ActivityRemoteDataSource remoteDataSource,
    required ActivityLocalCache localCache,
    required SupabaseClient supabaseClient,
  }) : _remoteDataSource = remoteDataSource,
       _localCache = localCache,
       _supabaseClient = supabaseClient;

  /// Creates a new ActivityRepositoryImpl with default dependencies
  static Future<ActivityRepositoryImpl> create() async {
    final supabaseClient = Supabase.instance.client;
    final prefs = await SharedPreferences.getInstance();

    return ActivityRepositoryImpl(
      remoteDataSource: ActivityRemoteDataSourceImpl(
        supabaseClient: supabaseClient,
      ),
      localCache: ActivityLocalCache(prefs: prefs),
      supabaseClient: supabaseClient,
    );
  }

  @override
  Future<Either<Failure, List<Activity>>> getActivities() async {
    try {
      // Try to get activities from cache first
      final cachedActivities = await _localCache.getAllActivities();

      if (cachedActivities != null) {
        return Right(
          cachedActivities.map((model) => model.toDomain()).toList(),
        );
      }

      // If not in cache, fetch from remote
      final remoteActivities = await _remoteDataSource.getActivities();

      // Cache the fetched activities
      await _localCache.cacheAllActivities(remoteActivities);

      return Right(remoteActivities.map((model) => model.toDomain()).toList());
    } catch (e) {
      return Left(ServerFailure('Failed to get activities: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Activity>>> getFilteredActivities({
    MoodType? mood,
    String? searchQuery,
    ActivityCategory? category,
    ActivitySortOption sortOption = ActivitySortOption.newest,
    bool includePrivate = false,
    String? userId,
    bool hideDownvoted = true,
    List<String>? countryCodes,
  }) async {
    try {
      // Ensure we have valid country codes
      final effectiveCountryCodes =
          countryCodes?.isNotEmpty == true ? countryCodes! : ['en'];

      // Only use cache for queries without search term (those are more likely to be repeated)
      if (searchQuery == null || searchQuery.isEmpty) {
        final cachedActivities = await _localCache.getFilteredActivities(
          mood: mood,
          searchQuery: searchQuery,
          category: category,
          sortOption: sortOption,
          countryCodes: effectiveCountryCodes,
        );

        if (cachedActivities != null) {
          return Right(
            cachedActivities.map((model) => model.toDomain()).toList(),
          );
        }
      }

      // If not in cache or search query present, fetch from remote
      final remoteActivities = await _remoteDataSource.getFilteredActivities(
        mood: mood,
        searchQuery: searchQuery,
        category: category,
        sortOption: sortOption,
        includePrivate: includePrivate,
        userId: userId,
        hideDownvoted: hideDownvoted,
        countryCodes: effectiveCountryCodes,
      );

      // Cache the fetched activities if not a search query
      if (searchQuery == null || searchQuery.isEmpty) {
        await _localCache.cacheFilteredActivities(
          activities: remoteActivities,
          mood: mood,
          searchQuery: searchQuery,
          category: category,
          sortOption: sortOption,
          countryCodes: effectiveCountryCodes,
        );
      }

      return Right(remoteActivities.map((model) => model.toDomain()).toList());
    } catch (e) {
      return Left(ServerFailure('Failed to get filtered activities: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logMood(String userId, Mood mood) async {
    try {
      await _remoteDataSource.logMood(userId, mood);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to log mood: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> getActivityById(String activityId) async {
    try {
      // Fetch directly from remote data source
      final activities = await _remoteDataSource.getActivities();
      
      // Find the activity with the matching ID
      final activityModel = activities.firstWhere(
        (activity) => activity.id == activityId,
        orElse: () => throw Exception('Activity not found'),
      );
      
      return Right(activityModel.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to get activity by ID: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Mood>>> getMoodHistory(String userId) async {
    try {
      final moodHistory = await _remoteDataSource.getMoodHistory(userId);
      return Right(moodHistory);
    } catch (e) {
      return Left(ServerFailure('Failed to get mood history: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> createActivity({
    required String userId,
    required String title,
    required String description,
    required ActivityCategory category,
    required int duration,
    required List<MoodType> recommendedFor,
    List<String> healthBenefits = const [],
    String? imageUrl,
    String? creatorName,
    bool showCreatorName = true,
    bool isPrivate = false,
    String countryCode = 'en',
  }) async {
    try {
      final activity = await _remoteDataSource.createActivity(
        userId: userId,
        title: title,
        description: description,
        category: category,
        duration: duration,
        recommendedFor: recommendedFor,
        healthBenefits: healthBenefits,
        imageUrl: imageUrl,
        creatorName: creatorName,
        showCreatorName: showCreatorName,
        isPrivate: isPrivate,
        countryCode: countryCode,
      );

      // Invalidate caches since we added a new activity
      await _localCache.clearCache();

      return Right(activity.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to create activity: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Activity>>> getUserActivities(
    String userId,
  ) async {
    try {
      // Try to get activities from cache first
      final cachedActivities = await _localCache.getUserActivities(userId);

      if (cachedActivities != null) {
        return Right(
          cachedActivities.map((model) => model.toDomain()).toList(),
        );
      }

      // If not in cache, fetch from remote
      final remoteActivities = await _remoteDataSource.getUserActivities(
        userId,
      );

      // Cache the fetched activities
      await _localCache.cacheUserActivities(userId, remoteActivities);

      return Right(remoteActivities.map((model) => model.toDomain()).toList());
    } catch (e) {
      return Left(ServerFailure('Failed to get user activities: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> updateActivity(Activity activity) async {
    try {
      final activityModel = ActivityModel.fromDomain(activity);
      final updatedModel = await _remoteDataSource.updateActivity(
        activityModel,
      );

      // Invalidate caches for this activity
      await _localCache.invalidateActivity(activity.id);

      return Right(updatedModel.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to update activity: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteActivity(
    String activityId,
    String userId,
  ) async {
    try {
      await _remoteDataSource.deleteActivity(activityId, userId);

      // Invalidate caches since we deleted an activity
      await _localCache.clearCache();

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to delete activity: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> upvoteActivity(
    String activityId,
    String userId,
  ) async {
    try {
      final updatedActivity = await _remoteDataSource.upvoteActivity(
        activityId,
        userId,
      );

      // Invalidate caches for this activity
      await _localCache.invalidateActivity(activityId);

      return Right(updatedActivity.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to upvote activity: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> downvoteActivity(
    String activityId,
    String userId,
  ) async {
    try {
      final updatedActivity = await _remoteDataSource.downvoteActivity(
        activityId,
        userId,
      );

      // Invalidate caches for this activity
      await _localCache.invalidateActivity(activityId);

      return Right(updatedActivity.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to downvote activity: $e'));
    }
  }

  @override
  Future<Either<Failure, Activity>> removeVote(
    String activityId,
    String userId,
  ) async {
    try {
      final updatedActivity = await _remoteDataSource.removeVote(
        activityId,
        userId,
      );

      // Invalidate caches for this activity
      await _localCache.invalidateActivity(activityId);

      return Right(updatedActivity.toDomain());
    } catch (e) {
      return Left(ServerFailure('Failed to remove vote: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Activity>>> getActivitiesForMood(
    MoodType mood,
  ) async {
    // Delegate to getFilteredActivities with mood parameter
    return getFilteredActivities(mood: mood);
  }

  @override
  Future<Either<Failure, void>> setFavorite(
    String activityId,
    String userId,
    bool isFavorite,
  ) async {
    try {
      await _remoteDataSource.setFavorite(activityId, userId, isFavorite);

      // Invalidate caches since we updated favorites
      await _localCache.clearCache();

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to set favorite status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Activity>>> getFavoriteActivities(
    String userId,
  ) async {
    try {
      // Try to get activities from cache first
      // For MVP, we'll skip caching implementation and directly use remote data source

      final activityModels = await _remoteDataSource.getFavoriteActivities(
        userId,
      );

      // Convert models to domain entities and set isFavorite to true
      final activities =
          activityModels
              .map((model) => model.toDomain(isFavorite: true))
              .toList();

      return Right(activities);
    } catch (e) {
      return Left(ServerFailure('Failed to fetch favorite activities: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> setFoodFavorite(
    String foodId,
    String userId,
    bool isFavorite,
  ) async {
    try {
      await _remoteDataSource.setFoodFavorite(foodId, userId, isFavorite);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to set food favorite status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<dynamic>>> getFavoriteFoods(String userId) async {
    try {
      final foods = await _remoteDataSource.getFavoriteFoods(userId);
      return Right(foods);
    } catch (e) {
      return Left(ServerFailure('Failed to get favorite foods: $e'));
    }
  }

  @override
  Future<Either<Failure, ActivityComment>> addComment({
    required String activityId,
    required String userId,
    required String userName,
    required String text,
  }) async {
    try {
      // Get user profile info for avatar URL
      String? userAvatarUrl;
      try {
        final userProfile =
            await _supabaseClient
                .from('user_profiles')
                .select('avatar_url')
                .eq('id', userId)
                .maybeSingle();
        userAvatarUrl = userProfile?['avatar_url'];
      } catch (e) {
        // If we can't get the avatar URL, continue without it
      }

      final comment = await _remoteDataSource.addComment(
        activityId: activityId,
        userId: userId,
        userName: userName,
        text: text,
        userAvatarUrl: userAvatarUrl,
      );

      return Right(comment);
    } catch (e) {
      return Left(ServerFailure('Failed to add comment: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ActivityComment>>> getComments(
    String activityId,
  ) async {
    try {
      final currentUserId = _supabaseClient.auth.currentUser?.id;
      final comments = await _remoteDataSource.getComments(
        activityId,
        currentUserId,
      );
      return Right(comments);
    } catch (e) {
      return Left(ServerFailure('Failed to get comments: $e'));
    }
  }

  @override
  Stream<List<ActivityComment>> getCommentsStream(String activityId) {
    final currentUserId = _supabaseClient.auth.currentUser?.id;
    return _remoteDataSource.getCommentsStream(activityId, currentUserId);
  }

  @override
  Future<Either<Failure, void>> deleteComment({
    required String commentId,
    required String userId,
  }) async {
    try {
      await _remoteDataSource.deleteComment(
        commentId: commentId,
        userId: userId,
      );

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to delete comment: $e'));
    }
  }

  @override
  Future<Either<Failure, ActivityComment>> editComment({
    required String commentId,
    required String userId,
    required String newText,
  }) async {
    try {
      final updatedComment = await _remoteDataSource.editComment(
        commentId: commentId,
        userId: userId,
        newText: newText,
      );

      return Right(updatedComment);
    } catch (e) {
      return Left(ServerFailure('Failed to edit comment: $e'));
    }
  }

  @override
  Future<Either<Failure, ActivityComment>> toggleCommentLike({
    required String commentId,
    required String userId,
  }) async {
    try {
      final likedComment = await _remoteDataSource.toggleCommentLike(
        commentId: commentId,
        userId: userId,
      );

      return Right(likedComment);
    } catch (e) {
      return Left(ServerFailure('Failed to toggle comment like: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> reportComment({
    required String commentId,
    required String userId,
    required String reason,
  }) async {
    try {
      await _remoteDataSource.reportComment(
        commentId: commentId,
        userId: userId,
        reason: reason,
      );

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to report comment: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateActivityStatus({
    required String activityId,
    required String userId,
    required bool completed,
    required Duration timeSpent,
  }) async {
    if (kDebugMode) {
      print('ActivityRepository: Updating activity status');
      print('ActivityRepository: Activity ID: $activityId');
      print('ActivityRepository: User ID: $userId');
      print('ActivityRepository: Completed: $completed');
      print(
        'ActivityRepository: Time spent: ${timeSpent.inMinutes} minutes, ${timeSpent.inSeconds % 60} seconds',
      );
    }

    try {
      // Record the activity completion in Supabase
      final response =
          await _supabaseClient.from('activity_completions').insert({
        'activity_id': activityId,
        'user_id': userId,
        'completed': completed,
        'time_spent_minutes': timeSpent.inMinutes,
        'completed_at': DateTime.now().toIso8601String(),
          }).select();

      if (kDebugMode) {
        print('ActivityRepository: Activity completion recorded successfully');
        print('ActivityRepository: Response: $response');
      }

      // If we're tracking stats, update them as well
      try {
        // First try to get current stats
        final currentStats =
            await _supabaseClient
                .from('user_activity_stats')
                .select()
                .eq('user_id', userId)
                .eq('activity_id', activityId)
                .maybeSingle();

        final int currentCompletions = currentStats?['total_completions'] ?? 0;
        final int currentTimeSpent =
            currentStats?['total_time_spent_minutes'] ?? 0;

        if (kDebugMode) {
          print(
            'ActivityRepository: Current stats - Completions: $currentCompletions, Time spent: $currentTimeSpent minutes',
          );
        }

        // Then update with new values
        final statsResponse =
            await _supabaseClient.from('user_activity_stats').upsert({
          'user_id': userId,
          'activity_id': activityId,
          'last_completed_at': DateTime.now().toIso8601String(),
          'total_completions': currentCompletions + 1,
          'total_time_spent_minutes': currentTimeSpent + timeSpent.inMinutes,
            }, onConflict: 'user_id, activity_id').select();

        if (kDebugMode) {
          print('ActivityRepository: Activity stats updated successfully');
          print('ActivityRepository: Stats response: $statsResponse');
        }
      } catch (e) {
        // If stats tracking fails, we still want to record the completion
        // so just log the error and continue
        if (kDebugMode) {
          print('ActivityRepository: Failed to update activity stats: $e');
        }
      }

      return const Right(null);
    } catch (e) {
      if (kDebugMode) {
        print('ActivityRepository: Error updating activity status: $e');
      }
      return Left(ServerFailure('Failed to update activity status: $e'));
    }
  }
}
