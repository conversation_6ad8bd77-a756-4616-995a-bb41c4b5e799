import 'package:dartz/dartz.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/data/local/local_profile_data_source.dart';
import 'package:detoxme/data/local/local_reward_data_source.dart';
import 'package:detoxme/data/local/models/queued_update_hive_model.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/reward_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:detoxme/domain/repositories/local_sync_queue_repository.dart';

class RewardRepositoryImpl implements RewardRepository {
  final LocalRewardDataSource localDataSource;
  final LocalProfileDataSource localProfileDataSource;
  final LocalSyncQueueRepository localSyncQueueRepository;
  final SupabaseClient supabaseClient;
  final NetworkInfo networkInfo;

  RewardRepositoryImpl({
    required this.localDataSource,
    required this.localProfileDataSource,
    required this.localSyncQueueRepository,
    required this.supabaseClient,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, PlayerStats>> getPlayerStats() async {
    try {
      // Get current user info first
      final currentUserId = supabaseClient.auth.currentUser?.id;
      if (currentUserId == null) {
        // Should not happen if user is authenticated to reach this point
        return const Left(ServerFailure('User not authenticated.'));
      }

      String currentUsername = 'Player'; // Default username
      try {
        final profile = await localProfileDataSource.getProfile();
        // Check for null profile AND non-null/non-empty username
        if (profile != null && (profile.username ?? '').isNotEmpty) {
          // We know username is not null here, so use !
          currentUsername = profile.username!;
        }
      } catch (e) {
        // Log error fetching profile, but proceed with default username
        if (kDebugMode) {
          print('Error fetching profile for username: $e');
        }
      }

      // Try fetching existing stats
      final localStats = await localDataSource.getPlayerStats();

      if (localStats == null) {
        // No stats exist, create initial stats with fetched user info
        if (kDebugMode) {
          print(
            'No local player stats found, creating initial for user: $currentUserId',
          );
        }
        final initialStats = PlayerStats(
          userId: currentUserId,
          username: currentUsername,
          // Default level, xp, etc.
        );
        // Optionally save these initial stats back immediately?
        // await localDataSource.savePlayerStats(initialStats); // Consider this
        return Right(initialStats);
      } else {
        // Stats exist, return them (assuming they have correct userId/username)
        // TODO: Potentially update userId/username on loadedStats if they mismatch?
        return Right(localStats);
      }
    } on Failure catch (f) {
      // Catch specific Failures first
      return Left(f);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting player stats from repository: $e');
      }
      return const Left(CacheFailure('Failed to retrieve player stats.'));
    }
  }

  @override
  Future<Either<Failure, SyncStatus>> savePlayerStats(PlayerStats stats) async {
    // 1. Save locally first
    try {
      await localDataSource.savePlayerStats(stats);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving player stats locally: $e');
      }
      return const Left(CacheFailure('Failed to save player stats locally.'));
    }

    // 2. Attempt to sync to Supabase if online
    if (await networkInfo.isConnected) {
      try {
        // Prepare data for Supabase directly here for immediate sync attempt
        final upsertData = {
          'user_id': stats.userId,
          'username': stats.username,
          'total_xp': stats.currentXp,
          'updated_at': DateTime.now().toIso8601String(),
        };
        await supabaseClient.from('player_stats').upsert(upsertData);
        if (kDebugMode) {
          print('Player stats synced to Supabase for user: ${stats.username}');
        }
        return const Right(SyncStatus.syncedRemotely);
      } on Exception catch (e) {
        if (kDebugMode) {
          print(
            'Error syncing player stats to Supabase for user ${stats.username}: $e',
          );
        }
        // Remote sync failed, queue the update using the generic method
        await _queueUpdate(stats);
        return const Right(SyncStatus.syncedLocally);
      }
    } else {
      // Offline: Local save succeeded, queue the update
      if (kDebugMode) {
        print(
          'Network offline. Player stats saved locally only for user: ${stats.username}.',
        );
      }
      await _queueUpdate(stats);
      return const Right(SyncStatus.syncedLocally);
    }
  }

  // Helper method to queue the update using the generic model
  Future<void> _queueUpdate(PlayerStats stats) async {
    try {
      final payload = {
        'userId': stats.userId,
        'username': stats.username,
        'currentLevel': stats.currentLevel,
        'currentXp': stats.currentXp,
        'earnedBadgeIds': List.from(stats.earnedBadgeIds),
        'earnedCardIds': List.from(stats.earnedCardIds),
      };

      final queuedUpdate = QueuedUpdateHiveModel(
        updateType: 'playerStats',
        payload: payload,
        queuedAt: DateTime.now(),
      );

      await localSyncQueueRepository.addUpdateToQueue(queuedUpdate);
    } catch (e) {
      if (kDebugMode) {
        print('Error adding player stats update to sync queue: $e');
      }
    }
  }

  @override
  Future<List<Reward>> getRedeemedRewards() async {
    try {
      // Get current user ID
      final currentUserId = supabaseClient.auth.currentUser?.id;
      if (currentUserId == null) {
        if (kDebugMode) {
          print('Cannot get redeemed rewards: User not authenticated.');
        }
        return [];
      }

      // First try to get local rewards
      List<Reward> redeemedRewards = [];
      try {
        final localRewards = await localDataSource.getRedeemedRewards();
        if (localRewards != null && localRewards.isNotEmpty) {
          redeemedRewards.addAll(localRewards);
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error fetching local redeemed rewards: $e');
        }
        // Continue with empty list if local fetch fails
      }

      // Try fetching from Supabase if online
      if (await networkInfo.isConnected) {
        try {
          final data = await supabaseClient
              .from('redeemed_rewards')
              .select()
              .eq('user_id', currentUserId)
              .order('redeemed_at', ascending: false);

          if (data.isNotEmpty) {
            // Convert data to Reward entities
            final remoteRewards =
                (data as List).map<Reward>((item) {
                  // Parse the data into a Reward object
                  return Reward(
                    id: item['reward_id'] as String,
                    userId: item['user_id'] as String,
                    title: item['title'] as String,
                    description: item['description'] as String?,
                    pointsEarned: item['points_cost'] as int,
                    rewardType:
                        item['reward_type'] as String? ??
                        RewardType.couponRedemption,
                    sourceType: item['source_type'] as String?,
                    sourceId: item['source_id'] as String?,
                    createdAt:
                        item['redeemed_at'] != null
                            ? DateTime.parse(item['redeemed_at'] as String)
                            : DateTime.now(),
                    metadata:
                        item['metadata'] != null
                            ? item['metadata'] as Map<String, dynamic>
                            : {
                              'provider': item['provider'] as String?,
                              'provider_logo_url':
                                  item['provider_logo_url'] as String?,
                              'redemption_code':
                                  item['redemption_code'] as String?,
                              'category': item['category'] as String?,
                            },
                  );
                }).toList();

            // If we have both local and remote rewards, merge them
            // Use a map to deduplicate by reward ID
            final Map<String, Reward> mergedRewards = {};

            // Add local rewards first
            for (final reward in redeemedRewards) {
              mergedRewards[reward.id] = reward;
            }

            // Add remote rewards, overwriting local ones if they exist
            for (final reward in remoteRewards) {
              mergedRewards[reward.id] = reward;
            }

            // Convert map values back to a list and sort by redemption date
            redeemedRewards =
                mergedRewards.values.toList()..sort(
                  (a, b) => (b.createdAt).compareTo(a.createdAt,
                  ),
                );

            // Save merged rewards to local storage (overwrite previous)
            try {
              await localDataSource.saveRedeemedRewards(redeemedRewards);
            } catch (e) {
              if (kDebugMode) {
                print('Error saving merged rewards locally: $e');
              }
              // Continue even if local save fails
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error fetching redeemed rewards from Supabase: $e');
          }
          // Continue with local rewards if Supabase fetch fails
        }
      }

      return redeemedRewards;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getRedeemedRewards: $e');
      }
      return []; // Return empty list on error
    }
  }
}
