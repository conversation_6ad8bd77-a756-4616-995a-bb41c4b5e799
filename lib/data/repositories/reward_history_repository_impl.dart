import 'package:dartz/dartz.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/data/remote/reward_remote_data_source.dart';
import 'package:detoxme/domain/entities/reward.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/reward_history_repository.dart';

/// Implementation of RewardHistoryRepository
class RewardHistoryRepositoryImpl implements RewardHistoryRepository {
  /// Remote data source for rewards
  final RewardRemoteDataSource remoteDataSource;

  /// Network info service
  final NetworkInfo networkInfo;

  /// Constructor
  RewardHistoryRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Reward>>> getRewardHistory() async {
    if (await networkInfo.isConnected) {
      try {
        final rewards = await remoteDataSource.getRewardHistory();
        return Right(rewards);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(
        NetworkFailure(
          'No internet connection. Please try again when you\'re online.',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Map<String, RewardSummary>>> getRewardSummary() async {
    if (await networkInfo.isConnected) {
      try {
        final summaryData = await remoteDataSource.getRewardSummary();

        // Convert the raw data to RewardSummary objects
        final Map<String, RewardSummary> summaries = {};

        for (final item in summaryData['data'] as List<dynamic>) {
          final Map<String, dynamic> summary = item as Map<String, dynamic>;
          final rewardType = summary['reward_type'] as String;

          summaries[rewardType] = RewardSummary(
            rewardType: rewardType,
            count: (summary['count'] as num).toInt(),
            totalPoints: (summary['total_points'] as num).toInt(),
          );
        }

        return Right(summaries);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(
        NetworkFailure(
          'No internet connection. Please try again when you\'re online.',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Reward>> addCustomReward({
    required String title,
    required String description,
    required int pointsEarned,
    required String rewardType,
    Map<String, dynamic>? metadata,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final reward = await remoteDataSource.addCustomReward(
          title: title,
          description: description,
          pointsEarned: pointsEarned,
          rewardType: rewardType,
          metadata: metadata,
        );
        return Right(reward);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(
        NetworkFailure(
          'No internet connection. Please try again when you\'re online.',
        ),
      );
    }
  }
}
