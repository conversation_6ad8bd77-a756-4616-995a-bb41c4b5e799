import 'package:dartz/dartz.dart';
import 'package:detoxme/core/network/network_info.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/data/local/local_food_mood_data_source.dart';
import 'package:detoxme/data/remote/remote_food_mood_data_source.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/food_mood_repository.dart';
import 'package:flutter/foundation.dart'; // Import for kDebugMode
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of [FoodMoodRepository]
class FoodMoodRepositoryImpl implements FoodMoodRepository {
  final RemoteFoodMoodDataSource _remoteDataSource;
  final LocalFoodMoodDataSource _localDataSource;

  /// Network info
  final NetworkInfo networkInfo;

  /// Supabase client for remote operations
  final SupabaseClient supabaseClient;

  /// App locale provider for language settings
  final AppLocaleProvider localeProvider;

  /// Creates a new instance of [FoodMoodRepositoryImpl]
  FoodMoodRepositoryImpl({
    required RemoteFoodMoodDataSource remoteDataSource,
    required LocalFoodMoodDataSource localDataSource,
    required this.networkInfo,
    required this.supabaseClient,
    required this.localeProvider,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource;

  @override
  Future<Either<Failure, List<FoodRecommendation>>> getAllFoodRecommendations({
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
  }) async {
    try {
      final recommendations =
          await _remoteDataSource.getAllFoodRecommendations();
      await _localDataSource.updateFoodRecommendations(recommendations);
      return Right(recommendations);
    } catch (e) {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<FoodRecommendation>>>
  getFoodRecommendationsForMood(
    MoodType mood, {
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
    String? countryCode,
  }) async {
    // 1. Check network connection first
    if (!await networkInfo.isConnected) {
      if (kDebugMode) {
        print(
          '[FoodMoodRepo] No network connection detected for getFoodRecommendationsForMood.',
        );
      }
      // Optionally, try loading from cache here if desired for offline mode
      // final cachedRecommendations = await _localDataSource.getFoodRecommendationsForMood(mood);
      // if (cachedRecommendations.isNotEmpty) {
      //   return Right(cachedRecommendations);
      // }
      return Left(NetworkFailure());
    }

    // 2. Try fetching from remote data source
    try {
      final recommendations = await _remoteDataSource.getFoodRecommendationsForMood(
        mood,
        // Pass sortOption and excludeTooManyDownvotes if your remote source supports them
        // sortOption: sortOption,
        // excludeTooManyDownvotes: excludeTooManyDownvotes,
      );

      // 3. Try updating local cache (only if remote fetch succeeds)
      try {
        await _localDataSource.updateFoodRecommendations(recommendations);
      } catch (cacheError, stackTrace) {
        if (kDebugMode) {
          print(
            '[FoodMoodRepo] Error updating local cache for mood $mood: $cacheError\n$stackTrace',
          );
        }
        // Decide if failing to cache should prevent returning data.
        // Here, we still return the fetched data but log the cache error.
        // return Left(CacheFailure('Failed to update local cache: $cacheError'));
      }

      return Right(recommendations);
    } on PostgrestException catch (e, stackTrace) {
      // Catch specific Supabase/API errors
      if (kDebugMode) {
        print(
          '[FoodMoodRepo] Supabase error fetching for mood $mood: ${e.message}\n'
          'Code: ${e.code}\n'
          'Details: ${e.details}\n'
          'Hint: ${e.hint}\n'
          '$stackTrace',
        );
      }
      return Left(ServerFailure('API Error: ${e.message} (Code: ${e.code})'));
    } catch (e, stackTrace) {
      // Catch any other unexpected errors during remote fetch
      if (kDebugMode) {
        print(
          '[FoodMoodRepo] Unexpected error fetching for mood $mood: $e\n$stackTrace',
        );
      }
      return Left(ServerFailure('An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, FoodRecommendation>> getFoodRecommendationById(
    String id,
  ) async {
    try {
      final recommendation = await _remoteDataSource.getFoodRecommendationById(
        id,
      );
      if (recommendation == null) {
        return Left(NotFoundFailure());
      }
      return Right(recommendation);
    } catch (e) {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, VoteType?>> submitVote(
    String recommendationId,
    VoteType vote,
  ) async {
    try {
      if (kDebugMode) {
        print('Repository submitVote: id=$recommendationId, vote=$vote');
      }

      // If we're toggling off a vote (removing it), just call removeVote
      final currentRecommendation = await _remoteDataSource
          .getFoodRecommendationById(recommendationId);
      if (currentRecommendation?.userVote == vote) {
        if (kDebugMode) {
          print('Removing vote since user clicked the same vote type again');
        }
        await _remoteDataSource.removeVote(recommendationId);
        return const Right(null);
      } else {
        // Otherwise submit the new vote (which will remove any existing vote first)
        await _remoteDataSource.submitVote(recommendationId, vote);
        return Right(vote);
      }
    } on PostgrestException catch (e) {
      if (kDebugMode) {
        print('PostgrestException in submitVote: ${e.message}');
      }
      return Left(ServerFailure('API Error: ${e.message}'));
    } catch (e) {
      if (kDebugMode) {
        print('Exception in submitVote: $e');
      }
      return Left(NetworkFailure());
    }
  }

  @override
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  }) async {
    try {
      final recommendations =
          await _remoteDataSource.getAllFoodRecommendations();

      // Apply filters
      var filtered = recommendations;

      // Apply mood filter
      if (mood != null) {
        filtered =
            filtered.where((r) => r.moodTypes?.contains(mood) == true).toList();
      }

      // Apply country code filter
      if (countryCode != null) {
        filtered =
            filtered
                .where(
                  (r) =>
                      r
                          .countryCode
                          .isEmpty || // Items with no country code are shown to all users
                      r.countryCode ==
                          countryCode, // Match specific country code
                )
                .toList();

        if (kDebugMode) {
          print(
            'Filtered to ${filtered.length} items for country code: $countryCode',
          );
        }
      }

      // Apply pagination
      if (offset != null && limit != null) {
        final start = offset;
        final end = offset + limit;
        if (start < filtered.length) {
          filtered = filtered.sublist(
            start,
            end < filtered.length ? end : filtered.length,
          );
        } else {
          filtered = [];
        }
      }

      return filtered;
    } catch (e) {
      // Try to get cached recommendations if remote fails
      if (kDebugMode) {
        print('Error fetching remote data, falling back to cache: $e');
      }

      var cachedRecommendations =
          await _localDataSource.getAllFoodRecommendations();

      // Apply country code filter to cached data
      if (countryCode != null && cachedRecommendations.isNotEmpty) {
        cachedRecommendations =
            cachedRecommendations
                .where(
                  (r) => r.countryCode.isEmpty || r.countryCode == countryCode,
                )
                .toList();
      }

      return cachedRecommendations;
    }
  }
}
