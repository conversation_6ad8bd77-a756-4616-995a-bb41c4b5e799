import 'package:dartz/dartz.dart';
import 'package:detoxme/data/remote/notification_remote_data_source.dart';
import 'package:detoxme/domain/entities/user_notification.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/notification_repository.dart';

/// Implementation of [NotificationRepository]
class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationRemoteDataSource _remoteDataSource;

  /// Creates a new [NotificationRepositoryImpl]
  NotificationRepositoryImpl({
    required NotificationRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  @override
  Stream<List<UserNotification>> getUserNotificationsStream(String userId) {
    return _remoteDataSource.getUserNotificationsStream(userId);
  }

  @override
  Future<Either<Failure, List<UserNotification>>> getUserNotifications(
    String userId,
  ) async {
    try {
      final notifications = await _remoteDataSource.getUserNotifications(
        userId,
      );
      return Right(notifications);
    } catch (e) {
      return Left(ServerFailure('Failed to get notifications: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> markNotificationAsRead(
    String notificationId,
    String userId,
  ) async {
    try {
      await _remoteDataSource.markNotificationAsRead(notificationId, userId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to mark notification as read: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> markAllNotificationsAsRead(
    String userId,
  ) async {
    try {
      await _remoteDataSource.markAllNotificationsAsRead(userId);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure('Failed to mark all notifications as read: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> deleteNotification(
    String notificationId,
    String userId,
  ) async {
    try {
      await _remoteDataSource.deleteNotification(notificationId, userId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to delete notification: $e'));
    }
  }

  @override
  Future<Either<Failure, UserNotification>> createNotification({
    required String userId,
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? actionData,
  }) async {
    try {
      final notification = await _remoteDataSource.createNotification(
        userId: userId,
        title: title,
        message: message,
        type: type,
        actionData: actionData,
      );
      return Right(notification);
    } catch (e) {
      return Left(ServerFailure('Failed to create notification: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getUnreadNotificationCount(String userId) async {
    try {
      final count = await _remoteDataSource.getUnreadNotificationCount(userId);
      return Right(count);
    } catch (e) {
      return Left(ServerFailure('Failed to get unread notification count: $e'));
    }
  }

  @override
  Stream<int> getUnreadNotificationCountStream(String userId) {
    return _remoteDataSource.getUnreadNotificationCountStream(userId);
  }

  @override
  Future<Either<Failure, UserNotification>> createCommentLikeNotification({
    required String commentAuthorId,
    required String likerUserName,
    required String commentText,
    required String activityId,
    required String commentId,
  }) async {
    try {
      // Truncate comment text if too long
      final truncatedComment =
          commentText.length > 50
              ? '${commentText.substring(0, 50)}...'
              : commentText;

      final notification = await _remoteDataSource.createNotification(
        userId: commentAuthorId,
        title: 'Someone liked your comment!',
        message: '$likerUserName liked your comment: "$truncatedComment"',
        type: NotificationType.commentLike,
        actionData: {
          'activity_id': activityId,
          'comment_id': commentId,
          'liker_name': likerUserName,
        },
      );
      return Right(notification);
    } catch (e) {
      return Left(
        ServerFailure('Failed to create comment like notification: $e'),
      );
    }
  }
}
