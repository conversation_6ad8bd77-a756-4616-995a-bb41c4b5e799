import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/mood_quote.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/mood_quote_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Implementation of the mood quote repository
class MoodQuoteRepositoryImpl implements MoodQuoteRepository {
  /// The Supabase client
  final SupabaseClient _supabaseClient;

  /// Creates a new mood quote repository
  MoodQuoteRepositoryImpl({required SupabaseClient supabaseClient}) 
      : _supabaseClient = supabaseClient;
  
  /// Creates a new repository with the default Supabase client
  static Future<MoodQuoteRepositoryImpl> create() async {
    final supabaseClient = Supabase.instance.client;
    return MoodQuoteRepositoryImpl(supabaseClient: supabaseClient);
  }

  @override
  Future<Either<Failure, List<MoodQuote>>> getQuotesByMood({
    required String moodType,
    required String language,
    int? limit,
  }) async {
    try {
      var query = _supabaseClient
          .from('mood_quotes')
          .select()
          .eq('mood_type', moodType)
          .eq('language', language)
          .eq('is_active', true)
          .order('created_at', ascending: false);
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final response = await query;
      
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(response);
      
      if (data.isEmpty) {
        return Left(NotFoundFailure(
          'No quotes found for mood: $moodType in language: $language',
        ));
      }
      
      final quotes = data.map((json) => MoodQuote.fromMap(json)).toList();
      
      return Right(quotes);
    } catch (e) {
      debugPrint('Error getting quotes by mood: $e');
      return Left(ServerFailure(
        'Failed to get quotes: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, MoodQuote>> getRandomQuoteByMood({
    required String moodType,
    required String language,
  }) async {
    try {
      // Get a random quote using PostgreSQL's RANDOM() function
      final response = await _supabaseClient
          .from('mood_quotes')
          .select()
          .eq('mood_type', moodType)
          .eq('language', language)
          .eq('is_active', true)
          .limit(1)
          .order('RANDOM()');
      
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(response);
      
      if (data.isEmpty) {
        return Left(NotFoundFailure(
          'No quotes found for mood: $moodType in language: $language',
        ));
      }
      
      final quote = MoodQuote.fromMap(data.first);
      
      return Right(quote);
    } catch (e) {
      debugPrint('Error getting random quote by mood: $e');
      return Left(ServerFailure(
        'Failed to get random quote: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableMoodTypes() async {
    try {
      final response = await _supabaseClient
          .from('mood_quotes')
          .select('mood_type')
          .eq('is_active', true);
      
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(response);
      
      final moodTypes = data
          .map((item) => item['mood_type'] as String)
          .toSet()
          .toList();
      
      return Right(moodTypes);
    } catch (e) {
      debugPrint('Error getting available mood types: $e');
      return Left(ServerFailure(
        'Failed to get mood types: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, MoodQuote>> createMoodQuote(MoodQuote quote) async {
    try {
      final response = await _supabaseClient
          .from('mood_quotes')
          .insert(quote.toMap())
          .select()
          .single();
      
      final createdQuote = MoodQuote.fromMap(response);
      
      return Right(createdQuote);
    } catch (e) {
      debugPrint('Error creating mood quote: $e');
      return Left(ServerFailure(
        'Failed to create quote: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, MoodQuote>> updateMoodQuote(MoodQuote quote) async {
    try {
      final response = await _supabaseClient
          .from('mood_quotes')
          .update(quote.toMap())
          .eq('id', quote.id)
          .select()
          .single();
      
      final updatedQuote = MoodQuote.fromMap(response);
      
      return Right(updatedQuote);
    } catch (e) {
      debugPrint('Error updating mood quote: $e');
      return Left(ServerFailure(
        'Failed to update quote: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMoodQuote(String id) async {
    try {
      await _supabaseClient
          .from('mood_quotes')
          .delete()
          .eq('id', id);
      
      return const Right(null);
    } catch (e) {
      debugPrint('Error deleting mood quote: $e');
      return Left(ServerFailure(
        'Failed to delete quote: ${e.toString()}',
      ));
    }
  }
}