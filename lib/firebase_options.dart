// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAs4iWxY9N7XLuHVyXAFTSO8sh6TCBgGaY',
    appId: '1:383615206409:web:9d27191a7502ccc8b424d8',
    messagingSenderId: '383615206409',
    projectId: 'detoxme-3741a',
    authDomain: 'detoxme-3741a.firebaseapp.com',
    storageBucket: 'detoxme-3741a.firebasestorage.app',
    measurementId: 'G-2J4RDMM0XP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDouxJeN3R3TAnzykZTDCYBtKlSR8oaN84',
    appId: '1:383615206409:android:0027ea1cf6511225b424d8',
    messagingSenderId: '383615206409',
    projectId: 'detoxme-3741a',
    storageBucket: 'detoxme-3741a.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBrdUGCKWY9Jsd5XIJV4WYTXfKwx3bqNb4',
    appId: '1:383615206409:ios:09058d832788670bb424d8',
    messagingSenderId: '383615206409',
    projectId: 'detoxme-3741a',
    storageBucket: 'detoxme-3741a.firebasestorage.app',
    iosClientId: '383615206409-1sjen0b82robhckcdd22ngdqt5tcnnda.apps.googleusercontent.com',
    iosBundleId: 'com.innovatio.detoxme',
  );
}
