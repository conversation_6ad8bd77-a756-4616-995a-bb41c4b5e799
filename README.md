# DetoxMe App

A mobile app helping users reduce their digital device usage and build healthier technology habits.

## Features

### Digital Detox Activities
- Browse activities designed to reduce screen time
- Filter activities by mood, category, and duration
- Track completed activities

### Reward System
- Earn points by completing digital detox activities
- Redeem points for real-world rewards and coupons
- View reward history and track achievements

### Mood Tracking
- Record daily mood using the MoodOMeter
- Get personalized activity recommendations based on mood
- Track mood patterns over time

### Social Features
- Participate in community challenges
- Share progress with friends
- Leaderboards for motivation

## Reward History Feature

The reward history feature allows users to:

- View a chronological history of all rewards earned
- See detailed statistics of rewards by type (activities, coupons, bonuses, achievements)
- Track total points earned over time
- Filter reward history by type and date

### Technical Implementation

The reward history feature follows clean architecture principles:

- **Domain Layer**: Contains the Reward entity and RewardHistoryRepository interface
- **Data Layer**: Implements the repository with remote data sources
- **Presentation Layer**: Uses BLoC/Cubit pattern for state management
- **UI Layer**: Displays rewards in a chronological list grouped by date

### Database Schema

The rewards table in Supabase stores:
- User ID
- Reward type (activity_completion, coupon_redemption, bonus, achievement)
- Points earned
- Timestamp
- Source information (linked activity or coupon)
- Additional metadata

## Getting Started

This project uses Flutter and Supabase:

1. Clone the repository
2. Set up environment variables
3. Run `flutter pub get` to install dependencies
4. Run the app with `flutter run`

## Tech Stack

- Flutter for cross-platform mobile development
- Supabase for backend (database, auth, and functions)
- BLoC/Cubit for state management
- GoRouter for navigation
- Hive for local data persistence
