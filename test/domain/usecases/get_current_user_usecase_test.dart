import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/user_profile.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/user_repository.dart';
import 'package:detoxme/domain/usecases/get_current_user_usecase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockUserRepository extends Mo<PERSON> implements UserRepository {}

void main() {
  late GetCurrentUserUseCase useCase;
  late MockUserRepository mockRepository;

  setUp(() {
    mockRepository = MockUserRepository();
    useCase = GetCurrentUserUseCase(userRepository: mockRepository);
  });

  final testUserProfile = UserProfile(
    id: 'test-id',
    email: '<EMAIL>',
    username: 'testuser',
    birthYear: 2000,
    gender: 'Male',
    goals: ['Reduce screen time', 'Be more active'],
    lifeSituation: 'Student',
    avatarUrl: 'https://example.com/avatar.jpg',
    bio: 'Test bio',
  );

  test('should get current user profile from repository', () async {
    // Arrange
    when(
      () => mockRepository.getProfile(),
    ).thenAnswer((_) async => Right(testUserProfile));

    // Act
    final result = await useCase();

    // Assert
    expect(result, Right(testUserProfile));
    verify(() => mockRepository.getProfile()).called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return a failure when repository fails', () async {
    // Arrange
    final failure = ServerFailure('Failed to get user profile');
    when(
      () => mockRepository.getProfile(),
    ).thenAnswer((_) async => Left(failure));

    // Act
    final result = await useCase();

    // Assert
    expect(result, Left(failure));
    verify(() => mockRepository.getProfile()).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}
