import 'package:bloc_test/bloc_test.dart';
import 'package:detoxme/domain/entities/leaderboard_entry.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/presentation/bloc/leaderboard/leaderboard_cubit.dart';
import 'package:detoxme/presentation/bloc/leaderboard/leaderboard_state.dart';
import 'package:detoxme/presentation/ui/screens/leaderboard/leaderboard_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Mock Cubit
class MockLeaderboardCubit extends MockCubit<LeaderboardState>
    implements LeaderboardCubit {}

void main() {
  late MockLeaderboardCubit mockLeaderboardCubit;

  // Sample data
  final leaderboardList = [
    const LeaderboardEntry(rank: 1, username: 'Player1', totalXp: 1000),
    const LeaderboardEntry(rank: 2, username: 'Player2', totalXp: 950),
  ];

  // Helper to pump the widget with necessary providers
  Future<void> pumpWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: AppLocalizations.supportedLocales,
        home: BlocProvider<LeaderboardCubit>.value(
          value: mockLeaderboardCubit,
          child: const LeaderboardScreen(),
        ),
      ),
    );
  }

  setUp(() {
    mockLeaderboardCubit = MockLeaderboardCubit();
  });

  group('LeaderboardScreen', () {
    testWidgets('renders CircularProgressIndicator when state is Loading', (
      tester,
    ) async {
      when(() => mockLeaderboardCubit.state).thenReturn(LeaderboardLoading());

      await pumpWidget(tester);

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('renders error message when state is Error', (tester) async {
      final failure = ServerFailure('Network Error');
      when(
        () => mockLeaderboardCubit.state,
      ).thenReturn(LeaderboardError(failure));

      await pumpWidget(tester);
      await tester.pumpAndSettle(); // Allow time for localization

      expect(
        find.textContaining('Failed to load leaderboard: Network Error'),
        findsOneWidget,
      );
    });

    testWidgets('renders empty message when state is Loaded with empty list', (
      tester,
    ) async {
      when(
        () => mockLeaderboardCubit.state,
      ).thenReturn(const LeaderboardLoaded([]));

      await pumpWidget(tester);
      await tester.pumpAndSettle(); // Allow time for localization

      expect(find.text('The leaderboard is empty.'), findsOneWidget);
    });

    testWidgets('renders ListView when state is Loaded with data', (
      tester,
    ) async {
      when(
        () => mockLeaderboardCubit.state,
      ).thenReturn(LeaderboardLoaded(leaderboardList));

      await pumpWidget(tester);

      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(ListTile), findsNWidgets(leaderboardList.length));
      expect(find.text('Player1'), findsOneWidget);
      expect(find.text('1000 XP'), findsOneWidget);
      expect(find.text('Player2'), findsOneWidget);
      expect(find.text('950 XP'), findsOneWidget);
    });
  });
}
