import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/level.dart';
import 'package:detoxme/domain/entities/player_stats.dart';
import 'package:detoxme/domain/entities/task.dart' as entity;
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/reward_repository.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/reward/reward_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockRewardRepository extends Mock implements RewardRepository {}

void main() {
  late MockRewardRepository mockRewardRepository;
  late RewardCubit rewardCubit;

  // Sample data for testing
  const initialPlayerStats = PlayerStats(
    userId: 'test_user',
    username: 'Tester',
    currentLevel: 1,
    currentXp: 50,
    earnedBadgeIds: [],
    earnedCardIds: [],
  );

  final testTask = entity.Task(
    id: 'test_task_1',
    title: 'Test Task',
    description: 'Complete this for testing.',
    duration: const Duration(minutes: 5),
  );

  setUp(() {
    mockRewardRepository = MockRewardRepository();
    rewardCubit = RewardCubit(rewardRepository: mockRewardRepository);

    // Stub the initial loadPlayerStats call often needed before taskCompleted
    // This might require adjustments based on how the cubit is used in tests
    when(
      () => mockRewardRepository.getPlayerStats(),
    ).thenAnswer((_) async => const Right(initialPlayerStats));
  });

  tearDown(() {
    rewardCubit.close();
  });

  group('RewardCubit', () {
    test('initial state is RewardInitial', () {
      expect(rewardCubit.state, RewardInitial());
    });

    group('loadPlayerStats', () {
      blocTest<RewardCubit, RewardState>(
        'emits [RewardLoading, RewardLoaded] when successful',
        setUp: () {
          // Reset the stubbing for this specific test if needed
          when(
            () => mockRewardRepository.getPlayerStats(),
          ).thenAnswer((_) async => const Right(initialPlayerStats));
        },
        build: () => rewardCubit,
        act: (cubit) => cubit.loadPlayerStats(),
        expect:
            () => [
              RewardLoading(),
              isA<RewardLoaded>().having(
                (state) => state.stats,
                'stats',
                initialPlayerStats,
              ),
            ],
        verify: (_) {
          verify(() => mockRewardRepository.getPlayerStats()).called(1);
        },
      );

      blocTest<RewardCubit, RewardState>(
        'emits [RewardLoading, RewardError] when repository fails',
        setUp: () {
          when(() => mockRewardRepository.getPlayerStats()).thenAnswer(
            (_) async => const Left(ServerFailure('Failed to load')),
          );
        },
        build: () => rewardCubit,
        act: (cubit) => cubit.loadPlayerStats(),
        expect:
            () => [
              RewardLoading(),
              isA<RewardError>().having(
                (state) => state.failure.message,
                'message',
                'Failed to load',
              ),
            ],
        verify: (_) {
          verify(() => mockRewardRepository.getPlayerStats()).called(1);
        },
      );
    });

    group('taskCompleted', () {
      // Pre-load state for taskCompleted tests
      final loadedStateBefore = RewardLoaded(
        stats: initialPlayerStats,
        currentLevelInfo: getLevelForXp(initialPlayerStats.currentXp),
        xpForNextLevel: getXpForNextLevel(initialPlayerStats.currentLevel),
      );

      // Expected stats after task completion (50 initial XP + 50 from task's 5 min duration)
      final statsAfter = initialPlayerStats.copyWith(currentXp: 100);

      blocTest<RewardCubit, RewardState>(
        'emits RewardLoaded with updated XP when save succeeds (syncedRemotely)',
        seed: () => loadedStateBefore, // Start from a loaded state
        setUp: () {
          when(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).thenAnswer((_) async => const Right(SyncStatus.syncedRemotely));
        },
        build: () => rewardCubit,
        act: (cubit) => cubit.taskCompleted(testTask),
        expect:
            () => [
              isA<RewardLoaded>()
                  .having((state) => state.stats, 'stats', statsAfter)
                  .having(
                    (state) => state.xpGained,
                    'xpGained',
                    50,
                  ), // 5 min * 10 XP/min
            ],
        verify: (_) {
          verify(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).called(1);
        },
      );

      blocTest<RewardCubit, RewardState>(
        'emits RewardLoaded with updated XP when save succeeds (syncedLocally)',
        seed: () => loadedStateBefore,
        setUp: () {
          when(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).thenAnswer((_) async => const Right(SyncStatus.syncedLocally));
        },
        build: () => rewardCubit,
        act: (cubit) => cubit.taskCompleted(testTask),
        expect:
            () => [
              isA<RewardLoaded>()
                  .having((state) => state.stats, 'stats', statsAfter)
                  .having((state) => state.xpGained, 'xpGained', 50),
            ],
        verify: (_) {
          verify(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).called(1);
        },
      );

      blocTest<RewardCubit, RewardState>(
        'emits RewardError when save fails',
        seed: () => loadedStateBefore,
        setUp: () {
          when(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).thenAnswer(
            (_) async => const Left(CacheFailure('Local save failed')),
          );
        },
        build: () => rewardCubit,
        act: (cubit) => cubit.taskCompleted(testTask),
        expect:
            () => [
              isA<RewardError>().having(
                (state) => state.failure.message,
                'message',
                'Local save failed',
              ),
            ],
        verify: (_) {
          verify(
            () => mockRewardRepository.savePlayerStats(statsAfter),
          ).called(1);
        },
      );

      blocTest<RewardCubit, RewardState>(
        'calculates level up correctly',
        seed:
            () => RewardLoaded(
              stats: initialPlayerStats.copyWith(currentXp: 90),
              currentLevelInfo: getLevelForXp(90),
              xpForNextLevel: getXpForNextLevel(1),
            ),
        setUp: () {
          // Assume level 2 starts at 100 XP (based on game_data.dart)
          final statsAfterLevelUp = initialPlayerStats.copyWith(
            currentXp: 140,
            currentLevel: 2,
          );
          when(
            () => mockRewardRepository.savePlayerStats(statsAfterLevelUp),
          ).thenAnswer((_) async => const Right(SyncStatus.syncedRemotely));
        },
        build: () => rewardCubit,
        act:
            (cubit) =>
                cubit.taskCompleted(testTask), // Task gives 50 XP (5 * 10)
        expect:
            () => [
              isA<RewardLoaded>()
                  .having((state) => state.stats.currentXp, 'xp', 140)
                  .having((state) => state.stats.currentLevel, 'level', 2)
                  .having(
                    (state) => state.levelReached,
                    'levelReached',
                    isNotNull,
                  )
                  .having(
                    (state) => state.levelReached?.levelNumber,
                    'levelReachedNumber',
                    2,
                  ),
            ],
      );

      // TODO: Add tests for badge logic (requires mocking gameBadges or providing test data)
      // TODO: Add tests for card logic (requires mocking Random and/or gameCards)
    });

    group('clearRewardNotifications', () {
      final loadedStateWithNotifications = RewardLoaded(
        stats: initialPlayerStats,
        currentLevelInfo: getLevelForXp(initialPlayerStats.currentXp),
        xpForNextLevel: getXpForNextLevel(initialPlayerStats.currentLevel),
        xpGained: 50,
        levelReached: const Level(
          levelNumber: 2,
          name: 'Seedling',
          xpRequired: 100,
        ),
        badgeEarned: null,
        cardEarned: null,
      );
      final loadedStateWithoutNotifications =
          loadedStateWithNotifications.copyWithoutNotifications();

      blocTest<RewardCubit, RewardState>(
        'emits RewardLoaded with null notification fields',
        seed: () => loadedStateWithNotifications,
        build: () => rewardCubit,
        act: (cubit) => cubit.clearRewardNotifications(),
        expect: () => [loadedStateWithoutNotifications],
      );

      blocTest<RewardCubit, RewardState>(
        'does nothing if state is not RewardLoaded',
        seed: () => RewardInitial(),
        build: () => rewardCubit,
        act: (cubit) => cubit.clearRewardNotifications(),
        expect: () => [], // No state change expected
      );
    });
  });
}

// Re-add helper stubs TEMPORARILY to resolve linter issue
// TODO: Refactor tests to use actual game_data.dart logic or mock GameData
Level getLevelForXp(int xp) {
  // Use correct constructor from domain/entities/level.dart
  if (xp >= 100) {
    return const Level(levelNumber: 2, name: 'Seedling', xpRequired: 100);
  }
  return const Level(levelNumber: 1, name: 'Sprout', xpRequired: 0);
}

int getXpForNextLevel(int currentLevel) {
  if (currentLevel == 1) return 100; // XP required to reach level 2
  // Add more levels as defined in game_data.dart if needed for tests
  return 250; // Placeholder for XP required to reach level 3
}
