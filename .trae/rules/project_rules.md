# Flutter & Dart Best Practices

This document outlines general best practices for Flutter and Dart development that apply to any Flutter project, not just this specific app.

Keep going until the job is completely solved before ending your turn

If you’re unsure about the code or files, open them - dont hallucinate.

Plan thoroughly before every tool call and reflect on the outcome after.

AND DONT RUN THE APP OR DEBUG MODE OR WHATEVER.

## Effective Dart Rules

### Naming Conventions
1. Use terms consistently throughout your code.
2. Follow existing mnemonic conventions when naming type parameters (e.g., `E` for element, `K`/`V` for key/value, `T`/`S`/`U` for generic types).
3. Name types using `UpperCamelCase` (classes, enums, typedefs, type parameters).
4. Name extensions using `UpperCamelCase`.
5. Name packages, directories, and source files using `lowercase_with_underscores`.
6. Name import prefixes using `lowercase_with_underscores`.
7. Name other identifiers using `lowerCamelCase` (variables, parameters, named parameters).
8. Capitalize acronyms and abbreviations longer than two letters like words.
9. Avoid abbreviations unless the abbreviation is more common than the unabbreviated term.
10. Consider making code read like a sentence when designing APIs.
11. Prefer a noun phrase for non-boolean properties or variables.
12. Prefer a non-imperative verb phrase for boolean properties or variables.
13. Prefer the positive form for boolean property and variable names.

### Types and Functions
1. Use class modifiers to control if your class can be extended or used as an interface.
2. Type annotate variables without initializers.
3. Type annotate fields and top-level variables if the type isn't obvious.
4. Annotate return types on function declarations.
5. Annotate parameter types on function declarations.
6. Write type arguments on generic invocations that aren't inferred.
7. Annotate with `dynamic` instead of letting inference fail.
8. Use `Future<void>` as the return type of asynchronous members that do not produce values.
9. Use getters for operations that conceptually access properties.
10. Use setters for operations that conceptually change properties.
11. Use a function declaration to bind a function to a name.
12. Use inclusive start and exclusive end parameters to accept a range.

### Style
1. Format your code using `dart format`.
2. Use curly braces for all flow control statements.
3. Prefer `final` over `var` when variable values won't change.
4. Use `const` for compile-time constants.

### Imports & Files
1. Don't import libraries inside the `src` directory of another package.
2. Don't allow import paths to reach into or out of `lib`.
3. Prefer relative import paths within a package.
4. Don't use `/lib/` or `../` in import paths.
5. Consider writing a library-level doc comment for library files.

### Structure
1. Keep files focused on a single responsibility.
2. Limit file length to maintain readability.
3. Group related functionality together.
4. Prefer making fields and top-level variables `final`.
5. Consider making your constructor `const` if the class supports it.
6. Prefer making declarations private.

## Bloc/Cubit Best Practices

### State Management Principles
1. Separate business logic from UI.
2. Use Cubit for simple state management; use Bloc for more complex, event-driven state management.
3. Keep states immutable - never modify state directly.
4. Extend Equatable for state classes to implement value equality.
5. Handle all possible states in the UI (loading, success, error, etc.).

### Cubit Patterns
1. Define the initial state by passing it to the superclass constructor.
2. Only use the `emit` method inside a Cubit or Bloc.
3. Avoid direct bloc-to-bloc communication to prevent tight coupling.
4. Use BlocListener for side effects like navigation or showing dialogs.
5. Use BlocProvider for dependency injection of cubits.
6. Use context.read() to access a cubit in event handlers without subscribing to changes.
7. Handle error states properly in all asynchronous operations.

### State Class Design
1. Make state classes immutable and implement a copyWith method.
2. Use `const` constructors for state classes whenever possible.
3. Include all relevant properties in the `props` getter when using Equatable.
4. Consider implementing extension methods on state classes for derived properties.

## Flutter UI Guidelines

### Widget Best Practices
1. Use `StatelessWidget` whenever possible.
2. Extract reusable widgets into separate components.
3. Keep build methods simple and focused.
4. Use `const` constructors for widgets when possible to improve performance.
5. Implement proper error boundaries.
6. Set keys on widgets when their identity matters.

### Performance Best Practices
1. Avoid expensive operations in build methods.
2. Implement pagination for large lists.
3. Use proper list view optimization techniques.
4. Implement proper memory management.
5. Keep state as local as possible to minimize rebuilds.

### Layout Guidelines
1. Use proper constraints to avoid overflow errors.
2. Wrap ListView inside a Column with Expanded to avoid unbounded height errors.
3. Constrain the width of widgets like TextField using Expanded or SizedBox.
4. Never call setState or showDialog directly inside build methods.
5. Ensure each ScrollController is only attached to a single scrollable widget.

### Common Flutter Errors
1. "RenderFlex overflowed" - Wrap children in Flexible, Expanded, or set constraints.
2. "Vertical viewport was given unbounded height" - Add bounded height to ListView in Column.
3. "An InputDecorator cannot have an unbounded width" - Constrain TextField width.
4. "setState called during build" - Move setState calls outside of build.
5. "The ScrollController is attached to multiple scroll views" - Use unique controllers.
6. "RenderBox was not laid out" - Check for missing or unbounded constraints.

## Clean Architecture Principles

### Layer Separation
1. Separate your features into UI Layer, Domain Layer, and Data Layer.
2. Only allow communication between adjacent layers.
3. Keep the Domain layer independent of frameworks.
4. Use repository interfaces in the Domain layer, implementations in the Data layer.
5. Use dependency injection to provide implementations to the UI layer.

### Responsibilities by Layer
1. UI Layer: Present data and handle user interactions
2. Domain Layer: Define business logic, entities, and repository interfaces
3. Data Layer: Implement repositories, manage remote and local data sources

### Data Flow
1. Follow unidirectional data flow (UI → Domain → Data and back).
2. Data changes should always happen in the data layer, not in the UI.
3. The UI should always reflect the current (immutable) state.
4. Use repository pattern as single source of truth for data.

## Testing Best Practices
1. Write unit tests for business logic.
2. Write widget tests for UI components.
3. Use proper mocking strategies for dependencies.
4. Test both success and error paths.
5. Keep tests focused on a single functionality.
6. Use `blocTest` for testing Cubits and Blocs.
7. Test the initial state and all state transitions.
8. Mock cubits/blocs in widget tests to verify UI behavior for all states.

# DetoxMe App Guidelines

This document outlines design patterns, UI components, and architectural guidelines specific to the DetoxMe app.

## Project Architecture

### Layer Structure
```
/lib
├── core # Utilities, Constants, Extensions, Widgets
├── data # Repositories implementations, Data Sources (Local & Remote)
├── domain # Entities, Repository interfaces, UseCases
└── presentation
    ├── bloc # Cubits/Blocs for state management
    └── ui # Screens, Widgets, Themes
```

### Core Layer
The core directory contains app-wide utilities and base components:
- `constants` - App-wide constant values and enums
- `extensions` - Extension methods on existing types
- `widgets` - Reusable widgets used across the app
- `themes` - App theming and styling
- `router` - App navigation configuration using GoRouter
- `network` - Network info and connectivity handling
- `services` - App-wide services (notifications, analytics)

### Domain Layer
The domain directory contains business logic independent of any framework:
- `entities` - Plain Dart objects representing core business models (Activity, Mood, UserProfile, etc.)
- `repositories` - Repository interfaces
- `failure` - Domain-specific failure classes

### Data Layer
The data directory implements data access and persistence:
- `repositories` - Concrete implementations of repository interfaces
- `local` - Local data sources (Hive)
- `remote` - Remote data sources (Supabase)

### Presentation Layer
The presentation directory handles UI and state management:
- `bloc` - State management using Cubit/Bloc pattern (organized by feature)
- `ui` - UI components organized by feature
  - `screens` - Main screen widgets
  - `widgets` - Common widgets used across features

## Design System

### Color Usage

#### Primary Color Application
- Use primary color for main actions, selected states, and key UI elements
- Apply `withValues(alpha: x)` instead of `withOpacity()` for transparency
- Use color with alpha values: `theme.colorScheme.primary.withValues(alpha: 0.1)` for container backgrounds

#### Color Patterns
- Use gradient backgrounds for featured content cards (primary to secondary container colors)
- Apply `theme.colorScheme.surface` for card backgrounds
- Use themed container backgrounds for icons: `color.withValues(alpha: 0.1)`
- Selected elements use primary colors, unselected use `onSurface` colors

#### Color Consistency Between List and Detail Views
- Entities should maintain the same color between their list item and detail view
- Activities use category color, food recommendations use mood type color
- Use identical color determination methods in both list and detail components
- Store entity colors in class variables to avoid recalculation

#### Standard Alpha Values
| UI Element | Alpha Value | Example Usage |
|------------|-------------|---------------|
| Card background | 0.1 - 0.2 | `color.withValues(alpha: 0.1)` |
| Card border | 0.3 | `color.withValues(alpha: 0.3)` |
| Icon background | 0.1 | `color.withValues(alpha: 0.1)` |
| Gradient overlay (light) | 100 (0.39) | `color.withAlpha(100)` |
| Gradient overlay (dark) | 180 (0.7) | `color.withAlpha(180)` |
| Pill background | 40 (0.16) | `Colors.white.withAlpha(40)` |
| Section highlight | 30-60 (0.12-0.24) | `color.withAlpha(30)` |

### Typography Hierarchy

- Section headings: `theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)`
- Card titles: `theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)`
- Body text: `theme.textTheme.bodyMedium`
- Secondary text: `theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)`
- Metrics/stats: `theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)`

### Card & Container Design

- Consistent borderRadius for cards: `BorderRadius.circular(16)`
- Container shadows: `BoxShadow(color: theme.colorScheme.shadow.withValues(alpha: 0.05), blurRadius: 10)`
- Border styling: `Border.all(color: theme.colorScheme.outlineVariant.withValues(alpha: 0.1), width: 1)`

### Icon Styling

- Place icons in circular/rounded containers with themed backgrounds
- Icon background: `color.withValues(alpha: 0.1)`
- Container padding: 8-12px
- Icon size: 18-24px for regular icons, 40px+ for featured icons

### Animation Guidelines

- Staggered loading: FadeInUp with increasing delays for list items
- Standard animation duration: 300-800ms
- Apply smooth transitions with `Curves.easeInOut`

## UI Components

### Card Components

#### Feature Card
```dart
Container(
  width: double.infinity,
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        theme.colorScheme.primaryContainer,
        theme.colorScheme.secondaryContainer,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: theme.colorScheme.shadow.withValues(alpha: 51),
        blurRadius: 15,
        offset: const Offset(0, 6),
      ),
    ],
  ),
  child: Padding(
    padding: const EdgeInsets.all(20.0),
    child: // content
  ),
)
```

#### Stat Card
```dart
Container(
  width: 160,
  padding: const EdgeInsets.all(16),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Icon row with category color
      // Title and value
    ],
  ),
)
```

#### Activity Card
```dart
Container(
  width: 180,
  margin: const EdgeInsets.only(right: 16),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        color.withValues(alpha: 20),
        color.withValues(alpha: 40),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: color.withValues(alpha: 51),
      width: 1.5,
    ),
  ),
  child: // content
)
```

### Custom Components

#### Section Header
```dart
Row(
  children: [
    Icon(icon, color: theme.colorScheme.primary, size: 20),
    const SizedBox(width: 12),
    Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.onSurface,
      ),
    ),
    const Spacer(),
    if (showViewAll)
      TextButton(
        onPressed: onViewAllPressed,
        child: const Text('View All'),
      ),
  ],
)
```

#### Tag/Chip
```dart
Container(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: theme.colorScheme.primary.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(20),
  ),
  child: Text(
    label,
    style: theme.textTheme.bodySmall?.copyWith(
      color: theme.colorScheme.onSurface,
      fontWeight: FontWeight.w500,
    ),
  ),
)
```

#### Empty State
```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(28),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
      width: 0.2,
    ),
  ),
  child: Center(
    child: Column(
      children: [
        // Icon, title, message, action button
      ],
    ),
  ),
)
```

## Detail Screen Patterns

### Flexible Header Structure
```dart
NestedScrollView(
  controller: _scrollController,
  headerSliverBuilder: (context, innerBoxIsScrolled) {
    return [
      SliverAppBar(
        expandedHeight: 250.0,
        floating: false,
        pinned: true,
        backgroundColor: itemColor,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: _buildFlexibleHeader(context, state),
        ),
        title: AnimatedOpacity(
          opacity: _isScrolled ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(item.title, style: titleStyle),
        ),
        centerTitle: true,
      ),
      // Tab bar implementation
    ];
  },
  body: TabBarView(
    controller: _tabController,
    children: [
      // Info tab
      // Discussion tab
    ],
  ),
)
```

### Header Background
```dart
Positioned.fill(
  child: Stack(
    children: [
      // Background image
      Positioned.fill(
        child: CachedNetworkImage(
          imageUrl: item.imageUrl,
          fit: BoxFit.cover,
          // Caching options
          placeholder: (context, url) => _buildImagePlaceholder(),
          errorWidget: (context, error, stackTrace) => _buildImagePlaceholder(),
        ),
      ),
      // Gradient overlay
      Positioned.fill(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                itemColor.withAlpha(100),
                itemColor.withAlpha(180),
              ],
            ),
          ),
        ),
      ),
    ],
  ),
)
```

### Rewards/Benefits Section
```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(20),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        itemColor.withAlpha(30),
        itemColor.withAlpha(60),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: itemColor.withAlpha(100), width: 1),
  ),
  child: Column(
    children: [
      // Title, icon, value, subtitle
    ],
  ),
)
```

## State Management Implementation

### Screen Structure with Cubit
```dart
// Provider pattern
class DetailScreen extends StatelessWidget {
  final String itemId;
  
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ItemCubit>(
          create: (context) => getIt<ItemCubit>()..loadItem(itemId),
        ),
        BlocProvider<OtherCubit>(
          create: (context) => getIt<OtherCubit>(),
        ),
      ],
      child: const DetailView(),
    );
  }
}

// View with BlocConsumer
class DetailView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ItemCubit, ItemState>(
      listener: (context, state) {
        // Handle side effects like navigation, dialogs, etc.
      },
      builder: (context, state) {
        // Build UI based on state
      },
    );
  }
}
```

### Converting from setState to Cubit
When refactoring screens from local state to Cubit pattern:
1. Identify all variables managed with `setState`
2. Create a state class with all required properties
3. Implement Cubit methods to update state
4. Replace `setState` calls with Cubit method calls
5. Replace direct state usage with BlocBuilder/Consumer

## Navigation

- Use GoRouter for app navigation
- Define routes in a central location
- Use the DetoxBottomNavBar for main navigation
- Navigation actions use `context.go()` or `context.push()` methods

## Error Handling

- Use Either type from dartz for error handling in repositories
- Define domain-specific failures in domain/failure
- Handle errors appropriately in the presentation layer
- Show user-friendly error messages with SnackBar or Dialog

## Offline-First Approach

- Local data sources serve as single source of truth
- Remote operations are queued when offline and synced when online
- Network state is monitored via the NetworkInfo service
- Repository implementations handle connection state and data sync

## Internationalization

- Localization files located in localization/arb
- Use AppLocalizations for accessing localized strings
- Access via: `final l10n = AppLocalizations.of(context)!;`

# Detail Screen Design Patterns

This document outlines the established design patterns for detail screens in the DetoxMe app, focusing on the implementation in [ActivityDetailScreen](mdc:lib/presentation/ui/screens/activities/widgets/activity_detail_screen.dart) and [FoodRecommendationDetailScreen](mdc:lib/presentation/ui/screens/food_mood/food_recommendation_details_screen.dart).

## Flexible Header Pattern

### Collapsible Header Structure
- Use `NestedScrollView` with `SliverAppBar` for collapsible header
- Set `pinned: true` to keep the app bar visible while scrolling
- Set `expandedHeight: 250.0` for consistent header size
- Use `FlexibleSpaceBar` with `background` for the header content
- Monitor scroll position to animate title opacity

```dart
return NestedScrollView(
  controller: _scrollController,
  headerSliverBuilder: (context, innerBoxIsScrolled) {
    return [
      SliverAppBar(
        expandedHeight: 250.0,
        floating: false,
        pinned: true,
        backgroundColor: itemColor,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: _buildFlexibleHeader(context, state),
        ),
        title: AnimatedOpacity(
          opacity: _isScrolled ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(item.title, style: titleStyle),
        ),
        centerTitle: true,
      ),
      // Tab bar as SliverPersistentHeader
    ];
  },
  body: TabBarView(...),
);
```

### Scroll Behavior Implementation
- Add a `ScrollController` to monitor scroll position
- Implement a `_onScroll` method to update header state
- Track scroll position with a `_isScrolled` boolean state
- Add/remove listener in `initState`/`dispose`

```dart
@override
void initState() {
  super.initState();
  _scrollController.addListener(_onScroll);
}

void _onScroll() {
  setState(() {
    _isScrolled = _scrollController.offset > 10;
  });
}

@override
void dispose() {
  _scrollController.removeListener(_onScroll);
  _scrollController.dispose();
  super.dispose();
}
```

## Tab Bar Implementation

### Persistent Tab Header
- Use `SliverPersistentHeader` with custom delegate
- Create a `_SliverTabBarDelegate` class to manage tab bar rendering
- Use consistent colors based on item theme
- Set `pinned: true` to keep tab bar visible while scrolling

```dart
SliverPersistentHeader(
  delegate: _SliverTabBarDelegate(
    TabBar(
      controller: _tabController,
      tabs: [Tab(text: 'Info'), Tab(text: 'Discussion')],
      labelColor: itemColor,
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.bold,
        fontSize: 16,
      ),
      unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
      indicatorColor: itemColor,
    ),
    backgroundColor: theme.scaffoldBackgroundColor,
  ),
  pinned: true,
),
```

### Tab Bar Delegate Implementation
```dart
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  final Color backgroundColor;

  _SliverTabBarDelegate(this.tabBar, {required this.backgroundColor});

  @override
  Widget build(context, shrinkOffset, overlapsContent) {
    return Container(color: backgroundColor, child: tabBar);
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant _SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar ||
        backgroundColor != oldDelegate.backgroundColor;
  }
}
```

## Header Content Design

### Header Background
- Use item-specific color for gradient overlay
- Apply `CachedNetworkImage` for remote images with error handling
- Implement consistent placeholder for image loading/error states
- Create gradient overlay with semi-transparent item-specific colors

```dart
Positioned.fill(
  child: Stack(
    children: [
      // Image
      Positioned.fill(
        child: CachedNetworkImage(
          imageUrl: item.imageUrl,
          fit: BoxFit.cover,
          maxWidthDiskCache: 800,
          maxHeightDiskCache: 400,
          memCacheWidth: 800,
          memCacheHeight: 400,
          useOldImageOnUrlChange: true,
          placeholder: (context, url) => _buildImagePlaceholder(),
          errorWidget: (context, error, stackTrace) => _buildImagePlaceholder(),
        ),
      ),
      // Gradient overlay
      Positioned.fill(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                itemColor.withAlpha(100),
                itemColor.withAlpha(180),
              ],
            ),
          ),
        ),
      ),
    ],
  ),
)
```

### Header Content Structure
- Place title and metadata at bottom left with shadow for readability
- Title with `headlineSmall` style, bold, white color, and text shadow
- Add metadata pill with semi-transparent white background
- Place voting controls at bottom right with semi-transparent container

```dart
// Title and metadata at bottom left
Positioned(
  left: 20,
  right: 20,
  bottom: 20,
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Text(
        item.title,
        style: theme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: [
            Shadow(color: Colors.black45, blurRadius: 3, offset: Offset(0, 1)),
          ],
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      const SizedBox(height: 8),
      // Metadata pill
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(40),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(metadataIcon, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text(
              metadataText,
              style: metadataTextStyle,
            ),
          ],
        ),
      ),
    ],
  ),
),

// Voting controls at bottom right
Positioned(
  bottom: 20,
  right: 20,
  child: Material(
    color: Colors.transparent,
    child: _buildVotingControls(),
  ),
),
```

## Content Tab Design

### Info Tab
- Use `SingleChildScrollView` with `BouncingScrollPhysics`
- Create consistent sections with title + content pattern
- Include eye-catching rewards/points section
- Add bottom space for floating action button (if present)

```dart
SingleChildScrollView(
  physics: const BouncingScrollPhysics(),
  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Creator info if available
      if (item.createdBy != null) _buildCreatorInfo(),
      
      // Description section
      _buildSection(
        context,
        title: l10n.description,
        child: Text(
          item.description,
          style: theme.textTheme.bodyLarge,
        ),
      ),
      const SizedBox(height: 24),
      
      // Eye-catching points/benefits section
      _buildRewardsSection(),
      const SizedBox(height: 24),
      
      // Additional sections as needed
      ...
      
      // Bottom space for action button
      const SizedBox(height: 80),
    ],
  ),
),
```

### Reward/Benefits Section
- Use gradient background with item-specific color
- Apply eye-catching icon and typography
- Use consistent borders and rounded corners

```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(20),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        itemColor.withAlpha(30),
        itemColor.withAlpha(60),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: itemColor.withAlpha(100), width: 1),
  ),
  child: Column(
    children: [
      Text(titleText, style: titleStyle, textAlign: TextAlign.center),
      const SizedBox(height: 12),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(rewardIcon, color: itemColor, size: 32),
          const SizedBox(width: 8),
          Text(rewardValueText, style: valueStyle),
        ],
      ),
      const SizedBox(height: 4),
      Text(subtitleText, style: subtitleStyle, textAlign: TextAlign.center),
    ],
  ),
),
```

### Discussion Tab
- Use `Column` with `Expanded` list area and bottom input
- Show empty state when no comments exist
- Style comments as cards with consistent margins
- Implement comment input row with matching theme colors

```dart
Column(
  children: [
    // Comments list
    Expanded(
      child: comments.isEmpty
          ? _buildEmptyCommentsState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: comments.length,
              itemBuilder: (context, index) => _buildCommentCard(comments[index]),
            ),
    ),

    // Comment input
    Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
              minLines: 1,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _submitComment,
            icon: Icon(Icons.send, color: Colors.white),
            style: IconButton.styleFrom(
              backgroundColor: itemColor,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    ),
  ],
),
```

## Color Consistency

### Entity-Based Colors
- For activities, use `activity.category.color`
- For food recommendations, derive color from mood type or use a consistent algorithm:
  ```dart
  Color _getMoodColor(MoodType? mood) {
    if (mood == null) return Colors.blueGrey;
    
    switch (mood) {
      case MoodType.happy: return Colors.amber;
      case MoodType.sad: return Colors.blue;
      ...
    }
  }
  
  Color _getRandomColor() {
    final colors = [Colors.orange, Colors.purple, ...];
    final hash = item.id.hashCode.abs();
    return colors[hash % colors.length];
  }
  ```

### Color Application
- Set SliverAppBar background to entity color
- Use the same color for tab indicator
- Apply the color to section headings
- Use the color for action buttons
- Apply consistent alpha values for gradients

## Animation Patterns

### Loading Animations
- Implement staggered loading with consistent timing
- Apply `FadeInUp` with 800ms duration and appropriate delays

### Interactive Animations
- Use animation controllers for interactive elements (likes/upvotes)
- Show heart/star burst animations on positive interactions
- Reset animations on completion

## Implementation Notes

1. Ensure consistent initialization in initState:
   - TabController
   - ScrollController
   - Animation controllers
   - Initial data/state
   
2. Clean up in dispose:
   - Remove listeners
   - Dispose controllers
   
3. Use BlocConsumers for monitoring state changes:
   - Listen for changes in active/completed state
   - Listen for content updates
   - Listen for error states

4. Extract reusable components into private methods:
   - `_buildFlexibleHeader`
   - `_buildSection`
   - `_buildEmptyState`
   - `_buildVotingControls`

# Cubit State Management in UI Screens

This document outlines the established patterns for implementing proper state management using Cubits in the UI layer of the DetoxMe app. Following these patterns ensures consistent state handling and avoids issues with local state management.

## Key Principles

1. **Avoid Local State**: Minimize use of `setState` in screens with Cubit patterns
2. **Use BlocProvider**: Provide Cubits through `BlocProvider` for dependency injection 
3. **React to State Changes**: Use `BlocBuilder` or `BlocConsumer` to react to state updates
4. **Separate Logic from UI**: Keep business logic in Cubits, presentation logic in UI
5. **Use BlocListener for Side Effects**: Handle navigation, dialogs, and other side effects in listeners

## Implementation Patterns

### Screen Structure with Cubit

The recommended structure for screens using Cubit state management:

```dart
// Provider pattern
class ActivityScreen extends StatelessWidget {
  const ActivityScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivityCubit>(
      create: (context) => getIt<ActivityCubit>()..loadActivities(),
      child: const ActivityView(),
    );
  }
}

// View implementation with BlocConsumer
class ActivityView extends StatelessWidget {
  const ActivityView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Handle side effects like navigation, dialogs, etc.
        if (state is ActivityError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        if (state is ActivityLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ActivitiesLoaded) {
          return _buildContent(context, state.activities);
        } else if (state is ActivityError) {
          return _buildErrorState(context, state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContent(BuildContext context, List<Activity> activities) {
    // UI building logic
  }

  Widget _buildErrorState(BuildContext context, String message) {
    // Error UI building logic
  }
}
```

### Handling Multiple Cubits

For screens that require multiple Cubits, use `MultiBlocProvider` to inject them:

```dart
class DetailScreen extends StatelessWidget {
  final String itemId;
  
  const DetailScreen({Key? key, required this.itemId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ItemCubit>(
          create: (context) => getIt<ItemCubit>()..loadItem(itemId),
        ),
        BlocProvider<CommentsCubit>(
          create: (context) => getIt<CommentsCubit>()..loadComments(itemId),
        ),
        BlocProvider<ActivityTimerCubit>(
          create: (context) => getIt<ActivityTimerCubit>(),
        ),
      ],
      child: const DetailView(),
    );
  }
}
```

### Using BlocListener for Side Effects

When you need to react to state changes without rebuilding the UI, use `BlocListener`:

```dart
BlocListener<ActivityTimerCubit, ActivityTimerState>(
  listener: (context, state) {
    if (state is ActivityTimerCompleted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(l10n.activityComplete),
          content: Text(l10n.activityCompleteMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.ok),
            ),
          ],
        ),
      );
      
      // Call another cubit method
      context.read<ActivityCubit>().completeActivity();
    }
  },
  child: const SizedBox.shrink(), // No UI to build
),
```

### Multiple State Reactions with MultiBlocListener

Group multiple listeners with `MultiBlocListener`:

```dart
MultiBlocListener(
  listeners: [
    BlocListener<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Activity state reactions
      },
    ),
    BlocListener<ActivityTimerCubit, ActivityTimerState>(
      listener: (context, state) {
        // Timer state reactions
      },
    ),
    BlocListener<RewardCubit, RewardState>(
      listener: (context, state) {
        // Reward state reactions
      },
    ),
  ],
  child: Scaffold(
    // The rest of your UI
  ),
),
```

### Accessing Cubits in Event Handlers

Use `context.read()` to access Cubits in event handlers:

```dart
ElevatedButton(
  onPressed: () {
    // Get a reference to the cubit without subscribing to changes
    final activityCubit = context.read<ActivityCubit>();
    activityCubit.startActivity();
    
    // If another cubit needs to be updated as well
    context.read<ActivityTimerCubit>().startTimer();
  },
  child: Text(l10n.startActivity),
),
```

### Selective Rebuilds with BlocSelector

Use `BlocSelector` to rebuild only when specific parts of the state change:

```dart
BlocSelector<ProfileCubit, ProfileState, bool>(
  selector: (state) => state is ProfileLoaded ? state.isEditing : false,
  builder: (context, isEditing) {
    return isEditing
      ? _buildEditForm(context)
      : _buildProfileView(context);
  },
),
```

## State Classes and Extensions

### Using copyWith for State Updates

Create immutable states with `copyWith` methods for easy updates:

```dart
class ActivityDetailState extends Equatable {
  final Activity? activity;
  final bool isLoading;
  final String? error;
  final bool isActive;
  final int elapsedSeconds;
  final List<Comment> comments;
  
  const ActivityDetailState({
    this.activity,
    this.isLoading = false,
    this.error,
    this.isActive = false,
    this.elapsedSeconds = 0,
    this.comments = const [],
  });
  
  ActivityDetailState copyWith({
    Activity? activity,
    bool? isLoading,
    String? error,
    bool? isActive,
    int? elapsedSeconds,
    List<Comment>? comments,
  }) {
    return ActivityDetailState(
      activity: activity ?? this.activity,
      isLoading: isLoading ?? this.isLoading,
      error: error,  // null is valid to clear errors
      isActive: isActive ?? this.isActive,
      elapsedSeconds: elapsedSeconds ?? this.elapsedSeconds,
      comments: comments ?? this.comments,
    );
  }
  
  @override
  List<Object?> get props => [
    activity, isLoading, error, isActive, elapsedSeconds, comments
  ];
}
```

### State Extension Methods

Add extension methods to extract common state properties:

```dart
extension ActivityDetailStateExtensions on ActivityDetailState {
  bool get isCompleted => 
    activity != null && activity!.completionDate != null;
    
  bool get canStart => 
    activity != null && !isLoading && !isActive && !isCompleted;
    
  bool get canPause => isActive;
  
  bool get canResume => !isActive && elapsedSeconds > 0 && !isCompleted;
  
  bool get hasComments => comments.isNotEmpty;
  
  String get formattedElapsedTime {
    final minutes = (elapsedSeconds / 60).floor();
    final seconds = elapsedSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
```

## Converting from setState to Cubit Pattern

When refactoring a screen from local state to Cubit pattern:

1. **Identify State Variables**: Identify all variables managed with `setState`
2. **Create Cubit State Class**: Define a state class with all required properties
3. **Implement Cubit Methods**: Create methods in Cubit to update state
4. **Replace setState Calls**: Replace all `setState` calls with Cubit method calls
5. **Use BlocBuilder/Consumer**: Replace direct state usage with BlocBuilder/Consumer

### Before (with setState):

```dart
class ActivityDetailScreen extends StatefulWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  State<ActivityDetailScreen> createState() => _ActivityDetailScreenState();
}

class _ActivityDetailScreenState extends State<ActivityDetailScreen> {
  bool _isActive = false;
  int _elapsedSeconds = 0;
  Timer? _timer;
  
  void _startActivity() {
    setState(() {
      _isActive = true;
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }
  
  void _pauseActivity() {
    _timer?.cancel();
    setState(() {
      _isActive = false;
    });
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // UI implementation using _isActive and _elapsedSeconds
  }
}
```

### After (with Cubit):

```dart
// ActivityTimerCubit
class ActivityTimerCubit extends Cubit<ActivityTimerState> {
  Timer? _timer;
  
  ActivityTimerCubit() : super(const ActivityTimerState());
  
  void startTimer() {
    emit(state.copyWith(isActive: true));
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      emit(state.copyWith(
        elapsedSeconds: state.elapsedSeconds + 1
      ));
    });
  }
  
  void pauseTimer() {
    _timer?.cancel();
    emit(state.copyWith(isActive: false));
  }
  
  void resetTimer() {
    _timer?.cancel();
    emit(state.copyWith(
      isActive: false,
      elapsedSeconds: 0,
    ));
  }
  
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}

// UI Implementation
class ActivityDetailScreen extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ActivityTimerCubit>(),
      child: ActivityDetailView(activity: activity),
    );
  }
}

class ActivityDetailView extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailView({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActivityTimerCubit, ActivityTimerState>(
      builder: (context, state) {
        // UI implementation using state.isActive and state.elapsedSeconds
        return Scaffold(
          // ...
          floatingActionButton: state.isActive
              ? FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().pauseTimer(),
                  child: const Icon(Icons.pause),
                )
              : FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().startTimer(),
                  child: const Icon(Icons.play_arrow),
                ),
        );
      },
    );
  }
}
```

## Common Cubit Usage Patterns

### Loading Data

```dart
class ItemCubit extends Cubit<ItemState> {
  final ItemRepository _repository;
  
  ItemCubit(this._repository) : super(const ItemState.initial());
  
  Future<void> loadItem(String id) async {
    emit(const ItemState.loading());
    
    try {
      final item = await _repository.getItem(id);
      emit(ItemState.loaded(item));
    } catch (e) {
      emit(ItemState.error(e.toString()));
    }
  }
}
```

### Handling Form Input

```dart
class FormCubit extends Cubit<FormState> {
  FormCubit() : super(const FormState());
  
  void nameChanged(String value) {
    emit(state.copyWith(
      name: value,
      nameError: value.isEmpty ? 'Name cannot be empty' : null,
    ));
  }
  
  void emailChanged(String value) {
    final emailValid = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);
    emit(state.copyWith(
      email: value,
      emailError: !emailValid ? 'Invalid email address' : null,
    ));
  }
  
  bool get isValid => state.nameError == null && 
                     state.emailError == null &&
                     state.name.isNotEmpty &&
                     state.email.isNotEmpty;
  
  Future<void> submit() async {
    if (!isValid) return;
    
    emit(state.copyWith(isSubmitting: true));
    
    try {
      // Repository call
      emit(state.copyWith(isSuccess: true, isSubmitting: false));
    } catch (e) {
      emit(state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      ));
    }
  }
}
```

### Managing Tab State

```dart
class TabCubit extends Cubit<int> {
  TabCubit() : super(0);
  
  void changeTab(int index) => emit(index);
}

// In UI
BlocProvider(
  create: (context) => TabCubit(),
  child: BlocBuilder<TabCubit, int>(
    builder: (context, activeTabIndex) {
      return DefaultTabController(
        length: 3,
        initialIndex: activeTabIndex,
        child: Scaffold(
          appBar: AppBar(
            bottom: TabBar(
              onTap: (index) => context.read<TabCubit>().changeTab(index),
              tabs: [
                Tab(text: 'Tab 1'),
                Tab(text: 'Tab 2'),
                Tab(text: 'Tab 3'),
              ],
            ),
          ),
          body: TabBarView(
            physics: const NeverScrollableScrollPhysics(), // Prevent swiping to change tabs
            children: [
              // Tab contents
            ],
          ),
        ),
      );
    },
  ),
),
```

## Best Practices

1. **Use Dependency Injection**: Inject dependencies through constructors to allow for easy testing
2. **Keep States Immutable**: Always create new state instances; never mutate existing state
3. **Handle Error States**: Include error handling in all asynchronous operations
4. **Make Small, Focused Cubits**: Create purpose-specific Cubits rather than large, general ones
5. **Consistency in State Design**: Follow a consistent pattern for state class design
6. **Use Equatable for States**: Extend Equatable to prevent unnecessary rebuilds


# Color Consistency Between List and Detail Views

This document outlines the established patterns for maintaining color consistency between list items and their corresponding detail screens in the DetoxMe app. Consistent color usage helps users visually associate items with their detailed views.

## Key Principles

1. **Entity-Based Theming**: Each entity type has a consistent color source
2. **Color Persistence**: The same color from a list item card appears in its detail view
3. **Consistent Alpha Values**: Standard alpha values are used for backgrounds, overlays, and containers
4. **Semantic Color Usage**: Colors are used consistently for specific UI elements

## Item-Detail Color Consistency

### Activities

Activities use category colors as their primary theme color. This ensures visual consistency from the activity card to the detail screen.

- In [ActivityCard](mdc:lib/presentation/ui/screens/dashboard/widgets/activity_card.dart):
  ```dart
  final categoryColor = activity.category?.color ?? theme.colorScheme.tertiary;
  
  // Card uses category color for background and border
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        categoryColor.withValues(alpha: 0.1),
        categoryColor.withValues(alpha: 0.2),
      ],
      // ...
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: categoryColor.withValues(alpha: 0.3),
      width: 1.5,
    ),
  ),
  ```

- In [ActivityDetailScreen](mdc:lib/presentation/ui/screens/activities/widgets/activity_detail_screen.dart):
  ```dart
  // Header uses the same category color
  final categoryColor = activity.category?.color ?? theme.colorScheme.tertiary;
  
  // AppBar background matches activity card color
  SliverAppBar(
    backgroundColor: categoryColor,
    // ...
  ),
  
  // Tab bar and indicators use matching color
  TabBar(
    labelColor: categoryColor,
    indicatorColor: categoryColor,
    // ...
  ),
  ```

### Food Recommendations

Food recommendations derive their colors from associated mood types or use a consistent algorithm to ensure the same color appears in both list and detail views.

- In [FoodRecommendationCard](mdc:lib/presentation/ui/screens/dashboard/widgets/food_recommendation_card.dart):
  ```dart
  // Determine color based on mood types or a fallback algorithm
  Color _getCardColor(FoodRecommendation recommendation) {
    if (recommendation.moodTypes?.isNotEmpty == true) {
      return _getMoodColor(recommendation.moodTypes!.first);
    }
    return _getRandomColor(recommendation);
  }
  
  // Use the color for card styling
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        cardColor.withValues(alpha: 0.1),
        cardColor.withValues(alpha: 0.2),
      ],
      // ...
    ),
    // ...
  ),
  ```

- In [FoodRecommendationDetailsScreen](mdc:lib/presentation/ui/screens/food_mood/food_recommendation_details_screen.dart):
  ```dart
  // Use the same algorithm to determine the color
  _recommendationColor = widget.recommendation.moodTypes?.isNotEmpty == true 
      ? _getMoodColor(widget.recommendation.moodTypes!.first)
      : _getRandomColor();
  
  // Apply to app bar
  SliverAppBar(
    backgroundColor: _recommendationColor,
    // ...
  ),
  
  // And to UI elements
  TabBar(
    labelColor: _recommendationColor,
    indicatorColor: _recommendationColor,
    // ...
  ),
  ```

## Color Algorithm Consistency

To ensure colors are consistently applied across list and detail views, identical color determination functions should be used in both components:

```dart
// Identical implementation in both list and detail components
Color _getMoodColor(MoodType? mood) {
  if (mood == null) return Colors.blueGrey;
  
  switch (mood) {
    case MoodType.happy: return Colors.amber;
    case MoodType.sad: return Colors.blue;
    case MoodType.anxious: return Colors.teal;
    case MoodType.angry: return Colors.deepOrange;
    case MoodType.tired: return Colors.purple;
    case MoodType.excited: return Colors.pink;
    case MoodType.calm: return Colors.green;
    case MoodType.neutral: return Colors.grey;
    case MoodType.inspired: return Colors.lightBlue;
    default: return Colors.blueGrey;
  }
}

// For entities without clear color mapping, use consistent algorithm
Color _getRandomColor(Entity entity) {
  final colors = [
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.green,
  ];
  
  // Using entity ID hash ensures the same entity always gets the same color
  final hash = entity.id.hashCode.abs();
  return colors[hash % colors.length];
}
```

## Standard Alpha Values

Apply consistent alpha values for UI elements:

| UI Element | Alpha Value | Example Usage |
|------------|-------------|---------------|
| Card background | 0.1 - 0.2 | `color.withValues(alpha: 0.1)` |
| Card border | 0.3 | `color.withValues(alpha: 0.3)` |
| Icon background | 0.1 | `color.withValues(alpha: 0.1)` |
| Gradient overlay (light) | 100 (0.39) | `color.withAlpha(100)` |
| Gradient overlay (dark) | 180 (0.7) | `color.withAlpha(180)` |
| Pill background | 40 (0.16) | `Colors.white.withAlpha(40)` |
| Section highlight | 30-60 (0.12-0.24) | `color.withAlpha(30)` |

## Color Application in UI Elements

### Headers
- AppBar/SliverAppBar background: Full entity color
- Image overlay gradient: Entity color with alpha values 100-180
- Title text: White with shadow for readability

### Content Elements
- Section headings: Entity color
- Interactive elements (buttons): Entity color
- Tab indicators: Entity color
- Highlighted sections: Entity color with alpha values 30-60

### Icons
- Icon backgrounds: Entity color with alpha value 0.1
- Icon color: Entity color

## Implementation Guidelines

1. **Extract Color Logic**
   - Place color determination methods in a common location or implement identically
   - Use the same color sources in both list and detail views

2. **Color Extraction Timing**
   - For list views, extract color during build
   - For detail views, set color in initState and update on content changes

3. **Variable Usage**
   - Store colors in class variables to avoid recalculation
   - Update stored colors when content changes

4. **Theme Integration**
   - Always use theme colors as fallbacks
   - Apply accent colors through theme when appropriate

5. **Consistent Methods**
   ```dart
   // In initState
   _entityColor = _determineEntityColor(entity);
   
   // When entity updates
   if (_currentEntity != newEntity) {
     setState(() {
       _currentEntity = newEntity;
       _entityColor = _determineEntityColor(newEntity);
     });
   }
   ```
