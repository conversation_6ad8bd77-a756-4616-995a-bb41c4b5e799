# Logging & Analytics System

This document outlines the logging and analytics system implemented in the DetoxMe application.

## Overview

The system consists of three main components:

1. **LoggerService** - A structured logging utility for local logs
2. **AnalyticsService** - A comprehensive analytics tracking system using Supabase
3. **AnalyticsBlocObserver** - A BlocObserver for tracking state and event changes

## Getting Started

### Replacing Print Statements

Instead of using `print()`, use the utility functions from `lib/core/utils/print_to_log.dart`:

```dart
// Before
print("Loading user data");

// After
import 'package:detoxme/core/utils/print_to_log.dart';

logDebug("Loading user data");
```

### Log Levels

Choose the appropriate log level for your messages:

- `logDebug` - For detailed diagnostic information, disabled in production
- `logInfo` - For general information about app operation
- `logWarning` - For potential issues that don't cause errors
- `logError` - For errors and exceptions

### Tracking Analytics

Track user behavior and app performance with these analytics functions:

```dart
import 'package:detoxme/core/utils/print_to_log.dart';

// Track a custom event
trackEvent('button_clicked', {'button_id': 'login_button'});

// Track a screen view
trackScreenView('HomeScreen', {'came_from': 'OnboardingScreen'});

// Track a user action
trackUserAction('refresh_data', {'data_type': 'feed'});

// Track an error for analytics
trackError('network_error', 'Failed to load user data', {'status_code': 404});
```

## Supabase Analytics Tables

The analytics system uses the following Supabase tables:

1. `analytics_events` - Stores all analytics events
2. `analytics_config` - Configuration for analytics collection

You can also use the Supabase RPC function `log_analytics_event()` directly from SQL:

```sql
-- Log an event from SQL
SELECT log_analytics_event(
  'sql_event', 
  '{"source": "database_trigger", "table": "users"}'::jsonb, 
  '123e4567-e89b-12d3-a456-************'::uuid
);
```

## Advanced Usage

### Extension Methods

You can use the extension methods to log information about specific objects:

```dart
// Log debug info about a user object
user.logDebug('User loaded from database');

// Log an error related to a repository
repository.logError('Failed to fetch data', exception, stackTrace);

// Track an event related to a specific object
task.trackEvent('task_completed', {'duration': 120});
```

### BlocObserver Integration

The system automatically tracks Bloc state changes through the `AnalyticsBlocObserver`. Important state transitions (e.g., loading → success, loading → error) are automatically tracked for analytics.

### Batch Sending

Analytics events are sent in batches to improve performance:

- Events are queued and sent every 30 seconds
- If more than 10 events accumulate, they're sent immediately
- Failed events are stored and retried with exponential backoff

## Disabling Analytics

Users can opt out of analytics by calling:

```dart
AnalyticsService().setAnalyticsEnabled(false);
```

This setting should be exposed in your app's settings screen to comply with privacy regulations.

## Debugging

Use the Supabase dashboard to view analytics data. You can query the `analytics_events` table directly:

```sql
-- View recent events
SELECT * FROM analytics_events 
ORDER BY timestamp DESC 
LIMIT 100;

-- View events by type
SELECT event, COUNT(*) 
FROM analytics_events 
GROUP BY event 
ORDER BY COUNT(*) DESC;

-- View error events
SELECT * FROM analytics_events 
WHERE event = 'error' 
ORDER BY timestamp DESC;
```

## Adding New Event Types

When adding new event types, follow these conventions:

1. Use snake_case for event names
2. Group related events with prefixes (e.g., `user_`, `content_`, `system_`)
3. Be consistent with property names across events
4. Document new event types in your PR

## Implementation Details

The underlying implementation uses:

- Singleton pattern for service instances
- Automatic user and session tracking
- Batch processing with retry logic
- Device and app version information
- Structured JSON logging 