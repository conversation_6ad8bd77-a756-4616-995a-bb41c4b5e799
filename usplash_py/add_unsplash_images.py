import pandas as pd

def generate_unsplash_url(query):
    formatted = query.replace(" ", "+").lower()
    return f"https://source.unsplash.com/featured/?{formatted}"

# Lade CSVs
activities_df = pd.read_csv("activities.csv")
food_df = pd.read_csv("food.csv")

# Ergänze URLs
activities_df["image_url"] = activities_df.apply(
    lambda row: generate_unsplash_url(f"{row['title']} {row['category']}"), axis=1
)
food_df["image_url"] = food_df.apply(
    lambda row: generate_unsplash_url(f"{row['name']} {row['mood_types']}"), axis=1
)

# Speichere neue CSVs
activities_df.to_csv("activities_with_images.csv", index=False)
food_df.to_csv("food_with_images.csv", index=False)