# .github/workflows/ci.yaml
name: Flutter CI

on:
  push:
    branches: [ main ] # Or your primary branch name
  pull_request:
    branches: [ main ] # Or your primary branch name

jobs:
  analyze_and_test:
    name: Analyze & Test
    runs-on: ubuntu-latest # Use the latest Ubuntu runner

    steps:
      - name: Checkout code
        uses: actions/checkout@v4 # Use the latest checkout action

      - name: Set up Flutter
        uses: subosito/flutter-action@v2 # Use the latest flutter action
        with:
          channel: 'stable' # Or specify a Flutter version
          # Optional: cache Flutter dependencies
          cache: true 

      - name: Install dependencies
        run: flutter pub get

      - name: Check formatting
        run: flutter format --output=none --set-exit-if-changed .

      - name: Analyze project code
        run: flutter analyze

      # Note: Ensure you have tests in the `test` directory
      - name: Run tests
        run: flutter test 