// Send Personalized Notifications
// This Supabase Edge Function sends personalized notifications to users based on their profiles
// It can be scheduled to run at specific times using Supabase Scheduled Functions

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

interface DeviceToken {
    id: string
    user_id: string
    fcm_token: string
    last_updated: string
    device_info?: string
}

interface Profile {
    id: string
    age_group?: string
    gender?: string
    family_status?: string
    goals?: string[]
}

// Types of notifications
enum NotificationType {
    Morning = 'morning',      // 6am-12pm
    Afternoon = 'afternoon',  // 12pm-6pm
    Evening = 'evening',      // 6pm-10pm
    General = 'general'       // Default
}

// Firebase Cloud Messaging API endpoint
const FCM_URL = 'https://fcm.googleapis.com/fcm/send'

serve(async (req) => {
    try {
        // Get request data
        const { apiKey, schedule } = await req.json()

        // Validation
        if (!apiKey) {
            return new Response(JSON.stringify({ error: 'FCM API key is required' }), {
                headers: { 'Content-Type': 'application/json' },
                status: 400,
            })
        }

        // Create Supabase client using service role (necessary for admin operations)
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL') ?? '',
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        )

        // Determine notification type based on schedule or current time
        const notificationType = getNotificationTypeFromSchedule(schedule)

        // Get active device tokens
        const { data: deviceTokens, error: tokenError } = await supabaseAdmin
            .from('device_tokens')
            .select('*')

        if (tokenError) {
            console.error('Error fetching device tokens:', tokenError)
            return new Response(JSON.stringify({ error: 'Failed to fetch device tokens' }), {
                headers: { 'Content-Type': 'application/json' },
                status: 500,
            })
        }

        // If no tokens found, return success but with empty result
        if (!deviceTokens || deviceTokens.length === 0) {
            return new Response(JSON.stringify({ result: 'No device tokens found' }), {
                headers: { 'Content-Type': 'application/json' },
            })
        }

        // Group tokens by user_id for personalization
        const tokensByUser = groupTokensByUser(deviceTokens as DeviceToken[])

        // Fetch user profiles for personalization
        const userIds = Object.keys(tokensByUser)
        const { data: profiles, error: profileError } = await supabaseAdmin
            .from('user_profiles')
            .select('id, age_group, gender, family_status, goals')
            .in('id', userIds)

        if (profileError) {
            console.error('Error fetching profiles:', profileError)
            // Continue with default messages if profiles can't be fetched
        }

        // Map profiles by user ID for easy lookup
        const profilesMap = (profiles || []).reduce((map, profile) => {
            map[profile.id] = profile
            return map
        }, {} as Record<string, Profile>)

        // Send notifications to all users
        const results = await Promise.all(
            Object.entries(tokensByUser).map(async ([userId, tokens]) => {
                const profile = profilesMap[userId]
                const notificationContent = getPersonalizedNotification(profile, notificationType)
                return sendNotificationToUser(tokens, notificationContent, apiKey)
            })
        )

        // Return results
        return new Response(JSON.stringify({
            result: 'Notifications sent',
            sent: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            type: notificationType
        }), {
            headers: { 'Content-Type': 'application/json' },
        })

    } catch (error) {
        console.error('Unexpected error:', error)
        return new Response(JSON.stringify({ error: 'Internal server error' }), {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
        })
    }
})

// Group device tokens by user
function groupTokensByUser(tokens: DeviceToken[]): Record<string, string[]> {
    return tokens.reduce((result, token) => {
        if (!result[token.user_id]) {
            result[token.user_id] = []
        }
        result[token.user_id].push(token.fcm_token)
        return result
    }, {} as Record<string, string[]>)
}

// Get notification type based on schedule or current time
function getNotificationTypeFromSchedule(schedule?: string): NotificationType {
    // If specific schedule is provided, use it
    if (schedule) {
        return schedule as NotificationType
    }

    // Otherwise determine based on current hour (UTC)
    const hour = new Date().getUTCHours()

    if (hour >= 6 && hour < 12) {
        return NotificationType.Morning
    } else if (hour >= 12 && hour < 18) {
        return NotificationType.Afternoon
    } else if (hour >= 18 && hour < 22) {
        return NotificationType.Evening
    } else {
        return NotificationType.General
    }
}

// Send notification to a user's devices
async function sendNotificationToUser(
    tokens: string[],
    notification: { title: string, body: string },
    apiKey: string
): Promise<{ success: boolean, tokens: string[], error?: string }> {
    try {
        // Configure the FCM message
        const message = {
            registration_ids: tokens, // Multiple tokens
            notification: {
                title: notification.title,
                body: notification.body,
                sound: 'default'
            },
            data: {
                type: 'detox_reminder'
            },
            priority: 'high',
            content_available: true
        }

        // Send the message to FCM
        const response = await fetch(FCM_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `key=${apiKey}`
            },
            body: JSON.stringify(message)
        })

        if (!response.ok) {
            const errorText = await response.text()
            console.error('FCM Error:', errorText)
            return { success: false, tokens, error: errorText }
        }

        const result = await response.json()
        return { success: true, tokens, ...result }

    } catch (error) {
        console.error('Error sending notification:', error)
        return { success: false, tokens, error: error.message }
    }
}

// Get personalized notification content based on user profile
function getPersonalizedNotification(
    profile: Profile | undefined,
    type: NotificationType
): { title: string, body: string } {
    // If no profile or missing data, return general message
    if (!profile) {
        return getGeneralNotification(type)
    }

    // Determine which set of messages to use based on family status
    if (profile.family_status === 'Have children' || profile.family_status?.includes('children')) {
        return getParentNotification(type)
    } else if (profile.family_status === 'Single' || profile.family_status?.includes('Single')) {
        return getSingleNotification(type)
    } else if (profile.goals?.includes('Improve focus & concentration') ||
        profile.goals?.some(goal => goal.includes('focus'))) {
        return getProfessionalNotification(type)
    } else {
        return getGeneralNotification(type)
    }
}

// Get a random message from a list
function getRandomMessage(messages: Array<{ title: string, body: string }>): { title: string, body: string } {
    const index = Math.floor(Math.random() * messages.length)
    return messages[index]
}

// Get notification for parents
function getParentNotification(type: NotificationType): { title: string, body: string } {
    switch (type) {
        case NotificationType.Morning:
            return getRandomMessage([
                {
                    title: 'Good Morning!',
                    body: 'The time with your children is precious. Put your phone down and share a morning moment with them.',
                },
                {
                    title: 'Start the Day Together',
                    body: 'How about a quick family breakfast without screens? The connection you build now lasts forever.',
                },
            ])

        case NotificationType.Afternoon:
            return getRandomMessage([
                {
                    title: 'Afternoon Break',
                    body: 'Take 15 minutes to be fully present with your family. Your phone can wait.',
                },
                {
                    title: 'Family Connection',
                    body: 'Childhoods go by quickly. Pause your scrolling and make some afternoon memories instead.',
                },
            ])

        case NotificationType.Evening:
            return getRandomMessage([
                {
                    title: 'Family Evening',
                    body: 'The evening hours are golden for family connection. How about a device-free dinner tonight?',
                },
                {
                    title: 'Bedtime Connection',
                    body: 'Bedtime stories create more lasting memories than social media. Be present tonight.',
                },
            ])

        case NotificationType.General:
        default:
            return getRandomMessage([
                {
                    title: 'Family Mindfulness',
                    body: 'The time with your children is precious. Put your phone down and give them your full attention.',
                },
                {
                    title: 'Digital Parenting',
                    body: 'Being present creates stronger family bonds. Take a break from screens and connect with your children.',
                },
            ])
    }
}

// Get notification for singles
function getSingleNotification(type: NotificationType): { title: string, body: string } {
    switch (type) {
        case NotificationType.Morning:
            return getRandomMessage([
                {
                    title: 'Mindful Morning',
                    body: 'Start your day intentionally. Try a 10-minute phone-free breakfast to set a peaceful tone.',
                },
                {
                    title: 'Morning for You',
                    body: 'Give yourself the gift of a distraction-free morning. The world can wait a few minutes.',
                },
            ])

        case NotificationType.Afternoon:
            return getRandomMessage([
                {
                    title: 'Afternoon Reset',
                    body: 'The world is fast and loud. Take a moment for yourself, away from your phone.',
                },
                {
                    title: 'Mid-day Pause',
                    body: 'Your attention is valuable. Step away from the screen and reconnect with yourself.',
                },
            ])

        case NotificationType.Evening:
            return getRandomMessage([
                {
                    title: 'Evening Peace',
                    body: 'Create a peaceful evening ritual without screens. Your mind will thank you for the rest.',
                },
                {
                    title: 'Screen-Free Evening',
                    body: 'Scrolling before bed affects sleep quality. Try a book or gentle stretching instead.',
                },
            ])

        case NotificationType.General:
        default:
            return getRandomMessage([
                {
                    title: 'Time for You',
                    body: 'The world is fast and loud. Take a moment for yourself, away from your phone.',
                },
                {
                    title: 'Digital Balance',
                    body: 'Your attention is your most valuable asset. Invest it in your wellbeing, not your screen.',
                },
            ])
    }
}

// Get notification for professionals/focus-oriented users
function getProfessionalNotification(type: NotificationType): { title: string, body: string } {
    switch (type) {
        case NotificationType.Morning:
            return getRandomMessage([
                {
                    title: 'Productive Morning',
                    body: 'Start your day with intention, not distraction. Try 30 minutes of focused work before checking messages.',
                },
                {
                    title: 'Morning Focus',
                    body: 'Your morning sets the tone for your day. Protect your best hours from digital distractions.',
                },
            ])

        case NotificationType.Afternoon:
            return getRandomMessage([
                {
                    title: 'Afternoon Clarity',
                    body: 'Short breaks without your phone help you stay focused and relaxed. Take 10 minutes to reset.',
                },
                {
                    title: 'Focus Reset',
                    body: 'Combat the afternoon slump with a brief digital detox. Step away from all screens for 15 minutes.',
                },
            ])

        case NotificationType.Evening:
            return getRandomMessage([
                {
                    title: 'Evening Boundaries',
                    body: 'Create a mental boundary between work and rest. Put work communications aside for the evening.',
                },
                {
                    title: 'Productivity Reset',
                    body: 'Allow your brain to truly recharge by stepping away from screens this evening.',
                },
            ])

        case NotificationType.General:
        default:
            return getRandomMessage([
                {
                    title: 'Focus Enhancer',
                    body: 'Short breaks without your phone help you stay focused and relaxed.',
                },
                {
                    title: 'Productivity Boost',
                    body: 'Your best work happens in a state of flow. Protect your focus by setting healthy boundaries with technology.',
                },
            ])
    }
}

// Get general notification for users with no specific profile
function getGeneralNotification(type: NotificationType): { title: string, body: string } {
    switch (type) {
        case NotificationType.Morning:
            return getRandomMessage([
                {
                    title: 'Morning Mindfulness',
                    body: 'Start your day intentionally. Take a few moments away from screens to set your priorities.',
                },
                {
                    title: 'Good Morning',
                    body: 'Consider beginning your day with presence, not pixels. Your attention deserves direction.',
                },
            ])

        case NotificationType.Afternoon:
            return getRandomMessage([
                {
                    title: 'Afternoon Reset',
                    body: 'Take a brief digital detox to refresh your mind. Just 10 minutes can make a difference.',
                },
                {
                    title: 'Midday Mindfulness',
                    body: 'Your attention is being pulled in many directions. Reclaim it with a brief screen break.',
                },
            ])

        case NotificationType.Evening:
            return getRandomMessage([
                {
                    title: 'Evening Wind Down',
                    body: 'Digital blue light can affect sleep quality. Consider switching to offline activities now.',
                },
                {
                    title: 'Rest & Recovery',
                    body: 'Your mind deserves rest from digital stimulation. Create some screen-free time tonight.',
                },
            ])

        case NotificationType.General:
        default:
            return getRandomMessage([
                {
                    title: 'Digital Wellness',
                    body: 'Your digital life should support your real life, not replace it. Take a moment to reconnect offline.',
                },
                {
                    title: 'Mindful Technology',
                    body: 'Use technology intentionally, not habitually. Your attention is too valuable to give away unconsciously.',
                },
            ])
    }
} 