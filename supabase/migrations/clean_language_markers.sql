-- SQL script to clean language markers from food recommendations
-- This script removes language markers like [XX] and (xx) from food names and descriptions

-- Update food names to remove language markers
UPDATE public.food_recommendations
SET name = TRIM(
  REGEXP_REPLACE(
    REGEXP_REPLACE(name, '^\s*\[\w+\]\s*', ''),  -- Remove [XX] at the beginning
    '\s*\(\w+\)\s*$', '')                        -- Remove (xx) at the end
);

-- Update food descriptions to remove language markers
UPDATE public.food_recommendations
SET description = TRIM(
  REGEXP_REPLACE(
    REGEXP_REPLACE(description, '^\s*\[\w+\]\s*', ''),  -- Remove [XX] at the beginning
    '\s*\(\w+\)\s*$', '')                               -- Remove (xx) at the end
);

-- Verify changes (optional - you can remove this part after execution)
SELECT id, name, description 
FROM public.food_recommendations 
LIMIT 20;
