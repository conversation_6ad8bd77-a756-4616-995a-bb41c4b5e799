-- Migration: Create notifications and comment likes tables
-- This migration creates the necessary tables for notifications and comment like functionality

-- Create user_notifications table
CREATE TABLE IF NOT EXISTS public.user_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'general',
    action_data JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_read B<PERSON><PERSON><PERSON><PERSON> DEFAULT false NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_notifications_user_id_idx ON public.user_notifications(user_id);
CREATE INDEX IF NOT EXISTS user_notifications_timestamp_idx ON public.user_notifications(timestamp DESC);
CREATE INDEX IF NOT EXISTS user_notifications_is_read_idx ON public.user_notifications(user_id, is_read);

-- Enable RLS
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

-- Create policies for user_notifications
CREATE POLICY "Users can view their own notifications"
    ON public.user_notifications
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
    ON public.user_notifications
    FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications"
    ON public.user_notifications
    FOR DELETE
    USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all notifications"
    ON public.user_notifications
    USING (auth.role() = 'service_role');

-- Update activity_comments table to include likes count
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'activity_comments' 
        AND column_name = 'likes'
    ) THEN
        ALTER TABLE public.activity_comments ADD COLUMN likes INTEGER DEFAULT 0 NOT NULL;
        
        -- Create index for likes
        CREATE INDEX IF NOT EXISTS activity_comments_likes_idx ON public.activity_comments(likes DESC);
    END IF;
END
$$;

-- Create activity_comment_likes table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.activity_comment_likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    comment_id UUID REFERENCES public.activity_comments(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Unique constraint to prevent duplicate likes
    UNIQUE(comment_id, user_id)
);

-- Create indexes for comment likes
CREATE INDEX IF NOT EXISTS activity_comment_likes_comment_id_idx ON public.activity_comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS activity_comment_likes_user_id_idx ON public.activity_comment_likes(user_id);

-- Enable RLS for comment likes
ALTER TABLE public.activity_comment_likes ENABLE ROW LEVEL SECURITY;

-- Create policies for activity_comment_likes
CREATE POLICY "Users can view all comment likes"
    ON public.activity_comment_likes
    FOR SELECT
    USING (true);

CREATE POLICY "Users can manage their own comment likes"
    ON public.activity_comment_likes
    FOR ALL
    USING (auth.uid() = user_id);

-- Create function to update comment likes count
CREATE OR REPLACE FUNCTION update_comment_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment likes count
        UPDATE public.activity_comments 
        SET likes = likes + 1 
        WHERE id = NEW.comment_id;
        
        -- Create notification for comment author (but not if they like their own comment)
        INSERT INTO public.user_notifications (user_id, title, message, type, action_data)
        SELECT 
            ac.user_id,
            'Someone liked your comment!',
            up.username || ' liked your comment: "' || 
            CASE 
                WHEN LENGTH(ac.text) > 50 THEN LEFT(ac.text, 50) || '...'
                ELSE ac.text
            END || '"',
            'commentLike',
            jsonb_build_object(
                'comment_id', NEW.comment_id,
                'activity_id', ac.activity_id,
                'liker_name', up.username
            )
        FROM public.activity_comments ac
        LEFT JOIN public.user_profiles up ON up.id = NEW.user_id
        WHERE ac.id = NEW.comment_id 
        AND ac.user_id != NEW.user_id; -- Don't notify if user likes their own comment
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement likes count
        UPDATE public.activity_comments 
        SET likes = GREATEST(0, likes - 1)
        WHERE id = OLD.comment_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic likes count updates
DROP TRIGGER IF EXISTS trigger_update_comment_likes_count ON public.activity_comment_likes;
CREATE TRIGGER trigger_update_comment_likes_count
    AFTER INSERT OR DELETE ON public.activity_comment_likes
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_likes_count();

-- Update existing comments to calculate current likes count
UPDATE public.activity_comments 
SET likes = (
    SELECT COUNT(*) 
    FROM public.activity_comment_likes 
    WHERE comment_id = activity_comments.id
);

-- Add comment to tables
COMMENT ON TABLE public.user_notifications IS 'Stores user notifications for various events';
COMMENT ON TABLE public.activity_comment_likes IS 'Stores likes for activity comments';
COMMENT ON COLUMN public.activity_comments.likes IS 'Number of likes this comment has received'; 