# SQL Migrations for DetoxMe

This directory contains SQL migrations for the DetoxMe app's Supabase database.

## Migration files

- `20230707000000_add_user_id_unique_constraint.sql` - Adds a unique constraint on the `user_id` column in the `detox_points` table to enable upsert operations
- `20230707000001_create_initialize_detox_points_function.sql` - Creates an RPC function to initialize detox points for a user
- `20230707000002_update_detox_points_schema.sql` - Fixes the schema by enforcing UUID format for the id column and converting any temporary IDs
- `20230707000003_fix_detox_points_fallback.sql` - Fallback migration using a simpler approach to fix UUID issues in the detox_points table

## How to apply migrations

You can apply these migrations in several ways:

### Using the Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy the contents of each migration file
4. Paste into the SQL Editor
5. Click "Run" to execute the SQL

**Important Note**: For the UUID issue, you should apply `20230707000003_fix_detox_points_fallback.sql` if you encounter any errors with `20230707000002_update_detox_points_schema.sql`. The fallback uses a simpler approach that's more likely to succeed.

### Using Supabase CLI

If you have the Supabase CLI installed:

```bash
supabase db push
```

This will push all migrations in this directory to your Supabase project.

## Verification

After applying migrations, you can verify they were successful:

### Verify unique constraint:

```sql
SELECT
  c.conname AS constraint_name,
  c.contype AS constraint_type,
  pg_get_constraintdef(c.oid) AS constraint_definition
FROM pg_constraint c
JOIN pg_namespace n ON n.oid = c.connamespace
WHERE c.conrelid = 'public.detox_points'::regclass
  AND n.nspname = 'public';
```

This should show the `unique_user_id` constraint.

### Verify RPC function:

```sql
SELECT
  p.proname AS function_name,
  pg_get_functiondef(p.oid) AS function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
  AND p.proname = 'initialize_user_detox_points';
```

This should show the definition of the `initialize_user_detox_points` function.

### Verify id column type:

```sql
SELECT 
  column_name, 
  data_type 
FROM 
  information_schema.columns 
WHERE 
  table_schema = 'public' 
  AND table_name = 'detox_points' 
  AND column_name = 'id';
```

This should show that the id column is of type 'uuid'. 