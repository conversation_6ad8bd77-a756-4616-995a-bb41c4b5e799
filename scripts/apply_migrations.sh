#!/bin/bash

# Script to apply Supabase migrations
# This script requires the Supabase CLI to be installed

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Please install it first."
    echo "Visit https://supabase.com/docs/guides/cli for installation instructions."
    exit 1
fi

# Navigate to the project root
cd "$(dirname "$0")/.."

# Apply migrations
echo "Applying Supabase migrations..."
supabase db reset

echo "Migrations applied successfully!"
