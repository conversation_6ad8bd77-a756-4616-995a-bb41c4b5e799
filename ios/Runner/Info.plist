<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>DetoxMe</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>detoxme</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		
		<!-- Live Activities Support -->
		<key>NSSupportsLiveActivities</key>
		<true/>

		<!-- Apple Sign In Configuration -->
		<key>CFBundleURLTypes</key>
		<array>
			<!-- URL scheme for app deep linking -->
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.innovatio.detoxme</string>
				</array>
			</dict>
			<!-- URL scheme for Google Sign-in -->
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.383615206409-1sjen0b82robhckcdd22ngdqt5tcnnda</string>
				</array>
			</dict>
		</array>

		<!-- Associated Domains for Universal Links -->
		<key>com.apple.developer.associated-domains</key>
		<array>
			<string>applinks:vbsdxnhkpoipturpmshl.supabase.co</string>
			<string>applinks:detoxme-3741a.firebaseapp.com</string>
			<string>webcredentials:vbsdxnhkpoipturpmshl.supabase.co</string>
		</array>

		<!-- Sign in with Apple capability -->
		<key>com.apple.developer.applesignin</key>
		<array>
			<string>Default</string>
		</array>

		<!-- Location permissions -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to your location to show nearby activities and calculate distances.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This app needs access to your location to track outdoor activities even when the app is in the background.</string>

		<!-- Allow arbitrary loads for authentication -->
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
	</dict>
</plist>
