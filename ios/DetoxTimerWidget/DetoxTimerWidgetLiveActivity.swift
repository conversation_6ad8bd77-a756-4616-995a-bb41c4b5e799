//
//  DetoxTimerWidgetLiveActivity.swift
//  DetoxTimerWidget
//
//  Created by <PERSON> on 4/25/25.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct DetoxTimerWidgetAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

struct DetoxTimerWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: DetoxTimerWidgetAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

extension DetoxTimerWidgetAttributes {
    fileprivate static var preview: DetoxTimerWidgetAttributes {
        DetoxTimerWidgetAttributes(name: "World")
    }
}

extension DetoxTimerWidgetAttributes.ContentState {
    fileprivate static var smiley: DetoxTimerWidgetAttributes.ContentState {
        DetoxTimerWidgetAttributes.ContentState(emoji: "😀")
     }
     
     fileprivate static var starEyes: DetoxTimerWidgetAttributes.ContentState {
         DetoxTimerWidgetAttributes.ContentState(emoji: "🤩")
     }
}

#Preview("Notification", as: .content, using: DetoxTimerWidgetAttributes.preview) {
   DetoxTimerWidgetLiveActivity()
} contentStates: {
    DetoxTimerWidgetAttributes.ContentState.smiley
    DetoxTimerWidgetAttributes.ContentState.starEyes
}
