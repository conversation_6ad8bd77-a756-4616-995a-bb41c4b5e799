PODS:
  - app_links (0.0.2):
    - Flutter
  - audioplayers_da<PERSON>win (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_app_group_directory (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - live_activities (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_group_directory (from `.symlinks/plugins/flutter_app_group_directory/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - live_activities (from `.symlinks/plugins/live_activities/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_app_group_directory:
    :path: ".symlinks/plugins/flutter_app_group_directory/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  live_activities:
    :path: ".symlinks/plugins/live_activities/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_messaging: 75bc93a4df25faccad67f6662ae872ac9ae69b64
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_group_directory: 55b5362007d1c0cb45dc1dd1e94f67d615f45a6b
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  live_activities: f2e133059358f99655c8d181d65ff54f024a6e93
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sms_autofill: b36b2147535657fea83d7f3898d7831de70bd8e4
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: e30f02f9d1c72c47bb6344a0a748c9d268180865

COCOAPODS: 1.16.2
