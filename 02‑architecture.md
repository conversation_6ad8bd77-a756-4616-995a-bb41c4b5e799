# Architektur & Projektstruktur

## Schichtenmodell (Clean Architecture)

```plaintext
/lib
├── core        # Utilities, Constants, Extensions
├── data        # Repositories, Data Sources (Hive & Supabase)
├── domain      # Entities, UseCases
└── presentation
    ├── bloc    # Cubit/Bloc-Logik
    └── ui      # Screens, Widgets, Themes, Localization
```

## Ordnerstruktur im Detail

- **core/**  
  - `constants.dart`  
  - `extensions.dart`  
  - `theme_cubit.dart`  

- **data/**  
  - `/local/` (Hive-Adapter, DAO)  
  - `/remote/` (Supabase-Service, API-Clients)  
  - `task_repository.dart`  

- **domain/**  
  - `task.dart` (Entity)  
  - `get_tasks_usecase.dart`  
  - `complete_task_usecase.dart`  

- **presentation/**  
  - `/bloc/task/` (TaskCubit, TaskState)  
  - `/ui/screens/` 
    - `/home/<USER>
      - `home_screen.dart`
      - `/widgets/` (Screen-specific widgets for Home)
    - `/leaderboard/`
      - `leaderboard_screen.dart`
      - `/widgets/`
    - `/settings/`
      - `settings_screen.dart`
      - `/widgets/`
    - `/task_detail/`
      - `task_detail_screen.dart`
      - `/widgets/`
    - `/user_selection/`
      - `user_selection_screen.dart`
      - `/widgets/`
  - `/ui/widgets/` (Common reusable widgets, e.g., TimerOverlay, BadgeDisplay)
  - `/ui/themes/`
  - `/ui/localization/`

## Entwicklungs-Flow für neue Features

1. **Analyse & UseCase**  
   - Neuen UseCase im Ordner `domain` anlegen.  
2. **Datenzugriff**  
   - Repository-Interface im `domain` definieren.  
   - Implementierung in `data` (Hive &/oder Supabase) schreiben.  
3. **State Management**  
   - Neuen Cubit in `presentation/bloc` erstellen oder bestehenden erweitern.  
   - Zustände und Methoden modellieren.  
4. **UI-Implementierung**  
   - Screen bzw. Widget entwerfen und umsetzen.  
   - Gemeinsame Styles und die Internationalisierung (i18n) verwenden.  
5. **Testing**  
   - Unit-Tests für UseCases & Cubits.  
   - Widget-Tests für wesentliche UI-Komponenten.  

## Cursor-AI Prompts (Tech Onboarding)

- **Boilerplate-Generator:**  
  > "Generate a new Cubit in `presentation/bloc/task` to manage long-duration TaskTimer states, with start, pause and reset, persisting to Hive."

- **Localization:**  
  > "Add new keys `long_break_started` and `long_break_completed` to the `en.arb` and `de.arb` files and generate the corresponding localization code using `intl_utils`."

- **Theming:**  
  > "Create a reusable `TimerOverlay` widget that adapts to both light and dark themes, showing a full-screen black overlay with a countdown timer in the center."
