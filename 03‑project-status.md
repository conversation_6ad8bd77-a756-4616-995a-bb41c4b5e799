# MindfulLife - Projektstatus & Roadmap

## Einführung
**MindfulLife** ist eine App, die Erwachsene hilft, bewusster mit ihrer Handyzeit umzugehen. Sie kombiniert empathische AI-Benachrichtigungen, personalisierte Challenges und echte Belohnungen, um digitale Detox-Ziele zu unterstützen.

---

## Projekt-Status & Offene Aufgaben

### Fertiggestellte Kernfeatures (✅)
- **Grundarchitektur**: Flutter, Supabase, Hive
- **Basis-UI**: Light/Dark Mode, i18n
- **Kinderfunktionen**: Profil, Aufgaben, Belohnungen
- **Technische Infra**: CI/CD, Fastlane, Offline-Sync

---

### Offene Erwachsenen-Features (Priorisiert)

#### High-Priority (Q3 2025)
| ID | Feature | AI-Prompt für Grafiken | Status |
|----|---------|------------------------|--------|
| T23 | **Erwachsenen-Profil** | "Minimalistisches Profil-UI für Digital Detox App, neutrales Farbschema, mit Altersgruppen-Selector und Lebensziel-Icons, clean und professionell" | ✅ | 
| T24 | **Detox Points System** | "Belohnungsdashboard mit Punkteanzeige, progressivem Level-Balken und einlösbaren Gutscheinkarten im minimalistischen Stil" | 🔄 |
| T31 | **Onboarding-Redesign** | "Serie von 5 Onboarding-Screens für Erwachsene: 1) Willkommen, 2) Lebenssituation-Auswahl, 3) Ziele, 4) Tutorial-Comic, 5) Abschluss - alle im Corporate Teal/Warmgrau" | ✅ |

#### Mid-Priority (Q4 2025)
| ID | Feature | AI-Prompt für Grafiken | Status |
|----|---------|------------------------|--------|
| T27 | **KI-Benachrichtigungen** | "Push-Notification-Designs mit empathischen Botschaften für verschiedene Profile (Eltern: Spielzeug-Icon, Berufstätige: Büro-Icon)" | 🚫 |
| T32 | **Detox Buddy Avatare** | "12 erwachsene Avatare im Flat-Design: Business-Personen, Eltern, Künstler - alle mit entspannten Posen und digitalen Detox-Symbolen" | 🚫 |
| T40 | **Marketing-Assets** | "App-Icon mit Smartphone und wachsender Pflanze im Teal/Orange, Splashscreen mit progressivem Ladebalken und MindfulLife-Logo" | 🚫 |

#### Low-Priority (Q1 2026)
| ID | Feature | AI-Prompt für Grafiken | Status |
|----|---------|------------------------|--------|
| T34 | **Community-Features** | "Gruppen-Challenge-UI mit anonymisierten Avataren, Fortschrittsbalken und Chat-Blase mit Detox-Tipps" | 🚫 |
| T35 | **B2B-Dashboard** | "Enterprise-Dashboard mit Team-Statistiken: Balkendiagramme zur Offline-Zeit, Heatmap der produktivsten Stunden" | 🚫 |

---

## AI-Prompts für visuelle Assets

### App-Logo
```text
Minimalist app icon showing a smartphone with a plant growing from its screen, teal (#008080) and warm gray (#707070) color scheme, flat design with subtle shadow, 3D render, 8K resolution - aspect ratio 1:1
```

### Onboarding-Screens
```text
Series of 5 mobile app onboarding screens for digital wellbeing:
1. Welcome screen with calming nature background
2. Life situation selector (single/parent/professional)
3. Goal setting with icon grid
4. Tutorial comic strip about phone addiction
5. Completion screen with progress bar - all in teal/orange palette, ultra-clean UI
```

### Detox Buddy Avatare
```text
Set of 12 diverse adult characters for wellness app:
- Business woman meditating
- Dad playing with kids
- Artist sketching outdoors
- Senior couple gardening
Each in semi-realistic flat design with teal accents, transparent PNG background
```

### Belohnungs-Icons
```text
50+ premium quality icons for reward system:
- Coffee cup (cafe discount)
- Book (reading time)
- Hiking boot (outdoor activity)
- Yoga mat (mindfulness)
Filled line style, teal/orange palette, 128x128px
```

---

## Technische Implementierungen

### Supabase-Schema Migration (T25)
```sql
CREATE TABLE rewards (
  id UUID PRIMARY KEY,
  partner_id UUID REFERENCES partners,
  points_required INT,
  expiry_date TIMESTAMP
);
```

### TensorFlow Lite Model (T33)
```python
# Sample mood detection model
model = Sequential([
  layers.TextVectorization(),
  layers.Embedding(256, 128),
  layers.LSTM(64),
  layers.Dense(3, activation='softmax') # Positive/Neutral/Negative
])
```

### Emergency Access Flow (T30)
```dart
void showEmergencyExit() {
  showDialog(
    context: context,
    builder: (_) => ReflectionDialog(
      question: "What made you need your phone right now?",
      onComplete: _unlockDevice
    )
  );
}
```

---

## Nächste Schritte
- Designs generieren mit obigen Prompts (2-3 Varianten pro Asset)
- T23-T24 entwickeln (Erwachsenen-Profil + Points System)
- Beta-Test planen mit Fokusgruppen (T39)

---

## Roadmap-Zeitplan
| Phase    | Zeitraum   | Ziele                                 |
|----------|------------|---------------------------------------|
| Phase 1  | Q3 2025    | Profil-System, Points, Onboarding      |
| Phase 2  | Q4 2025    | KI-Benachrichtigungen, Avatare        |
| Phase 3  | Q1 2026    | Community-Features, B2B-Integration   |

---

## Team-Zuordnung
| Rollen  | Verantwortlich | Aufgaben                                 |
|---------|---------------|------------------------------------------|
| UX/UI   | Design-Team   | Onboarding, Avatare, Belohnungs-UI       |
| AI      | AI-Team       | KI-Modelle, Push-Logik                   |
| Dev     | Flutter-Team  | Profil-System, Sync-Integration          |

---

## Feedback & Anpassungen
- Brauchst du spezifischere Prompts für ein Asset?
- Möchtest du technische Details zu einem Feature vertiefen?
- Soll die Roadmap priorisiert werden?
Bitte lass es mich wissen! 🚀

---

### Anpassungsmöglichkeiten:
1. **Priorisierung**: Füge `❗`-Markierungen für "Muss-Haben"-Features hinzu.
2. **Deadline-Optimierung**: Aktualisiere Fälligkeitsdaten basierend auf Ressourcen.
3. **Kostenplanung**: Füge Budget-Zeilen für Design/Entwicklung hinzu.