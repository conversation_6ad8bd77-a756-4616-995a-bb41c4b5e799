---
description:
globs:
alwaysApply: true
---
# Color Consistency Between List and Detail Views

This document outlines the established patterns for maintaining color consistency between list items and their corresponding detail screens in the DetoxMe app. Consistent color usage helps users visually associate items with their detailed views.

## Key Principles

1. **Entity-Based Theming**: Each entity type has a consistent color source
2. **Color Persistence**: The same color from a list item card appears in its detail view
3. **Consistent Alpha Values**: Standard alpha values are used for backgrounds, overlays, and containers
4. **Semantic Color Usage**: Colors are used consistently for specific UI elements

## Item-Detail Color Consistency

### Activities

Activities use category colors as their primary theme color. This ensures visual consistency from the activity card to the detail screen.

- In [ActivityCard](mdc:lib/presentation/ui/screens/dashboard/widgets/activity_card.dart):
  ```dart
  final categoryColor = activity.category?.color ?? theme.colorScheme.tertiary;
  
  // Card uses category color for background and border
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        categoryColor.withValues(alpha: 0.1),
        categoryColor.withValues(alpha: 0.2),
      ],
      // ...
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: categoryColor.withValues(alpha: 0.3),
      width: 1.5,
    ),
  ),
  ```

- In [ActivityDetailScreen](mdc:lib/presentation/ui/screens/activities/widgets/activity_detail_screen.dart):
  ```dart
  // Header uses the same category color
  final categoryColor = activity.category?.color ?? theme.colorScheme.tertiary;
  
  // AppBar background matches activity card color
  SliverAppBar(
    backgroundColor: categoryColor,
    // ...
  ),
  
  // Tab bar and indicators use matching color
  TabBar(
    labelColor: categoryColor,
    indicatorColor: categoryColor,
    // ...
  ),
  ```

### Food Recommendations

Food recommendations derive their colors from associated mood types or use a consistent algorithm to ensure the same color appears in both list and detail views.

- In [FoodRecommendationCard](mdc:lib/presentation/ui/screens/dashboard/widgets/food_recommendation_card.dart):
  ```dart
  // Determine color based on mood types or a fallback algorithm
  Color _getCardColor(FoodRecommendation recommendation) {
    if (recommendation.moodTypes?.isNotEmpty == true) {
      return _getMoodColor(recommendation.moodTypes!.first);
    }
    return _getRandomColor(recommendation);
  }
  
  // Use the color for card styling
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        cardColor.withValues(alpha: 0.1),
        cardColor.withValues(alpha: 0.2),
      ],
      // ...
    ),
    // ...
  ),
  ```

- In [FoodRecommendationDetailsScreen](mdc:lib/presentation/ui/screens/food_mood/food_recommendation_details_screen.dart):
  ```dart
  // Use the same algorithm to determine the color
  _recommendationColor = widget.recommendation.moodTypes?.isNotEmpty == true 
      ? _getMoodColor(widget.recommendation.moodTypes!.first)
      : _getRandomColor();
  
  // Apply to app bar
  SliverAppBar(
    backgroundColor: _recommendationColor,
    // ...
  ),
  
  // And to UI elements
  TabBar(
    labelColor: _recommendationColor,
    indicatorColor: _recommendationColor,
    // ...
  ),
  ```

## Color Algorithm Consistency

To ensure colors are consistently applied across list and detail views, identical color determination functions should be used in both components:

```dart
// Identical implementation in both list and detail components
Color _getMoodColor(MoodType? mood) {
  if (mood == null) return Colors.blueGrey;
  
  switch (mood) {
    case MoodType.happy: return Colors.amber;
    case MoodType.sad: return Colors.blue;
    case MoodType.anxious: return Colors.teal;
    case MoodType.angry: return Colors.deepOrange;
    case MoodType.tired: return Colors.purple;
    case MoodType.excited: return Colors.pink;
    case MoodType.calm: return Colors.green;
    case MoodType.neutral: return Colors.grey;
    case MoodType.inspired: return Colors.lightBlue;
    default: return Colors.blueGrey;
  }
}

// For entities without clear color mapping, use consistent algorithm
Color _getRandomColor(Entity entity) {
  final colors = [
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.green,
  ];
  
  // Using entity ID hash ensures the same entity always gets the same color
  final hash = entity.id.hashCode.abs();
  return colors[hash % colors.length];
}
```

## Standard Alpha Values

Apply consistent alpha values for UI elements:

| UI Element | Alpha Value | Example Usage |
|------------|-------------|---------------|
| Card background | 0.1 - 0.2 | `color.withValues(alpha: 0.1)` |
| Card border | 0.3 | `color.withValues(alpha: 0.3)` |
| Icon background | 0.1 | `color.withValues(alpha: 0.1)` |
| Gradient overlay (light) | 100 (0.39) | `color.withAlpha(100)` |
| Gradient overlay (dark) | 180 (0.7) | `color.withAlpha(180)` |
| Pill background | 40 (0.16) | `Colors.white.withAlpha(40)` |
| Section highlight | 30-60 (0.12-0.24) | `color.withAlpha(30)` |

## Color Application in UI Elements

### Headers
- AppBar/SliverAppBar background: Full entity color
- Image overlay gradient: Entity color with alpha values 100-180
- Title text: White with shadow for readability

### Content Elements
- Section headings: Entity color
- Interactive elements (buttons): Entity color
- Tab indicators: Entity color
- Highlighted sections: Entity color with alpha values 30-60

### Icons
- Icon backgrounds: Entity color with alpha value 0.1
- Icon color: Entity color

## Implementation Guidelines

1. **Extract Color Logic**
   - Place color determination methods in a common location or implement identically
   - Use the same color sources in both list and detail views

2. **Color Extraction Timing**
   - For list views, extract color during build
   - For detail views, set color in initState and update on content changes

3. **Variable Usage**
   - Store colors in class variables to avoid recalculation
   - Update stored colors when content changes

4. **Theme Integration**
   - Always use theme colors as fallbacks
   - Apply accent colors through theme when appropriate

5. **Consistent Methods**
   ```dart
   // In initState
   _entityColor = _determineEntityColor(entity);
   
   // When entity updates
   if (_currentEntity != newEntity) {
     setState(() {
       _currentEntity = newEntity;
       _entityColor = _determineEntityColor(newEntity);
     });
   }
   ```
