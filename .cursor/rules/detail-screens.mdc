---
description:
globs:
alwaysApply: true
---
# Detail Screen Design Patterns

This document outlines the established design patterns for detail screens in the DetoxMe app, focusing on the implementation in [ActivityDetailScreen](mdc:lib/presentation/ui/screens/activities/widgets/activity_detail_screen.dart) and [FoodRecommendationDetailScreen](mdc:lib/presentation/ui/screens/food_mood/food_recommendation_details_screen.dart).

## Flexible Header Pattern

### Collapsible Header Structure
- Use `NestedScrollView` with `SliverAppBar` for collapsible header
- Set `pinned: true` to keep the app bar visible while scrolling
- Set `expandedHeight: 250.0` for consistent header size
- Use `FlexibleSpaceBar` with `background` for the header content
- Monitor scroll position to animate title opacity

```dart
return NestedScrollView(
  controller: _scrollController,
  headerSliverBuilder: (context, innerBoxIsScrolled) {
    return [
      SliverAppBar(
        expandedHeight: 250.0,
        floating: false,
        pinned: true,
        backgroundColor: itemColor,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: _buildFlexibleHeader(context, state),
        ),
        title: AnimatedOpacity(
          opacity: _isScrolled ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(item.title, style: titleStyle),
        ),
        centerTitle: true,
      ),
      // Tab bar as SliverPersistentHeader
    ];
  },
  body: TabBarView(...),
);
```

### Scroll Behavior Implementation
- Add a `ScrollController` to monitor scroll position
- Implement a `_onScroll` method to update header state
- Track scroll position with a `_isScrolled` boolean state
- Add/remove listener in `initState`/`dispose`

```dart
@override
void initState() {
  super.initState();
  _scrollController.addListener(_onScroll);
}

void _onScroll() {
  setState(() {
    _isScrolled = _scrollController.offset > 10;
  });
}

@override
void dispose() {
  _scrollController.removeListener(_onScroll);
  _scrollController.dispose();
  super.dispose();
}
```

## Tab Bar Implementation

### Persistent Tab Header
- Use `SliverPersistentHeader` with custom delegate
- Create a `_SliverTabBarDelegate` class to manage tab bar rendering
- Use consistent colors based on item theme
- Set `pinned: true` to keep tab bar visible while scrolling

```dart
SliverPersistentHeader(
  delegate: _SliverTabBarDelegate(
    TabBar(
      controller: _tabController,
      tabs: [Tab(text: 'Info'), Tab(text: 'Discussion')],
      labelColor: itemColor,
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.bold,
        fontSize: 16,
      ),
      unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
      indicatorColor: itemColor,
    ),
    backgroundColor: theme.scaffoldBackgroundColor,
  ),
  pinned: true,
),
```

### Tab Bar Delegate Implementation
```dart
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  final Color backgroundColor;

  _SliverTabBarDelegate(this.tabBar, {required this.backgroundColor});

  @override
  Widget build(context, shrinkOffset, overlapsContent) {
    return Container(color: backgroundColor, child: tabBar);
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant _SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar ||
        backgroundColor != oldDelegate.backgroundColor;
  }
}
```

## Header Content Design

### Header Background
- Use item-specific color for gradient overlay
- Apply `CachedNetworkImage` for remote images with error handling
- Implement consistent placeholder for image loading/error states
- Create gradient overlay with semi-transparent item-specific colors

```dart
Positioned.fill(
  child: Stack(
    children: [
      // Image
      Positioned.fill(
        child: CachedNetworkImage(
          imageUrl: item.imageUrl,
          fit: BoxFit.cover,
          maxWidthDiskCache: 800,
          maxHeightDiskCache: 400,
          memCacheWidth: 800,
          memCacheHeight: 400,
          useOldImageOnUrlChange: true,
          placeholder: (context, url) => _buildImagePlaceholder(),
          errorWidget: (context, error, stackTrace) => _buildImagePlaceholder(),
        ),
      ),
      // Gradient overlay
      Positioned.fill(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                itemColor.withAlpha(100),
                itemColor.withAlpha(180),
              ],
            ),
          ),
        ),
      ),
    ],
  ),
)
```

### Header Content Structure
- Place title and metadata at bottom left with shadow for readability
- Title with `headlineSmall` style, bold, white color, and text shadow
- Add metadata pill with semi-transparent white background
- Place voting controls at bottom right with semi-transparent container

```dart
// Title and metadata at bottom left
Positioned(
  left: 20,
  right: 20,
  bottom: 20,
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      Text(
        item.title,
        style: theme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: [
            Shadow(color: Colors.black45, blurRadius: 3, offset: Offset(0, 1)),
          ],
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      const SizedBox(height: 8),
      // Metadata pill
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(40),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(metadataIcon, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text(
              metadataText,
              style: metadataTextStyle,
            ),
          ],
        ),
      ),
    ],
  ),
),

// Voting controls at bottom right
Positioned(
  bottom: 20,
  right: 20,
  child: Material(
    color: Colors.transparent,
    child: _buildVotingControls(),
  ),
),
```

## Content Tab Design

### Info Tab
- Use `SingleChildScrollView` with `BouncingScrollPhysics`
- Create consistent sections with title + content pattern
- Include eye-catching rewards/points section
- Add bottom space for floating action button (if present)

```dart
SingleChildScrollView(
  physics: const BouncingScrollPhysics(),
  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Creator info if available
      if (item.createdBy != null) _buildCreatorInfo(),
      
      // Description section
      _buildSection(
        context,
        title: l10n.description,
        child: Text(
          item.description,
          style: theme.textTheme.bodyLarge,
        ),
      ),
      const SizedBox(height: 24),
      
      // Eye-catching points/benefits section
      _buildRewardsSection(),
      const SizedBox(height: 24),
      
      // Additional sections as needed
      ...
      
      // Bottom space for action button
      const SizedBox(height: 80),
    ],
  ),
),
```

### Reward/Benefits Section
- Use gradient background with item-specific color
- Apply eye-catching icon and typography
- Use consistent borders and rounded corners

```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(20),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        itemColor.withAlpha(30),
        itemColor.withAlpha(60),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: itemColor.withAlpha(100), width: 1),
  ),
  child: Column(
    children: [
      Text(titleText, style: titleStyle, textAlign: TextAlign.center),
      const SizedBox(height: 12),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(rewardIcon, color: itemColor, size: 32),
          const SizedBox(width: 8),
          Text(rewardValueText, style: valueStyle),
        ],
      ),
      const SizedBox(height: 4),
      Text(subtitleText, style: subtitleStyle, textAlign: TextAlign.center),
    ],
  ),
),
```

### Discussion Tab
- Use `Column` with `Expanded` list area and bottom input
- Show empty state when no comments exist
- Style comments as cards with consistent margins
- Implement comment input row with matching theme colors

```dart
Column(
  children: [
    // Comments list
    Expanded(
      child: comments.isEmpty
          ? _buildEmptyCommentsState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: comments.length,
              itemBuilder: (context, index) => _buildCommentCard(comments[index]),
            ),
    ),

    // Comment input
    Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
              minLines: 1,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _submitComment,
            icon: Icon(Icons.send, color: Colors.white),
            style: IconButton.styleFrom(
              backgroundColor: itemColor,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    ),
  ],
),
```

## Color Consistency

### Entity-Based Colors
- For activities, use `activity.category.color`
- For food recommendations, derive color from mood type or use a consistent algorithm:
  ```dart
  Color _getMoodColor(MoodType? mood) {
    if (mood == null) return Colors.blueGrey;
    
    switch (mood) {
      case MoodType.happy: return Colors.amber;
      case MoodType.sad: return Colors.blue;
      ...
    }
  }
  
  Color _getRandomColor() {
    final colors = [Colors.orange, Colors.purple, ...];
    final hash = item.id.hashCode.abs();
    return colors[hash % colors.length];
  }
  ```

### Color Application
- Set SliverAppBar background to entity color
- Use the same color for tab indicator
- Apply the color to section headings
- Use the color for action buttons
- Apply consistent alpha values for gradients

## Animation Patterns

### Loading Animations
- Implement staggered loading with consistent timing
- Apply `FadeInUp` with 800ms duration and appropriate delays

### Interactive Animations
- Use animation controllers for interactive elements (likes/upvotes)
- Show heart/star burst animations on positive interactions
- Reset animations on completion

## Implementation Notes

1. Ensure consistent initialization in initState:
   - TabController
   - ScrollController
   - Animation controllers
   - Initial data/state
   
2. Clean up in dispose:
   - Remove listeners
   - Dispose controllers
   
3. Use BlocConsumers for monitoring state changes:
   - Listen for changes in active/completed state
   - Listen for content updates
   - Listen for error states

4. Extract reusable components into private methods:
   - `_buildFlexibleHeader`
   - `_buildSection`
   - `_buildEmptyState`
   - `_buildVotingControls`
