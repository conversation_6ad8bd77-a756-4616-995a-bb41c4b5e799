---
description:
globs:
alwaysApply: true
---
# DetoxMe App Rules Index

This document serves as an index for all the Cursor Rules available in this project. The rules are organized into two main categories:

## Global Flutter Best Practices

These rules apply to any Flutter project and contain general best practices for Flutter and Dart development.

- [**Global Flutter Practices**](mdc:global_flutter_practices.mdc) - Best practices for Flutter and Dart development that apply to any project, including:
  - Effective Dart naming conventions
  - Type and function usage
  - Code style guidelines
  - Bloc/Cubit state management principles
  - Widget and performance best practices
  - Clean architecture principles
  - Testing best practices

## DetoxMe App-Specific Guidelines

These rules are specific to the DetoxMe app, including its architecture, design system, and UI components.

- [**DetoxMe App Guidelines**](mdc:detox_app_guidelines.mdc) - Comprehensive guidelines for the DetoxMe app, including:
  - Project architecture
  - Design system
  - UI components
  - Detail screen patterns
  - State management implementation
  - Navigation guidelines
  - Error handling approaches
  - Offline-first implementation
  - Internationalization

## Specialized Guidelines

These rules provide more detailed guidance on specific aspects of the app:

- [**Detail Screens**](mdc:detail-screens.mdc) - Guidelines for implementing detail screens with flexible headers, tab bars, and consistent layouts
- [**Color Consistency**](mdc:color-consistency.mdc) - Patterns for maintaining color consistency between list items and detail views
- [**Cubit UI Patterns**](mdc:cubit-ui-patterns.mdc) - Detailed patterns for implementing proper Cubit state management in UI screens

## Legacy Rules

These rules are now consolidated into the main rule files above:

- `archtecture.mdc` - Consolidated into DetoxMe App Guidelines
- `design-guidelines.mdc` - Consolidated into DetoxMe App Guidelines
- `ui-components.mdc` - Consolidated into DetoxMe App Guidelines
- `detoxmerule.mdc` - Consolidated into Global Flutter Practices

## Using These Rules

When developing for the DetoxMe app:

1. First, follow the general Flutter best practices in the Global Flutter Practices document
2. For app-specific concerns, refer to the DetoxMe App Guidelines
3. For specialized features like detail screens or color systems, refer to the specialized guidelines

These rules should be used as a reference guide to maintain consistency in code style, architecture, and UI design throughout the app.
