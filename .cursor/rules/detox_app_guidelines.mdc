---
description:
globs:
alwaysApply: true
---
# DetoxMe App Guidelines

This document outlines design patterns, UI components, and architectural guidelines specific to the DetoxMe app.

## Project Architecture

### Layer Structure
```
/lib
├── core # Utilities, Constants, Extensions, Widgets
├── data # Repositories implementations, Data Sources (Local & Remote)
├── domain # Entities, Repository interfaces, UseCases
└── presentation
    ├── bloc # Cubits/Blocs for state management
    └── ui # Screens, Widgets, Themes
```

### Core Layer
The core directory contains app-wide utilities and base components:
- `constants` - App-wide constant values and enums
- `extensions` - Extension methods on existing types
- `widgets` - Reusable widgets used across the app
- `themes` - App theming and styling
- `router` - App navigation configuration using GoRouter
- `network` - Network info and connectivity handling
- `services` - App-wide services (notifications, analytics)

### Domain Layer
The domain directory contains business logic independent of any framework:
- `entities` - Plain Dart objects representing core business models (Activity, Mood, UserProfile, etc.)
- `repositories` - Repository interfaces
- `failure` - Domain-specific failure classes

### Data Layer
The data directory implements data access and persistence:
- `repositories` - Concrete implementations of repository interfaces
- `local` - Local data sources (Hive)
- `remote` - Remote data sources (Supabase)

### Presentation Layer
The presentation directory handles UI and state management:
- `bloc` - State management using Cubit/Bloc pattern (organized by feature)
- `ui` - UI components organized by feature
  - `screens` - Main screen widgets
  - `widgets` - Common widgets used across features

## Design System

### Color Usage

#### Primary Color Application
- Use primary color for main actions, selected states, and key UI elements
- Apply `withValues(alpha: x)` instead of `withOpacity()` for transparency
- Use color with alpha values: `theme.colorScheme.primary.withValues(alpha: 0.1)` for container backgrounds

#### Color Patterns
- Use gradient backgrounds for featured content cards (primary to secondary container colors)
- Apply `theme.colorScheme.surface` for card backgrounds
- Use themed container backgrounds for icons: `color.withValues(alpha: 0.1)`
- Selected elements use primary colors, unselected use `onSurface` colors

#### Color Consistency Between List and Detail Views
- Entities should maintain the same color between their list item and detail view
- Activities use category color, food recommendations use mood type color
- Use identical color determination methods in both list and detail components
- Store entity colors in class variables to avoid recalculation

#### Standard Alpha Values
| UI Element | Alpha Value | Example Usage |
|------------|-------------|---------------|
| Card background | 0.1 - 0.2 | `color.withValues(alpha: 0.1)` |
| Card border | 0.3 | `color.withValues(alpha: 0.3)` |
| Icon background | 0.1 | `color.withValues(alpha: 0.1)` |
| Gradient overlay (light) | 100 (0.39) | `color.withAlpha(100)` |
| Gradient overlay (dark) | 180 (0.7) | `color.withAlpha(180)` |
| Pill background | 40 (0.16) | `Colors.white.withAlpha(40)` |
| Section highlight | 30-60 (0.12-0.24) | `color.withAlpha(30)` |

### Typography Hierarchy

- Section headings: `theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)`
- Card titles: `theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)`
- Body text: `theme.textTheme.bodyMedium`
- Secondary text: `theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)`
- Metrics/stats: `theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)`

### Card & Container Design

- Consistent borderRadius for cards: `BorderRadius.circular(16)`
- Container shadows: `BoxShadow(color: theme.colorScheme.shadow.withValues(alpha: 0.05), blurRadius: 10)`
- Border styling: `Border.all(color: theme.colorScheme.outlineVariant.withValues(alpha: 0.1), width: 1)`

### Icon Styling

- Place icons in circular/rounded containers with themed backgrounds
- Icon background: `color.withValues(alpha: 0.1)`
- Container padding: 8-12px
- Icon size: 18-24px for regular icons, 40px+ for featured icons

### Animation Guidelines

- Staggered loading: FadeInUp with increasing delays for list items
- Standard animation duration: 300-800ms
- Apply smooth transitions with `Curves.easeInOut`

## UI Components

### Card Components

#### Feature Card
```dart
Container(
  width: double.infinity,
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        theme.colorScheme.primaryContainer,
        theme.colorScheme.secondaryContainer,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: theme.colorScheme.shadow.withValues(alpha: 51),
        blurRadius: 15,
        offset: const Offset(0, 6),
      ),
    ],
  ),
  child: Padding(
    padding: const EdgeInsets.all(20.0),
    child: // content
  ),
)
```

#### Stat Card
```dart
Container(
  width: 160,
  padding: const EdgeInsets.all(16),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Icon row with category color
      // Title and value
    ],
  ),
)
```

#### Activity Card
```dart
Container(
  width: 180,
  margin: const EdgeInsets.only(right: 16),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [
        color.withValues(alpha: 20),
        color.withValues(alpha: 40),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: color.withValues(alpha: 51),
      width: 1.5,
    ),
  ),
  child: // content
)
```

### Custom Components

#### Section Header
```dart
Row(
  children: [
    Icon(icon, color: theme.colorScheme.primary, size: 20),
    const SizedBox(width: 12),
    Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.onSurface,
      ),
    ),
    const Spacer(),
    if (showViewAll)
      TextButton(
        onPressed: onViewAllPressed,
        child: const Text('View All'),
      ),
  ],
)
```

#### Tag/Chip
```dart
Container(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: theme.colorScheme.primary.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(20),
  ),
  child: Text(
    label,
    style: theme.textTheme.bodySmall?.copyWith(
      color: theme.colorScheme.onSurface,
      fontWeight: FontWeight.w500,
    ),
  ),
)
```

#### Empty State
```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(28),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
      width: 0.2,
    ),
  ),
  child: Center(
    child: Column(
      children: [
        // Icon, title, message, action button
      ],
    ),
  ),
)
```

## Detail Screen Patterns

### Flexible Header Structure
```dart
NestedScrollView(
  controller: _scrollController,
  headerSliverBuilder: (context, innerBoxIsScrolled) {
    return [
      SliverAppBar(
        expandedHeight: 250.0,
        floating: false,
        pinned: true,
        backgroundColor: itemColor,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: _buildFlexibleHeader(context, state),
        ),
        title: AnimatedOpacity(
          opacity: _isScrolled ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(item.title, style: titleStyle),
        ),
        centerTitle: true,
      ),
      // Tab bar implementation
    ];
  },
  body: TabBarView(
    controller: _tabController,
    children: [
      // Info tab
      // Discussion tab
    ],
  ),
)
```

### Header Background
```dart
Positioned.fill(
  child: Stack(
    children: [
      // Background image
      Positioned.fill(
        child: CachedNetworkImage(
          imageUrl: item.imageUrl,
          fit: BoxFit.cover,
          // Caching options
          placeholder: (context, url) => _buildImagePlaceholder(),
          errorWidget: (context, error, stackTrace) => _buildImagePlaceholder(),
        ),
      ),
      // Gradient overlay
      Positioned.fill(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                itemColor.withAlpha(100),
                itemColor.withAlpha(180),
              ],
            ),
          ),
        ),
      ),
    ],
  ),
)
```

### Rewards/Benefits Section
```dart
Container(
  width: double.infinity,
  padding: const EdgeInsets.all(20),
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        itemColor.withAlpha(30),
        itemColor.withAlpha(60),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: itemColor.withAlpha(100), width: 1),
  ),
  child: Column(
    children: [
      // Title, icon, value, subtitle
    ],
  ),
)
```

## State Management Implementation

### Screen Structure with Cubit
```dart
// Provider pattern
class DetailScreen extends StatelessWidget {
  final String itemId;
  
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ItemCubit>(
          create: (context) => getIt<ItemCubit>()..loadItem(itemId),
        ),
        BlocProvider<OtherCubit>(
          create: (context) => getIt<OtherCubit>(),
        ),
      ],
      child: const DetailView(),
    );
  }
}

// View with BlocConsumer
class DetailView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ItemCubit, ItemState>(
      listener: (context, state) {
        // Handle side effects like navigation, dialogs, etc.
      },
      builder: (context, state) {
        // Build UI based on state
      },
    );
  }
}
```

### Converting from setState to Cubit
When refactoring screens from local state to Cubit pattern:
1. Identify all variables managed with `setState`
2. Create a state class with all required properties
3. Implement Cubit methods to update state
4. Replace `setState` calls with Cubit method calls
5. Replace direct state usage with BlocBuilder/Consumer

## Navigation

- Use GoRouter for app navigation
- Define routes in a central location
- Use the DetoxBottomNavBar for main navigation
- Navigation actions use `context.go()` or `context.push()` methods

## Error Handling

- Use Either type from dartz for error handling in repositories
- Define domain-specific failures in domain/failure
- Handle errors appropriately in the presentation layer
- Show user-friendly error messages with SnackBar or Dialog

## Offline-First Approach

- Local data sources serve as single source of truth
- Remote operations are queued when offline and synced when online
- Network state is monitored via the NetworkInfo service
- Repository implementations handle connection state and data sync

## Internationalization

- Localization files located in localization/arb
- Use AppLocalizations for accessing localized strings
- Access via: `final l10n = AppLocalizations.of(context)!;`
