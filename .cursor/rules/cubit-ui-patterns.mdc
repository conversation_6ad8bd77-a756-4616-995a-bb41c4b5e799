---
description:
globs:
alwaysApply: true
---
# Cubit State Management in UI Screens

This document outlines the established patterns for implementing proper state management using Cubits in the UI layer of the DetoxMe app. Following these patterns ensures consistent state handling and avoids issues with local state management.

## Key Principles

1. **Avoid Local State**: Minimize use of `setState` in screens with Cubit patterns
2. **Use BlocProvider**: Provide Cubits through `BlocProvider` for dependency injection 
3. **React to State Changes**: Use `BlocBuilder` or `BlocConsumer` to react to state updates
4. **Separate Logic from UI**: Keep business logic in Cubits, presentation logic in UI
5. **Use BlocListener for Side Effects**: Handle navigation, dialogs, and other side effects in listeners

## Implementation Patterns

### Screen Structure with Cubit

The recommended structure for screens using Cubit state management:

```dart
// Provider pattern
class ActivityScreen extends StatelessWidget {
  const ActivityScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivityCubit>(
      create: (context) => getIt<ActivityCubit>()..loadActivities(),
      child: const ActivityView(),
    );
  }
}

// View implementation with BlocConsumer
class ActivityView extends StatelessWidget {
  const ActivityView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Handle side effects like navigation, dialogs, etc.
        if (state is ActivityError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        if (state is ActivityLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ActivitiesLoaded) {
          return _buildContent(context, state.activities);
        } else if (state is ActivityError) {
          return _buildErrorState(context, state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContent(BuildContext context, List<Activity> activities) {
    // UI building logic
  }

  Widget _buildErrorState(BuildContext context, String message) {
    // Error UI building logic
  }
}
```

### Handling Multiple Cubits

For screens that require multiple Cubits, use `MultiBlocProvider` to inject them:

```dart
class DetailScreen extends StatelessWidget {
  final String itemId;
  
  const DetailScreen({Key? key, required this.itemId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ItemCubit>(
          create: (context) => getIt<ItemCubit>()..loadItem(itemId),
        ),
        BlocProvider<CommentsCubit>(
          create: (context) => getIt<CommentsCubit>()..loadComments(itemId),
        ),
        BlocProvider<ActivityTimerCubit>(
          create: (context) => getIt<ActivityTimerCubit>(),
        ),
      ],
      child: const DetailView(),
    );
  }
}
```

### Using BlocListener for Side Effects

When you need to react to state changes without rebuilding the UI, use `BlocListener`:

```dart
BlocListener<ActivityTimerCubit, ActivityTimerState>(
  listener: (context, state) {
    if (state is ActivityTimerCompleted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(l10n.activityComplete),
          content: Text(l10n.activityCompleteMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.ok),
            ),
          ],
        ),
      );
      
      // Call another cubit method
      context.read<ActivityCubit>().completeActivity();
    }
  },
  child: const SizedBox.shrink(), // No UI to build
),
```

### Multiple State Reactions with MultiBlocListener

Group multiple listeners with `MultiBlocListener`:

```dart
MultiBlocListener(
  listeners: [
    BlocListener<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Activity state reactions
      },
    ),
    BlocListener<ActivityTimerCubit, ActivityTimerState>(
      listener: (context, state) {
        // Timer state reactions
      },
    ),
    BlocListener<RewardCubit, RewardState>(
      listener: (context, state) {
        // Reward state reactions
      },
    ),
  ],
  child: Scaffold(
    // The rest of your UI
  ),
),
```

### Accessing Cubits in Event Handlers

Use `context.read()` to access Cubits in event handlers:

```dart
ElevatedButton(
  onPressed: () {
    // Get a reference to the cubit without subscribing to changes
    final activityCubit = context.read<ActivityCubit>();
    activityCubit.startActivity();
    
    // If another cubit needs to be updated as well
    context.read<ActivityTimerCubit>().startTimer();
  },
  child: Text(l10n.startActivity),
),
```

### Selective Rebuilds with BlocSelector

Use `BlocSelector` to rebuild only when specific parts of the state change:

```dart
BlocSelector<ProfileCubit, ProfileState, bool>(
  selector: (state) => state is ProfileLoaded ? state.isEditing : false,
  builder: (context, isEditing) {
    return isEditing
      ? _buildEditForm(context)
      : _buildProfileView(context);
  },
),
```

## State Classes and Extensions

### Using copyWith for State Updates

Create immutable states with `copyWith` methods for easy updates:

```dart
class ActivityDetailState extends Equatable {
  final Activity? activity;
  final bool isLoading;
  final String? error;
  final bool isActive;
  final int elapsedSeconds;
  final List<Comment> comments;
  
  const ActivityDetailState({
    this.activity,
    this.isLoading = false,
    this.error,
    this.isActive = false,
    this.elapsedSeconds = 0,
    this.comments = const [],
  });
  
  ActivityDetailState copyWith({
    Activity? activity,
    bool? isLoading,
    String? error,
    bool? isActive,
    int? elapsedSeconds,
    List<Comment>? comments,
  }) {
    return ActivityDetailState(
      activity: activity ?? this.activity,
      isLoading: isLoading ?? this.isLoading,
      error: error,  // null is valid to clear errors
      isActive: isActive ?? this.isActive,
      elapsedSeconds: elapsedSeconds ?? this.elapsedSeconds,
      comments: comments ?? this.comments,
    );
  }
  
  @override
  List<Object?> get props => [
    activity, isLoading, error, isActive, elapsedSeconds, comments
  ];
}
```

### State Extension Methods

Add extension methods to extract common state properties:

```dart
extension ActivityDetailStateExtensions on ActivityDetailState {
  bool get isCompleted => 
    activity != null && activity!.completionDate != null;
    
  bool get canStart => 
    activity != null && !isLoading && !isActive && !isCompleted;
    
  bool get canPause => isActive;
  
  bool get canResume => !isActive && elapsedSeconds > 0 && !isCompleted;
  
  bool get hasComments => comments.isNotEmpty;
  
  String get formattedElapsedTime {
    final minutes = (elapsedSeconds / 60).floor();
    final seconds = elapsedSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
```

## Converting from setState to Cubit Pattern

When refactoring a screen from local state to Cubit pattern:

1. **Identify State Variables**: Identify all variables managed with `setState`
2. **Create Cubit State Class**: Define a state class with all required properties
3. **Implement Cubit Methods**: Create methods in Cubit to update state
4. **Replace setState Calls**: Replace all `setState` calls with Cubit method calls
5. **Use BlocBuilder/Consumer**: Replace direct state usage with BlocBuilder/Consumer

### Before (with setState):

```dart
class ActivityDetailScreen extends StatefulWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  State<ActivityDetailScreen> createState() => _ActivityDetailScreenState();
}

class _ActivityDetailScreenState extends State<ActivityDetailScreen> {
  bool _isActive = false;
  int _elapsedSeconds = 0;
  Timer? _timer;
  
  void _startActivity() {
    setState(() {
      _isActive = true;
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }
  
  void _pauseActivity() {
    _timer?.cancel();
    setState(() {
      _isActive = false;
    });
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // UI implementation using _isActive and _elapsedSeconds
  }
}
```

### After (with Cubit):

```dart
// ActivityTimerCubit
class ActivityTimerCubit extends Cubit<ActivityTimerState> {
  Timer? _timer;
  
  ActivityTimerCubit() : super(const ActivityTimerState());
  
  void startTimer() {
    emit(state.copyWith(isActive: true));
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      emit(state.copyWith(
        elapsedSeconds: state.elapsedSeconds + 1
      ));
    });
  }
  
  void pauseTimer() {
    _timer?.cancel();
    emit(state.copyWith(isActive: false));
  }
  
  void resetTimer() {
    _timer?.cancel();
    emit(state.copyWith(
      isActive: false,
      elapsedSeconds: 0,
    ));
  }
  
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}

// UI Implementation
class ActivityDetailScreen extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ActivityTimerCubit>(),
      child: ActivityDetailView(activity: activity),
    );
  }
}

class ActivityDetailView extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailView({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActivityTimerCubit, ActivityTimerState>(
      builder: (context, state) {
        // UI implementation using state.isActive and state.elapsedSeconds
        return Scaffold(
          // ...
          floatingActionButton: state.isActive
              ? FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().pauseTimer(),
                  child: const Icon(Icons.pause),
                )
              : FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().startTimer(),
                  child: const Icon(Icons.play_arrow),
                ),
        );
      },
    );
  }
}
```

## Common Cubit Usage Patterns

### Loading Data

```dart
class ItemCubit extends Cubit<ItemState> {
  final ItemRepository _repository;
  
  ItemCubit(this._repository) : super(const ItemState.initial());
  
  Future<void> loadItem(String id) async {
    emit(const ItemState.loading());
    
    try {
      final item = await _repository.getItem(id);
      emit(ItemState.loaded(item));
    } catch (e) {
      emit(ItemState.error(e.toString()));
    }
  }
}
```

### Handling Form Input

```dart
class FormCubit extends Cubit<FormState> {
  FormCubit() : super(const FormState());
  
  void nameChanged(String value) {
    emit(state.copyWith(
      name: value,
      nameError: value.isEmpty ? 'Name cannot be empty' : null,
    ));
  }
  
  void emailChanged(String value) {
    final emailValid = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);
    emit(state.copyWith(
      email: value,
      emailError: !emailValid ? 'Invalid email address' : null,
    ));
  }
  
  bool get isValid => state.nameError == null && 
                     state.emailError == null &&
                     state.name.isNotEmpty &&
                     state.email.isNotEmpty;
  
  Future<void> submit() async {
    if (!isValid) return;
    
    emit(state.copyWith(isSubmitting: true));
    
    try {
      // Repository call
      emit(state.copyWith(isSuccess: true, isSubmitting: false));
    } catch (e) {
      emit(state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      ));
    }
  }
}
```

### Managing Tab State

```dart
class TabCubit extends Cubit<int> {
  TabCubit() : super(0);
  
  void changeTab(int index) => emit(index);
}

// In UI
BlocProvider(
  create: (context) => TabCubit(),
  child: BlocBuilder<TabCubit, int>(
    builder: (context, activeTabIndex) {
      return DefaultTabController(
        length: 3,
        initialIndex: activeTabIndex,
        child: Scaffold(
          appBar: AppBar(
            bottom: TabBar(
              onTap: (index) => context.read<TabCubit>().changeTab(index),
              tabs: [
                Tab(text: 'Tab 1'),
                Tab(text: 'Tab 2'),
                Tab(text: 'Tab 3'),
              ],
            ),
          ),
          body: TabBarView(
            physics: const NeverScrollableScrollPhysics(), // Prevent swiping to change tabs
            children: [
              // Tab contents
            ],
          ),
        ),
      );
    },
  ),
),
```

## Best Practices

1. **Use Dependency Injection**: Inject dependencies through constructors to allow for easy testing
2. **Keep States Immutable**: Always create new state instances; never mutate existing state
3. **Handle Error States**: Include error handling in all asynchronous operations
4. **Make Small, Focused Cubits**: Create purpose-specific Cubits rather than large, general ones
5. **Consistency in State Design**: Follow a consistent pattern for state class design
6. **Use Equatable for States**: Extend Equatable to prevent unnecessary rebuilds
