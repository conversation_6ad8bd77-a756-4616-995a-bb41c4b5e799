{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "sbp_v0_3f46b0f24a694f5b8d0ef33b1f39260dd9e5f21d"]}, "ClickUp": {"command": "npx", "args": ["-y", "@taazkareem/clickup-mcp-server@latest"], "env": {"CLICKUP_API_KEY": "*********************************************", "CLICKUP_TEAM_ID": "9018619376", "DOCUMENT_SUPPORT": "true"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSALNEW_flFCaHA_hzuRsC4xbqVygSU"}}, "image-generator-gpt-image": {"command": "npx imagegen-mcp --models gpt-image-1", "env": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************"}}}}