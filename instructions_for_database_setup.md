# Device Tokens Table Setup for Supabase

Follow these instructions to set up the `device_tokens` table in your Supabase project to enable FCM token storage and push notifications.

## Quick Fix for "column fcm_token does not exist" Error

If you're seeing the error `ERROR: 42703: column "fcm_token" does not exist`, use this quick fix method:

1. Log in to your Supabase project at https://app.supabase.com
2. Navigate to the "SQL Editor" section in the left sidebar
3. Create a new query by clicking the "+" button
4. Copy and paste the following SQL code:

```sql
-- Fix script that handles both creating the table and fixing missing columns
DO $$
BEGIN
    -- Check if table exists
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'device_tokens') THEN
        -- Create the table from scratch
        CREATE TABLE public.device_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            fcm_token TEXT NOT NULL,
            last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            device_info TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX device_tokens_user_id_idx ON public.device_tokens(user_id);
        CREATE INDEX device_tokens_fcm_token_idx ON public.device_tokens(fcm_token);
        
        -- Enable RLS
        ALTER TABLE public.device_tokens ENABLE ROW LEVEL SECURITY;

        -- Add policies
        CREATE POLICY "Users can manage their own tokens" 
            ON public.device_tokens 
            USING (auth.uid() = user_id);
            
        CREATE POLICY "Users can insert their own tokens"
            ON public.device_tokens
            FOR INSERT
            WITH CHECK (auth.uid() = user_id);
            
        CREATE POLICY "Service role can access all tokens"
            ON public.device_tokens
            USING (auth.role() = 'service_role');
            
        -- Add comment
        COMMENT ON TABLE public.device_tokens IS 'Stores FCM tokens for push notifications';
        
        RAISE NOTICE 'Created device_tokens table from scratch';
    ELSE
        -- Table exists, check if fcm_token column exists
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'device_tokens' AND column_name = 'fcm_token') THEN
            -- Add the missing column
            ALTER TABLE public.device_tokens ADD COLUMN fcm_token TEXT;
            
            -- If it was added as nullable but should be NOT NULL, update it
            -- We first add it as nullable to avoid errors on existing rows, then set a default,
            -- then make it NOT NULL
            UPDATE public.device_tokens SET fcm_token = '' WHERE fcm_token IS NULL;
            ALTER TABLE public.device_tokens ALTER COLUMN fcm_token SET NOT NULL;
            
            -- Add the missing index
            CREATE INDEX IF NOT EXISTS device_tokens_fcm_token_idx ON public.device_tokens(fcm_token);
            
            RAISE NOTICE 'Added fcm_token column to existing device_tokens table';
        ELSE
            RAISE NOTICE 'device_tokens table and fcm_token column already exist';
        END IF;
    END IF;
END
$$;
```

5. Run the query by clicking the "Run" button

This script automatically:
- Creates the table if it doesn't exist
- Adds the `fcm_token` column if the table exists but the column is missing
- Sets up all necessary indexes and permissions

## Standard Setup Options (if you prefer to start from scratch)

### Option 1: Using Supabase Studio (Web Interface)

1. Log in to your Supabase project at https://app.supabase.com
2. Navigate to the "SQL Editor" section in the left sidebar
3. Create a new query by clicking the "+" button
4. Copy and paste the following SQL code:

```sql
-- Create device_tokens table for FCM token storage
CREATE TABLE IF NOT EXISTS device_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  fcm_token TEXT NOT NULL,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  device_info TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS device_tokens_user_id_idx ON device_tokens(user_id);

-- Create index on fcm_token for faster duplicate checks
CREATE INDEX IF NOT EXISTS device_tokens_fcm_token_idx ON device_tokens(fcm_token);

-- Add row level security policies
ALTER TABLE device_tokens ENABLE ROW LEVEL SECURITY;

-- Policy for users to manage only their own tokens
CREATE POLICY "Users can manage their own tokens"
  ON device_tokens
  USING (auth.uid() = user_id);

-- Allow users to insert their own tokens
CREATE POLICY "Users can insert their own tokens"
  ON device_tokens
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for service role to access all tokens
CREATE POLICY "Service role can access all tokens"
  ON device_tokens
  USING (auth.role() = 'service_role');

-- Comment on table
COMMENT ON TABLE device_tokens IS 'Stores FCM tokens for push notifications';
```

5. Run the query by clicking the "Run" button
6. Verify that the table was created by checking the "Table Editor" section

## Option 2: Using Supabase CLI

If you have the Supabase CLI installed, you can run:

1. Save the SQL code from above to a file named `create_device_tokens_table.sql` in your project's `supabase/migrations` directory
2. Run the migration:

```bash
supabase migration up
```

3. Alternatively, push the changes directly to your remote Supabase project:

```bash
supabase db push
```

## Verify the Setup

After running the SQL, you should:

1. Check the "Table Editor" in Supabase Studio to confirm the `device_tokens` table exists
2. Verify that the RLS policies are properly applied in the "Authentication" > "Policies" section
3. Try registering a token through your app to ensure it works correctly

## Using the Table

The `device_tokens` table stores:

- `id`: Unique identifier for each token record
- `user_id`: The user this token belongs to (references auth.users)
- `fcm_token`: The Firebase Cloud Messaging token
- `last_updated`: When this token was last updated
- `device_info`: Optional info about the device (model, OS, etc.)
- `created_at`: When this record was created

The app will automatically store FCM tokens in this table when users log in, and remove them when users log out. 