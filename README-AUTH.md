# Authentication Setup Guide

This guide explains how to set up authentication for the DetoxMe app using Supabase.

## Supported Authentication Methods

The app supports the following authentication methods:

1. **Email/Password** - Standard email and password authentication
2. **Google Sign-In** - OAuth authentication with Google
3. **Apple Sign-In** - OAuth authentication with Apple (iOS/macOS only)
4. **Phone Authentication** - SMS OTP verification

## Supabase Configuration

### 1. Enable Authentication Providers

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Providers
3. Enable the following providers:
   - Email (with "Confirm email" option)
   - Phone
   - Google
   - Apple

### 2. Configure OAuth Providers

#### Google OAuth

1. Create a Google Cloud project
2. Enable the Google Identity API
3. Configure OAuth consent screen
4. Create OAuth client ID credentials
5. Add authorized redirect URIs:
   - `https://vbsdxnhkpoipturpmshl.supabase.co/auth/v1/callback`
   - `com.innovatio.detoxme://login-callback/`
6. Add the Client ID and Secret to Supabase Google provider settings

#### Apple OAuth

1. Register your app in the Apple Developer portal
2. Enable "Sign in with Apple" capability
3. Create a Services ID
4. Configure the domain and return URL in the Apple Developer portal
5. Generate a key for Sign in with Apple
6. Add the key, Team ID, and Services ID to Supabase Apple provider settings

## App Configuration

### iOS Configuration

1. Update Info.plist with the following:
   - URL schemes for deep linking
   - Associated domains for universal links
   - Sign in with Apple capability

2. Update the bundle ID in GoogleService-Info.plist to match your app's bundle ID

### Android Configuration

1. Update AndroidManifest.xml with intent filters for deep linking
2. Configure the URL scheme to match your app's package name
3. Update the google-services.json file with the correct package name

## Testing Authentication

1. Run the app on a device or simulator
2. Test each authentication method:
   - Email/Password: Register a new account and verify email
   - Google Sign-In: Click the "Continue with Google" button
   - Apple Sign-In: Click the "Continue with Apple" button (iOS/macOS only)
   - Phone Authentication: Enter a phone number and verify with OTP

## Troubleshooting

### Common Issues

1. **OAuth Redirect Errors**
   - Check that the redirect URLs are correctly configured in both Supabase and the OAuth provider

2. **Apple Sign-In Errors**
   - Ensure the app has the "Sign in with Apple" capability enabled
   - Verify that the associated domains are correctly configured

3. **Phone Authentication Errors**
   - Make sure the phone number is in E.164 format (e.g., +**********)
   - Check that SMS verification is enabled in Supabase

4. **Deep Linking Issues**
   - Verify that the URL schemes match the app's bundle ID/package name
   - Check that the intent filters are correctly configured in AndroidManifest.xml
